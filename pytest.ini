[tool:pytest]
# pytest配置文件 - 基于AQUA宪法TDD要求
minversion = 6.0
addopts = 
    -ra -q 
    --strict-markers
    --strict-config
    --cov=src
    --cov=main
    --cov-branch
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=80
    --junit-xml=reports/junit.xml
    --html=reports/report.html
    --self-contained-html

testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    e2e: marks tests as end-to-end tests
    api: marks tests as API tests
    config: marks tests as configuration tests
    database: marks tests as database tests

# 测试覆盖率目标
[coverage:run]
source = src, main
omit = 
    */tests/*
    */venv/*
    */__pycache__/*
    */migrations/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod