# AQUA 个人开发者版预提交配置
# 目标：保持基础代码质量，减少不必要约束

repos:
  - repo: local
    hooks:
      # 个人开发者友好的预提交钩子
      - id: aqua-personal-precommit
        name: AQUA个人开发合规校验
        entry: bash scripts/git_hooks/pre-commit-personal
        language: system
        pass_filenames: false
        always_run: true

      # 仅在修改前端文件时运行前端检查
      - id: frontend-format-conditional
        name: 前端格式化 (仅当修改前端文件时)
        entry: bash -c 'if git diff --cached --name-only | grep -q "^frontend/"; then cd frontend && pnpm exec prettier --write src/**/*.{ts,vue}; fi'
        language: system
        files: ^frontend/src/.*\.(ts|vue)$