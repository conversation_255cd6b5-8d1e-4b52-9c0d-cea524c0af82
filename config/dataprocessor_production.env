# DataProcessor生产环境配置模板
# 文件: config/dataprocessor_production.env
# 版本: v1.0
# 创建日期: 2025-08-04
# 用途: DataProcessor生产环境部署配置

# =============================================================================
# DataProcessor核心配置
# =============================================================================

# DataProcessor主开关 - 控制是否启用数据处理器
# 值: true/false
# 默认: true
# 影响: 完全控制DataProcessor功能启用状态
AQUA_ENABLE_DATA_PROCESSOR=true

# 失败安全模式 - 处理器失败时是否回退到原始数据流
# 值: true/false  
# 默认: true
# 影响: 决定处理失败时的行为(回退 vs 报错)
AQUA_DATA_PROCESSOR_FAIL_SAFE=true

# 质量统计显示 - 是否在控制台显示数据质量统计信息
# 值: true/false
# 默认: false (生产环境建议关闭)
# 影响: 控制台输出的详细级别
AQUA_SHOW_QUALITY_STATS=false

# =============================================================================
# 阶段化部署配置示例
# =============================================================================

# 阶段1: 测试验证阶段
# AQUA_ENABLE_DATA_PROCESSOR=true
# AQUA_DATA_PROCESSOR_FAIL_SAFE=true
# AQUA_SHOW_QUALITY_STATS=true

# 阶段2: 灰度部署阶段  
# AQUA_ENABLE_DATA_PROCESSOR=true
# AQUA_DATA_PROCESSOR_FAIL_SAFE=true
# AQUA_SHOW_QUALITY_STATS=false

# 阶段3: 全量部署阶段
# AQUA_ENABLE_DATA_PROCESSOR=true
# AQUA_DATA_PROCESSOR_FAIL_SAFE=true
# AQUA_SHOW_QUALITY_STATS=false

# 阶段4: 优化稳定阶段(可选严格模式)
# AQUA_ENABLE_DATA_PROCESSOR=true
# AQUA_DATA_PROCESSOR_FAIL_SAFE=false  # 可选: 严格质量控制
# AQUA_SHOW_QUALITY_STATS=false

# =============================================================================
# 应急场景配置
# =============================================================================

# 紧急回滚配置 - 完全禁用DataProcessor
# AQUA_ENABLE_DATA_PROCESSOR=false
# AQUA_DATA_PROCESSOR_FAIL_SAFE=true
# AQUA_SHOW_QUALITY_STATS=false

# 调试模式配置 - 启用详细输出
# AQUA_ENABLE_DATA_PROCESSOR=true
# AQUA_DATA_PROCESSOR_FAIL_SAFE=true
# AQUA_SHOW_QUALITY_STATS=true

# =============================================================================
# 使用说明
# =============================================================================

# 1. 部署前准备:
#    - 复制此文件到目标环境
#    - 根据部署阶段调整配置值
#    - 确保环境变量正确加载

# 2. 配置生效方式:
#    - 方式1: 直接设置环境变量 export AQUA_ENABLE_DATA_PROCESSOR=true
#    - 方式2: 加载环境文件 source config/dataprocessor_production.env
#    - 方式3: 容器环境通过--env-file参数加载

# 3. 配置验证:
#    - 检查: echo $AQUA_ENABLE_DATA_PROCESSOR
#    - 测试: 运行数据采集任务观察行为

# 4. 监控建议:
#    - 观察日志中DataProcessor相关信息
#    - 监控数据质量评分变化
#    - 关注处理性能指标

# =============================================================================
# 注意事项
# =============================================================================

# ⚠️ 重要提醒:
# - 生产环境建议保持AQUA_DATA_PROCESSOR_FAIL_SAFE=true
# - 首次部署建议启用AQUA_SHOW_QUALITY_STATS=true进行观察
# - 配置变更后需要重启服务才能生效
# - 紧急情况下可通过设置AQUA_ENABLE_DATA_PROCESSOR=false快速回滚

# 📊 性能指标:
# - 处理能力: 15万+条/秒
# - 质量提升: 自动清洗和去重
# - 兼容性: 完全向后兼容
# - 可靠性: 失败自动回退

# 🔗 相关文档:
# - 技术文档: docs/tasks/DEV_Tasks_DataProcessorDesignTODOList.md
# - 部署策略: docs/tasks/DataProcessor_Deployment_Strategy.md
# - 配置说明: CLAUDE.md - DataProcessor Environment Variables