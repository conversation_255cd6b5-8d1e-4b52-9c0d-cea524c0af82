{"test_summary": {"platform": "<PERSON>", "start_time": "2025-07-31T07:25:50.901433", "end_time": "2025-07-31T07:25:52.889139", "total_duration": 1.987706, "total_tests": 3, "passed_tests": 0, "failed_tests": 3, "success_rate": 0.0}, "test_results": {"cli_commands": {"success": false, "exit_code": 4, "execution_time": 0.7556469440460205, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.11.3, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/Documents/AQUA/Dev/AQUA\nconfigfile: pytest.ini\nplugins: anyio-4.9.0\ncollecting ... collected 0 items / 1 error\n\n==================================== ERRORS ====================================\n_______ ERROR collecting tests/integration/test_aqua_cli_integration.py ________\n.venv/lib/python3.11/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\n.venv/lib/python3.11/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1206: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1178: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1149: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:690: in _load_unlocked\n    ???\n.venv/lib/python3.11/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_aqua_cli_integration.py:22: in <module>\n    from aqua.main import app\nsrc/aqua/main.py:21: in <module>\n    from aqua.cli.windows_compat import setup_windows_compatibility_enhanced, windows_compatibility_command\nE     File \"/Users/<USER>/Documents/AQUA/Dev/AQUA/src/aqua/cli/windows_compat.py\", line 9\nE       import winreg if platform.system() == \"Windows\" else None\nE                     ^^\nE   SyntaxError: invalid syntax\n=========================== short test summary info ============================\nERROR tests/integration/test_aqua_cli_integration.py\n=============================== 1 error in 0.39s ===============================\n", "stderr": "ERROR: found no collectors for /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration\n\n", "timeout": 120, "command": "/Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python -m pytest /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration -v --tb=short", "json_report": {"returncode": 4, "stdout_lines": 39, "stderr_lines": 3}}, "setup_wizard": {"success": false, "exit_code": 4, "execution_time": 0.6208438873291016, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.11.3, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/Documents/AQUA/Dev/AQUA\nconfigfile: pytest.ini\nplugins: anyio-4.9.0\ncollecting ... collected 0 items / 1 error\n\n==================================== ERRORS ====================================\n_______ ERROR collecting tests/integration/test_aqua_cli_integration.py ________\n.venv/lib/python3.11/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\n.venv/lib/python3.11/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1206: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1178: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1149: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:690: in _load_unlocked\n    ???\n.venv/lib/python3.11/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_aqua_cli_integration.py:22: in <module>\n    from aqua.main import app\nsrc/aqua/main.py:21: in <module>\n    from aqua.cli.windows_compat import setup_windows_compatibility_enhanced, windows_compatibility_command\nE     File \"/Users/<USER>/Documents/AQUA/Dev/AQUA/src/aqua/cli/windows_compat.py\", line 9\nE       import winreg if platform.system() == \"Windows\" else None\nE                     ^^\nE   SyntaxError: invalid syntax\n=========================== short test summary info ============================\nERROR tests/integration/test_aqua_cli_integration.py\n=============================== 1 error in 0.26s ===============================\n", "stderr": "ERROR: found no collectors for /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestSetupWizardIntegration\n\n", "timeout": 180, "command": "/Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python -m pytest /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestSetupWizardIntegration -v --tb=short", "json_report": {"returncode": 4, "stdout_lines": 39, "stderr_lines": 3}}, "health_checker": {"success": false, "exit_code": 4, "execution_time": 0.6006369590759277, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.11.3, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/Documents/AQUA/Dev/AQUA\nconfigfile: pytest.ini\nplugins: anyio-4.9.0\ncollecting ... collected 0 items / 1 error\n\n==================================== ERRORS ====================================\n_______ ERROR collecting tests/integration/test_aqua_cli_integration.py ________\n.venv/lib/python3.11/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\n.venv/lib/python3.11/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py:126: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1206: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1178: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1149: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:690: in _load_unlocked\n    ???\n.venv/lib/python3.11/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_aqua_cli_integration.py:22: in <module>\n    from aqua.main import app\nsrc/aqua/main.py:21: in <module>\n    from aqua.cli.windows_compat import setup_windows_compatibility_enhanced, windows_compatibility_command\nE     File \"/Users/<USER>/Documents/AQUA/Dev/AQUA/src/aqua/cli/windows_compat.py\", line 9\nE       import winreg if platform.system() == \"Windows\" else None\nE                     ^^\nE   SyntaxError: invalid syntax\n=========================== short test summary info ============================\nERROR tests/integration/test_aqua_cli_integration.py\n=============================== 1 error in 0.24s ===============================\n", "stderr": "ERROR: found no collectors for /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration\n\n", "timeout": 240, "command": "/Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python -m pytest /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration -v --tb=short", "json_report": {"returncode": 4, "stdout_lines": 39, "stderr_lines": 3}}}, "environment_info": {"python_version": "3.11.3 (v3.11.3:f3909b8bc8, Apr  4 2023, 20:12:10) [Clang 13.0.0 (clang-1300.0.29.30)]", "platform": "macOS-12.7.6-x86_64-i386-64bit", "architecture": ["64bit", ""], "processor": "i386", "project_root": "/Users/<USER>/Documents/AQUA/Dev/AQUA"}}