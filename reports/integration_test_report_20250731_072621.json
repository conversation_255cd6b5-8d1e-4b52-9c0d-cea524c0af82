{"test_summary": {"platform": "<PERSON>", "start_time": "2025-07-31T07:26:19.533518", "end_time": "2025-07-31T07:26:21.261955", "total_duration": 1.728437, "total_tests": 3, "passed_tests": 1, "failed_tests": 2, "success_rate": 33.33333333333333}, "test_results": {"cli_commands": {"success": false, "exit_code": 1, "execution_time": 0.581061840057373, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.11.3, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/Documents/AQUA/Dev/AQUA\nconfigfile: pytest.ini\nplugins: anyio-4.9.0\ncollecting ... collected 4 items\n\ntests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration::test_main_app_startup PASSED [ 25%]\ntests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration::test_enhanced_welcome_banner PASSED [ 50%]\ntests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration::test_environment_variable_handling PASSED [ 75%]\ntests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration::test_all_commands_help_accessibility FAILED [100%]\n\n=================================== FAILURES ===================================\n_______ TestCLICommandsIntegration.test_all_commands_help_accessibility ________\n/Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py:131: in test_all_commands_help_accessibility\n    assert result.exit_code == 0, f\"Command {command} help failed\"\nE   AssertionError: Command init help failed\nE   assert 2 == 0\nE    +  where 2 = <Result SystemExit(2)>.exit_code\n=========================== short test summary info ============================\nFAILED tests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration::test_all_commands_help_accessibility\n========================= 1 failed, 3 passed in 0.23s ==========================\n", "stderr": "", "timeout": 120, "command": "/Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python -m pytest /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestCLICommandsIntegration -v --tb=short", "json_report": {"returncode": 1, "stdout_lines": 24, "stderr_lines": 1}}, "setup_wizard": {"success": true, "exit_code": 0, "execution_time": 0.5698602199554443, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.11.3, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/Documents/AQUA/Dev/AQUA\nconfigfile: pytest.ini\nplugins: anyio-4.9.0\ncollecting ... collected 4 items\n\ntests/integration/test_aqua_cli_integration.py::TestSetupWizardIntegration::test_setup_command_execution PASSED [ 25%]\ntests/integration/test_aqua_cli_integration.py::TestSetupWizardIntegration::test_macos_platform_detection PASSED [ 50%]\ntests/integration/test_aqua_cli_integration.py::TestSetupWizardIntegration::test_memory_detection_macos PASSED [ 75%]\ntests/integration/test_aqua_cli_integration.py::TestSetupWizardIntegration::test_data_source_detection_integration PASSED [100%]\n\n============================== 4 passed in 0.21s ===============================\n", "stderr": "", "timeout": 180, "command": "/Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python -m pytest /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestSetupWizardIntegration -v --tb=short", "json_report": {"returncode": 0, "stdout_lines": 15, "stderr_lines": 1}}, "health_checker": {"success": false, "exit_code": 1, "execution_time": 0.5680398941040039, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.11.3, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/Documents/AQUA/Dev/AQUA\nconfigfile: pytest.ini\nplugins: anyio-4.9.0\ncollecting ... collected 5 items\n\ntests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_doctor_command_basic_execution PASSED [ 20%]\ntests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_health_checker_python_environment FAILED [ 40%]\ntests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_health_checker_platform_compatibility PASSED [ 60%]\ntests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_disk_space_check_macos FAILED [ 80%]\ntests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_permissions_check_macos FAILED [100%]\n\n=================================== FAILURES ===================================\n_____ TestHealthCheckerIntegration.test_health_checker_python_environment ______\n/Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py:212: in test_health_checker_python_environment\n    assert result.status in [\"healthy\", \"warning\"]\nE   AssertionError: assert <HealthStatus.HEALTHY: 'healthy'> in ['healthy', 'warning']\nE    +  where <HealthStatus.HEALTHY: 'healthy'> = HealthCheckResult(name='Python版本', status=<HealthStatus.HEALTHY: 'healthy'>, message='Python 3.11.3 ✅', details=None, auto_fix_available=False, fix_command=None, fix_description=None).status\n___________ TestHealthCheckerIntegration.test_disk_space_check_macos ___________\n/Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py:233: in test_disk_space_check_macos\n    assert result.status in [\"healthy\", \"warning\", \"critical\"]\nE   AssertionError: assert <HealthStatus.HEALTHY: 'healthy'> in ['healthy', 'warning', 'critical']\nE    +  where <HealthStatus.HEALTHY: 'healthy'> = HealthCheckResult(name='磁盘空间', status=<HealthStatus.HEALTHY: 'healthy'>, message='磁盘空间充足: 132.7GB (14.2%) ✅', details=None, auto_fix_available=False, fix_command=None, fix_description=None).status\n__________ TestHealthCheckerIntegration.test_permissions_check_macos ___________\n/Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py:244: in test_permissions_check_macos\n    assert result.status in [\"healthy\", \"warning\"]\nE   AssertionError: assert <HealthStatus.HEALTHY: 'healthy'> in ['healthy', 'warning']\nE    +  where <HealthStatus.HEALTHY: 'healthy'> = HealthCheckResult(name='文件权限', status=<HealthStatus.HEALTHY: 'healthy'>, message='文件权限正常 ✅', details=None, auto_fix_available=False, fix_command=None, fix_description=None).status\n=========================== short test summary info ============================\nFAILED tests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_health_checker_python_environment\nFAILED tests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_disk_space_check_macos\nFAILED tests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration::test_permissions_check_macos\n========================= 3 failed, 2 passed in 0.22s ==========================\n", "stderr": "", "timeout": 240, "command": "/Users/<USER>/Documents/AQUA/Dev/AQUA/.venv/bin/python -m pytest /Users/<USER>/Documents/AQUA/Dev/AQUA/tests/integration/test_aqua_cli_integration.py::TestHealthCheckerIntegration -v --tb=short", "json_report": {"returncode": 1, "stdout_lines": 36, "stderr_lines": 1}}}, "environment_info": {"python_version": "3.11.3 (v3.11.3:f3909b8bc8, Apr  4 2023, 20:12:10) [Clang 13.0.0 (clang-1300.0.29.30)]", "platform": "macOS-12.7.6-x86_64-i386-64bit", "architecture": ["64bit", ""], "processor": "i386", "project_root": "/Users/<USER>/Documents/AQUA/Dev/AQUA"}}