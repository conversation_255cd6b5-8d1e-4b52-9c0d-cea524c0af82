# AQUA数据采集完全移除MOCK机制重构方案

## 🎯 问题分析

当前系统存在Mock数据机制，这在**任何环境下**都是不可接受的：

1. **数据质量风险**: 任何Mock数据都可能影响量化策略的开发和验证
2. **一致性问题**: 开发/测试环境使用Mock数据会导致与生产环境行为不一致
3. **专业标准**: 专业的金融数据工具应该100%使用真实数据源
4. **开发陷阱**: 开发者可能基于Mock数据特征编写代码，导致生产环境问题

## 💡 新的设计理念

**"Real Data Only" - 仅使用真实数据**
- 所有环境（开发/测试/生产）都使用真实TUSHARE数据源
- 数据源不可用时直接报错，绝不降级到Mock
- 确保开发者从第一天就适应真实数据源的特性

## 🔧 重构方案

### 阶段1: 完全移除MOCK机制

#### 1.1 统一配置（移除环境区分）
```python
# config/data_source.py
import os
from typing import Optional

class DataSourceConfig:
    """统一的数据源配置 - 仅支持真实数据源"""
    
    @property
    def tushare_token(self) -> Optional[str]:
        return os.getenv('TUSHARE_TOKEN')
    
    @property
    def is_configured(self) -> bool:
        return self.tushare_token is not None
    
    def validate(self) -> None:
        """验证数据源配置"""
        if not self.is_configured:
            raise DataSourceNotConfiguredError(
                "未配置TUSHARE_TOKEN环境变量。\n"
                "请访问 https://tushare.pro/ 获取Token并设置环境变量：\n"
                "export TUSHARE_TOKEN='your_token_here'"
            )
```

#### 1.2 简化配置文件
```toml
# config/settings.toml (简化版)
[datasources.tushare]
enabled = true
token = "${TUSHARE_TOKEN}"  # 从环境变量读取
rate_limit = 200
points_per_minute = 2000

# 移除所有Mock相关配置
# 移除环境区分配置
```

### 阶段2: CollectService重构

#### 2.1 完全移除Mock和降级机制
```python
# src/cli/services/collect_service.py (Real Data Only版本)
def collect_data(self, symbols: List[str], source: str = 'tushare', **kwargs):
    """数据采集 - 仅使用真实数据源"""
    
    # 预检查：确保数据源已配置
    data_config = DataSourceConfig()
    data_config.validate()  # 如果未配置会抛出异常
    
    try:
        if source == 'tushare':
            extractor = self._get_tushare_extractor()
            if not extractor or not extractor.is_connected():
                raise DataSourceUnavailableError(
                    "TUSHARE数据源连接失败。\n"
                    "请检查:\n"
                    "1. 网络连接是否正常\n"
                    "2. TUSHARE_TOKEN是否正确\n"
                    "3. TUSHARE服务是否可用\n"
                    "4. 账户积分是否充足\n\n"
                    "解决方案:\n"
                    "- 检查网络连接\n"
                    "- 验证Token: python -m src.cli.main --check-token\n"
                    "- 访问 https://tushare.pro/ 查看服务状态"
                )
            
            # 仅使用真实数据源采集
            return self._collect_real_data(extractor, symbols, **kwargs)
            
        else:
            raise UnsupportedDataSourceError(f"不支持的数据源: {source}")
            
    except Exception as e:
        # 任何错误都直接抛出，不降级
        if isinstance(e, (DataSourceUnavailableError, DataSourceNotConfiguredError)):
            raise
        else:
            raise DataCollectionError(f"数据采集失败: {e}")
```

#### 2.2 新增数据源验证
```python
def validate_datasource_for_production(self) -> Dict[str, Any]:
    """生产环境数据源验证"""
    validation_result = {
        'tushare': {'available': False, 'issues': []},
        'environment': CURRENT_ENV.value,
        'production_ready': False
    }
    
    # TUSHARE验证
    if not os.getenv('TUSHARE_TOKEN'):
        validation_result['tushare']['issues'].append('未设置TUSHARE_TOKEN环境变量')
    
    try:
        extractor = self._get_real_tushare_extractor()
        if extractor and extractor.is_connected():
            validation_result['tushare']['available'] = True
        else:
            validation_result['tushare']['issues'].append('TUSHARE连接失败')
    except Exception as e:
        validation_result['tushare']['issues'].append(f'TUSHARE验证错误: {e}')
    
    # 生产就绪检查
    validation_result['production_ready'] = (
        validation_result['tushare']['available'] and 
        len(validation_result['tushare']['issues']) == 0
    )
    
    return validation_result
```

### 阶段3: 开发便利性措施（基于真实数据）

#### 3.1 本地缓存机制
```python
# src/utils/data_cache.py
class RealDataCache:
    """基于真实数据的本地缓存机制"""
    
    def __init__(self, cache_dir: str = "cache/real_data"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def get_cached_data(self, symbols: List[str], date_range: str) -> Optional[DataFrame]:
        """获取缓存的真实数据"""
        cache_key = self._generate_cache_key(symbols, date_range)
        cache_file = self.cache_dir / f"{cache_key}.parquet"
        
        if cache_file.exists():
            # 检查缓存是否过期（如超过1天）
            if (time.time() - cache_file.stat().st_mtime) < 86400:
                return pd.read_parquet(cache_file)
        return None
    
    def cache_data(self, data: DataFrame, symbols: List[str], date_range: str):
        """缓存真实数据（来自TUSHARE）"""
        cache_key = self._generate_cache_key(symbols, date_range)
        cache_file = self.cache_dir / f"{cache_key}.parquet"
        data.to_parquet(cache_file)
```

#### 3.2 快速开发环境设置
```bash
# scripts/dev_setup.sh
#!/bin/bash
echo "AQUA开发环境快速设置（基于真实数据源）"

# 检查Python版本
python_version=$(python --version 2>&1 | awk '{print $2}')
echo "Python版本: $python_version"

# 安装依赖
pip install -r requirements.txt

# 检查TUSHARE Token
if [ -z "$TUSHARE_TOKEN" ]; then
    echo "❌ 未设置TUSHARE_TOKEN环境变量"
    echo "请访问 https://tushare.pro/ 获取Token"
    echo "然后运行: export TUSHARE_TOKEN='your_token_here'"
    exit 1
else
    echo "✅ TUSHARE_TOKEN已设置"
fi

# 验证数据源连接
python -m src.cli.main --check-token
if [ $? -eq 0 ]; then
    echo "✅ 开发环境设置完成，可以开始使用真实数据源"
else
    echo "❌ 数据源连接验证失败，请检查Token配置"
    exit 1
fi
```

#### 4.1 简化CLI命令（移除环境区分）
```python
# src/cli/commands/datasource.py
@click.command()
@click.option('--check-token', is_flag=True, help='验证TUSHARE Token')
@click.option('--test-connection', is_flag=True, help='测试数据源连接')
def datasource(check_token, test_connection):
    """数据源管理命令"""
    service = CollectService()
    
    if check_token:
        try:
            data_config = DataSourceConfig()
            data_config.validate()
            click.echo("✅ TUSHARE Token配置正确")
            
            # 进一步测试Token有效性
            extractor = service._get_tushare_extractor()
            if extractor and extractor.is_connected():
                click.echo("✅ TUSHARE连接测试成功")
                # 显示账户信息
                info = extractor.get_account_info()
                click.echo(f"📊 积分余额: {info.get('points', '未知')}")
            else:
                click.echo("❌ TUSHARE连接失败，请检查Token或网络")
                sys.exit(1)
                
        except DataSourceNotConfiguredError as e:
            click.echo(f"❌ {e}")
            sys.exit(1)
    
    if test_connection:
        try:
            # 执行小规模数据采集测试
            result = service.collect_data(['000001.SZ'], preview=True)
            if result.get('success'):
                click.echo("✅ 数据源连接和采集测试成功")
                click.echo(f"📈 测试数据量: {result.get('estimated_rows', 0)}行")
            else:
                click.echo("❌ 数据采集测试失败")
                sys.exit(1)
        except Exception as e:
            click.echo(f"❌ 连接测试失败: {e}")
            sys.exit(1)
```

#### 4.2 重构collect命令
```python
@click.command()
@click.argument('symbols')
@click.option('--preview', is_flag=True, help='预览模式，不实际采集')
def collect(symbols, preview):
    """数据采集命令 - Real Data Only版本"""
    
    # 启动前检查：确保数据源已配置
    try:
        data_config = DataSourceConfig()
        data_config.validate()
    except DataSourceNotConfiguredError as e:
        click.echo(f"❌ 数据源未配置: {e}")
        click.echo("\n快速配置指南:")
        click.echo("1. 访问 https://tushare.pro/ 注册并获取Token")
        click.echo("2. 设置环境变量: export TUSHARE_TOKEN='your_token_here'")
        click.echo("3. 验证配置: python -m src.cli.main datasource --check-token")
        sys.exit(1)
    
    service = CollectService()
    
    try:
        # 执行数据采集（仅真实数据源）
        result = service.collect_data(symbols.split(','), preview=preview)
        
        # 显示结果
        if result.get('success'):
            mode = "预览" if preview else "采集"
            click.echo(f"✅ 数据{mode}成功!")
            click.echo(f"📊 数据来源: TUSHARE (真实数据)")
            click.echo(f"📈 数据量: {result.get('total_rows', 0)}行")
            
            if not preview:
                click.echo(f"💾 保存位置: {result.get('database_path', 'data/aqua_dev.duckdb')}")
        else:
            click.echo("❌ 数据采集失败")
            for error in result.get('errors', []):
                click.echo(f"   - {error}")
            sys.exit(1)
            
    except (DataSourceUnavailableError, DataCollectionError) as e:
        click.echo(f"❌ 数据采集错误: {e}")
        click.echo("\n故障排除建议:")
        click.echo("1. 检查网络连接: ping tushare.pro")
        click.echo("2. 验证Token: python -m src.cli.main datasource --check-token")
        click.echo("3. 测试连接: python -m src.cli.main datasource --test-connection")
        sys.exit(1)
```
        result = service.validate_datasource_for_production()
        
        if result['production_ready']:
            click.echo("✅ 生产环境验证通过，可以安全使用真实数据")
        else:
            click.echo("❌ 生产环境验证失败:")
            for source, info in result.items():
                if isinstance(info, dict) and 'issues' in info:
                    for issue in info['issues']:
                        click.echo(f"  - {issue}")
            sys.exit(1)
    
    if env:
        os.environ['AQUA_ENV'] = env
        click.echo(f"环境已设置为: {env}")
```

#### 3.2 重构collect命令
```python
@click.command()
@click.argument('symbols')
@click.option('--force-production', is_flag=True, 
              help='强制生产模式（禁用Mock数据）')
def collect(symbols, force_production):
    """数据采集命令 - 生产安全版本"""
    
    # 强制生产模式检查
    if force_production:
        os.environ['AQUA_ENV'] = 'production'
    
    env_config = EnvironmentConfig()
    
    # 生产环境预检查
    if env_config.fail_on_datasource_error:
        service = CollectService()
        validation = service.validate_datasource_for_production()
        
        if not validation['production_ready']:
            click.echo("❌ 生产环境数据源验证失败，无法继续采集")
            click.echo("请先运行: python -m src.cli.main env --validate")
            sys.exit(1)
        
        click.echo("✅ 生产环境验证通过，使用真实数据源")
    
    # 执行数据采集
    try:
        result = service.collect_data(symbols.split(','))
        
        # 明确标识数据来源
        data_source_type = result.get('data_source_type', 'unknown')
        if data_source_type == 'mock' and env_config.fail_on_datasource_error:
            raise ProductionDataSourceError("生产环境不允许使用Mock数据")
        
        click.echo(f"✅ 数据采集成功!")
        click.echo(f"📊 数据来源: {data_source_type.upper()}")
        click.echo(f"📈 采集数据量: {result.get('total_rows', 0)}行")
        
    except ProductionDataSourceError as e:
        click.echo(f"❌ 生产环境错误: {e}")
        sys.exit(1)
```

### 阶段4: 异常处理重构

#### 4.1 新增专用异常类
```python
# src/utils/exceptions.py
class DataSourceUnavailableError(AquaException):
    """数据源不可用异常"""
    pass

class ProductionDataSourceError(AquaException):
    """生产环境数据源错误"""
    pass

class MockDataInProductionError(AquaException):
    """生产环境中使用Mock数据错误"""
    pass
```

### 阶段5: 文档更新

#### 5.1 更新用户指南
移除所有关于Mock数据的误导性描述，明确区分开发和生产使用：

```markdown
## 环境设置

### 生产环境使用（推荐）
```bash
# 设置生产环境
export AQUA_ENV=production

# 设置TUSHARE Token（必需）
export TUSHARE_TOKEN="your_token_here"

# 验证环境
python -m src.cli.main env --validate

# 采集数据（仅使用真实数据源）
python -m src.cli.main collect 000001.SZ
```

### 开发环境使用
```bash
# 设置开发环境
export AQUA_ENV=development

# 可以不设置Token，会使用Mock数据
python -m src.cli.main collect 000001.SZ
```

### 重要提醒
⚠️ **生产环境绝不使用Mock数据**
- 生产模式下必须配置真实TUSHARE Token
- 数据源不可用时会报错而不是降级
- 所有采集的数据都是真实市场数据
```

## 🚀 实施计划

### 第1周：核心重构（MOCK彻底清理）
- [ ] 完全移除所有Mock相关代码和类
- [ ] 移除智能降级机制
- [ ] 统一配置文件，移除环境区分
- [ ] 重构CollectService为Real Data Only版本

### 第2周：开发便利性和CLI重构  
- [ ] 实现基于真实数据的缓存机制
- [ ] 创建快速开发环境设置脚本
- [ ] 重构CLI命令，简化为统一的数据源管理
- [ ] 完善异常处理和错误提示

### 第3周：测试和文档
- [ ] 全面测试Real Data Only模式
- [ ] 更新所有文档，移除Mock相关内容
- [ ] 创建用户迁移指南
- [ ] 验证TUSHARE免费额度是否满足开发需求

## 📊 预期效果

### ✅ **数据质量保证**
- **100%真实数据** - 任何环境下都不会有Mock数据污染
- **一致性保证** - 开发/测试/生产环境数据特征完全一致
- **质量可信** - 所有决策都基于真实市场数据

### ✅ **专业工具标准**
- **Real Data Only** - 符合专业金融工具的严格标准
- **数据源透明** - 用户明确知道所有数据来源于TUSHARE
- **错误明确** - 数据源问题时直接报错，绝不静默降级

### ✅ **开发体验优化**
- **真实环境开发** - 从第一天就适应真实数据源特性
- **智能缓存** - 基于真实数据的本地缓存减少重复请求
- **快速设置** - 一键脚本配置开发环境
- **清晰诊断** - 详细的错误提示和解决方案

### ✅ **用户信任提升**
- **专业可靠** - 彻底消除对数据真实性的疑虑
- **透明运行** - 明确的数据来源标识和状态提示
- **故障友好** - 完善的故障排除指导

## 🎯 核心优势

### **"Real Data Only"设计理念的价值**
1. **消除数据不一致风险** - 避免Mock数据特征与真实数据差异
2. **提升开发质量** - 开发者必须处理真实数据的复杂性
3. **增强用户信任** - 用户确信所有数据都是真实市场数据
4. **符合行业标准** - 专业金融工具的必备特征

这个重构方案将AQUA从一个"有Mock备选的工具"升级为**"专业的真实数据采集工具"**，完全符合量化交易对数据质量的严格要求。