# AQUA CLI 用户指南

## 概述

AQUA CLI是一个专为个人量化开发者设计的命令行工具，提供数据采集、系统监控、配置管理等核心功能。

## 快速开始

### 安装和配置

1. **初始化系统**
```bash
python aqua_cli.py init
```

2. **检查系统状态**
```bash
python aqua_cli.py status
```

3. **查看帮助**
```bash
python aqua_cli.py --help
```

### 基本使用流程

```bash
# 1. 初始化配置
python aqua_cli.py init --interactive

# 2. 检查数据源能力
python aqua_cli.py collect --check-capabilities

# 3. 预览数据采集
python aqua_cli.py collect 000001.SZ --preview

# 4. 执行数据采集
python aqua_cli.py collect 000001.SZ --last-days 30

# 5. 查看系统状态
python aqua_cli.py status --verbose
```

## 核心命令详解

### 1. aqua collect - 数据采集命令

数据采集是AQUA的核心功能，支持多数据源、多品种、灵活时间范围的数据采集。

#### 基础语法
```bash
python aqua_cli.py collect [OPTIONS] [SYMBOLS...]
```

#### 主要参数

**数据源选择**
- `--source tushare|mysql|csv|api` - 选择数据源（默认：tushare）

**品种分类**
- `--type stock|futures|options|bonds` - 数据类型（默认：stock）

**时间范围**
- `--start-date YYYY-MM-DD` - 开始日期
- `--end-date YYYY-MM-DD` - 结束日期  
- `--last-days N` - 最近N天
- `--last-weeks N` - 最近N周
- `--last-months N` - 最近N月
- `--period ytd|qtd|mtd|wtd` - 预设周期

**数据频率**
- `--freq daily|weekly|monthly|1min|5min|15min|30min|60min` - 数据频率

**智能功能**
- `--preview` - 预览模式，显示数据概览而不实际采集
- `--interactive` - 交互式配置向导
- `--check-capabilities` - 检查数据源能力

**配置支持**
- `--config FILE` - 使用配置文件
- `--template NAME` - 使用预设模板

#### 使用示例

**基础股票采集**
```bash
# 采集平安银行最近30天日线数据
python aqua_cli.py collect 000001.SZ --last-days 30

# 采集多只股票
python aqua_cli.py collect 000001.SZ 000002.SZ 600036.SH --period ytd
```

**期货数据采集**
```bash
# 采集IF期货5分钟数据
python aqua_cli.py collect IF2024 --type futures --freq 5min --last-weeks 2
```

**预览和检查**
```bash
# 预览数据采集概览
python aqua_cli.py collect 000001.SZ --preview

# 检查数据源能力
python aqua_cli.py collect --check-capabilities
```

**交互式模式**
```bash
# 启动交互式向导
python aqua_cli.py collect --interactive
```

**使用模板**
```bash
# 使用预设模板
python aqua_cli.py collect --template bank_stocks_daily --preview
```

### 2. aqua status - 系统状态查询

查看AQUA系统运行状态和统计信息。

#### 基础语法
```bash
python aqua_cli.py status [OPTIONS]
```

#### 主要参数
- `--verbose, -v` - 详细信息模式
- `--performance, -p` - 显示性能指标
- `--activities, -a` - 显示最近活动

#### 使用示例

**基础状态查询**
```bash
python aqua_cli.py status
```

**详细系统信息**
```bash
python aqua_cli.py status --verbose
```

**性能监控**
```bash
python aqua_cli.py status --performance
```

**完整信息**
```bash
python aqua_cli.py status --verbose --performance --activities
```

### 3. aqua init - 系统初始化

初始化AQUA系统配置和目录结构。

#### 基础语法
```bash
python aqua_cli.py init [OPTIONS]
```

#### 主要参数
- `--interactive, -i` - 交互式配置向导（默认）
- `--minimal, -m` - 最小化配置
- `--force, -f` - 强制重新初始化

#### 使用示例

**交互式初始化**
```bash
python aqua_cli.py init
```

**最小化配置**
```bash
python aqua_cli.py init --minimal
```

**强制重新初始化**
```bash
python aqua_cli.py init --force
```

## 高级功能

### 交互式向导

AQUA CLI提供智能的交互式向导，帮助用户配置复杂参数。

#### 数据采集向导
```bash
python aqua_cli.py collect --interactive
```

向导将引导您完成：
1. 股票代码选择
2. 数据源配置
3. 数据类型和频率设置
4. 时间范围选择
5. 输出格式配置

#### 系统初始化向导
```bash
python aqua_cli.py init --interactive
```

向导将配置：
1. 数据源设置
2. 存储配置
3. 日志设置
4. 性能参数

### 模板系统

AQUA支持预定义模板，简化常用配置。

#### 可用模板
- `bank_stocks_daily` - 银行股日线数据采集
- `tech_stocks_5min` - 科技股5分钟数据采集
- `index_weekly` - 指数周线数据采集

#### 使用模板
```bash
python aqua_cli.py collect --template bank_stocks_daily --preview
```

### 配置文件支持

支持YAML格式的配置文件。

#### 配置文件示例
```yaml
# collection_config.yaml
symbols: 
  - "000001.SZ"
  - "000002.SZ"
  - "600036.SH"
source: "tushare"
data_type: "stock"
freq: "daily"
period: "ytd"
preview: true
```

#### 使用配置文件
```bash
python aqua_cli.py collect --config collection_config.yaml
```

## Shell补全

AQUA CLI支持bash、zsh和fish的命令补全。

### 安装补全
```bash
# 对于bash
python -c "from src.cli.utils.completion import setup_completion; setup_completion('bash')"

# 对于zsh
python -c "from src.cli.utils.completion import setup_completion; setup_completion('zsh')"

# 对于fish
python -c "from src.cli.utils.completion import setup_completion; setup_completion('fish')"
```

### 使用补全
安装后，您可以使用Tab键补全命令、参数和选项：
```bash
python aqua_cli.py col<Tab>    # 补全为 collect
python aqua_cli.py collect --so<Tab>  # 补全为 --source
```

## 主题定制

AQUA CLI支持多种界面主题。

### 可用主题
- `default` - AQUA默认主题（蓝色主调）
- `professional` - 专业商务主题
- `dark` - 深色主题
- `minimal` - 极简主题
- `colorful` - 多彩主题

### 预览主题
```python
from src.cli.utils.themes import ThemeManager
theme_manager = ThemeManager()
theme_manager.preview_theme('dark')
```

## 故障排除

### 常见问题

#### 1. 命令不被识别
**症状**：`command not found: aqua`
**解决方案**：
```bash
# 使用完整路径
python /path/to/aqua_cli.py --help

# 或添加到PATH
export PATH=$PATH:/path/to/AQUA/
```

#### 2. 中文字符显示异常
**症状**：中文字符显示为乱码或方块
**解决方案**：
```bash
# 设置正确的编码
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# Windows用户
chcp 65001
```

#### 3. 启动速度慢
**症状**：CLI启动时间超过2秒
**解决方案**：
- 检查Python环境和依赖
- 考虑使用最小化配置
- 清理不必要的日志文件

#### 4. 内存使用过高
**症状**：CLI占用内存过多
**解决方案**：
```bash
# 检查系统状态
python aqua_cli.py status --performance

# 使用最小化配置
python aqua_cli.py init --minimal
```

### 日志调试

#### 启用详细日志
```bash
python aqua_cli.py --verbose status
```

#### 查看日志文件
```bash
# 日志文件位置
ls logs/
tail -f logs/aqua.log
```

### 性能监控

#### 查看性能指标
```bash
python aqua_cli.py status --performance
```

#### 监控启动时间
```bash
time python aqua_cli.py --help
```

## 最佳实践

### 1. 配置管理
- 使用`aqua init`初始化标准配置
- 将常用配置保存为模板
- 定期备份配置文件

### 2. 数据采集
- 使用`--preview`先预览数据概览
- 合理设置时间范围避免过度采集
- 利用模板提高效率

### 3. 系统维护
- 定期运行`aqua status`检查系统健康
- 清理旧日志文件
- 监控磁盘空间使用

### 4. 性能优化
- 使用`--parallel`并行采集提高速度
- 启用数据压缩节省存储
- 合理配置内存限制

## 高级配置

### 环境变量

AQUA CLI支持以下环境变量：

```bash
# Tushare配置
export TUSHARE_TOKEN="your_token_here"

# 数据库配置
export AQUA_DB_PATH="/custom/path/aqua.duckdb"

# 日志配置
export AQUA_LOG_LEVEL="DEBUG"
export AQUA_LOG_DIR="/custom/logs/"

# 性能配置
export AQUA_MAX_WORKERS="8"
export AQUA_MEMORY_LIMIT="8GB"
```

### 自定义配置

在`~/.aqua/config.toml`中进行个性化配置：

```toml
[cli]
default_theme = "dark"
auto_completion = true
show_progress = true

[data_collection]
default_source = "tushare"
batch_size = 1000
timeout = 30

[display]
terminal_width = 120
max_table_rows = 50
date_format = "%Y-%m-%d"
```

## 集成开发

### Python API集成

```python
# 在Python代码中使用CLI功能
import subprocess

def collect_data(symbol, days=30):
    cmd = [
        'python', 'aqua_cli.py', 'collect', 
        symbol, '--last-days', str(days)
    ]
    result = subprocess.run(cmd, capture_output=True, text=True)
    return result.returncode == 0
```

### 脚本自动化

```bash
#!/bin/bash
# daily_collection.sh

# 每日数据采集脚本
python aqua_cli.py collect --template daily_stocks
python aqua_cli.py status --performance

# 检查错误
if [ $? -ne 0 ]; then
    echo "数据采集失败，请检查日志"
    exit 1
fi

echo "每日数据采集完成"
```

## 版本信息

当前版本：AQUA CLI v1.0.0
支持平台：Windows 10/11, macOS 10.15+, Linux
Python要求：3.11+

## 获取帮助

### 内置帮助
```bash
python aqua_cli.py --help           # 主帮助
python aqua_cli.py collect --help   # collect命令帮助
python aqua_cli.py status --help    # status命令帮助
python aqua_cli.py init --help      # init命令帮助
```

### 在线文档
- 项目文档：docs/
- 技术文档：docs/cli_user_guide.md
- API文档：docs/api_reference.md

### 社区支持
- 问题反馈：GitHub Issues
- 功能建议：GitHub Discussions
- 技术交流：项目Wiki

---

**AQUA CLI - 专为个人量化开发者打造的专业工具**