# AQUA 权威项目路线图

**文档地位**: 本文档是AQUA项目发展的**唯一、权威的战略规划文档**。它定义了项目的长期愿景、核心架构原则以及高级别的史诗级（Epic）演进路线。所有具体的战术任务清单（如`docs/tasks/Dev_Tasks_EPIC*.md`）都必须与本路线图保持战略一致。

---

## 1. 项目愿景 (Project Vision)

AQUA 致力于成为个人量化投资者的终极武器库。它将深度融合专业的金融投机经验与现代软件工程实践，打造一个从数据采集、清洗、存储，到策略回测、模拟交易，再到AI辅助决策的全链路、高度自动化、配置驱动的本地优先量化平台。

---

## 2. 核心架构原则 (Core Architectural Principles)

AQUA项目的所有技术决策和实现都必须严格遵循以下四大核心原则：

1.  **统一框架，插件化源 (Unified Framework, Pluggable Sources)**: 构建统一的数据处理框架，将每一种数据源（CSV, MySQL, API等）视为可插拔的适配器，最大化代码复用，最小化扩展成本。

2.  **ELT而非ETL (ELT over ETL)**: 优先采用 `Extract-Load-Transform` 模式。先将原始数据快速加载到暂存区（Staging Area），再利用DuckDB的强大性能进行后续的转换、清洗和去重，以保证数据保真性、高性能和逻辑解耦。

3.  **状态化与幂等性 (Stateful & Idempotent)**: 所有数据处理流程，特别是增量更新，必须是状态化的（记录处理水位线）和幂等的（重复执行无副作用），确保数据的一致性和可靠性。

4.  **配置驱动一切 (Configuration-Driven Everything)**: `config/settings.toml` 是驱动所有行为的唯一事实来源。杜绝硬编码，所有数据源连接、导入模式、业务参数等都必须在配置文件中定义。

---

## 3. 高级架构蓝图 (High-Level Architecture Blueprint)

AQUA的数据核心遵循一个三层流水线架构，确保数据流的清晰、高效和可维护。

```mermaid
graph TD
    subgraph Sources
        A[CSV Files]
        B[MySQL Database]
        C[API Endpoints]
    end

    subgraph Acquisition Layer
        direction LR
        A1[CsvExtractor]
        B1[MySqlExtractor]
        C1[ApiExtractor]
    end

    subgraph Orchestration & Staging Layer
        U[Unified Import Manager]
        S[Staging Tables in DuckDB]
        L[State Tracking Table]
    end
    
    subgraph Transformation & Integration Layer
        T[Transformation Engine <br/> (dbt or Python Scripts)]
        F[Final Clean Tables <br/> (Per DATA_DICTIONARY.md)]
    end

    A --> A1
    B --> B1
    C --> C1

    A1 --> U
    B1 --> U
    C1 --> U

    U --> S
    U --> L

    S --> T
    L --> T

    T --> F
```

---

## 4. 史诗级路线图 (Epic Roadmap)

| Epic ID | Epic 名称                  | 状态      | 核心目标                                                     |
|---------|----------------------------|-----------|--------------------------------------------------------------|
| EPIC 1  | 项目基础建设与规范落地     | ✅ 已完成 | 建立稳定、自动化的开发环境与项目规范。                       |
| EPIC 2  | 第一版核心数据服务         | ✅ 已完成 | 实现基于FROMC2C的CSV数据导入与基础浏览功能。                   |
| **EPIC 3**  | **统一数据框架重构**       | **🚀 进行中** | **构建统一的、可扩展的ELT数据导入与采集框架。**              |
| EPIC 4  | 策略回测引擎               | 📝 规划中 | 开发高性能、事件驱动的向量化回测引擎。                       |
| EPIC 5  | 实时模拟交易系统           | 📝 规划中 | 实现对接实时行情的模拟交易与资金管理。                       |
| EPIC 6  | AI分析与洞察模块           | 📝 规划中 | 集成AI能力，提供自然语言查询、异常检测和策略建议。           |

---
*最后更新: 2025-07-20*
*维护者: AQUA项目团队*
