# AQUA CLI 快速入门教程

## 5分钟快速上手

欢迎使用AQUA CLI！本教程将引导您在5分钟内掌握核心功能。

### 第1步：检查安装 (30秒)

首先验证AQUA CLI是否正常工作：

```bash
# 检查版本信息
python aqua_cli.py --version

# 查看主帮助
python aqua_cli.py --help
```

**期望输出**：
```
AQUA CLI v1.0.0
个人开发者量化分析平台
支持平台: Windows 10/11, macOS 10.15+
```

如果看到错误，请检查Python环境和项目路径。

### 第2步：系统初始化 (1分钟)

运行初始化向导配置系统：

```bash
python aqua_cli.py init --interactive
```

**向导流程**：
1. **数据源配置** - 是否启用Tushare（推荐选择"是"）
2. **存储配置** - 使用默认数据库路径
3. **日志配置** - 选择合适的日志级别
4. **性能配置** - 根据您的机器性能选择

**💡 提示**：首次使用建议选择默认配置，后续可以调整。

### 第3步：检查系统状态 (30秒)

验证系统初始化是否成功：

```bash
python aqua_cli.py status
```

**期望看到**：
- ✅ CLI接口正常
- ✅ 数据库连接正常
- ⚠️ 其他组件显示"开发中"（正常现象）

```bash
# 查看详细系统信息
python aqua_cli.py status --verbose
```

### 第4步：数据源能力检查 (30秒)

检查可用的数据源和功能：

```bash
python aqua_cli.py collect --check-capabilities
```

**期望输出**：
```
TUSHARE 数据源能力
┌─────────┬────────────────┐
│ 项目    │ 支持情况       │
├─────────┼────────────────┤
│ 状态    │ ✅ 可用        │
│ 支持类型│ stock, futures │
│ 支持频率│ daily, 1min    │
└─────────┴────────────────┘
```

### 第5步：第一次数据采集 (2分钟)

现在进行第一次数据采集！

#### 5.1 预览数据采集
```bash
# 预览平安银行股票数据
python aqua_cli.py collect 000001.SZ --preview
```

**输出示例**：
```
数据预览概览
• 目标标的: 000001.SZ
• 数据源: tushare
• 数据类型: stock
• 数据频率: daily
• 预估行数: 250
• 预估大小: 125 KB
```

#### 5.2 实际数据采集
```bash
# 采集最近30天的数据
python aqua_cli.py collect 000001.SZ --last-days 30
```

**看到进度条**：
```
🔄 采集1只股票数据 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100% 0:00:01
✅ 成功采集 1 个标的的 stock 数据
采集行数: 100
```

### 第6步：验证采集结果 (30秒)

检查数据是否成功采集：

```bash
python aqua_cli.py status --verbose
```

在输出中查看数据统计部分，应该显示新增的数据记录。

## 常用操作示例

### 多股票采集
```bash
# 采集多只银行股
python aqua_cli.py collect 000001.SZ 000002.SZ 600036.SH --last-days 30
```

### 使用交互式向导
```bash
# 启动采集向导
python aqua_cli.py collect --interactive
```

按照提示选择：
1. 输入股票代码
2. 选择数据源
3. 设置时间范围
4. 确认执行

### 使用预设模板
```bash
# 查看可用模板
python aqua_cli.py collect --template bank_stocks_daily --preview
```

### 系统监控
```bash
# 查看性能指标
python aqua_cli.py status --performance

# 查看最近活动
python aqua_cli.py status --activities
```

## 下一步学习

恭喜！您已经完成了AQUA CLI的快速入门。现在您可以：

### 📚 深入学习
- 阅读[完整用户指南](cli_user_guide.md)
- 探索[高级功能](advanced_features.md)
- 查看[API参考](api_reference.md)

### 🛠️ 个性化配置
- 配置Shell补全提高效率
- 选择喜欢的界面主题
- 创建自定义采集模板

### 🚀 进阶使用
- 学习批量数据采集
- 掌握多数据源集成
- 使用脚本自动化

## 常见问题

### Q: 如何获取Tushare Token？
A: 访问 https://tushare.pro 注册账户，在个人中心获取Token。

### Q: 数据存储在哪里？
A: 默认存储在 `data/aqua.duckdb` 文件中。

### Q: 如何重置配置？
A: 运行 `python aqua_cli.py init --force`

### Q: 遇到错误怎么办？
A: 
1. 查看详细错误：`python aqua_cli.py --verbose status`
2. 检查日志：`cat logs/aqua.log`
3. 重新初始化：`python aqua_cli.py init --force`

## 获取帮助

任何时候都可以使用内置帮助：

```bash
python aqua_cli.py --help                    # 主帮助
python aqua_cli.py collect --help           # collect命令帮助
python aqua_cli.py status --help            # status命令帮助
python aqua_cli.py init --help              # init命令帮助
```

---

**🎉 欢迎使用AQUA CLI - 让量化分析更简单！**

**下一步**：阅读[完整用户指南](cli_user_guide.md)解锁更多功能