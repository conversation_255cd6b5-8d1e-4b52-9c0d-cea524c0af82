# AQUA项目QA手册

> **定位说明**: 本文档为AQUA期货与A股数据平台的官方QA手册，提供常见问题解答和操作指导。

---

## 📋 目录

### [第一章：数据导入操作手册](#第一章数据导入操作手册)
- [Q1：如何快速开始数据导入？](#q1如何快速开始数据导入)
- [Q2：如何重置数据库？](#q2如何重置数据库)
- [Q3：如何导入CSV数据？](#q3如何导入csv数据)
- [Q4：如何导入MySQL数据？](#q4如何导入mysql数据)
- [Q5：遇到常见错误怎么办？](#q5遇到常见错误怎么办)
- [Q6：FromC2C期货数据导入专题](#q6fromc2c期货数据导入专题)
- [Q7：如何查看导入日志？](#q7如何查看导入日志)
- [Q8：如何验证导入结果？](#q8如何验证导入结果)
- [Q9：如何进行高级自定义操作？](#q9如何进行高级自定义操作)

### [第二章：环境管理与配置](#第二章环境管理与配置)
- [Q10：如何使用新的智能启动系统？](#q10如何使用新的智能启动系统)
- [Q11：如何选择和切换运行环境？](#q11如何选择和切换运行环境)
- [Q12：环境配置有什么区别？](#q12环境配置有什么区别)
- [Q13：如何使用环境初始化脚本？](#q13如何使用环境初始化脚本)
- [Q14：启动脚本系统如何工作？](#q14启动脚本系统如何工作)
- [Q15：配置验证和错误修复](#q15配置验证和错误修复)
- [Q16：中国网络环境下如何解决依赖安装问题？](#q16中国网络环境下如何解决依赖安装问题)

### [第三章：Claude Code工具体系使用指南](#第三章claude-code工具体系使用指南)
- [Q17：如何最大化发挥Claude Code的价值？](#q17如何最大化发挥claude-code的价值)
- [Q18：个人开发者的Claude Code工作流是什么？](#q18个人开发者的claude-code工作流是什么)
- [Q19：如何使用Claude Code自动化工具？](#q19如何使用claude-code自动化工具)
- [Q20：如何建立个人质量监控体系？](#q20如何建立个人质量监控体系)
- [Q21：Claude Code工具集成的最佳实践](#q21claude-code工具集成的最佳实践)

### [第四章：AI工具宪法执行系统](#第四章ai工具宪法执行系统)
- [Q22：如何确保AI工具完全遵守开发规范？](#q22如何确保ai工具完全遵守开发规范)
- [Q23：启动AI工具时应该如何操作？](#q23启动ai工具时应该如何操作)
- [Q24：开发过程中如何监控AI工具行为？](#q24开发过程中如何监控ai工具行为)
- [Q25：AI工具违规时如何处理？](#q25ai工具违规时如何处理)
- [Q26：如何处理AI工具的审批请求？](#q26如何处理ai工具的审批请求)
- [Q27：日常维护和监控应该做什么？](#q27日常维护和监控应该做什么)

---

# 第三章：Claude Code工具体系使用指南

## Q17：如何最大化发挥Claude Code的价值？

### Q：我是个人开发者，如何最大化发挥Claude Code和相关工具的价值？

**A：四层递进式使用策略**

#### 🎯 Layer 1: 基础交互优化（立即可用）

**1. 精准任务描述**
```text
❌ 错误示例：
"帮我优化代码"

✅ 正确示例：  
"分析 src/data_import/fromC2C_csv_main_contract_importer.py 的代码复用机会，
然后基于 TDD 原则实现一个通用的 CSV 导入器基类，
要求：95% 测试覆盖率，支持配置驱动，包含错误处理和性能监控"
```

**2. 利用 CLAUDE.md / GEMINI.md 执行模式**
```bash
# 简单任务 - RAPID模式
"使用RAPID模式修复 config/settings.toml 中的路径配置错误"

# 标准任务 - STANDARD模式  
"使用STANDARD模式实现用户认证模块，包含完整的TDD流程"

# 复杂任务 - ARCHITECT模式
"使用ARCHITECT模式重构整个数据处理架构，设计可扩展的插件系统"
```

#### 🔧 Layer 2: 自动化工具集成（1-2天掌握）

**使用分析工具提升效率：**

```bash
# 1. 代码复用分析 - 每个新功能必做
python scripts/claude_tools/reuse_analyzer.py "实现期货数据实时监控功能"

# 2. 智能模式选择 - 了解任务复杂度
python scripts/claude_tools/mode_selector.py "重构数据库连接池管理"

# 3. TDD模板生成 - 快速启动开发
python scripts/claude_tools/tdd_generator.py --feature "market_data_processor" --type core

# 4. 质量指标监控 - 定期评估项目健康
python scripts/claude_tools/quality_calculator.py --generate-report
```

#### 📋 Layer 3: 系统化工作流（1周建立习惯）

**标准开发流程：**

```bash
# 步骤1：需求分析 + 复用评估
echo "新功能：期货价格预警系统" | \
python scripts/claude_tools/reuse_analyzer.py

# 步骤2：确定开发模式
python scripts/claude_tools/mode_selector.py "期货价格预警系统实现"

# 步骤3：生成开发模板（如果是STANDARD/ARCHITECT模式）
python scripts/claude_tools/tdd_generator.py \
  --feature "price_alert_system" \
  --type core \
  --scenarios happy_path edge_cases performance

# 步骤4：使用模板进行 TDD 开发
# ... 开发过程 ...

# 步骤5：质量验证
python scripts/claude_tools/quality_calculator.py --data-file current_metrics.json
```

#### 🚀 Layer 4: 个人效能体系（持续优化）

**建立个人开发仪表板：**

```python
# 个人效能监控脚本示例
def daily_dev_report():
    """生成每日开发效能报告"""
    
    # 1. 代码质量趋势
    quality_report = run_quality_analysis()
    
    # 2. 复用率统计  
    reuse_stats = calculate_reuse_metrics()
    
    # 3. TDD 遵循度
    tdd_compliance = measure_tdd_compliance()
    
    # 4. 个人生产力指标
    productivity_metrics = {
        "features_completed": count_completed_features(),
        "test_coverage_trend": get_coverage_trend(),
        "code_quality_score": get_quality_score(),
        "time_saved_by_reuse": calculate_time_savings()
    }
    
    return generate_dashboard(quality_report, reuse_stats, productivity_metrics)
```

---

## Q18：个人开发者的Claude Code工作流是什么？

### Q：具体的个人开发工作流应该是怎样的？

**A：三阶段工作流**

#### 📊 阶段1：分析与规划（5-10分钟）

```bash
# 1. 复用分析（必做）
python scripts/claude_tools/reuse_analyzer.py "新功能需求描述"
# 输出：复用策略 + 预估时间节省

# 2. 复杂度评估（推荐）  
python scripts/claude_tools/mode_selector.py "新功能需求描述"
# 输出：RAPID/STANDARD/ARCHITECT 模式建议

# 3. 模板准备（STANDARD/ARCHITECT模式）
python scripts/claude_tools/tdd_generator.py --feature "feature_name" --type core
# 输出：测试模板 + 实现模板
```

#### 🔨 阶段2：实现与开发（主要时间）

**Claude Code 对话模式：**

```text
我要基于以下分析结果实现新功能：

【复用分析结果】：
- 策略：EXTEND_REUSE  
- 基础代码：src/data_import/csv_importer.py
- 时间节省：70%

【执行模式】：STANDARD
【生成模板】：已创建 test_market_data_processor.py 和 market_data_processor.py

请帮我：
1. 分析现有 csv_importer.py 的扩展点
2. 基于生成的模板实现 MarketDataProcessor
3. 遵循 Red-Green-Refactor TDD 循环
4. 确保 95% 测试覆盖率
```

#### ✅ 阶段3：质量验证（5-15分钟）

```bash
# 1. 运行测试套件
pytest tests/ --cov=src --cov-report=term-missing

# 2. 代码质量检查
black src/ tests/
mypy src/
ruff check src/ tests/

# 3. 整体质量评估
python scripts/claude_tools/quality_calculator.py --generate-report

# 4. 提交代码（如有需要）
git add . && git commit -m "feat: 实现期货数据处理器 #T_ID_market-data-processor"
```

---

## Q19：如何使用Claude Code自动化工具？

### Q：这些自动化工具的具体使用方法和场景是什么？

**A：四大核心工具使用指南**

#### 🔍 工具1：代码复用分析器

**使用场景：** 每个新功能开始前

```bash
# 基础用法
python scripts/claude_tools/reuse_analyzer.py "实现股票数据采集功能"

# 高级用法  
python scripts/claude_tools/reuse_analyzer.py \
  "实现期货持仓分析算法" \
  --format json \
  --project-root /path/to/project

# 批量分析
features=("用户认证" "数据缓存" "报告生成")
for feature in "${features[@]}"; do
  python scripts/claude_tools/reuse_analyzer.py "$feature"
done
```

**输出解读：**
```text
🔍 Reuse Analysis Results
━━━━━━━━━━━━━━━━━━━━━━
Strategy: EXTEND_REUSE
Justification: 发现良好相似性。现有实现可扩展满足新需求。
Time Savings: 70% 时间节省

📁 Found Matches:
  • data_processor.py:DataProcessor (similarity: 0.75)
  • risk_calculator.py:RiskCalculator (similarity: 0.68)

💡 Recommendation:
  扩展现有功能以满足新需求
```

#### 🎯 工具2：模式选择器

**使用场景：** 确定开发策略

```bash
# 简单任务
python scripts/claude_tools/mode_selector.py "修复配置文件中的拼写错误"
# 预期输出：RAPID 模式

# 中等任务  
python scripts/claude_tools/mode_selector.py "实现新的数据验证模块"
# 预期输出：STANDARD 模式

# 复杂任务
python scripts/claude_tools/mode_selector.py "重构整个数据处理架构为微服务"
# 预期输出：ARCHITECT 模式
```

#### 🧪 工具3：TDD模板生成器

**使用场景：** STANDARD/ARCHITECT 模式下快速启动

```bash
# 核心模块模板
python scripts/claude_tools/tdd_generator.py \
  --feature "portfolio_optimizer" \
  --type core

# 标准模块模板
python scripts/claude_tools/tdd_generator.py \
  --feature "report_generator" \
  --type standard \
  --scenarios happy_path edge_cases performance

# 指定输出目录
python scripts/claude_tools/tdd_generator.py \
  --feature "data_validator" \
  --output-dir src/validation/
```

**生成的文件：**
- `test_portfolio_optimizer.py` - 完整测试套件
- `portfolio_optimizer.py` - 实现模板

#### 📈 工具4：质量指标计算器

**使用场景：** 定期质量评估

```bash
# 快速报告
python scripts/claude_tools/quality_calculator.py --generate-report

# 使用自定义数据
python scripts/claude_tools/quality_calculator.py \
  --data-file project_metrics.json \
  --generate-report

# JSON输出用于仪表板
python scripts/claude_tools/quality_calculator.py --format json > dashboard_data.json
```

**质量指标说明：**
- **代码复用率** (目标 >60%): 反映复用策略执行效果
- **测试覆盖率** (核心 >95%, 其他 >85%): 反映TDD执行质量  
- **TDD合规度** (目标 >90%): 反映测试优先开发遵循度
- **代码质量分** (目标 >9.0/10): 反映代码标准遵循情况
- **缺陷密度** (目标 <1 bug/KLOC): 反映代码质量稳定性

---

## Q20：如何建立个人质量监控体系？

### Q：作为个人开发者，如何建立有效的质量监控体系？

**A：三层质量监控架构**

#### 🎛️ Layer 1: 实时质量守门（开发过程中）

**Pre-commit 钩子配置：**
```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: aqua-quality-check
        name: AQUA Quality Gate
        entry: bash
        language: system
        args: ['-c', 'python scripts/claude_tools/quality_calculator.py --quick-check || exit 1']
        
      - id: reuse-analysis
        name: Reuse Analysis Required
        entry: bash  
        language: system
        args: ['-c', 'echo "记住：新功能前先运行 reuse_analyzer.py"']
```

**开发时质量检查：**
```bash
# 每次提交前运行
python scripts/claude_tools/quality_calculator.py --quick-check
# 如果质量指标低于阈值，拒绝提交
```

#### 📊 Layer 2: 周期性深度分析（每日/每周）

**每日质量报告：**
```bash
#!/bin/bash
# scripts/daily_quality_report.sh

echo "📊 AQUA项目每日质量报告 - $(date)"
echo "================================="

# 1. 运行完整质量分析
python scripts/claude_tools/quality_calculator.py --generate-report

# 2. 代码覆盖率趋势
pytest --cov=src --cov-report=term | grep "TOTAL"

# 3. 技术债务检查
radon cc src/ --average
radon mi src/ --average

# 4. 性能基准测试
python -m pytest tests/performance/ -v

echo "================================="
echo "💡 改进建议："
echo "1. 重点关注覆盖率 <85% 的模块"  
echo "2. 重构复杂度 >10 的函数"
echo "3. 提升可维护性指数 <65 的文件"
```

**每周深度分析：**
```python
# scripts/weekly_analysis.py
def weekly_deep_analysis():
    """每周深度质量分析"""
    
    # 1. 质量趋势分析
    quality_trends = analyze_quality_trends(weeks=4)
    
    # 2. 复用效果评估
    reuse_effectiveness = measure_reuse_impact()
    
    # 3. TDD实践评估
    tdd_metrics = evaluate_tdd_practice()
    
    # 4. 个人生产力分析
    productivity_analysis = analyze_productivity()
    
    # 生成改进计划
    improvement_plan = generate_improvement_plan(
        quality_trends, reuse_effectiveness, 
        tdd_metrics, productivity_analysis
    )
    
    return {
        "summary": generate_executive_summary(),
        "trends": quality_trends,
        "reuse": reuse_effectiveness,
        "tdd": tdd_metrics,
        "productivity": productivity_analysis,
        "improvement_plan": improvement_plan
    }
```

#### 🎯 Layer 3: 个人效能优化（持续改进）

**个人开发仪表板：**
```python
# dashboard/personal_metrics.py
class PersonalDeveloperDashboard:
    """个人开发者效能仪表板"""
    
    def __init__(self):
        self.metrics = {
            "daily": self._collect_daily_metrics(),
            "weekly": self._collect_weekly_metrics(),
            "monthly": self._collect_monthly_metrics()
        }
    
    def _collect_daily_metrics(self):
        """每日指标收集"""
        return {
            "lines_of_code": self._count_daily_loc(),
            "tests_written": self._count_daily_tests(),
            "reuse_instances": self._count_reuse_instances(),
            "quality_score": self._get_current_quality_score(),
            "focus_time": self._measure_focus_time()
        }
    
    def generate_personal_insights(self):
        """生成个人洞察"""
        insights = []
        
        # 生产力模式分析
        if self._detect_high_productivity_patterns():
            insights.append("🚀 最佳工作时间段：9-11AM，建议安排复杂任务")
        
        # 质量改进建议
        quality_gaps = self._identify_quality_gaps()
        for gap in quality_gaps:
            insights.append(f"⚡ 改进机会：{gap['description']}")
        
        # 复用成效
        reuse_impact = self._calculate_reuse_impact()
        insights.append(f"♻️ 复用策略节省时间：{reuse_impact['hours_saved']}小时/周")
        
        return insights
```

**关键质量指标监控：**

| 指标类别 | 监控频率 | 目标值 | 预警阈值 | 行动触发 |
|---------|---------|--------|---------|---------|
| **测试覆盖率** | 每次提交 | 核心>95%, 其他>85% | <80% | 暂停新功能，补充测试 |
| **代码复用率** | 每日 | >60% | <40% | 分析复用机会，重构 |
| **质量分数** | 每日 | >9.0/10 | <8.0/10 | 代码质量专项整治 |
| **TDD合规度** | 每周 | >90% | <70% | TDD实践强化训练 |
| **技术债务** | 每周 | <10% | >20% | 技术债务偿还计划 |

---

## Q21：Claude Code工具集成的最佳实践

### Q：如何将Claude Code工具体系集成到我的日常开发工作中？

**A：渐进式集成策略**

#### 🌱 第一周：基础集成

**目标：** 建立基本使用习惯

```bash
# 1. 设置别名便于快速使用
echo 'alias reuse="python scripts/claude_tools/reuse_analyzer.py"' >> ~/.bashrc
echo 'alias mode="python scripts/claude_tools/mode_selector.py"' >> ~/.bashrc  
echo 'alias tdd="python scripts/claude_tools/tdd_generator.py"' >> ~/.bashrc
echo 'alias quality="python scripts/claude_tools/quality_calculator.py"' >> ~/.bashrc

# 2. 创建快速检查脚本
cat > quick_check.sh << 'EOF'
#!/bin/bash
echo "🔍 快速质量检查..."
quality --quick-check
if [ $? -eq 0 ]; then
    echo "✅ 质量检查通过"
else
    echo "❌ 质量问题需要关注"
fi
EOF
chmod +x quick_check.sh

# 3. 每天至少使用一次复用分析
# 养成习惯：新功能前先问 "有没有可复用的代码？"
```

#### 🔧 第二周：工作流整合

**目标：** 将工具融入开发流程

```bash
# 创建标准开发模板
cat > new_feature.sh << 'EOF'
#!/bin/bash
if [ -z "$1" ]; then
    echo "用法: ./new_feature.sh '功能描述'"
    exit 1
fi

FEATURE="$1"
echo "🚀 开始新功能开发：$FEATURE"

# 步骤1：复用分析
echo "📊 分析复用机会..."
reuse "$FEATURE"

echo -n "继续开发？ (y/n): "
read -r response
if [[ ! $response =~ ^[Yy]$ ]]; then
    exit 0
fi

# 步骤2：模式选择
echo "🎯 分析开发模式..."
mode "$FEATURE"

echo -n "生成TDD模板？ (y/n): "
read -r tdd_response
if [[ $tdd_response =~ ^[Yy]$ ]]; then
    echo -n "功能名称 (snake_case): "
    read -r feature_name
    echo -n "模块类型 (core/standard): "
    read -r module_type
    tdd --feature "$feature_name" --type "$module_type"
fi

echo "✅ 开发环境准备完成！"
EOF
chmod +x new_feature.sh
```

#### 🚀 第三周：自动化集成

**目标：** 自动化质量监控

```bash
# 1. Git Hooks 集成
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "🔍 运行质量检查..."

# 快速质量检查
python scripts/claude_tools/quality_calculator.py --quick-check
if [ $? -ne 0 ]; then
    echo "❌ 质量检查失败，请改进后再提交"
    exit 1
fi

# 代码格式检查
black --check src/ tests/
mypy src/
ruff check src/ tests/

echo "✅ 质量检查通过"
EOF
chmod +x .git/hooks/pre-commit

# 2. 每日自动报告
cat > daily_report.sh << 'EOF'
#!/bin/bash
# 添加到 crontab: 0 18 * * * /path/to/daily_report.sh

cd /path/to/AQUA/project
echo "📊 $(date) - AQUA项目每日报告" >> logs/daily_reports.log
python scripts/claude_tools/quality_calculator.py --generate-report >> logs/daily_reports.log
echo "---" >> logs/daily_reports.log
EOF
```

#### 📈 第四周：高级优化

**目标：** 个性化和深度集成

```python
# scripts/personal_optimization.py
class PersonalDevelopmentOptimizer:
    """个人开发优化器"""
    
    def __init__(self):
        self.preferences = self._load_personal_preferences()
        self.history = self._load_development_history()
    
    def smart_mode_recommendation(self, task_description):
        """基于个人历史的智能模式推荐"""
        base_recommendation = run_mode_selector(task_description)
        
        # 基于个人历史调整
        personal_adjustment = self._analyze_personal_patterns(task_description)
        
        return self._combine_recommendations(base_recommendation, personal_adjustment)
    
    def personalized_reuse_analysis(self, requirement):
        """个性化复用分析"""
        # 标准复用分析
        standard_analysis = run_reuse_analyzer(requirement)
        
        # 基于个人代码偏好调整
        personal_matches = self._find_personal_code_patterns(requirement)
        
        return self._merge_analysis(standard_analysis, personal_matches)
    
    def adaptive_quality_thresholds(self):
        """自适应质量阈值"""
        current_skill_level = self._assess_current_skill_level()
        
        # 根据技能水平调整质量要求
        if current_skill_level == "beginner":
            return {"test_coverage": 0.80, "quality_score": 8.0}
        elif current_skill_level == "intermediate":  
            return {"test_coverage": 0.90, "quality_score": 9.0}
        else:  # advanced
            return {"test_coverage": 0.95, "quality_score": 9.5}
```

**集成VS Code/IDE：**
```json
// .vscode/tasks.json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "AQUA: 复用分析",
            "type": "shell",
            "command": "python",
            "args": ["scripts/claude_tools/reuse_analyzer.py", "${input:taskDescription}"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "AQUA: 质量检查",
            "type": "shell", 
            "command": "python",
            "args": ["scripts/claude_tools/quality_calculator.py", "--generate-report"],
            "group": "test"
        }
    ],
    "inputs": [
        {
            "id": "taskDescription",
            "description": "描述要分析的功能需求",
            "default": "新功能实现",
            "type": "promptString"
        }
    ]
}
```

**最终效果：**
- ⚡ **效率提升**：通过复用分析平均节省 60% 开发时间
- 🎯 **质量保障**：自动化质量门控确保代码标准
- 📊 **数据驱动**：基于指标的持续改进决策
- 🔄 **习惯养成**：工具使用成为自然开发习惯
- 🚀 **技能成长**：通过工具反馈加速技能提升

通过这四周的渐进式集成，Claude Code工具体系将完全融入你的个人开发工作流，成为提升效率和质量的强大助手。

### [附录：版本历史与废弃功能](#附录版本历史与废弃功能)

---

# 第零章：V4.0架构与核心文档体系

## Q0：AQUA V4.0采用什么架构，核心文档体系是什么？

### Q：V4.0架构的核心特点和文档体系是什么？

**A：V4.0采用统一业务表架构，建立了完整的核心文档体系**

#### 🏗️ V4.0统一业务表架构特点

- **业务驱动**: 以业务表为核心，应用层直接访问，无需复杂JOIN
- **数据透明**: 通过元数据字段管理数据来源，多数据源统一视图
- **查询简化**: 单表查询优先，性能优秀，维护简单
- **扩展灵活**: 新数据源只需添加适配器，业务表结构保持稳定

#### 📚 核心文档体系 (2025-07-28更新)

**权威架构文档**:
- **[V4_UNIFIED_ARCHITECTURE.md](./architecture/V4_UNIFIED_ARCHITECTURE.md)** - V4.0架构总览文档
- **[DATA_DICTIONARY.md](./database/DATA_DICTIONARY.md)** - V4.0权威数据字典
- **[Dev_Tasks_EPIC2_V2.md](./tasks/Dev_Tasks_EPIC2_V2.md)** - 重构任务规划文档

**配置管理**:
- **[settings.toml](../config/settings.toml)** - 统一配置文件

#### 🚧 当前重构状态

**Phase 1**: ✅ 全量备份完成 (`feature/v4-refactor-full-backup-20250728`)
**Phase 2**: ✅ 文档统一化+代码清理完成 (2025-07-28)
**Phase 3**: ⏳ 数据采集MVP-CLI重新设计 (待开始)

**备份位置**: `deprecated/data_import_legacy/` - 完整的遗留代码备份

#### 🎯 架构优势

- ✅ **查询高效**: DuckDB OLAP优化，单表查询性能优秀
- ✅ **维护简单**: 统一的表结构和数据管理方式  
- ✅ **扩展便捷**: 新数据源只需添加适配器
- ✅ **质量可控**: 统一的数据质量评分和来源追溯

---

# 第一章：数据导入操作手册

## Q1：如何快速开始数据导入？

### Q：我是新用户，如何快速完成数据导入？

**A：推荐使用一键式完整导入流程**

```bash
# 1. 进入项目目录
cd /Users/<USER>/Documents/AQUA/Dev/AQUA

# 2. 激活虚拟环境（如果使用）
source venv/bin/activate  # 或使用 uv 管理的环境

# 3. 完整重置并导入数据
python scripts/data_import_manager.py --mode full-reset --env test
```

**流程说明：**
- 自动检查数据库健康状态
- 基于数据字典自动创建表结构
- 批量导入CSV和MySQL数据
- 生成详细的导入报告

---

## Q2：如何重置数据库？

### Q：数据库出现问题，如何安全重置？

**A：分情况处理**

**情况1：安全重置（推荐）**
```bash
# 会备份现有数据
python scripts/reset_database.py --env test --backup
```

**情况2：强制重置（谨慎使用）**
```bash
# 不备份，直接清空
python scripts/reset_database.py --env test --force
```

### Q：重置后如何验证数据库状态？

**A：使用健康检查工具**
```bash
# 数据库健康检查
python -c "from src.database.duckdb_init_check import DuckDBInitChecker; DuckDBInitChecker('config/settings.toml', 'test').display_health_report()"
```

---

## Q3：如何导入CSV数据？

### Q：只需要导入CSV数据，不需要MySQL数据怎么办？

**A：使用专用的FromC2C CSV导入工具**

#### 🚀 FromC2C期货主力合约数据导入（✅ 2025-07-20完成）

**最佳实践**：FromC2C专用导入器，支持Rich增强界面

```bash
# 推荐：完整导入流程（包含环境检查、表创建、数据导入、验证）
python scripts/fromC2C_import_cli.py --env dev --full

# 快速测试（每表限制5个文件）
python scripts/fromC2C_import_cli.py --env dev --max-files 5

# 只验证已导入数据
python scripts/fromC2C_import_cli.py --env dev --verify-only

# 只导入特定频率数据
python scripts/fromC2C_import_cli.py --env dev --table 15min
```

**数据源要求**：
- 数据路径：`/Users/<USER>/Documents/Data/FromC2C`
- 支持频率：5分钟、15分钟、30分钟
- 支持品种：89个期货品种
- 数据格式：`index,open,close,high,low,volume,money,open_interest,contract_code,date`

**导入特性**：
- ✅ 自动数据清洗（NaN值处理、重复记录去除）
- ✅ 数据质量验证（价格逻辑检查、OHLC关系验证）
- ✅ Rich增强界面（实时进度条、详细统计、错误报告）
- ✅ 严格遵循DATA_DICTIONARY.md标准表结构

#### 🆕 V4.0统一业务表架构导入（最新）

**架构重构完成**（✅ **2025-07-28完成**）：
```bash
# V4.0统一业务表架构特性
# 现有数据导入系统已在Phase 2清理中移除，正在重新设计

# Phase 2完成状态 (2025-07-28)
echo "✅ Phase 1: 全量备份完成"
echo "✅ Phase 2: 文档统一化+代码清理完成" 
echo "⏳ Phase 3: 数据采集MVP-CLI重新设计 - 待开始"

# 核心架构文档
echo "📄 架构文档: docs/architecture/V4_UNIFIED_ARCHITECTURE.md"
echo "📄 数据字典: docs/database/DATA_DICTIONARY.md (V4.0权威版)"
echo "📄 任务规划: docs/tasks/Dev_Tasks_EPIC2_V2.md"

# 当前备份位置
echo "🗂️ 代码备份: deprecated/data_import_legacy/"
echo "🔄 安全恢复: feature/v4-refactor-full-backup-20250728 分支"
```

**V4.0统一业务表架构特性**：
- ✅ **统一业务表设计**: 期货5表+股票5表+公共2表，元数据驱动
- ✅ **多数据源透明化**: 通过元数据字段管理数据来源和质量
- ✅ **查询简化**: 应用层直接查询业务表，无需复杂JOIN操作
- ✅ **扩展灵活**: 新数据源只需添加适配器，业务表结构保持稳定
- ✅ **质量可控**: 统一的数据质量评分和来源追溯机制
- ✅ **100%TDD驱动**: 集成测试覆盖率99%，423行生产代码
- ✅ **模式灵活**: 支持auto/full/incremental/validate四种提取模式

#### 🔄 兼容旧版导入方式

```bash
# 方法1：使用重构后的统一导入管理器
python scripts/data_import_manager.py --mode csv-only --env development

# 方法2：开发模式直接使用CSV导入器
cd /Users/<USER>/Documents/AQUA/Dev/AQUA
python scripts/env_init.py

# 环境初始化（开发环境自动，无需确认）
python -c "
from src.database.duckdb_init_check import DuckDBInitializer
result = DuckDBInitializer('development').initialize_database()
print(f'数据库初始化: {result}')
"

# 单文件导入（智能业务表映射）
python -c "
from src.data_import.csv_importer import CSVImporter
from pathlib import Path

with CSVImporter('development') as importer:
    result = importer.import_single_file(Path('data/al_主力合约_15分钟数据.csv'))
    print(f'导入结果: {result[\"table_name\"]} - {result[\"records_imported\"]}条')
"

# 批量导入（递归扫描所有子目录）
python -c "
from src.data_import.csv_importer import CSVImporter

with CSVImporter('development') as importer:
    result = importer.import_batch_files(max_files=10)
    print(f'批量导入: {result[\"processed_files\"]}/{result[\"total_files\"]} 成功')
"
```

### Q：2025-07-19重构版本有什么新特点？

**A：重构后增强的CSV导入特点：**
- ✅ **智能业务表映射**: 自动识别期货/股票文件并映射到标准业务表
  - `al_主力合约_15分钟数据.csv` → `fut_main_contract_kline_15min`
  - `000001_股票日K线.csv` → `stock_kline_daily`
  - `期货基础信息.csv` → `fut_basic_info`
- ✅ **数据标准化**: 字段名称标准化，时间格式统一，合约代码自动提取
- ✅ **统一组件架构**: 消除代码重复，使用统一的验证器、映射器、加载器
- ✅ **生产环境保护**: 生产环境需要三重确认，防止误操作
- ✅ **错误恢复增强**: 详细错误信息和解决建议
- ✅ **测试覆盖完整**: 统一测试套件，提供测试数据工厂

### Q：业务表映射规则是什么？

**A：智能文件名模式匹配**
```text
文件名模式 → 业务表名
*_主力合约_15分钟*.csv → fut_main_contract_kline_15min
*_股票日K线*.csv → stock_kline_daily
期货基础信息.csv → fut_basic_info
股票基础*.csv → stock_basic_info
其他文件.csv → 文件名（传统模式）
```

### Q：CSV文件格式有什么要求？

**A：重构后的格式要求：**
- **业务表文件**: 遵循DATA_DICTIONARY.md标准字段
- **字段映射**: 支持中文字段名自动转换为英文标准字段
- **编码格式**: UTF-8（自动检测GBK等其他编码）
- **文件大小**: 建议200MB以内，支持更大文件分块处理
- **数据验证**: 自动验证价格非负、合约代码格式等业务规则

---

## Q4：如何导入MySQL数据？

### Q：如何连接和导入MySQL数据？

**A：使用MySQL专用导入模式**

```bash
# 仅导入MySQL数据（推荐）
python scripts/data_import_manager.py --mode mysql-only --env test

# 跳过确认提示的快速导入
python scripts/data_import_manager.py --mode mysql-only --env test --skip-confirmation
```

### Q：如何测试MySQL连接？

**A：使用独立连接测试**
```bash
# 测试MySQL连接功能
python tests/integration/test_mysql_connection_only.py --env test

# 检查MySQL配置
python -c "from src.utils.config_loader import load_mysql_config; print(load_mysql_config('test'))"
```

### Q：MySQL导入有什么特点？

**A：MySQL导入特点包括：**
- **自动健康检查**: 导入前自动检查数据库状态
- **智能表选择**: 基于数据字典要求自动选择核心表（B3模式）
- **连接验证**: 自动验证MySQL连接和表结构
- **数据转换**: 自动将MySQL数据转换为DuckDB格式
- **错误处理**: 根据C3策略处理导入错误
- **详细日志**: 生成详细的导入日志和报告

### Q：MySQL表如何映射到DuckDB？

**A：系统自动识别以下MySQL表模式：**
- `stock_basic*` → stock_basic_info
- `stock_kline*` → stock_kline_daily  
- `futures_basic*` → fut_main_contract_kline_15min
- `future_*` → 对应的期货表结构

---

## Q5：遇到常见错误怎么办？

### Q：出现"数据库锁定错误"怎么办？

**A：错误信息与解决方案**
```
错误: IO Error: Could not set lock on file
解决: 关闭所有数据库连接工具（如DBeaver）
```

#### 🔄 2025-07-19重构版本新增错误类型

**重构版本常见错误处理：**
```
错误: 业务表数据合规性验证失败
原因: 数据不符合业务规则（如价格为负数、合约代码格式无效）
解决: 检查CSV文件数据质量，修复不合规数据
示例: 第2行 open价格不能为负数(-100.0)

错误: DataLoader导入组件缺失
原因: 重构后缺少数据加载器组件
解决: 检查代码完整性，确保所有组件正确导入

错误: 生产环境操作被阻止  
原因: 生产环境保护机制启动，需要三重确认
解决: 使用development/test环境，或按提示进行确认

错误: 业务表映射失败
原因: 文件名不符合业务表映射规则
解决: 重命名文件或指定table_name参数
```

### Q：MySQL连接失败怎么办？

**A：分步骤排查**
```bash
# 1. 检查网络连接
ping 192.168.2.100

# 2. 检查配置
python -c "from src.utils.config_loader import load_mysql_config; print(load_mysql_config('test'))"

# 3. 测试连接
python tests/integration/test_mysql_connection_only.py --env test
```

### Q：CSV文件格式错误怎么办？

**A：检查必需字段**
```
错误: 必需字段缺失
解决: 检查CSV文件是否包含必需的列（如：contract_code, open, close等）
```

### Q：内存不足怎么办？

**A：内存优化策略**
```bash
# 当前系统已经优化了内存使用，如果仍然遇到内存问题：
# 1. 关闭其他应用程序
# 2. 检查config/settings.toml中的batch_size配置
# 3. 分批导入CSV文件
```

### Q：数据库初始化失败怎么办？

**A：手动排查和修复**
```bash
# 1. 检查数据字典文件是否存在
ls -la docs/database/DATA_DICTIONARY.md

# 2. 手动运行数据库初始化
python scripts/reset_database.py --env test --force

# 3. 验证初始化结果
python -c "from src.database.duckdb_init_check import DuckDBInitChecker; DuckDBInitChecker('config/settings.toml', 'test').display_health_report()"
```

---

## Q6：如何查看导入日志？

### Q：如何实时查看导入进度？

**A：使用日志查看命令**
```bash
# 查看数据导入主日志
tail -f logs/data_import_$(date +%Y%m%d).log

# 查看CSV导入详细日志
tail -f logs/csv_import_$(date +%Y%m%d).log

# 查看MySQL导入详细日志
tail -f logs/mysql_import_$(date +%Y%m%d).log
```

### Q：如何查找错误信息？

**A：错误日志查看方法**
```bash
# 查看错误日志
grep "ERROR" logs/*.log

# 查看最近的错误
grep "ERROR" logs/*.log | tail -10
```

### Q：如何查看导入报告？

**A：报告查看命令**
```bash
# 查看最新的导入报告
ls -la logs/import_report_*.json | tail -1

# 查看最新的健康报告
ls -la logs/health_report_*.json | tail -1
```

---

## Q7：如何验证导入结果？

### Q：如何检查数据库健康状态？

**A：完整健康检查**
```bash
# 完整的数据库健康检查
python -c "from src.database.duckdb_init_check import DuckDBInitChecker; DuckDBInitChecker('config/settings.toml', 'test').display_health_report()"

# 数据库结构验证
python -c "from src.database.duckdb_init_check import DuckDBInitChecker; checker = DuckDBInitChecker('config/settings.toml', 'test'); print(checker.validate_database_structure())"

# 数据完整性检查 (查询新核心表)
python -c "from src.database.connection_manager import DuckDBConnectionManager; conn = DuckDBConnectionManager('test').get_connection(); print(conn.execute('SELECT COUNT(*) FROM fact_stock_kline').fetchone()); print(conn.execute('SELECT COUNT(*) FROM fact_future_kline').fetchone())"
```

### Q：如何查看导入统计信息？

**A：导入统计查看脚本**
```bash
# 查看导入统计（简化版）
python -c "
import json, os, glob
reports = glob.glob('logs/import_report_*.json')
if reports:
    latest = max(reports, key=os.path.getctime)
    with open(latest, 'r') as f:
        data = json.load(f)
        print(f'会话ID: {data.get(\"session_id\", \"N/A\")}')
        print(f'总记录数: {data.get(\"total_records\", 0):,}')
        print(f'CSV文件数: {data.get(\"total_files\", 0)}')
        print(f'MySQL表数: {data.get(\"total_tables\", 0)}')
        print(f'错误数: {len(data.get(\"errors\", []))}')
else:
    print('未找到导入报告')
"
```

### Q：如何查看健康报告摘要？

**A：健康报告查看脚本**
```bash
# 查看最新的健康报告
python -c "
import json, os, glob
reports = glob.glob('logs/health_report_*.json')
if reports:
    latest = max(reports, key=os.path.getctime)
    with open(latest, 'r') as f:
        data = json.load(f)
        print(f'连接状态: {data.get(\"connection_status\", \"unknown\")}')
        print(f'表结构状态: {data.get(\"structure_status\", \"unknown\")}')
        print(f'数据完整性: {data.get(\"integrity_status\", \"unknown\")}')
        print(f'表数量: {data.get(\"table_count\", 0)}')
else:
    print('未找到健康报告')
"
```

---

## Q8：如何进行高级自定义操作？

### Q：如何编写自定义导入脚本？

**A：自定义脚本示例**
```python
from src.data_import import CSVImporter, MySQLImporter
from src.database.duckdb_init_check import DuckDBInitChecker

# 数据库健康检查
checker = DuckDBInitChecker("config/settings.toml", "test")
health_report = checker.get_database_health_report()
print(f"数据库状态: {health_report['connection_status']}")

# 自定义CSV导入
csv_importer = CSVImporter("test")
results = csv_importer.import_batch_files(max_files=5, limit_per_file=1000)

# 自定义MySQL导入
mysql_importer = MySQLImporter("test") 
results = mysql_importer.import_batch_mysql_tables(["stock_basic_info"])
```

### Q：如何进行数据库管理操作？

**A：完整数据库管理示例**
```python
# 完整的数据库管理示例
from src.database.duckdb_init_check import DuckDBInitChecker

# 初始化检查器
checker = DuckDBInitChecker("config/settings.toml", "test")

# 检查数据库连接
conn_result = checker.check_database_connection()
print(f"连接状态: {conn_result.success}")

# 验证表结构
validate_result = checker.validate_database_structure()
print(f"表结构验证: {validate_result.success}")

# 检查数据完整性
integrity_result = checker.check_data_integrity()
print(f"数据完整性: {integrity_result.success}")

# 初始化数据库（如果需要）
if not validate_result.success:
    init_result = checker.initialize_database(force=True)
    print(f"数据库初始化: {init_result.success}")
```

### Q：如何调整导入配置？

**A：编辑配置文件 `config/settings.toml`**
```toml
[test.csv]
batch_size = 5000
max_file_size_mb = 200
enable_parallel = true

[test.mysql]
batch_size = 1000
connection_timeout = 30
```

### Q：推荐的工作流程是什么？

**A：标准数据导入流程**
```bash
# 1. 完整重置和导入（推荐用于初始化）
python scripts/data_import_manager.py --mode full-reset --env test

# 2. 仅CSV导入（已有数据库时）
python scripts/data_import_manager.py --mode csv-only --env test

# 3. 仅MySQL导入（已有数据库时）
python scripts/data_import_manager.py --mode mysql-only --env test

# 4. 检查导入结果
python -c "from src.database.duckdb_init_check import DuckDBInitChecker; DuckDBInitChecker('config/settings.toml', 'test').display_health_report()"
```

### Q：开发者日常操作有哪些？

**A：开发环境常用命令**
```bash
# 开发环境快速重置
python scripts/reset_database.py --env test --force

# 测试单一功能
python tests/integration/test_mysql_connection_only.py --env test

# 查看系统状态
ls -la logs/*.log | tail -10
```

---

## 🚨 重要注意事项

### Q：使用AQUA数据导入功能需要注意什么？

**A：以下8项重要提醒：**

1. **数据备份**: 重要操作前请备份数据
2. **环境隔离**: 测试环境与生产环境严格分离
3. **配置驱动**: 所有配置使用 `config/settings.toml`，避免硬编码
4. **数据字典**: 表结构严格遵循 `docs/database/DATA_DICTIONARY.md`
5. **统一工具**: 使用 `DuckDBInitChecker` 进行数据库管理，避免手动创建表
6. **健康检查**: 导入前自动进行数据库健康检查和结构验证
7. **自动化优先**: 推荐使用 `data_import_manager.py` 进行自动化导入
8. **逐步验证**: 先小批量测试，再全量导入

# 第二章：环境管理与配置

## Q10：如何使用新的智能启动系统？

### Q：AQUA新版本的启动方式有什么变化？

**A：AQUA现在提供智能双入口系统，更简单、更直观**

#### 🚀 新的统一启动方式（推荐）

```bash
# 1. 交互式环境选择启动（最简单）
python start.py

# 2. 直接指定环境启动
python start.py --env dev     # 开发环境
python start.py --env test    # 测试环境  
python start.py --env prod    # 生产环境

# 3. 带参数启动
python start.py --env dev --wait --monitor
```

#### ✨ 智能启动特性
- **环境选择菜单**：显示DEV/TEST/PROD三种环境供选择
- **配置预览**：选择环境后显示数据库路径、CSV目录等配置
- **一键启动**：自动完成环境初始化+服务启动
- **跨平台兼容**：Windows/macOS/Linux统一体验

---

## Q11：如何选择和切换运行环境？

### Q：DEV/TEST/PROD三种环境有什么区别？如何选择？

**A：三种环境对应不同的使用场景和数据配置**

#### 环境对比表

| 环境 | 用途 | 数据库位置 | 适用场景 |
|------|------|-----------|----------|
| **DEV** | 开发环境 | `~/Documents/Data/duckdb/AQUA/DataCenter_dev.duckdb` | 日常开发、功能测试、快速验证 |
| **TEST** | 测试环境 | `~/Documents/Data/duckdb/AQUA/DataCenter_test.duckdb` | 集成测试、性能验证、预发布 |
| **PROD** | 生产环境 | `/Users/<USER>/Documents/Data/duckdb/AQUA/DataCenter.duckdb` | 正式分析、策略运行、完整数据 |

#### 选择建议
- **新用户学习**：推荐使用DEV环境
- **功能测试**：使用TEST环境验证稳定性
- **正式分析**：使用PROD环境进行策略研究

### Q：如何查看当前环境的配置？

**A：启动时会自动显示环境配置预览**

选择环境后，系统会显示：
- 📁 数据库文件路径
- 📂 CSV数据源目录  
- 💾 备份目录位置
- 📝 日志目录位置
- 🗃️ 缓存目录位置

---

## Q12：环境配置有什么区别？

### Q：不同环境的配置参数有什么不同？

**A：主要区别在数据路径、性能参数和安全策略**

#### 开发环境 (DEV) 配置特点
```toml
[dev.database]
path = "~/Documents/Data/duckdb/AQUA/DataCenter_dev.duckdb"

[dev.performance]
max_workers = 2      # 较少并发
chunk_size = 500     # 较小批次
memory_limit_mb = 512  # 较小内存

[dev.csv]
batch_size = 1000
enable_parallel = false  # 关闭并行处理
```

#### 测试环境 (TEST) 配置特点
```toml
[test.database]  
path = "~/Documents/Data/duckdb/AQUA/DataCenter_test.duckdb"

[test.performance]
max_workers = 4      # 中等并发
chunk_size = 1000    # 中等批次  
memory_limit_mb = 1024  # 中等内存

[test.csv]
batch_size = 5000
enable_parallel = true  # 启用并行处理
```

#### 生产环境 (PROD) 配置特点
```toml
[prod.database]
path = "/Users/<USER>/Documents/Data/duckdb/AQUA/DataCenter.duckdb"

[prod.performance]  
max_workers = 8      # 最大并发
chunk_size = 5000    # 最大批次
memory_limit_mb = 4096  # 最大内存

[prod.csv]
batch_size = 10000
enable_parallel = true  # 启用并行处理
```

---

## Q13：如何使用环境初始化脚本？

### Q：env_init.py脚本的基本用法是什么？

**A：环境初始化脚本是AQUA项目的核心基础设施，所有启动脚本都依赖它进行环境校验**

#### 基本用法
```bash
# 1. 完整环境初始化（推荐首次使用，进入交互选择模式）
python scripts/env_init.py

# 2. 仅环境检测，不做安装修复（日常检查）
python scripts/env_init.py --check-only

# 3. 指定环境运行（新增CLI参数）
python scripts/env_init.py --env dev
python scripts/env_init.py --env test --check-only
python scripts/env_init.py --env prod --check-only  # 生产环境建议仅检测

# 4. 非交互模式（CI/CD适用）
python scripts/env_init.py --non-interactive --env test
```

#### 交互式环境选择（新功能）
脚本现在支持智能交互选择环境：
```bash
# 进入交互选择模式
python scripts/env_init.py

# 输出示例：
# 🎯 请选择AQUA环境类型：
#   1. dev - 开发环境 - 适合日常开发和测试
#   2. test - 测试环境 - 适合集成测试和验证  
#   3. prod - 生产环境 - 生产级配置，操作需谨慎
# 请输入环境编号 (1-3) [1]: 2
```

#### 脚本功能详解
- ✅ **UV环境管理**: 自动检测和安装UV，强制使用项目.venv环境
- ✅ **依赖管理**: 从config/settings.toml读取基础依赖，自动安装bootstrap包
- ✅ **网络智能适配**: 自动检测中国网络环境，选择最优镜像源
- ✅ **配置验证**: 全面验证settings.toml配置的完整性和合规性
- ✅ **数据库初始化**: 根据配置自动创建DuckDB数据库和必要目录
- ✅ **Rich增强界面**: 美观的进度条、状态显示和结果报告
- ✅ **智能环境选择**: CLI交互选择或命令行参数指定环境类型
- ✅ **执行结果摘要**: 显示详细的环境配置和下一步操作建议

#### 执行结果摘要（新功能）
脚本执行完成后将显示详细的配置摘要：
```text
🎯 AQUA环境初始化结果摘要
┏━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━┓
┃ 配置项             ┃ 当前值                                           ┃ 状态     ┃
┡━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━┩
│ 🌍 当前环境        │ test                                             │    ✅    │
│ 📱 应用名称        │ AQUA                                             │    ✅    │
│ 🔖 应用版本        │ 1.0                                              │    ✅    │
│ 🗄️ 数据库路径      │ data/aqua_test.duckdb                            │    ✅    │
│ 📁 数据目录        │ /Users/<USER>/Documents/Data/FromC2C              │    ✅    │
│ 📦 批处理大小      │ 5000                                             │    ✅    │
│ ⚙️ 执行模式        │ 🔍 检测模式 (仅检查)                             │    ℹ️    │
│ 🔧 环境变量        │ export AQUA_ENV=test                             │    💡    │
└────────────────────┴──────────────────────────────────────────────────────┴──────────┘

🎯 执行结果与下一步
✅ 环境检测完成

🚀 下一步建议:
• 设置环境变量: export AQUA_ENV=test
• 启动后端服务: ./start_backend.sh
• 启动前端服务: ./start_frontend.sh
• 查看配置指南: docs/CONFIGURATION_GUIDE.md
```

### Q：配置驱动的环境管理有什么优势？

**A：100%配置驱动管理，消除硬编码依赖**

#### 配置驱动特性
```toml
# config/settings.toml中的环境配置示例
[bootstrap]
dependencies = [
    "toml==0.10.2",
    "rich>=13.7.0", 
    "loguru==0.7.2"
]
max_retries = 3
timeout_seconds = 60

[network]
china_detection_timeout = 2
china_mirrors = [
    { url = "https://pypi.tuna.tsinghua.edu.cn/simple", name = "清华大学" },
    { url = "https://mirrors.aliyun.com/pypi/simple/", name = "阿里云" }
]
```

#### 配置管理器组件
```python
# 统一配置管理架构
from scripts.config_manager import AQUAConfigManager
from scripts.network_manager import ConfigDrivenNetworkManager  
from scripts.config_validator import AQUAConfigValidator

# 自动从settings.toml读取所有配置
config_manager = AQUAConfigManager()
network_manager = ConfigDrivenNetworkManager(config_manager)
validator = AQUAConfigValidator(config_manager)
```

### Q：环境初始化失败了怎么排查？

**A：分步骤诊断和修复**

#### 常见问题诊断
```bash
# 1. 检查配置文件完整性
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager
validator = AQUAConfigValidator(AQUAConfigManager())
validator.validate_all_configurations()
"

# 2. 检查网络连接和镜像源
python -c "
from scripts.network_manager import ConfigDrivenNetworkManager
from scripts.config_manager import AQUAConfigManager
network_mgr = ConfigDrivenNetworkManager(AQUAConfigManager())
print(f'中国网络: {network_mgr.is_china_network_from_config()}')
print(f'最优镜像: {network_mgr.get_fastest_mirror_from_config()}')
"

# 3. 检查UV工具状态
python -c "
from scripts.config_manager import AQUAConfigManager
config_mgr = AQUAConfigManager()
print(f'UV路径: {config_mgr.get_environment_config()[\"uv_path\"]}')
"
```

#### 错误修复策略
- **配置验证失败**: 检查TOML语法，补全缺失字段
- **网络问题**: 调整超时时间，更换镜像源
- **UV安装失败**: 检查系统权限，手动安装UV
- **依赖安装失败**: 清理缓存，重试安装

---

## Q11：启动脚本系统如何工作？

### Q：AQUA的启动脚本系统架构是什么？

**A：启动脚本系统采用分层架构，确保环境一致性**

#### 启动脚本依赖关系
```text
启动脚本层:
├── start_frontend.sh/bat   (前端启动)
├── start_backend.sh/bat    (后端启动)
└── start_fullstack.sh/bat  (全栈启动)
         ↓
环境校验层:
└── env_init.py (环境初始化与校验)
         ↓
配置管理层:
├── config_manager.py      (统一配置管理)
├── network_manager.py     (网络环境管理)
├── dependency_manager.py  (依赖管理)
└── config_validator.py    (配置验证)
         ↓
配置文件层:
└── config/settings.toml (配置源头)
```

#### 前端启动流程
```bash
# start_frontend.sh执行流程
./start_frontend.sh
├── 1. 调用env_init.py进行环境校验
├── 2. 检查pnpm依赖安装
├── 3. 检查Node.js版本(.nvmrc)
├── 4. 启动Vue 3 + Vite开发服务器
└── 5. 错误处理和日志记录
```

#### 后端启动流程
```bash
# start_backend.sh执行流程  
./start_backend.sh
├── 1. 调用env_init.py进行环境校验
├── 2. 检查Python虚拟环境(.venv)
├── 3. 验证FastAPI依赖完整性
├── 4. 启动uvicorn服务器
└── 5. 错误处理和日志记录
```

### Q：如何使用启动脚本？

**A：推荐使用统一的启动脚本，自动处理环境问题**

#### 基本启动命令
```bash
# Linux/macOS
./start_frontend.sh    # 启动前端开发服务器
./start_backend.sh     # 启动后端API服务器

# Windows
start_frontend.bat     # 启动前端开发服务器
start_backend.bat      # 启动后端API服务器
```

#### 环境特定启动
```bash
# 指定环境启动后端
AQUA_ENV=dev ./start_backend.sh
AQUA_ENV=test ./start_backend.sh
AQUA_ENV=prod ./start_backend.sh

# 指定端口启动
PORT=8080 ./start_backend.sh
```

#### 调试模式启动
```bash
# 启用详细日志
DEBUG=1 ./start_backend.sh

# 仅环境检查，不启动服务
AQUA_ENV=dev python scripts/env_init.py --check-only
```

### Q：启动脚本出错了怎么办？

**A：查看详细错误日志和系统状态**

#### 错误诊断步骤
```bash
# 1. 查看启动脚本日志
tail -f logs/startup_$(date +%Y%m%d).log

# 2. 手动运行环境校验
python scripts/env_init.py

# 3. 检查配置完整性
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager
validator = AQUAConfigValidator(AQUAConfigManager())
is_valid = validator.validate_all_configurations()
print(f'配置状态: {\"通过\" if is_valid else \"失败\"}')
"

# 4. 检查依赖状态
uv pip list | grep -E "(fastapi|rich|toml|loguru)"
```

---

## Q12：如何进行多环境管理？

### Q：AQUA如何支持多环境开发？

**A：通过settings.toml配置文件实现dev/test/prod环境无缝切换**

#### 环境配置结构
```toml
# config/settings.toml
[app]
default_environment = "dev"

# 开发环境
[dev]
[dev.database]
path = "data/aqua_dev.duckdb"
auto_create = true

[dev.csv]
data_dir = "/Users/<USER>/Documents/Data/FromC2C"
batch_size = 1000

# 测试环境
[test]
[test.database]
path = "data/aqua_test.duckdb"
auto_create = true

[test.csv]
batch_size = 5000

# 生产环境
[prod]
[prod.database]
path = "data/aqua_prod.duckdb"
auto_create = false  # 生产环境禁用自动创建
```

#### 环境切换方法
```bash
# 方法1：环境变量切换（推荐）
export AQUA_ENV=test
python scripts/env_init.py

# 方法2：临时环境切换
AQUA_ENV=dev ./start_backend.sh
AQUA_ENV=test python scripts/data_import_manager.py

# 方法3：永久设置（添加到shell配置）
echo "export AQUA_ENV=dev" >> ~/.bashrc
source ~/.bashrc
```

### Q：不同环境的配置有什么区别？

**A：每个环境针对性优化配置参数**

#### 环境特性对比
| 配置项 | dev | test | prod |
|--------|-----|------|------|
| 数据库自动创建 | ✅ | ✅ | ❌ |
| 批处理大小 | 1000 | 5000 | 10000 |
| 并行处理 | ❌ | ✅ | ✅ |
| 详细日志 | ✅ | ✅ | ❌ |
| 自动备份 | ❌ | ✅ | ✅ |
| 严格验证 | ❌ | ✅ | ✅ |

#### 环境隔离保护
```python
# 生产环境保护机制
def check_production_safety():
    current_env = get_current_environment()
    if current_env == 'prod':
        # 三重确认机制
        confirmations = [
            "确认在生产环境操作？(yes/no): ",
            "这将影响生产数据，再次确认？(YES/NO): ",
            "最后确认，输入'CONFIRM'继续: "
        ]
        # 必须全部确认才能继续
```

### Q：如何验证当前环境配置？

**A：使用环境检测和配置验证工具**

#### 环境状态检查
```bash
# 检查当前环境
python -c "
from scripts.config_manager import AQUAConfigManager
config_mgr = AQUAConfigManager()
env = config_mgr.get_current_environment()
config = config_mgr.get_environment_config(env)
print(f'当前环境: {env}')
print(f'数据库路径: {config[\"database\"][\"path\"]}')
print(f'数据目录: {config[\"csv\"][\"data_dir\"]}')
print(f'批处理大小: {config[\"csv\"][\"batch_size\"]}')
"

# 验证环境配置完整性
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager
validator = AQUAConfigValidator(AQUAConfigManager())
result = validator.validate_multi_environment_config()
print(f'多环境配置: {\"通过\" if result else \"失败\"}')
"
```

---

## Q13：配置验证和错误修复

### Q：如何进行配置验证？

**A：使用AQUAConfigValidator进行全面配置检查**

#### 完整配置验证
```bash
# 执行全面配置验证
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager

config_manager = AQUAConfigManager()
validator = AQUAConfigValidator(config_manager)

print('=== AQUA配置验证报告 ===')
is_valid = validator.validate_all_configurations()
print(f'整体状态: {\"✅ 通过\" if is_valid else \"❌ 失败\"}')
"
```

#### 分模块验证
```bash
# 验证应用基础配置
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager
validator = AQUAConfigValidator(AQUAConfigManager())
validator._validate_app_config()
"

# 验证网络配置
python -c "
from scripts.config_validator import AQUAConfigValidator  
from scripts.config_manager import AQUAConfigManager
validator = AQUAConfigValidator(AQUAConfigManager())
validator._validate_network_config()
"

# 验证环境配置
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager
validator = AQUAConfigValidator(AQUAConfigManager())
validator._validate_environment_config()
"
```

### Q：配置验证失败了怎么修复？

**A：根据验证级别和具体错误进行针对性修复**

#### 验证错误级别
- **🔴 致命错误(critical)**: 必须立即修复，否则系统无法运行
- **🟡 警告(warning)**: 建议修复，可能影响功能或性能
- **🔵 信息(info)**: 优化建议，不影响基本功能

#### 常见配置错误修复
```toml
# 错误1: 缺失必需的应用配置
[app]
# ❌ 缺失name字段
version = "2.0"

# ✅ 修复：添加必需字段
[app]
name = "AQUA"
version = "2.0" 
default_environment = "dev"

# 错误2: 网络配置参数不合理
[network]
china_detection_timeout = 0.1  # ❌ 过短
connection_retries = 100       # ❌ 过多

# ✅ 修复：调整为合理范围
[network]
china_detection_timeout = 2    # 2秒合理
connection_retries = 3         # 3次重试合理

# 错误3: 环境配置不完整
[dev]
# ❌ 缺失database配置段

# ✅ 修复：添加完整环境配置
[dev]
[dev.database]
path = "data/aqua_dev.duckdb"
auto_create = true
[dev.csv]
data_dir = "/path/to/data"
batch_size = 1000
```

#### 自动配置修复
```bash
# 创建配置修复脚本
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager

config_manager = AQUAConfigManager()
validator = AQUAConfigValidator(config_manager)

# 获取详细验证结果
validator.validate_all_configurations()

# 显示修复建议
for result in validator.validation_results:
    if result.error_level == 'critical':
        print(f'🔴 {result.message}')
        if result.suggestion:
            print(f'   修复建议: {result.suggestion}')
    elif result.error_level == 'warning':
        print(f'🟡 {result.message}')
        if result.suggestion:
            print(f'   建议: {result.suggestion}')
"
```

### Q：如何创建新环境配置？

**A：复制现有环境配置并调整参数**

#### 新环境配置模板
```toml
# 添加新的staging环境
[staging]
[staging.database]
path = "data/aqua_staging.duckdb"
auto_create = true
backup_dir = "data/backup/staging"

[staging.csv]
data_dir = "/staging/data"
batch_size = 2000
enable_parallel = true

[staging.mysql]
batch_size = 1500
connection_timeout = 30
```

#### 验证新环境配置
```bash
# 验证staging环境配置
AQUA_ENV=staging python -c "
from scripts.config_manager import AQUAConfigManager
from scripts.config_validator import AQUAConfigValidator

config_manager = AQUAConfigManager()
validator = AQUAConfigValidator(config_manager)

try:
    env_config = config_manager.get_environment_config('staging')
    print('✅ staging环境配置载入成功')
    print(f'数据库路径: {env_config[\"database\"][\"path\"]}')
    
    # 验证配置完整性
    is_valid = validator.validate_all_configurations()
    print(f'配置验证: {\"✅ 通过\" if is_valid else \"❌ 失败\"}')
except Exception as e:
    print(f'❌ staging环境配置错误: {e}')
"
```

---

## Q16：中国网络环境下如何解决依赖安装问题？

### Q：在中国网络环境下，如何解决`loguru`等PyPI包安装失败的问题？

**A：AQUA已集成专业的中国网络环境优化方案，智能解决依赖安装问题**

#### 🚀 快速解决方案

```bash
# 方法1: 使用优化后的启动脚本（推荐）
python start.py --env dev

# 方法2: 单独使用中国网络优化器
python scripts/china_network_optimizer.py

# 方法3: 测试修复效果
python test_china_network_fix.py
```

#### 🔧 智能优化特性

**1. 自动镜像源检测**
- ✅ 清华大学镜像源（优先级1）
- ✅ 阿里云镜像源（优先级2）
- ✅ 中科大镜像源（优先级3）
- ✅ 豆瓣镜像源（优先级4）
- ✅ 腾讯云镜像源（优先级5）

**2. SSL证书问题自动处理**
- ✅ 添加`--trusted-host`参数
- ✅ 创建不验证SSL的上下文
- ✅ 优雅降级到HTTP镜像源
- ✅ 详细错误诊断信息

**3. 多工具回退策略**
```
uv (项目) → uv (系统) → pip (项目) → pip (系统) → 离线安装
```

#### 🛠️ 常见问题解决

**问题1: SSL证书验证失败**
```
[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed
```
**解决方案**: 系统已自动添加`--trusted-host`参数，如仍有问题运行：
```bash
python scripts/china_network_optimizer.py
```

**问题2: 连接超时**
```
The handshake operation timed out
```
**解决方案**: 系统会自动切换到下一个镜像源，或手动配置永久镜像源：
```bash
python -c "from scripts.china_network_optimizer import ChinaNetworkOptimizer; ChinaNetworkOptimizer().setup_permanent_mirror('tsinghua')"
```

**问题3: 所有镜像源都不可用**
```
⚠️ 所有镜像源均不可用，将尝试无镜像源安装
```
**解决方案**: 系统会自动尝试无镜像源安装和离线安装模式

#### 📊 性能提升

| 场景 | 优化前 | 优化后 | 改进 |
|------|--------|--------|------|
| loguru安装时间 | 30-60s | 5-15s | 60%+ |
| 首次启动时间 | 2-5min | 30-90s | 70%+ |
| 镜像源切换 | 手动 | 自动 | 100% |
| SSL错误处理 | 手动配置 | 自动处理 | 100% |

#### 📝 验证安装

```bash
# 验证loguru安装
python -c "from loguru import logger; logger.info('Test'); print('✅ loguru正常')"

# 运行完整测试套件
python test_china_network_fix.py
```

**详细文档**: 参见 `docs/CHINA_NETWORK_OPTIMIZATION_GUIDE.md`

---

# 附录：版本历史与废弃功能

## 版本历史

### v2.2 - 配置驱动合规性改进 (2025-07-20)
- ✅ **100%配置驱动架构**: 实现完全配置驱动合规性，消除所有硬编码常量
- ✅ **统一配置管理器**: `AQUAConfigManager` - 统一配置读取和缓存管理
- ✅ **配置驱动网络管理**: `ConfigDrivenNetworkManager` - 网络环境智能检测和镜像源管理
- ✅ **配置驱动依赖管理**: `ConfigDrivenDependencyManager` - 从settings.toml读取依赖配置
- ✅ **全面配置验证**: `AQUAConfigValidator` - 三级配置验证(critical/warning/info)
- ✅ **多环境支持**: dev/test/prod环境无缝切换，环境特定配置
- ✅ **智能错误修复**: 详细错误信息和自动修复建议
- ✅ **启动脚本集成**: 所有启动脚本集成env_init.py环境校验流程
- ✅ **配置驱动文档**: 完整的配置使用指南和故障排除手册

### v2.1 - CSV导入功能重构优化 (2025-07-19)
- ✅ **CSV导入器完全重构**: 采用统一组件架构，消除代码重复
- ✅ **智能业务表映射**: 自动识别期货/股票文件并映射到标准业务表
- ✅ **数据标准化增强**: 字段名称标准化，时间格式统一，合约代码自动提取
- ✅ **生产环境保护机制**: 生产环境操作需要三重确认，防止误操作
- ✅ **统一组件架构**: 
  - `src/data_import/validators/csv_validator.py` - 统一CSV验证器
  - `src/data_import/mappers/business_table_mapper.py` - 智能业务表映射器
  - `src/data_import/loaders/data_loader.py` - 统一数据加载器
- ✅ **统一测试套件**: 
  - `tests/unit/test_csv_importer_unified.py` - 统一测试
  - `tests/fixtures/csv_test_data.py` - 测试数据工厂
- ✅ **代码复用宪法**: 严格执行代码复用，消除重复实现
- ✅ **增强错误处理**: 详细错误信息和解决建议

### v1.2 - 完整重构 (2025-07-16)
- ✅ 集成 `DuckDBInitChecker` 统一数据库管理
- ✅ 实现自动化数据库健康检查流程
- ✅ 基于数据字典的完整表结构创建
- ✅ 废弃硬编码表创建脚本
- ✅ 增强的错误处理和日志记录
- ✅ 自动化的数据导入前验证

### v1.1 - 集成DuckDBInitChecker (2025-07-16)
- 初步集成数据库检测工具

### v1.0 - 基础版本 (2025-07-16)
- 基础的数据导入功能

## 已废弃功能

### Q：哪些功能已经废弃？

**A：以下脚本已废弃，请勿使用：**

#### scripts/deprecated/create_basic_tables.py
- **废弃原因**: 违反配置驱动原则，硬编码表结构
- **替代方案**: 使用 `python scripts/reset_database.py` 基于数据字典自动创建表

#### 其他手动验证脚本
- **废弃原因**: 功能重复，维护成本高
- **替代方案**: 使用 `DuckDBInitChecker` 统一的数据库管理工具

## 技术支持

### Q：遇到问题如何获得技术支持？

**A：请提供以下信息：**
1. 完整的错误信息
2. 相关的日志文件
3. 操作系统和Python版本信息
4. 具体的操作步骤

---

*最后更新: 2025-07-27*  
*版本: v3.0 - AI工具宪法执行系统集成版*  
*文档类型: AQUA项目官方QA手册*  

### 🚀 2025-07-20 配置驱动版本使用摘要

**环境初始化最佳实践**:
```bash
# 1. 完整环境初始化（推荐）
cd /Users/<USER>/Documents/AQUA/Dev/AQUA && python scripts/env_init.py

# 2. 指定环境配置验证
AQUA_ENV=dev python scripts/env_init.py

# 3. 配置验证检查
python -c "
from scripts.config_validator import AQUAConfigValidator
from scripts.config_manager import AQUAConfigManager
validator = AQUAConfigValidator(AQUAConfigManager())
validator.validate_all_configurations()
"

# 4. 启动脚本使用（自动环境校验）
./start_backend.sh  # 自动调用env_init.py校验
```

**配置驱动亮点**: 100%配置驱动 | 智能环境选择 | 执行结果摘要 | 统一配置管理

---

# 第四章：AI工具宪法执行系统

## Q22：如何确保AI工具完全遵守开发规范？

### Q：Claude Code、Gemini等AI工具经常违反项目规范，如何解决？

**A：AQUA项目已建立完整的AI工具宪法执行框架，通过技术手段确保100%合规**

#### 🏛️ 核心设计原理

**"预防胜于补救，约束胜于信任，透明胜于黑盒"**

- **预执行验证**：任何操作前进行强制检查
- **违规阻断**：发现违规立即停止操作  
- **人类审批**：重大决策需要明确批准
- **透明可控**：所有决策过程可追溯

#### 🔧 技术架构组件

**1. 宪法执行器** (`scripts/claude_constitutional_enforcer.py`)
- 预执行合规检查
- 违规行为检测和阻断
- 人类审批流程管理
- 合规日志记录

**2. 操作模板** (`scripts/claude_operation_template.py`)  
- 标准化操作流程
- 强制合规声明
- 自动合规验证

**3. 合规检查器** (`scripts/compliance_checker.py`)
- 项目目录结构验证
- 违规文件自动检测
- 合规报告生成

**4. 自检程序** (`scripts/claude_self_check.py`)
- 启动时状态确认
- 合规承诺书生成
- 系统完整性验证

#### ✅ 验证成功指标

通过这套框架，已实现：

- ✅ **100%合规保证** - AI工具无法违反宪法规则
- ✅ **透明决策过程** - 所有操作都有完整记录  
- ✅ **人类最终控制** - 重要决策需要明确批准
- ✅ **自动违规修正** - 发现问题立即处理
- ✅ **持续监控能力** - 实时跟踪AI工具状态

---

## Q23：启动AI工具时应该如何操作？

### Q：每次打开Claude Code、Gemini或Cursor时的标准操作流程是什么？

**A：使用AQUA专用的AI工具启动器，确保完全合规**

#### 🚀 统一启动命令

```bash
# 进入AQUA项目目录
cd /Users/<USER>/Documents/AQUA/Dev/AQUA

# 启动不同AI工具
./scripts/aqua_ai claude     # 启动Claude Code
./scripts/aqua_ai gemini     # 启动Gemini CLI  
./scripts/aqua_ai cursor     # 启动Cursor AI

# 或使用完整命令
python scripts/ai_tools_launcher.py --tool claude
python scripts/ai_tools_launcher.py --tool gemini
python scripts/ai_tools_launcher.py --tool cursor
```

#### 📋 自动执行的启动流程

**步骤1: 项目合规检查**
- ✅ 检查项目目录结构
- ✅ 验证无违规文件存在
- ✅ 自动修正发现的问题

**步骤2: AI工具自检程序**
- ✅ 验证宪法文件存在
- ✅ 确认宪法执行器可用
- ✅ 生成合规承诺书

**步骤3: 初始化指令生成**
- ✅ 针对不同AI工具生成专用初始化消息
- ✅ 包含强制合规要求
- ✅ 设置违规阻断机制

**步骤4: 监控系统设置**
- ✅ 创建工具专用日志文件
- ✅ 启用实时监控
- ✅ 显示监控命令

#### 🤖 Claude Code启动示例

启动后会生成以下初始化消息，必须完整发送给Claude Code：

```
🏛️ AQUA项目Claude Code宪法执行初始化

合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则

📜 核心宪法文件 (最高优先级)：
- /Users/<USER>/Documents/AQUA/Dev/AQUA/CLAUDE.md
- /Users/<USER>/Documents/AQUA/Dev/AQUA/GEMINI.md
- /Users/<USER>/Documents/AQUA/Dev/AQUA/docs/ENHANCED_EXECUTION_INSTRUCTIONS.md

🚨 强制执行要求：

1. **合规声明**: 每次操作前必须声明"合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则"

2. **预执行检查**: 使用宪法执行器进行操作前验证

3. **文件放置规则** (严格执行，违规立即停止):
   - 测试文件 → tests/ 目录
   - 文档文件 → docs/ 目录  
   - 脚本文件 → scripts/ 目录
   - 源码文件 → src/ 或 frontend/src/ 目录
   - 🚫 绝不在根目录创建上述类型文件

4. **人类审批协议**: 发现更好方案时必须：
   - 立即暂停操作
   - 详细说明分析过程
   - 请求人类明确批准
   - 等待 "approve/reject/modify" 回复

5. **TDD强制要求**: 生产代码前必须先写测试用例

⚠️ 违规后果：严重违规将触发自动阻断，操作立即终止

🎯 请确认您理解并将严格遵循以上所有规范。
```

#### 💎 其他AI工具启动

**Gemini CLI启动**：获得针对Gemini优化的初始化指令
**Cursor AI启动**：获得针对IDE集成优化的初始化指令

---

## Q24：开发过程中如何监控AI工具行为？

### Q：如何实时监控AI工具是否遵循规范？

**A：使用多层监控系统，实时跟踪AI工具行为**

#### 📊 实时监控命令

```bash
# 启动完整监控（推荐）
./scripts/aqua_ai monitor

# 或分别监控不同类型的日志
tail -f logs/constitutional_enforcement.jsonl    # 宪法执行日志  
tail -f logs/approval_requests.jsonl            # 审批请求
tail -f logs/ai_tools/claude.log               # Claude专用日志
tail -f logs/ai_tools/gemini.log               # Gemini专用日志
tail -f logs/ai_tools/cursor.log               # Cursor专用日志
```

#### 🔍 监控内容说明

**1. 宪法执行日志** (`constitutional_enforcement.jsonl`)
- 每次合规检查记录
- 违规行为检测结果
- 违规处理决策过程

**2. 审批请求日志** (`approval_requests.jsonl`)
- AI工具请求人类审批的完整记录
- 包含当前方案vs建议方案对比
- 人类决策结果记录

**3. AI工具专用日志** (`ai_tools/*.log`)
- 工具启动和停止时间
- 操作执行记录
- 错误和异常信息

#### 📈 监控仪表板

**查看实时状态**：
```bash
# 生成实时合规报告
./scripts/aqua_ai report

# 检查项目合规状态
./scripts/aqua_ai check

# 查看最近的违规记录
python -c "
from scripts.claude_constitutional_enforcer import ClaudeConstitutionalEnforcer
enforcer = ClaudeConstitutionalEnforcer()
violations = enforcer._load_recent_violations()
print(f'最近发现 {len(violations)} 个合规问题')
for v in violations[-3:]:
    print(f'- {v.get(\"timestamp\", \"N/A\")}: {v.get(\"violation_level\", \"N/A\")}')
"
```

#### ⚠️ 预警机制

**自动预警触发条件**：
- 🔴 **严重违规**：在根目录创建测试/文档文件
- 🟡 **一般违规**：跳过复用分析、文件放置错误  
- 🔵 **提醒**：未遵循TDD原则、缺少合规声明

**预警处理**：
```bash
# 违规自动修正
python scripts/compliance_checker.py  # 选择 'y' 自动修正

# 向AI工具发送警告
echo "🚨 检测到违规行为，请立即停止当前操作并重新声明合规承诺"
```

---

## Q25：AI工具违规时如何处理？

### Q：发现AI工具违反规范时的处理流程是什么？

**A：分级处理机制，确保快速有效解决违规问题**

#### 🚨 违规检测和立即响应

**发现违规时立即执行**：
```bash
# 1. 检查违规详情
./scripts/aqua_ai check

# 2. 查看具体违规记录
python -c "
from scripts.claude_constitutional_enforcer import ClaudeConstitutionalEnforcer
enforcer = ClaudeConstitutionalEnforcer()
print(enforcer.generate_compliance_report())
"

# 3. 自动修正违规问题（如适用）
python scripts/compliance_checker.py  # 选择 'y' 进行自动修正
```

#### 📋 违规等级和处理方式

**🔴 严重违规 (CRITICAL)**
- **触发条件**：在根目录创建测试/文档文件、缺少合规声明
- **自动处理**：立即阻断操作，AI工具必须停止
- **人工处理**：向AI工具发送强制重置指令

```
🚨 检测到严重违规行为

您刚才的操作违反了AQUA项目宪法：
[具体违规描述]

请立即：
1. 停止当前操作
2. 重新声明合规承诺："合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则"
3. 按照正确规范重新执行

⚠️ 继续违规将导致会话终止。
```

**🟡 一般违规 (VIOLATION)**
- **触发条件**：文件放置位置不当、跳过复用分析
- **自动处理**：记录警告，允许继续但监控
- **人工处理**：提醒AI工具注意合规性

**🔵 警告 (WARNING)**
- **触发条件**：未遵循TDD原则、缺少完整文档
- **自动处理**：记录建议，不阻断操作
- **人工处理**：适时提醒最佳实践

#### 🔄 AI工具重置流程

**持续违规时的重置步骤**：
```bash
# 1. 重置项目合规状态  
python scripts/claude_self_check.py

# 2. 向AI工具发送重置指令
echo "
🏛️ 宪法合规重置

由于检测到违规行为，现在重置您的执行状态：

1. 请重新阅读宪法文件：/Users/<USER>/Documents/AQUA/Dev/AQUA/CLAUDE.md和/Users/<USER>/Documents/AQUA/Dev/AQUA/GEMINI.md
2. 重新声明合规承诺
3. 从预执行检查开始重新执行任务

所有之前的违规操作已被撤销，请严格按照规范重新开始。
"
```

#### 📊 违规模式分析

**定期分析违规趋势**：
```bash
# 分析最近的违规模式
python -c "
import json
from collections import Counter

violations = []
with open('logs/constitutional_enforcement.jsonl', 'r') as f:
    for line in f:
        try:
            entry = json.loads(line)
            if entry.get('violations'):
                violations.extend(entry['violations'])
        except:
            continue

print('📊 违规模式分析：')
for violation, count in Counter(violations).most_common(5):
    print(f'  {count}次: {violation}')
"
```

---

## Q26：如何处理AI工具的审批请求？

### Q：AI工具暂停并请求人类审批时应该如何响应？

**A：标准化审批流程，确保决策质量和效率**

#### 🤖 审批请求格式

当AI工具发现更好方案时，会显示：

```
🤖 Claude Code 请求人类审批
============================================================

📋 决策类型: 替代方案
⏰ 时间: 2025-07-27T10:30:00

📝 当前计划:
在根目录创建test_example.py测试文件

💡 建议替代方案:
在tests/unit/目录创建test_example.py，符合项目结构规范

🧠 推理过程:
根据CLAUDE.md/GEMINI.md 宪法第18条，测试文件应放在tests/目录下。
这样可以：
1. 保持项目结构清晰
2. 符合开发规范
3. 便于测试管理

🎯 需要您的决策:
请回复以下选项之一：
✅ 'approve' - 批准替代方案
❌ 'reject' - 拒绝，继续原计划
🔄 'modify' - 要求修改方案
============================================================
```

#### 📋 标准回复选项

**✅ 批准方案**
```bash
approve
```

**❌ 拒绝方案**
```bash
reject
```

**🔄 要求修改**
```bash
modify: 请在tests/integration/目录而不是tests/unit/目录创建测试文件
```

**📋 要求更多信息**
```bash
explain: 请详细说明为什么这个方案更好，有什么风险？
```

#### 🎯 审批决策指南

**何时批准 (approve)**：
- AI工具的建议明显更符合项目规范
- 建议方案能带来长期收益
- 推理过程逻辑清晰、依据充分

**何时拒绝 (reject)**：
- 当前方案已经足够好
- AI工具的建议偏离项目目标
- 时间紧迫，不适合做较大改动

**何时要求修改 (modify)**：
- AI工具方向正确但细节需要调整
- 有更好的第三种方案
- 需要考虑特定的项目约束

#### 📊 审批记录管理

**查看审批历史**：
```bash
# 查看最近的审批请求
cat logs/approval_requests.jsonl | tail -5 | jq '.'

# 分析审批模式
python -c "
import json
from pathlib import Path

approval_file = Path('logs/approval_requests.jsonl')
if approval_file.exists():
    with open(approval_file, 'r') as f:
        requests = [json.loads(line) for line in f if line.strip()]
    
    print(f'总审批请求: {len(requests)}')
    
    # 按决策类型统计
    decision_types = {}
    for req in requests:
        dt = req.get('decision_type', 'unknown')
        decision_types[dt] = decision_types.get(dt, 0) + 1
    
    print('按类型统计:')
    for dt, count in decision_types.items():
        print(f'  {dt}: {count}次')
else:
    print('暂无审批请求记录')
"
```

#### ⚡ 快速审批技巧

**常见审批场景**：

1. **文件放置优化** → 通常 `approve`
2. **代码复用建议** → 仔细评估后决定
3. **架构改进建议** → 通常 `modify` 要求更多细节
4. **测试策略调整** → 根据项目阶段决定

**批量审批**：
对于明显符合规范的建议，可以建立"预批准规则"：
```bash
# 可以在AI工具初始化时添加：
"对于将文件从根目录移动到正确目录的建议，自动批准"
```

---

## Q27：日常维护和监控应该做什么？

### Q：作为项目管理者，日常应该如何维护AI工具宪法执行系统？

**A：建立规律的维护节奏，确保系统长期稳定运行**

#### ☀️ 每日检查清单

**开始工作前 (5分钟)**：
```bash
# 1. 检查项目整体合规性
./scripts/aqua_ai check

# 2. 查看过夜的AI工具操作日志
cat logs/constitutional_enforcement.jsonl | tail -10

# 3. 处理待审批请求
cat logs/approval_requests.jsonl | tail -5

# 4. 确认根目录清洁
ls -la | grep -E "(test_.*\.py|.*_REPORT\.md|.*_script\.py)" || echo "✅ 根目录清洁"
```

**结束工作后 (5分钟)**：
```bash
# 1. 生成当日合规报告
./scripts/aqua_ai report > logs/daily_compliance_$(date +%Y%m%d).md

# 2. 清理临时文件
find . -name "*.tmp" -o -name "temp_*" -delete 2>/dev/null || true

# 3. 备份重要日志
cp logs/constitutional_enforcement.jsonl logs/backup/ 2>/dev/null || true
```

#### 📊 每周深度分析

**周一上午 (15分钟)**：
```bash
# 1. 生成上周合规趋势报告
python -c "
import json, glob, os
from datetime import datetime, timedelta
from collections import Counter

# 分析上周的违规模式
week_ago = datetime.now() - timedelta(days=7)
recent_violations = []

try:
    with open('logs/constitutional_enforcement.jsonl', 'r') as f:
        for line in f:
            try:
                entry = json.loads(line)
                entry_time = datetime.fromisoformat(entry.get('timestamp', '1970-01-01'))
                if entry_time > week_ago and entry.get('violations'):
                    recent_violations.extend(entry['violations'])
            except:
                continue
except FileNotFoundError:
    pass

print('📊 上周违规分析：')
if recent_violations:
    for violation, count in Counter(recent_violations).most_common(5):
        print(f'  {count}次: {violation}')
else:
    print('  ✅ 上周无违规记录')

print(f'  总违规次数: {len(recent_violations)}')
"

# 2. 检查AI工具使用频率
ls -la logs/ai_tools/ | grep -v '^d' | wc -l | xargs echo "活跃AI工具数量:"

# 3. 分析审批效率
python -c "
import json
from pathlib import Path

approval_file = Path('logs/approval_requests.jsonl') 
if approval_file.exists():
    with open(approval_file, 'r') as f:
        requests = [json.loads(line) for line in f if line.strip()]
    
    print(f'总审批请求: {len(requests)}')
    
    # 分析审批状态
    pending = len([r for r in requests if r.get('status') == 'PENDING_APPROVAL'])
    print(f'待处理审批: {pending}')
else:
    print('暂无审批数据')
"
```

#### 🗓️ 每月系统维护

**月初维护 (30分钟)**：
```bash
# 1. 清理旧日志文件 (保留3个月)
find logs/ -name "*.log" -mtime +90 -delete
find logs/ -name "*.jsonl" -mtime +90 -delete

# 2. 更新合规规则（如需要）
# 根据违规模式调整 scripts/claude_constitutional_enforcer.py

# 3. 系统健康检查
python scripts/claude_self_check.py

# 4. 生成月度合规报告
python -c "
print('📊 AQUA AI工具月度合规报告')
print('=' * 50)

# 统计月度数据
import os, glob
from datetime import datetime

current_month = datetime.now().strftime('%Y%m')
daily_reports = glob.glob(f'logs/daily_compliance_{current_month}*.md')

print(f'本月生成日报: {len(daily_reports)} 份')

# 检查系统组件
components = [
    'scripts/claude_constitutional_enforcer.py',
    'scripts/claude_operation_template.py', 
    'scripts/compliance_checker.py',
    'scripts/claude_self_check.py'
]

print('\\n系统组件状态:')
for component in components:
    if os.path.exists(component):
        print(f'  ✅ {component}')
    else:
        print(f'  ❌ {component} 缺失')

print('\\n✅ 月度维护完成')
"
```

#### 🔧 系统优化建议

**基于违规模式的规则调整**：
```python
# 如果发现某类违规频繁出现，可以在宪法执行器中加强检查
# 编辑 scripts/claude_constitutional_enforcer.py

def _check_frequent_violations(self, operation_details):
    """检查频繁违规模式"""
    
    # 根据月度分析结果添加特定检查
    if "test文件在根目录" in self.frequent_violations:
        # 加强测试文件检查
        pass
    
    if "缺少合规声明" in self.frequent_violations:
        # 加强合规声明检查  
        pass
```

**性能优化**：
```bash
# 如果日志文件过大，启用日志轮转
python -c "
import os
from pathlib import Path

log_files = ['constitutional_enforcement.jsonl', 'approval_requests.jsonl']
for log_file in log_files:
    log_path = Path(f'logs/{log_file}')
    if log_path.exists() and log_path.stat().st_size > 10 * 1024 * 1024:  # 10MB
        # 备份并重新开始
        backup_path = log_path.with_suffix(f'.{os.getpid()}.bak')
        log_path.rename(backup_path)
        log_path.touch()
        print(f'✅ {log_file} 已轮转备份')
"
```

#### 📈 持续改进机制

**收集用户反馈**：
- 记录审批决策的准确性
- 统计误报和漏报情况  
- 优化审批请求的信息完整性

**系统升级**：
- 定期检查新的违规模式
- 根据项目发展调整规则
- 改进监控界面和报告格式

**培训和文档**：
- 更新操作指南
- 分享最佳实践案例
- 建立FAQ知识库

#### 🎯 维护效果指标

**关键指标监控**：
- **违规率** < 5% (每周)
- **审批响应时间** < 2小时
- **误报率** < 10%
- **系统可用性** > 99%

**成功标准**：
- AI工具完全按规范操作
- 审批流程高效顺畅
- 项目结构始终整洁
- 开发效率持续提升

通过这套日常维护体系，您可以确保AI工具宪法执行系统长期稳定运行，持续为项目提供高质量的合规保障。

### 🎯 实际运行示例

#### CLI交互选择环境示例
```bash
$ python scripts/env_init.py --check-only

🎯 请选择AQUA环境类型：
  1. dev - 开发环境 - 适合日常开发和测试
  2. test - 测试环境 - 适合集成测试和验证  
  3. prod - 生产环境 - 生产级配置，操作需谨慎
请输入环境编号 (1-3) [1]: 2

# ... 环境初始化过程 ...

🎯 AQUA环境初始化结果摘要
┏━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ 配置项            ┃ 当前值                                         ┃  状态   ┃
┡━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ 🌍 当前环境       │ test                                           │   ✅    │
│ 📱 应用名称       │ AQUA                                           │   ✅    │
│ 🔖 应用版本       │ 1.0                                            │   ✅    │
│ 🗄️ 数据库路径      │ data/aqua_test.duckdb                          │   ✅    │
│ 📁 数据目录       │ /Users/<USER>/Documents/Data/FromC2C            │   ✅    │
│ 📦 批处理大小     │ 5000                                           │   ✅    │
│ ⚙️ 执行模式        │ 🔍 检测模式 (仅检查)                           │    ℹ️    │
│ 🔧 环境变量       │ export AQUA_ENV=test                           │   💡    │
└───────────────────┴────────────────────────────────────────────────┴─────────┘

🎯 执行结果与下一步
✅ 环境检测完成

🚀 下一步建议:
• 设置环境变量: export AQUA_ENV=test
• 启动后端服务: ./start_backend.sh
• 启动前端服务: ./start_frontend.sh
• 查看配置指南: docs/CONFIGURATION_GUIDE.md
```

#### 非交互模式示例（CI/CD适用）
```bash
# 直接指定环境，适合CI/CD流水线
$ python scripts/env_init.py --env test --non-interactive --check-only

# 输出会跳过交互选择，直接使用指定环境
🔍 检测到环境: test (来源: 命令行参数)
# ... 执行过程和摘要显示 ...
```