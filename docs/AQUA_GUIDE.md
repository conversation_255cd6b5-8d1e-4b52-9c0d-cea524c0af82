# AQUA_GUIDE.md  （唯一权威文档体系）

---

# 目录

- [GUIDE-01 项目介绍](#guide-01-项目介绍)
  - [1.8 权威路线图与任务清单](#18-权威路线图与任务清单)
- [GUIDE-02 业务场景](#guide-02-业务场景)
- [GUIDE-03 目录结构](#guide-03-目录结构)
- [GUIDE-04 技术栈](#guide-04-技术栈)
- [GUIDE-05 功能模块与API](#guide-05-功能模块与api)
- [GUIDE-06 数据字典（Data Dictionary）](#guide-06-数据字典data-dictionary)
- [GUIDE-07 规则与规范（Rules & Standards）](#guide-07-规则与规范rules--standards)
- [GUIDE-08 变更历史（Change Log）](#guide-08-变更历史change-log)
- [GUIDE-09 FAQ与知识库（FAQ & Knowledge Base）](#guide-09-faq与知识库faq--knowledge-base)
- [GUIDE-10 前端MVP开发与能力清单/契约分阶段推进规则](#guide-10-前端mvp开发与能力清单契约分阶段推进规则)
- [GUIDE-11 变更与版本管理策略（2024极简个人敏捷版）](#guide-11-变更与版本管理策略2024极简个人敏捷版)
- [COMMIT-STANDARD 提交消息规范（2024极简追溯版）](#commit-standard-提交消息规范2024极简追溯版)

---

# **[GUIDE-01] 项目介绍**

---

## **1.1 项目背景与愿景**
AQUA 致力于为个人量化开发者和专业投资者提供高效、可扩展、易用的期货与A股数据平台。支持本地数据仓库、策略回测、模拟交易、AI辅助洞察等核心功能。

- 来源：docs/old/README.md、project_intro.md

**愿景**：成为个人量化开发者和专业投资者信赖的一站式期货与A股数据处理、策略研究与交易模拟平台。

- 来源：01_PRD.md

### 1.2 项目定位与核心理念
AQUA 项目以"配置驱动、规则先行、自动化、跨平台"为核心理念，致力于降低量化研究与实盘模拟的技术门槛。

- 配置驱动：所有参数、路径、环境变量以config/settings.toml为唯一事实来源。
- 规则先行：所有开发、测试、变更、配置、接口、mock、任务流等，必须对齐权威文档和.rules规则，冲突时以权威文档为准。
- 自动化：所有依赖、配置、环境、数据库结构、测试等初始化和校验，必须通过scripts/env_init.py和启动脚本自动完成。
- 跨平台：所有工具、脚本、依赖、配置、路径、换行符等，兼容Win11/OSX。
- 前后端一体化：接口、字段、类型、枚举等严格对齐，接口契约唯一来源为02_FUNCTIONS.md。
- MVP演进：以最小可行产品为起点，分阶段推进，所有变更、任务、接口、mock、测试等如有调整，必须同步更新logs/dev_log.md和Dev_Tasks.md。

- 来源：00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md

### 1.3 行业痛点与AQUA解决方案
- 数据采集难、清洗繁琐、回测与实盘割裂、自动化与合规性不足。
- AQUA 方案：本地数据仓库、自动化数据处理、策略回测、模拟交易、AI辅助洞察，全部一体化、配置驱动、自动化。

- 来源：project_intro.md

### 1.4 主要目标与范围
- 实现多源数据的高效采集、清洗、复权与本地持久化存储。
- 提供灵活的策略构建、回测分析与绩效评估工具。
- 支持仿真交易与风险管理，助力策略实盘落地。
- 集成AI能力，辅助数据洞察、异常检测与策略优化。
- 降低部署与维护门槛，支持本地开发与未来云端扩展。

- 来源：project_intro.md、01_PRD.md

### 1.5 MVP阶段范围
- 数据导入与浏览（CSV→DuckDB）
- 基础策略回测与结果可视化
- 模拟交易与资金管理（初步）
- AI辅助交互（自然语言数据查询、规则校验）
- 前后端一体化、自动化脚本、配置驱动

- 来源：project_intro.md、01_PRD.md

### 1.6 目标用户与典型场景
- 目标用户：个人量化开发者、策略研究员、专业投资者
- 典型场景：
  - 本地高质量数据仓库搭建与管理
  - 快速策略开发、回测与优化
  - 仿真交易与资金管理演练
  - AI辅助的数据洞察与策略建议

- 来源：project_intro.md、01_PRD.md

### 1.7 主要特性概览
- 本地数据仓库（DuckDB，支持A股/期货多格式导入）
- **FromC2C专用CSV导入**：支持期货主力合约K线数据的高效批量导入（✅ 已完成）
- **智能环境选择机制**：支持DEV/TEST/PROD三环境配置驱动，用户友好的交互式选择（✅ 已完成）
- **中国网络环境优化**：专业解决PyPI镜像源和SSL证书问题，智能切换最佳镜像源（✅ 已完成）
- 自动化数据清洗、复权、主力合约判别
- 策略回测与绩效分析（收益率、回撤、夏普等）
- 模拟交易与资金管理
- AI驱动的数据查询、异常检测与报告生成
- 前后端分离，配置驱动，自动化脚本一键初始化
- 严格对齐权威文档与规则，支持自动化校验与日志追溯
- **Rich增强界面**：美观的进度条、状态显示和结果报告（✅ 已完成）
- **跨平台启动保障**：Windows/macOS/Linux统一兼容，新环境零配置启动（✅ 已完成）

- 来源：README.md、project_intro.md、01_PRD.md

### 1.8 权威路线图与任务清单
- **战略规划**: [AQUA_ROADMAP.md](./AQUA_ROADMAP.md) 是项目发展的唯一、权威的战略规划文档。
- **战术执行**: 具体的开发任务在 `docs/tasks/` 目录下按EPIC进行分解和追踪。

#### **EPIC 2 V2.0: 数据采集MVP-CLI重新设计** ✅ **设计完成** (2025-07-29)
- **🎯 项目目标**: 针对个人量化开发者，重新设计跨平台(Windows+macOS)数据采集MVP-CLI系统
- **💎 核心特色**: TUSHARE Pro深度集成，2100积分预算精确管理，个人开发者资源友好
- **📊 完成状态**:
  - Phase 1: 全量备份 ✅ 已完成 (2025-07-28)
  - Phase 2: 文档统一化+代码清理 ✅ 已完成 (2025-07-28) 
  - Phase 3: MVP设计规划 ✅ 已完成 (2025-07-29)
    - ✅ Stage 1: 需求确定阶段 - 个人开发者特征分析+跨平台需求
    - ✅ Stage 2: 架构规划阶段 - 7层系统架构+现有架构85%复用分析
    - ✅ Stage 3: 深化设计阶段 - 个人开发者优化+错误处理+监控界面+版本兼容
    - ✅ Stage 4: 原子化任务拆解 - 32个任务6个模块，个人开发者友好优化
    - ✅ Stage 5: TDD开发计划 - 实际数据优先测试策略
    - ✅ Stage 6: 交付验收 - 三级验收标准+15个里程碑
  - **📋 详细文档**: [MVP_Data_Collection_CLI_Design.md](./tasks/MVP_Data_Collection_CLI_Design.md) (1540行完整设计方案)
  - **🚀 下一步骤**: 等待用户确认设计方案，开始32个原子化任务的具体实施

#### **核心架构升级**
- **V4.0统一业务表架构**: 替代原有分层架构，详见 [V4_UNIFIED_ARCHITECTURE.md](./architecture/V4_UNIFIED_ARCHITECTURE.md)
- **个人开发者优化**: 内存峰值≤1.5GB，启动时间≤2秒，跨平台性能差异≤5%
- **TUSHARE Pro集成**: 2100积分预算管理，积分使用效率≥95%，智能降级策略
- **跨平台一致性**: Windows/macOS 100%功能一致，完整的错误处理和监控可视化

#### **历史参考**
- **V1.0任务**: [Dev_Tasks_EPIC2.md](./tasks/Dev_Tasks_EPIC2.md) - 原始EPIC2任务清单
- **V2.0重构**: [Dev_Tasks_EPIC2_V2.md](./tasks/Dev_Tasks_EPIC2_V2.md) - 基于V4.0架构的完整重构计划
- **归档文档**: 旧的规划和任务文档已归档至 `docs/old/` 目录

---

# **[GUIDE-02] 业务场景**

---

## **2.1 典型应用场景**
- 本地高质量期货与A股数据采集、清洗、复权与存储
- 策略开发、回测、绩效分析与可视化
- 模拟交易与资金管理
- AI辅助数据洞察、异常检测、策略优化
- 个人开发者一站式量化研究与自动化任务流
- 行业/宏观数据集成、新闻爬虫、因子管理、策略优化等扩展

- 来源：01_PRD.md、project_intro.md

### 2.2 目标用户与核心价值
- 目标用户：
  - 个人量化开发者：追求高效、自动化数据处理流程，需要稳定、高质量的本地数据源进行策略开发与回测。
  - 策略研究员：需要便捷的工具进行策略构建、因子分析、回测性能评估。
  - 专业投资者：希望通过系统化工具，辅助其进行市场分析、交易决策和风险控制。
- 核心价值：
  - 数据驱动决策：高质量数据支撑投资分析。
  - 效率提升：自动化流程缩短策略验证周期。
  - 智能辅助：AI赋能深度洞察与策略优化。
  - 本地化与私密性：数据本地存储，保障隐私。
  - 定制化与灵活性：模块化设计，开放接口。

- 来源：01_PRD.md

### 2.3 主要功能需求（场景映射）
- 数据仓库：多源数据导入、清洗、复权、主力合约判别、数据库浏览与管理、特色数据（行业、宏观、新闻）
- 策略抽屉：策略管理、参数配置、因子管理（A股/期货）
- 回测车间：回测任务管理、绩效报表、收益曲线、交易明细
- 模拟交易：交易计划管理、实时模拟、绩效报告
- AI驱动洞察：自然语言查询、异常检测、智能报告、策略优化建议
- 前端MVP：主页导航、数据中心、回测结果、AI交互视图

- 来源：01_PRD.md、00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md

### 2.4 业务流程与系统边界
- 数据流：CSV/外部数据→清洗/复权/主力判别→DuckDB存储→API服务→前端展示/分析/回测/AI
- 业务边界：本地部署为主，未来支持云端/容器化，所有配置、依赖、数据路径集中管理
- 任务流、变更、mock、接口、测试等均需结构化、可追溯，所有变更同步logs/dev_log.md和docs/tasks/Dev_Tasks.md

- 来源：00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md

---

## [GUIDE-03] 目录结构

### **3.1 顶层目录结构总览**

```text
AQUA_V1.2/
├── config/                 # 配置文件目录（全局配置、环境变量、多环境配置）
│   └── env/                # 多环境配置子目录（如dev、prod等）
├── data/                   # 数据存储目录（DuckDB文件、原始CSV、备份等）
│   ├── backup/             # 数据库及重要数据备份
│   ├── processed/          # 处理后数据（如清洗、复权结果）
│   └── raw/                # 原始数据（如CSV、外部采集文件）
├── docs/                   # 项目文档（蓝图、PRD、API、任务、数据字典等）
│   ├── ai_prompts/         # AI协作提示词库
│   ├── old/                # 历史归档文档，仅作参考
│   └── tasks/              # 任务流唯一登记源
├── frontend/               # 前端主目录（Vue3+TS）
│   ├── public/             # 前端静态资源
│   ├── src/                # 前端源代码（模块、组件、API、状态管理等）
│   │   ├── api/            # 后端API接口封装
│   │   ├── assets/         # 静态资源（图片、样式等）
│   │   ├── components/     # 通用UI组件
│   │   ├── modules/        # 业务页面模块
│   │   ├── router/         # 路由配置
│   │   └── stores/         # 状态管理（Pinia）
│   └── tests/              # 前端测试与mock数据
│       ├── mock/           # 前端mock/测试数据，结构与后端接口严格对齐
│       └── unit/           # 前端单元测试
├── logs/                   # 日志目录（结构化变更日志、测试报告、运行日志）
├── scripts/                # 自动化脚本（环境初始化、备份、恢复、监控、Git钩子等）
│   └── git_hooks/          # Git钩子脚本（如commit message校验）
├── src/                    # 后端主目录（核心、模块、接口、工具、定时任务、入口）
│   ├── core/               # 框架核心、全局服务、依赖注入
│   ├── interfaces/         # 公共接口与数据结构定义
│   ├── jobs/               # 定时任务、调度脚本
│   ├── modules/            # 业务功能模块
│   └── utils/              # 通用工具（logger、exceptions、time_utils等）
├── tests/                  # 后端测试用例
│   ├── e2e/                # 端到端测试
│   ├── frontend/           # 前端相关测试
│   │   ├── e2e/            # 前端端到端测试
│   │   ├── integration/    # 前端集成测试
│   │   ├── mock/           # 前端mock数据
│   │   └── unit/           # 前端单元测试
│   ├── integration/        # 后端集成测试
│   └── unit/               # 后端单元测试
├── chating.md              # AI/协作相关记录
├── eslint.config.mjs       # 前端ESLint配置
├── main.py                 # 后端主入口（如有）
├── package.json            # 前端依赖清单
├── pnpm-lock.yaml          # 前端依赖锁定文件
├── pnpm-workspace.yaml     # pnpm多包管理配置
├── pytest.ini              # 后端pytest配置
├── README.md               # 项目说明文档（快速参考，权威源为AQUA_GUIDE.md）
├── requirements.in         # Python依赖源文件
├── requirements.txt        # Python依赖清单
├── start_backend.bat       # 后端启动脚本（Windows）
├── start_backend.sh        # 后端启动脚本（Linux/Mac）
├── start_frontend.bat      # 前端启动脚本（Windows）
├── start_frontend.sh       # 前端启动脚本（Linux/Mac）
```

> 任何目录/文件结构变更，必须同步更新本文件、README、蓝图、任务流和变更日志，形成闭环。所有命名、用途、结构以本分区为唯一事实源。

### 3.2 各目录用途与规范

- **src/**：后端主目录，包含核心框架、业务模块、接口定义、工具函数、定时任务和服务入口。
- **frontend/**：前端主目录，包含业务页面、通用组件、状态管理、API封装、路由、静态资源、mock数据和构建产物。
- **config/**：所有配置文件，settings.toml为唯一事实源，.env.example为环境变量模板，env/为多环境配置。
- **data/**：本地数据存储，raw为原始数据，processed为清洗/复权结果，backup为数据库及重要数据备份。
- **scripts/**：自动化脚本，支持环境初始化、部署、监控、备份、恢复、Git钩子等，所有脚本均需中文注释和异常处理。
- **docs/**：权威文档目录，包含蓝图、PRD、API、数据字典、任务流、AI提示词、FAQ等，所有变更需同步更新。
- **logs/**：日志目录，dev_log.md为结构化变更日志，test_log.md为测试报告，app_{YYYYMMDD}.log为运行日志。
- **tests/**：后端测试用例，unit为单元测试，integration为集成测试，e2e为端到端测试。
- **requirements.txt/.in**：Python依赖清单及源文件，依赖管理统一用uv。
- **.nvmrc**：Node.js版本锁定，前端依赖管理强制用pnpm。
- **.gitignore**：Git忽略文件，敏感文件和本地环境不提交。
- **README.md**：项目说明文档，入口级简要说明。

- 来源：project_structure.md

### 3.3 命名规范与结构变更要求

- 目录、文件、变量、函数：全部采用小写snake_case，禁止拼音、缩写、歧义命名。
- 类名：PascalCase。
- 常量：UPPER_SNAKE_CASE。
- SQL表/字段：小写snake_case。
- 脚本、mock、测试、日志、文档等：命名清晰，含义明确，便于检索和自动化处理。
- 任何目录/文件结构变更，必须同步更新本文件、README、蓝图、任务流和变更日志。

- 来源：README.md、project_structure.md

### 3.4 配置驱动与唯一事实源

- 所有配置、数据路径、环境变量以config/settings.toml为唯一事实来源，禁止硬编码。
- .env.example仅为模板，实际敏感信息本地维护。
- 配置优先级：环境变量 > .env > settings.toml。
- 前后端、脚本、服务均通过配置文件读取参数，支持多环境自动切换。

- 来源：project_structure.md

### 3.5 目录初始化与自动化机制

- 推荐使用 `python scripts/env_init.py` 一键初始化环境和目录结构，自动检测/创建所有标准目录、虚拟环境、依赖、数据库等，确保环境一致性。
- 目录初始化脚本已集成于 scripts/env_init.py，首次环境初始化时自动创建标准目录结构。
- 任何结构变更需同步更新本文件、蓝图、任务流和变更日志，形成闭环。

- 来源：README.md、project_structure.md

---

## [GUIDE-04] 技术栈

### **4.1 后端技术栈**
- 语言与运行环境：Python 3.11及以上
- 主框架：FastAPI（高性能API服务，异步支持）
- 数据库：DuckDB（本地高性能OLAP数据库，支持SQL分析）
- 数据处理：Polars（高效DataFrame数据清洗、转换、分析）
- 依赖管理：uv（轻量级Python虚拟环境与依赖管理）、requirements.txt（依赖清单，锁定版本）
- 配置管理：config/settings.toml（唯一事实源，集中配置）、config/.env.example（环境变量模板，敏感信息本地覆盖）

- 来源：tech_stack.md、README.md、00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md

### 4.2 前端技术栈
- 主框架与语言：Vue 3、TypeScript、Vite
- 组件库与UI：Naive UI
- 状态管理：Pinia
- 路由与可视化：Vue Router、ECharts
- 依赖管理：pnpm（高效Node.js依赖管理，强制使用）、package.json（依赖清单，锁定版本）
- Node.js版本：.nvmrc（版本锁定，推荐22.16.0及以上）

- 来源：tech_stack.md、README.md、00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md

### 4.3 自动化与测试工具
- 后端测试：pytest（单元测试、集成测试，覆盖率≥80%）
- 前端测试：Jest/Vitest（单元测试、集成测试，mock数据结构与接口一致）
- 代码质量与格式化：pre-commit、ESLint、Prettier、Flake8、Black、MyPy
- 自动化脚本：scripts/env_init.py（环境与目录一键初始化）、scripts/deploy_*.sh/bat（部署自动化）、scripts/monitor_*.py/sh（服务监控）、scripts/backup_*.sh、restore_*.sh（备份与恢复）

- 来源：tech_stack.md、README.md

### 4.4 配置与环境管理
- 配置文件：config/settings.toml（全局配置，唯一事实源）、config/.env.example（环境变量模板，敏感信息本地维护）、config/env/（多环境配置子目录）
- 依赖锁定与升级：requirements.txt、package.json、.nvmrc，依赖变更需同步更新相关文件与日志
- 环境兼容性：支持Windows 11与macOS，强制UTF-8编码，无BOM，LF换行

- 来源：tech_stack.md、00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md

### 4.5 版本与兼容性要求
- Python：3.11及以上
- Node.js：22.16.0及以上（.nvmrc锁定）
- DuckDB：官方最新稳定版
- Polars：官方推荐版本，需与DuckDB兼容
- 前端依赖：严格锁定package.json，pnpm管理
- 自动化工具：pre-commit、uv、pnpm等需保持最新稳定

- 来源：tech_stack.md

### 4.6 依赖管理与自动化保障
- 后端依赖统一用uv管理，虚拟环境目录为.venv
- 前端依赖强制用pnpm，禁止npm/yarn
- 所有依赖、配置、环境变量均以config/settings.toml为唯一事实源，禁止硬编码
- 自动化脚本与Git Hooks保障依赖、格式、类型、敏感信息等校验，未通过禁止提交

- 来源：tech_stack.md、00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md

### 4.7 代码规范与自动化合规校验（pre-commit 框架）

#### 4.7.1 pre-commit 框架作用与合规意义
- pre-commit 是AQUA项目强制规范与自动化合规校验的核心工具，保障所有代码、配置、依赖、敏感信息等在提交前自动校验，未通过禁止提交。
- 通过本地自动化钩子，确保AI协作、人工开发、日志/任务同步、敏感信息防护等全流程闭环，适配中国网络环境。

#### 4.7.2 纯本地钩子模式的原因与优势
- 由于国内网络环境无法稳定访问GitHub，AQUA项目采用"纯本地钩子模式"，所有pre-commit钩子均为本地shell脚本，无外部依赖。
- 优势：
  - 首次初始化无需科学上网，完全本地可用。
  - 钩子内容可灵活扩展、定制，适配个人开发与Gitee场景。
  - 合规校验、格式化、敏感信息检测等全部闭环。

#### 4.7.3 配置结构
- `.pre-commit-config.yaml` 仅包含本地shell钩子：
  - `frontend-prettier-eslint`：前端Prettier/ESLint自动修复
  - `aqua-custom-precommit`：AQUA自定义合规校验（格式、敏感信息、日志/任务同步等）
- `scripts/git_hooks/pre-commit`：主钩子脚本，集成所有本地合规校验逻辑。

#### 4.7.4 本地合规校验流程与开发闭环
- 每次`git commit`前自动运行pre-commit钩子，执行：
  1. 前端代码格式化与Lint修复
  2. 敏感信息检测（如API密钥、密码等）
  3. 日志/任务同步校验（确保Dev_Tasks.md、dev_log.md实时更新）
  4. 其他自定义合规检查
- 所有校验未通过则阻断提交，开发者需修复后重试，确保开发流程安全、规范、可追溯。

#### 4.7.5 后续扩展建议
- 可根据项目演进，灵活扩展本地钩子内容，如：
  - 自动检查commit message规范与任务ID关联
  - 自动生成/同步API文档、mock数据、测试用例
  - 集成依赖漏洞扫描、代码安全扫描等
- 所有变更需同步更新`.pre-commit-config.yaml`、`scripts/git_hooks/pre-commit`、权威文档与日志。

---

# **[GUIDE-04.5] 环境管理与智能启动**

---

## **4.5.1 环境选择机制**

AQUA项目采用配置驱动的多环境管理，支持DEV（开发）、TEST（测试）、PROD（生产）三种环境，每个环境有独立的数据库、路径和配置参数。

### 环境概览

| 环境名称 | 用途说明 | 数据库路径 | 特点 |
|----------|----------|------------|------|
| DEV | 开发环境 | `~/Documents/Data/duckdb/AQUA/DataCenter_dev.duckdb` | 快速迭代，小数据集 |
| TEST | 测试环境 | `~/Documents/Data/duckdb/AQUA/DataCenter_test.duckdb` | 集成测试，接近生产配置 |
| PROD | 生产环境 | `/Users/<USER>/Documents/Data/duckdb/AQUA/DataCenter.duckdb` | 正式分析，完整数据集 |

### 启动方式

#### 1. 交互式环境选择
```bash
python start.py
```
系统将显示环境选择菜单，用户可交互式选择运行环境。

#### 2. 命令行指定环境
```bash
python start.py --env dev    # 开发环境
python start.py --env test   # 测试环境
python start.py --env prod   # 生产环境
```

#### 3. 带参数启动
```bash
python start.py --env dev --wait --monitor  # 开发环境+等待服务+监控
```

### 环境配置预览

选定环境后，系统会显示当前环境的配置预览：
- 数据库文件路径
- CSV数据源目录
- 备份目录位置
- 日志目录位置
- 缓存目录位置

## **4.5.2 智能双入口系统**

### 系统架构
```
start.py (智能路由器)
    ↓
environment_selector.py (环境选择器)
    ↓
env_init.py --startup-mode (环境初始化)
    ↓
service_manager.py (服务管理)
```

### 核心组件

| 组件 | 功能 | 位置 |
|------|------|------|
| start.py | 统一入口，智能路由 | 项目根目录 |
| environment_selector.py | 环境选择和配置预览 | scripts/ |
| service_manager.py | 服务启动和管理 | scripts/ |
| env_init.py | 环境初始化核心 | scripts/ |

### 特性优势
- **统一入口体验**：`python start.py` 一命令完成所有操作
- **环境感知配置**：自动适配不同环境的配置参数
- **跨平台兼容**：Windows/macOS/Linux统一支持
- **智能进程管理**：优雅启动和停止服务
- **模块化架构**：清晰的职责分离

---

# **[GUIDE-05] 功能模块与API**

---

## **5.1 功能模块分级与总览**

AQUA 系统采用分级、分模块设计，覆盖数据仓库、策略管理、回测分析、模拟交易、AI智能体等核心业务场景。所有功能严格对齐权威文档与API契约，便于前后端协作和自动化测试。

| 模块编号 | 模块名称     | 主要功能简述                 |
|----------|--------------|------------------------------|
| 0101     | 数据管理     | 数据导入、数据库浏览、清洗等 |
| 0201     | 策略抽屉     | 策略管理、因子管理           |
| 0301     | 回测车间     | 回测任务、绩效分析、明细     |
| 0401     | 模拟交易     | 交易计划、实时模拟、报告     |
| 0501     | AI智能体     | 智能查询、异常检测、报告     |

- 来源：functions_desc.md、02_FUNCTIONS.md

#### 5.1.1 分级功能树状结构
- 详见docs/old/02_FUNCTIONS.md，完整分级树已迁移至附录。

---

### 5.2 各模块详细说明

#### 5.2.1 数据仓库（data_warehouse）
- **数据导入**：支持CSV等多格式批量导入，自动目录扫描、文件名智能解析，字段校验、异常处理。
  - **FromC2C专用导入器** ✅ （**2025-07-20完成**）：
    - 期货主力合约K线数据专用导入（5分钟、15分钟、30分钟）
    - 数据源路径：`/Users/<USER>/Documents/Data/FromC2C`
    - 支持89个期货品种，覆盖2005-2025年历史数据
    - 自动数据清洗：NaN值处理、重复记录去除、数据类型转换
    - Rich增强界面：实时进度条、详细统计报表、错误信息展示
    - 命令行工具：`python scripts/fromC2C_import_cli.py --env dev --full`
    - **已成功导入713,944条记录，覆盖4,485+个期货合约**
  - **V4.0统一业务表架构** ✅ （**2025-07-28完成**）：
    - V4_UNIFIED_ARCHITECTURE.md架构文档建立
    - DATA_DICTIONARY.md V4.0版本确认为权威标准
    - 统一业务表设计：期货5表+股票5表+公共2表
    - 元数据驱动的多数据源透明化管理机制
    - Phase 2文档统一化+代码清理完成，为重新设计铺路
- 数据库浏览：直观浏览本地DuckDB所有表及数据内容，支持分页、结构展示。
- 数据清洗与处理：自动去重、复权（期货主力换月、A股除权）、主力合约判别、缺失值处理。
- 数据库管理：表结构、索引、权限管理（后台功能）。

#### 5.2.2 策略抽屉（strategy_drawer）
- 策略管理：创建、编辑、保存、加载、删除、分类管理交易策略。
- 因子管理：A股/期货因子库管理，支持参数配置、导入导出。

#### 5.2.3 回测车间（backtest_workshop）
- 回测任务管理：新建回测、参数配置、策略选择、任务进度管理。
- 回测结果分析：绩效报表（收益率、回撤、夏普等）、收益曲线、交易明细。

#### 5.2.4 模拟交易（simulator）
- 交易计划管理：创建、修改、查看模拟交易计划。
- 实时模拟与报告：订单执行、资金变动、绩效概览、交易明细。

#### 5.2.5 AI驱动洞察（ai_agent）
- 智能查询：自然语言查询数据和指标。
- 异常检测与预警：自动识别数据异常、市场异动、策略问题。
- 智能报告生成：自动生成市场分析或策略表现报告。
- 策略优化建议：AI辅助参数优化与规则建议。

- 来源：functions_desc.md、01_PRD.md

---

### 5.3 主要API接口一览

| 接口路径                | 方法 | 功能描述           | 归属模块   |
|-------------------------|------|--------------------|------------|
| /api/data/import        | POST | 数据文件批量导入   | 数据管理   |
| /api/data/tables        | GET  | 查询所有表结构     | 数据管理   |
| /api/backtest/performance | GET  | 回测绩效指标查询   | 回测车间   |
| /api/strategy           | POST/GET/PUT/DELETE | 策略管理   | 策略抽屉   |
| /api/ai/query           | POST | AI智能查询         | AI智能体   |

- 详细字段、类型、错误码等请参见docs/old/02_FUNCTIONS.md、api.md

#### 5.3.1 API接口定义示例

- [POST] /api/data/import
  - 功能：批量导入外部CSV等数据文件到本地数据库
  - 所属模块：数据管理
  - 请求参数：file（CSV文件）、data_type（可选）
  - 返回值：code、message、details
  - 示例：
    ```json
    {"code": 0, "message": "导入成功", "details": {"rows": 1000, "table": "stg_fromc2c_kline"}}
    ```
- [GET] /api/data/tables
  - 功能：查询本地数据库所有表及结构
  - 所属模块：数据管理
  - 返回值：code、tables（表结构列表）
  - 示例：
    ```json
    {"code": 0, "tables": [{"name": "fact_stock_kline", "columns": ["ts_code", "trade_date", ...]}]}
    ```
- 其他接口详见api.md、02_FUNCTIONS.md

- 来源：api.md、02_FUNCTIONS.md

---

### 5.4 关键测试点与质量要求
- 数据导入：文件格式校验、批量导入性能、异常处理、字段对齐。
- 数据库浏览：表结构展示、分页查询、权限控制。
- 策略管理：参数校验、保存/加载正确性、异常策略处理。
- 回测分析：指标准确性、边界数据、接口响应一致性。
- 模拟交易：订单执行流程、资金变动、报告生成。
- AI能力：自然语言解析准确率、异常检测召回率、报告内容完整性。
- 接口一致性：所有API字段、类型、枚举与数据字典、前后端契约严格对齐。
- 自动化测试：单元/集成/端到端测试覆盖率≥80%，mock数据结构与真实接口一致。

- 来源：functions_desc.md、api.md、01_PRD.md

---

### 5.5 附录：分级功能树与API契约快照
- 详见docs/old/02_FUNCTIONS.md、api.md，完整分级树、字段定义、接口示例、变更历史等已迁移至附录，便于AI/自动化解析与溯源。

---

## [GUIDE-06] 数据字典（Data Dictionary）

> 详见[DATA_DICTIONARY.md](./database/DATA_DICTIONARY.md)（唯一权威数据字典V4.0，采用统一业务表架构设计，所有表结构、字段、业务映射、API契约详见该文档）

### 6.1 数据表与字段总览

> **权威来源**: [DATA_DICTIONARY.md](./database/DATA_DICTIONARY.md) V4.0统一业务表架构版为所有核心数据表、字段、元数据、索引、业务规则的**唯一权威清单**，后续所有结构变更均以此为准。
> 
> **架构特点**: V4.0采用统一业务表架构，包含期货业务表(5个)、股票业务表(5个)、公共业务表(2个)，每个业务表都包含统一的元数据字段，支持多数据源透明化管理。
> 
> **相关文档**: 
> - 架构总览: [V4_UNIFIED_ARCHITECTURE.md](./architecture/V4_UNIFIED_ARCHITECTURE.md)
> - 重构计划: [Dev_Tasks_EPIC2_V2.md](./tasks/Dev_Tasks_EPIC2_V2.md)

### **6.2 主要数据表详细字段定义（字段名、类型、含义、约束、缺失值、示例）
- 6.2 主要数据表详细字段定义（字段名、类型、含义、约束、缺失值、示例）

- 6.3 API接口字段与数据结构（接口路径、字段、类型、说明、mock示例）
- 6.4 字段映射与标准（源-目标映射、配置驱动、settings.toml对齐说明）
- 6.5 变更与溯源机制（字段/表/接口变更流程、日志要求、追溯模板）

> 设计与内容完善后，将成为前后端、mock、测试、数据处理等所有数据相关开发的唯一事实源。所有变更须同步logs/dev_log.md和Dev_Tasks.md。

---

## [GUIDE-07] 规则与规范（Rules & Standards）

> 本分区为AQUA项目所有开发、测试、AI协作、自动化等规则的唯一权威来源，内容吸收自蓝图、PRD、实施手册、开发启动任务等权威文档。

### 7.1 核心理念与分层规则体系（来源：蓝图、PRD）
- 配置驱动、规则先行、自动化、跨平台、前后端一体化、MVP演进。
- 项目规则唯一来源于 .cursor/rules/，分层优先级：development > project_management > data > testing > deployment > mvp。

### 7.2 配置驱动与唯一事实源（来源：蓝图、实施手册、PRD）
- 所有参数、路径、环境变量、mock、任务、日志、数据字典、API文档等均以config/settings.toml为唯一事实源，敏感/本地覆盖用.env，提供.env.example模板。
- 配置读取优先级：环境变量 > .env > settings.toml。

### 7.3 自动化与环境依赖管理（来源：实施手册、开发启动任务）
- 后端依赖统一用uv，前端用pnpm，环境初始化、依赖校验、配置完整性、数据库结构自动初始化等全部通过scripts/env_init.py和启动脚本自动完成。
- Git Hooks强制校验依赖、格式、敏感信息、虚拟环境激活，未通过禁止提交。
- 自动化测试、mock、接口、数据等必须结构化、可追溯，测试覆盖率≥80%，mock数据结构与真实接口一致。

#### 7.3.1 环境初始化脚本使用方法

**推荐使用统一启动入口（已集成中国网络优化）：**
```bash
# 最简启动方式（推荐）
python start.py --env dev

# 交互式环境选择
python start.py

# 带参数启动
python start.py --env dev --wait --monitor
```

**传统方式（兼容保留）：**
```bash
# 完整环境初始化（推荐首次使用）
python scripts/env_init.py

# 仅检测环境，不做修复/安装（日常检查）
python scripts/env_init.py --check-only

# 环境特定初始化
AQUA_ENV=dev python scripts/env_init.py     # 开发环境
AQUA_ENV=test python scripts/env_init.py    # 测试环境
AQUA_ENV=prod python scripts/env_init.py --check-only  # 生产环境（建议仅检测）
```

**中国网络环境特别优化：**
```bash
# 测试网络优化效果
python test_china_network_fix.py

# 单独使用网络优化器
python scripts/china_network_optimizer.py

# 配置永久镜像源
python -c "from scripts.china_network_optimizer import ChinaNetworkOptimizer; ChinaNetworkOptimizer().setup_permanent_mirror('tsinghua')"
```

#### 7.3.2 项目启动脚本体系
所有启动脚本都依赖`env_init.py`进行环境校验：

**前端启动：**
```bash
# Linux/macOS
./start_frontend.sh

# Windows  
start_frontend.bat
```

**后端启动：**
```bash
# Linux/macOS
./start_backend.sh

# Windows
start_backend.bat
```

**启动脚本依赖关系：**
```
启动脚本 → env_init.py → 配置管理器组件 → settings.toml
```

- **start_frontend.sh/bat**: 校验环境 → 检查pnpm依赖 → 启动前端Vue服务
- **start_backend.sh/bat**: 校验环境 → 启动FastAPI服务(uvicorn)

### 7.4 任务流、日志与变更追溯（来源：蓝图、实施手册、开发启动任务）
- 所有开发任务、任务流、状态流、变更、分支、回滚，必须在Dev_Tasks.md结构化登记和同步更新。
- 日志条目必须引用任务ID，形成任务-日志-代码闭环。所有变更、mock、接口、测试等同步更新logs/dev_log.md。
- 任务状态流转（待开始→进行中→已完成→阻断）同步更新文档并记录说明。

### 7.5 代码、接口、mock、测试一致性（来源：蓝图、PRD、实施手册、开发启动任务）
- 前后端接口、字段、类型、枚举等严格对齐，接口契约唯一来源为02_FUNCTIONS.md。
- mock/测试数据结构与真实接口一致，所有变更需同步更新相关文档和日志。
- 自动化比对前端mock与后端API契约，发现不一致自动生成/提醒，确保mock数据结构永远一致。

### 7.6 AI协作与合规性（来源：开发启动任务、实施手册）
- 所有AI、CURSOR及自动化工具，生成/推理/编辑/提交前后，自动校验是否符合.rules及权威文档，发现不符应暂停并警告，等待人工确认。
- 任务分层（Epic-Feature-Task-Sub-task）与AI协作元数据标准化，AI生成内容需结构化自查，人工快速确认。
- AI协作日志自动归档，AI提示词库版本管理，AI代码审查三重保障（AI自查、人工审查、自动化校验）。

### 7.7 变更管理与知识闭环（来源：蓝图、实施手册、开发启动任务）
- 所有变更、任务、接口、mock、测试等如有调整，必须同步更新logs/dev_log.md和Dev_Tasks.md，保证全流程可追溯。
- 变更日志、任务流、知识库、FAQ自动联动，AI/脚本定期生成知识库草稿，开发者补充细节。

> 以上规则为AQUA项目开发、测试、AI协作、自动化等全流程的唯一权威标准，所有变更须同步更新本分区、logs/dev_log.md和Dev_Tasks.md。

---

## [GUIDE-08] 变更历史（Change Log）

> 本分区为AQUA项目所有开发、修复、优化、测试、配置、结构、接口、数据等变更的唯一权威溯源，内容结构化吸收自logs/dev_log.md。

### 8.1 变更登记原则
- 所有变更、任务、接口、mock、测试等如有调整，必须同步更新本分区、logs/dev_log.md和Dev_Tasks.md，保证全流程可追溯。
- 日志条目必须引用任务ID，形成任务-日志-代码闭环。
- 变更日志按日期倒序登记，支持自动生成，便于追溯和自动化。

### 8.2 日志模板
```json
{
  "timestamp": "2024-07-01T10:00:00+08:00",
  "type": "Feature / Fix / Refactor",
  "description": "简要说明本次变更内容",
  "affected_modules": ["src/modules/data_import.py", "docs/api.md"],
  "task_id": "010101-1",
  "verification_status": "Tested / Pending"
}
```

### 8.3 变更日志示例（精选）

```json
{
  "timestamp": "2025-07-20T07:00:00+08:00",
  "type": "Feature",
  "description": "FromC2C CSV导入系统完全重构完成，实现期货主力合约K线数据的高效批量导入。包含：1)FromC2C专用导入器；2)Rich增强界面；3)完整数据清洗；4)重复记录处理；5)数据质量验证。成功导入713,944条记录，覆盖4,485+个期货合约，时间跨度2005-2025年。",
  "affected_modules": [
    "src/data_import/fromC2C_csv_main_contract_importer.py",
    "src/data_import/mappers/data_dictionary_mapper.py", 
    "src/database/data_dictionary_schema.py",
    "src/database/connection_manager.py",
    "scripts/fromC2C_import_cli.py",
    "tests/unit/test_unified_csv_importer.py",
    "docs/AQUA_GUIDE.md",
    "docs/FAQ.md"
  ],
  "task_id": "fromC2C-csv-import-refactor-20250720",
  "verification_status": "Tested"
}
```

```json
{
  "timestamp": "2024-07-06T20:40:00+08:00",
  "type": "Refactor",
  "description": "AQUA_GUIDE.md全量结构化内容补全，分区递进吸纳docs/old及00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md全部核心内容，涵盖项目介绍、业务场景、目录结构、技术栈、功能模块与API、数据字典、规则与规范、变更历史、FAQ/知识库，全部表格/树状/模板化迁移，FAQ/知识库补全。",
  "affected_modules": ["docs/AQUA_GUIDE.md"],
  "verification_status": "Tested"
}
```

```json
{
  "timestamp": "2024-07-06T20:50:00+08:00",
  "type": "Refactor",
  "description": "将00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md移入docs/old目录，保持docs/下唯一权威文档整洁，所有蓝图内容已结构化吸纳至AQUA_GUIDE.md。",
  "affected_modules": ["docs/old/00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md"],
  "verification_status": "Tested"
}
```

```json
{
  "timestamp": "2024-07-05T10:00:00+08:00",
  "type": "Feature",
  "description": "完善scripts/env_init.py，实现自动虚拟环境、依赖、数据库初始化，TDD主流程闭环，异常分支合规。",
  "affected_modules": ["scripts/env_init.py", "tests/test_env_init.py", "config/settings.toml", "requirements.in", "requirements.txt"],
  "task_id": "Epic0-F01-T03",
  "verification_status": "Tested"
}
```

> 更多详细变更请参见logs/dev_log.md，所有变更均须同步登记，确保AQUA项目全流程可追溯。

---

## [GUIDE-09] FAQ与知识库（FAQ & Knowledge Base）

> 本分区为AQUA项目常见问题、经验总结、知识沉淀的唯一权威来源，内容结构化吸收自0302_REQUEST.md等权威文档。

### 9.1 FAQ/知识库建设原则
- 所有新问题、踩坑、优化经验，AI/脚本自动提取关键内容，归档到docs/faq.md或docs/knowledge_base.md，内容结构化（问题、场景、解决方案、相关任务ID/commit ID、日期）。
- FAQ/知识库与变更日志联动，变更日志（logs/dev_log.md）中如有"经验/FAQ"标签，自动同步到知识库。
- AI/脚本定期扫描日志和任务，自动生成知识库草稿，开发者补充细节。
- FAQ/知识库支持关键词检索、标签分类，结合AI问答接口，支持自然语言检索知识库内容。

### 9.2 FAQ/知识库自动化与AI协作
- AI协作日志自动归档，AI提示词库版本管理，AI代码审查三重保障（AI自查、人工审查、自动化校验）。
- 每次遇到新问题、踩坑、优化经验，自动归档到FAQ/知识库，AI辅助初稿，开发者补充。
- FAQ/知识库内容结构化，便于AI/脚本自动分析和复用。

### 9.3 FAQ/知识库内容示例（占位）
- 问题：如何一键初始化后端/前端开发环境？
  - 场景：首次克隆AQUA项目后，需快速搭建本地开发环境。
  - 解决方案：运行scripts/env_init.py自动完成虚拟环境、依赖、数据库初始化，前端用pnpm install，详见AQUA_GUIDE.md相关分区。
  - 相关任务ID：Epic0-F01-T03
  - 日期：2024-07-05

- 问题：如何保证前后端接口、mock、测试数据结构一致？
  - 场景：接口或数据结构变更后，需同步前端mock和测试用例。
  - 解决方案：开发脚本自动比对frontend/tests/mock/与后端API契约（02_FUNCTIONS.md），发现不一致自动生成/提醒，所有变更同步更新相关文档和日志。
  - 相关任务ID：Epic0-F01-T05
  - 日期：2024-07-06

- 问题：如何处理pnpm create vite的交互式安装？
  - 场景：在自动化脚本中执行 `pnpm create vite` 时，会因交互式提示而中断。
  - 解决方案：首先全局安装 `create-vite` (`pnpm add -g create-vite`)，如果遇到 `ERR_PNPM_NO_GLOBAL_BIN_DIR` 错误，先运行 `pnpm setup` 并 `source ~/.zshrc`（或对应shell的rc文件）刷新环境变量，然后再执行 `pnpm add -g create-vite`。最后，使用 `pnpm exec create-vite frontend --template vue-ts` 进行非交互式创建。
  - 相关任务ID：无 (项目初始化阶段)
  - 日期：2024-07-06

- 问题：如何解决前端Vue版本冲突问题？
  - 场景：`pnpm install` 可能错误安装旧版本Vue（如vue@1.x），导致Vue 3项目无法正常运行。
  - 解决方案：在 `frontend` 目录下，首先彻底清除旧的依赖缓存 (`rm -rf node_modules pnpm-lock.yaml`)。然后，在 `frontend/package.json` 中明确指定Vue 3版本（例如 `vue: "^3.5.17"`）到 `dependencies` 和 `devDependencies`，并移除任何冲突的旧依赖（如 `vue-typescript`）。最后重新运行 `pnpm install`。
  - 相关任务ID：无 (项目初始化阶段)
  - 日期：2024-07-06

- 问题：如何解决前端tsconfig配置错误导致的项目启动失败？
  - 场景：`tsconfig.app.json` 报错 `failed to resolve "extends":"@vue/tsconfig/tsconfig.dom.json"` 或其他 TypeScript 配置问题。
  - 解决方案：确保 `frontend/tsconfig.json` 包含完整的 `compilerOptions` 配置，包括 `lib`, `types`, `include` 等，并指向正确的路径。简化 `frontend/tsconfig.app.json` 为 `extends: "./tsconfig.json"`。在修改 `tsconfig` 后，执行 `rm -rf node_modules pnpm-lock.yaml` 并重新 `pnpm install` 以确保变更生效。
  - 相关任务ID：无 (项目初始化阶段)
  - 日期：2024-07-06

- 问题：如何处理前端 `package.json` 缺少 `scripts` 导致的项目无法运行或测试？
  - 场景：新创建的Vite项目可能缺少 `dev` 或 `test` 等常用 `scripts`。
  - 解决方案：手动编辑 `frontend/package.json`，在 `"scripts"` 字段中添加或修正缺失的命令，例如 `"dev": "vite"` 和 `"test": "vitest"`。
  - 相关任务ID：无 (项目初始化阶段)
  - 日期：2024-07-06

- 问题：前端项目启动后显示空白页面？
  - 场景：`pnpm run dev` 启动服务后，浏览器访问页面但内容为空白。
  - 解决方案：首先检查 `frontend/src/App.vue` 是否包含了 `<router-view />` 组件，以确保路由组件能够被正确渲染。此外，检查浏览器控制台是否有错误信息，并确保 `frontend/src/main.ts` 中正确注册了 `Vue Router` 和 `Pinia`。
  - 相关任务ID：无 (项目初始化阶段)
  - 日期：2024-07-06

> FAQ/知识库内容将持续自动归档和完善，所有开发、测试、AI协作、变更等经验均须沉淀于本分区，便于后续检索、复用和AI问答。

---

## [GUIDE-10] 前端MVP开发与能力清单/契约分阶段推进规则

1. 前端MVP开发应优先完成主入口、主页面、全局路由与导航等基础框架，严格对齐AQUA_GUIDE.md、蓝图、PRD、任务路线图等权威文档，确保产品方向和用户体验一致。
2. 能力清单与API契约（api.md）采用分阶段补全策略：初期先搭建能力清单和接口框架，后续根据后端MVP进度逐步细化字段、类型、错误码等。
3. 前端API封装、mock数据、类型定义、测试用例等，均以api.md为唯一事实源，禁止自创接口、字段或mock结构。
4. 每次接口或能力清单变更，必须同步更新api.md、02_FUNCTIONS.md、Dev_Tasks.md和logs/dev_log.md，确保前后端、mock、测试、任务流全链路一致、可追溯。
5. 推荐开发流程：主框架搭建→能力清单/契约框架建立→功能模块逐步集成→自动化校验与日志同步，形成最小可用闭环，便于敏捷迭代和个人开发节奏。

---

## [GUIDE-11] 变更与版本管理策略（2024极简个人敏捷版）

### 11.1 极简分支策略（One-Feature-One-Branch）
- 主分支：AQUA_V1.2（始终保持可用，仅合并已测试通过的变更）
- 特性/修复分支：feature/{任务ID}-{简要描述}，如 feature/epic0-f05-t01-tdd-refactor
- 每个原子任务/需求/修复都新建分支，开发/测试/重构/文档同步在该分支完成
- 分支名必须含任务ID，便于自动化追溯
- 合并前必须通过TDD测试，合并后立即删除分支

### 11.2 极简提交规范
- commit message: [type]: 变更简述 #任务ID
  - 例：feat: 新增API契约校验 #epic0-f05-t01
- 类型：feat/fix/refactor/docs/test/chore
- 每次提交必须带任务ID，便于日志/任务/分支自动关联

### 11.3 自动化合规闭环（pre-commit极简增强）
- 分支名校验：提交前自动检测分支名是否规范（feature/…），不规范警告
- commit message校验：自动检测是否含任务ID，不含则警告
- TDD强制：提交前自动运行相关测试（pytest/vitest），未通过阻断提交
- 日志/任务同步：变更后自动提醒同步更新logs/dev_log.md和Dev_Tasks.md

### 11.4 变更与回溯极简流程
1. 新任务/需求/修复 → 新建分支（feature/…）
2. 开发/测试/重构/文档 → 均在该分支完成，TDD驱动
3. 提交 → 每次commit带任务ID，自动校验
4. 合并 → 测试通过后合并到AQUA_V1.2，合并后删除分支
5. 日志/任务同步 → 变更后立即更新logs/dev_log.md和Dev_Tasks.md

### 11.5 可执行落地步骤
- 在README.md或docs/补充分支命名规范与管理流程
- 一键分支/提交命令模板（Win+OS X通用）
- pre-commit极简增强（建议先警告，后续可阻断）
- TDD与日志/任务同步

> 本策略为AQUA项目个人敏捷开发、TDD、AI协作、自动化合规的唯一权威标准，所有开发、测试、变更、配置、接口、mock、任务流等，必须严格遵循本分区及权威文档、规则体系，确保项目高效、规范、可追溯推进。

---

## [COMMIT-STANDARD] 提交消息规范（2024极简追溯版）

### 1. 强制格式
```
<type>: <summary> #T_ID_<task_id>

<detail>
```
- 第一行为类型、简要描述和任务ID，类型与冒号后有空格，任务ID前有#T_ID_前缀。
- 第二行为空行。
- 第三行起为详细描述（可选，建议说明变更动机、影响范围、验证方式等）。

### 2. 类型枚举
- feat：新功能
- fix：缺陷修复
- docs：文档变更
- style：代码格式（不影响功能）
- refactor：重构（非功能性优化）
- test：测试相关
- chore：构建/依赖/脚本/杂项

### 3. 模板示例
```
feat: 实现数据导入API #T_ID_epic0-f05-t01

实现POST /api/data/import接口，支持CSV批量导入，字段校验与异常处理，单元测试覆盖。
```
fix: 修复回测结果接口分页bug #T_ID_epic0-f05-t02

修正分页参数传递错误，补充异常测试用例，已在本地验证通过。
```

### 4. 可追溯性说明
- 每次提交必须带唯一任务ID（#T_ID_...），与Dev_Tasks.md、logs/dev_log.md、分支名一一对应，便于自动化追溯和知识闭环。
- commit message将被自动化脚本/AI工具解析，生成变更日志、任务流、知识库等。
- AI/自动化生成的提交，需在详细描述中注明prompt_id、ai_generated等元数据。

> 本规范为AQUA项目唯一提交消息标准，所有提交、变更、日志、任务等均需严格遵循，确保全链路可追溯、自动化合规与AI协作友好。

---

> 本文件为AQUA项目唯一权威事实源，所有开发、测试、配置、接口、mock、任务流、规则、变更等均以本文件为准。所有引用、自动化、AI协作、校验、生成等均以本文件为唯一锚点。 