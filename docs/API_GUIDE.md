# AQUA API 接口文档

## 概述

AQUA期货与A股数据平台提供RESTful API接口，支持数据查询、CSV导入、性能优化等功能。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1.0.0
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证方式

目前API暂未启用认证机制，所有接口均可直接访问。

## 通用响应格式

所有API接口均返回统一格式的JSON响应：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2023-01-01T00:00:00Z",
  "request_id": "unique-request-id"
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| success | boolean | 请求是否成功 |
| data | object/array | 返回的数据内容 |
| message | string | 响应消息 |
| timestamp | string | 响应时间戳 |
| request_id | string | 请求唯一标识 |

## 错误处理

当API请求失败时，返回错误信息：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2023-01-01T00:00:00Z"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|-----------|------|
| VALIDATION_ERROR | 422 | 参数验证失败 |
| NOT_FOUND | 404 | 资源不存在 |
| DATABASE_ERROR | 500 | 数据库错误 |
| CACHE_ERROR | 503 | 缓存服务错误 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |

## 核心API接口

### 1. 健康检查

检查API服务状态。

**请求**
```http
GET /health
```

**响应**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "**********",
    "timestamp": "2023-01-01T00:00:00Z"
  }
}
```

### 2. 数据管理API

#### 2.1 获取数据表列表

获取数据库中所有表的信息。

**请求**
```http
GET /api/data/tables?environment=dev
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| environment | string | 否 | 环境标识，默认为dev |

**响应**
```json
{
  "success": true,
  "data": [
    {
      "name": "stock_data",
      "record_count": 1000,
      "columns": [
        {
          "column_name": "id",
          "column_type": "INTEGER",
          "null": "NO",
          "key": "PRI",
          "default": null
        }
      ]
    }
  ],
  "total_count": 1,
  "message": "获取表列表成功"
}
```

#### 2.2 获取表数据

获取指定表的数据内容。

**请求**
```http
GET /api/data/tables/{table_name}?environment=dev&limit=10&offset=0&columns=id,name
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| table_name | string | 是 | 表名 |
| environment | string | 否 | 环境标识 |
| limit | integer | 否 | 返回记录数，默认10，最大1000 |
| offset | integer | 否 | 偏移量，默认0 |
| columns | string | 否 | 指定返回的列，逗号分隔 |

**响应**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "平安银行",
      "symbol": "000001"
    }
  ],
  "pagination": {
    "total": 1000,
    "limit": 10,
    "offset": 0,
    "has_next": true
  },
  "message": "获取表数据成功"
}
```

#### 2.3 获取表元数据

获取指定表的元数据信息。

**请求**
```http
GET /api/data/tables/{table_name}/meta?environment=dev
```

**响应**
```json
{
  "success": true,
  "data": {
    "table_name": "stock_data",
    "record_count": 1000,
    "column_count": 5,
    "columns": [
      {
        "column_name": "id",
        "column_type": "INTEGER",
        "null": "NO",
        "key": "PRI",
        "default": null
      }
    ]
  },
  "message": "获取表元数据成功"
}
```

#### 2.4 CSV文件导入 (重构版)

上传CSV文件并导入到数据库，支持智能业务表映射和数据标准化。

**请求**
```http
POST /api/data/import/csv?environment=development
Content-Type: multipart/form-data
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file | file | 是 | CSV文件 |
| environment | string | 否 | 环境标识(development/test/production) |
| table_name | string | 否 | 指定表名，不指定则自动映射 |
| mode | string | 否 | 导入模式(create/replace/append)，默认create |

**重构后增强功能**
- ✅ **智能业务表映射**: 自动识别期货/股票文件并映射到标准业务表
- ✅ **数据标准化**: 字段名称标准化，时间格式统一，合约代码提取
- ✅ **生产环境保护**: 生产环境需要三重确认，防止误操作
- ✅ **统一验证器**: 文件格式、数据合规性全面验证
- ✅ **错误恢复**: 详细错误信息和解决建议

**业务表映射规则**
```json
{
  "file_patterns": {
    "*_主力合约_15分钟*.csv": "fut_main_contract_kline_15min",
    "*_股票日K线*.csv": "stock_kline_daily", 
    "期货基础信息.csv": "fut_basic_info",
    "股票基础*.csv": "stock_basic_info"
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "table_name": "fut_main_contract_kline_15min",
    "records_imported": 1500,
    "is_business_table": true,
    "standardized_columns": [
      "contract_code", "trade_datetime", "open", "high", "low", "close"
    ],
    "warnings": [
      "发现3条重复数据已跳过"
    ],
    "duration_seconds": 2.35
  },
  "message": "CSV文件成功导入业务表"
}
```

**错误响应示例**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据合规性验证失败",
    "details": {
      "invalid_records": 5,
      "errors": [
        "第2行: open价格不能为负数(-100.0)",
        "第5行: contract_code格式无效(INVALID123)"
      ]
    }
  }
}
```

### 3. 性能优化API

#### 3.1 性能健康检查

检查性能相关服务状态。

**请求**
```http
GET /api/performance/health
```

**响应**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "cache_status": "online",
    "database_status": "connected",
    "timestamp": "2023-01-01T00:00:00Z"
  }
}
```

#### 3.2 获取股票数据

获取指定股票的历史数据。

**请求**
```http
GET /api/performance/stock/{symbol}?start_date=2023-01-01&end_date=2023-01-31&limit=100
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| symbol | string | 是 | 股票代码 |
| start_date | string | 否 | 开始日期，格式YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式YYYY-MM-DD |
| limit | integer | 否 | 返回记录数，默认100 |

**响应**
```json
{
  "success": true,
  "data": [
    {
      "symbol": "000001",
      "name": "平安银行",
      "date": "2023-01-01",
      "open": 10.50,
      "high": 10.60,
      "low": 10.40,
      "close": 10.55,
      "volume": 1000000
    }
  ],
  "from_cache": false,
  "message": "获取股票数据成功"
}
```

#### 3.3 获取期货数据

获取指定期货的历史数据。

**请求**
```http
GET /api/performance/futures/{symbol}?start_date=2023-01-01&limit=100
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| symbol | string | 是 | 期货代码 |
| start_date | string | 否 | 开始日期 |
| end_date | string | 否 | 结束日期 |
| limit | integer | 否 | 返回记录数 |

**响应**
```json
{
  "success": true,
  "data": [
    {
      "symbol": "IF2301",
      "name": "沪深300指数期货",
      "date": "2023-01-01",
      "open": 4000.0,
      "high": 4010.0,
      "low": 3990.0,
      "close": 4005.0,
      "volume": 50000
    }
  ],
  "from_cache": true,
  "message": "获取期货数据成功"
}
```

#### 3.4 获取市场概览

获取市场整体数据概览。

**请求**
```http
GET /api/performance/market/overview
```

**响应**
```json
{
  "success": true,
  "data": [
    {
      "market_type": "股票",
      "total_count": 5000,
      "active_count": 3000,
      "inactive_count": 2000,
      "activity_rate": 60.0
    }
  ],
  "from_cache": false,
  "message": "获取市场概览成功"
}
```

## 缓存机制

### 缓存策略

API采用多级缓存机制提升性能：

1. **L1缓存**: 内存缓存，快速访问
2. **L2缓存**: 磁盘缓存，持久化存储

### 缓存键规则

- 股票数据: `stock_data:{symbol}:{start_date}:{end_date}:{limit}`
- 期货数据: `futures_data:{symbol}:{start_date}:{end_date}:{limit}`
- 表列表: `tables:{environment}`
- 表数据: `table_data:{table_name}:{environment}:{limit}:{offset}`

### 缓存过期时间

- 股票/期货数据: 5分钟
- 表列表: 10分钟
- 表数据: 3分钟
- 市场概览: 1分钟

## 限流策略

为保证服务稳定性，API实施以下限流策略：

- 每IP每分钟最多100次请求
- 单次查询最多返回1000条记录
- 文件上传最大10MB

## 性能优化

### 查询优化建议

1. **使用分页**: 避免一次性获取大量数据
2. **指定列**: 只获取需要的字段
3. **合理使用缓存**: 相同查询会从缓存返回
4. **批量操作**: 多个相关请求可以合并

### 响应时间

- 缓存命中: < 50ms
- 数据库查询: < 500ms
- 大数据查询: < 2s

## 示例代码

### Python

```python
import requests
from pathlib import Path

# 获取股票数据
response = requests.get(
    'http://localhost:8000/api/performance/stock/000001',
    params={
        'start_date': '2023-01-01',
        'end_date': '2023-01-31',
        'limit': 100
    }
)

if response.status_code == 200:
    data = response.json()
    if data['success']:
        stock_data = data['data']
        print(f"获取到 {len(stock_data)} 条股票数据")
    else:
        print(f"请求失败: {data['error']['message']}")

# CSV文件导入示例 (重构版本)
def upload_csv_file(file_path, environment='development'):
    url = f'http://localhost:8000/api/data/import/csv?environment={environment}'
    
    with open(file_path, 'rb') as file:
        files = {'file': (Path(file_path).name, file, 'text/csv')}
        response = requests.post(url, files=files)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            data = result['data']
            print(f"导入成功: {data['table_name']} - {data['records_imported']}条记录")
            if data['is_business_table']:
                print(f"映射到业务表，标准化字段: {data['standardized_columns']}")
            if data['warnings']:
                print(f"警告: {data['warnings']}")
        else:
            print(f"导入失败: {result['error']['message']}")
            if 'details' in result['error']:
                for error in result['error']['details']['errors']:
                    print(f"  - {error}")
    else:
        print(f"请求失败: HTTP {response.status_code}")

# 使用示例
upload_csv_file('data/al_主力合约_15分钟数据.csv')
```

### JavaScript

```javascript
// 获取表列表
fetch('/api/data/tables?environment=dev')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('表列表:', data.data);
    } else {
      console.error('请求失败:', data.error.message);
    }
  })
  .catch(error => {
    console.error('网络错误:', error);
  });
```

### cURL

```bash
# 上传CSV文件 (重构版本，支持业务表映射)
curl -X POST \
  'http://localhost:8000/api/data/import/csv?environment=development' \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@al_主力合约_15分钟数据.csv'

# 上传并指定表名和模式
curl -X POST \
  'http://localhost:8000/api/data/import/csv?environment=development&table_name=custom_table&mode=replace' \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@data.csv'

# 获取期货数据
curl -X GET \
  'http://localhost:8000/api/performance/futures/IF2301?limit=50' \
  -H 'Accept: application/json'

# 获取表列表
curl -X GET \
  'http://localhost:8000/api/data/tables?environment=development' \
  -H 'Accept: application/json'
```

## 开发者工具

### API文档

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI JSON**: `http://localhost:8000/openapi.json`

### 调试工具

1. **健康检查**: `/health`
2. **性能监控**: `/api/performance/health`
3. **数据状态**: `/api/data/health`

## 更新日志

### v1.1.0 (2025-07-19) - CSV导入功能重构优化
- ✅ **CSV导入器重构**: 采用统一组件架构，消除代码重复
- ✅ **智能业务表映射**: 自动识别期货/股票文件并映射到标准业务表  
- ✅ **数据标准化增强**: 字段名称标准化，时间格式统一，合约代码自动提取
- ✅ **生产环境保护**: 新增三重确认机制，防止生产环境误操作
- ✅ **统一验证器**: 文件格式、数据合规性、业务规则全面验证
- ✅ **统一测试套件**: 消除测试代码重复，提供统一测试数据工厂
- ✅ **错误处理增强**: 详细错误信息和解决建议，支持断点续传

### v1.0.0 (2023-01-01)
- 初始版本发布
- 支持基础数据查询
- 实现缓存机制
- 添加CSV导入功能

## 开发模式最佳实践

### CSV导入功能使用指引

**开发环境快速上手**:
```bash
# 1. 环境准备
cd /Users/<USER>/Documents/AQUA/Dev/AQUA
python scripts/env_init.py

# 2. 数据库初始化
python -c "
from src.database.duckdb_init_check import DuckDBInitializer
result = DuckDBInitializer('development').initialize_database()
print(f'初始化完成: {result}')
"

# 3. 导入测试
python -c "
from src.data_import.csv_importer import CSVImporter
from pathlib import Path

with CSVImporter('development') as importer:
    result = importer.import_single_file(Path('data/your_file.csv'))
    print(f'导入: {result[\"table_name\"]} - {result[\"records_imported\"]}条')
"
```

**业务表映射验证**:
```python
from src.data_import.mappers.business_table_mapper import BusinessTableMapper

mapper = BusinessTableMapper()
table_name = mapper.map_file_to_table(Path('al_主力合约_15分钟数据.csv'))
print(f'映射结果: fut_main_contract_kline_15min')
```

## 技术支持

如有问题，请联系开发团队或在GitHub提交Issue。

**常见问题参考**: `/docs/FAQ.md`  
**任务清单**: `/docs/tasks/Dev_Tasks_EPIC2.md`

---

*文档最后更新时间: 2025-07-19*  
*重构优化版本: v1.1.0*