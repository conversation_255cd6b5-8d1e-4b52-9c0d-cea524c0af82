# Windows占位符目录问题修复指南

> **适用版本**: AQUA v2.0+  
> **问题描述**: Windows 11下运行`aqua.bat`后在项目根目录产生`{logs_root}`等错误目录  
> **修复状态**: ✅ 已修复 (2025-08-01)

---

## 🚨 问题现象

在Windows 11环境下运行`.\aqua.bat`启动AQUA服务后，在项目根目录（如`D:\AQUA\AQUA\`）下出现以下错误目录：

```
D:\AQUA\AQUA\{logs_root}\
D:\AQUA\AQUA\{datacenter_dir}\
D:\AQUA\AQUA\{cache_root}\
D:\AQUA\AQUA\{backup_root}\
```

这些目录名称包含未解析的占位符，表明路径解析系统存在问题。

## 🔍 问题根因

### 1. **配置系统冲突**
- AQUA项目存在两套配置系统：旧的`ConfigLoader`和新的`simple_config.py`
- `aqua.bat`启动的服务使用旧配置系统，在Windows下占位符解析不完整

### 2. **默认路径配置错误**
- Windows平台的占位符默认值使用了相对路径（如`"./logs"`）
- 应该使用Windows专用的绝对路径（如`"D:/Data/duckdb/AQUA/DataCenter/logs"`）

### 3. **占位符解析失败处理不当**
- 当占位符解析失败时，系统仍会尝试创建包含占位符的目录
- 缺少对未解析占位符的检测和阻断机制

## ✅ 解决方案

### 方案一：使用自动修复脚本（推荐）

**1. 运行修复脚本**
```powershell
# 在AQUA项目根目录下运行
python scripts/fix_windows_placeholder_dirs.py
```

**2. 按提示选择操作**
- `选项1`: 仅清理空的占位符目录（安全）
- `选项2`: 备份并清理所有占位符目录（推荐）
- `选项3`: 强制清理所有占位符目录（注意数据丢失）

**3. 预期输出**
```
🔧 AQUA Windows占位符目录修复工具
==================================================
📁 项目根目录: D:\AQUA\AQUA
🔍 正在检测占位符目录...
🚨 发现 3 个占位符目录/文件:
  📂 {logs_root} -> D:\AQUA\AQUA\{logs_root} (空)
  📂 {datacenter_dir} -> D:\AQUA\AQUA\{datacenter_dir} (包含5个文件)
  📂 {cache_root} -> D:\AQUA\AQUA\{cache_root} (空)

📊 清理结果:
  ✅ 已清理: 3 个
  ⚠️ 已跳过: 0 个
  ❌ 错误: 0 个

🎉 占位符目录修复完成!
```

### 方案二：手动清理

**1. 停止AQUA服务**
```powershell
# 关闭所有AQUA相关的命令窗口
# 或者按Ctrl+C停止服务
```

**2. 手动删除占位符目录**
```powershell
# 在项目根目录下执行
rmdir /s /q "{logs_root}" 2>nul
rmdir /s /q "{datacenter_dir}" 2>nul  
rmdir /s /q "{cache_root}" 2>nul
rmdir /s /q "{backup_root}" 2>nul
```

**3. 重新启动AQUA**
```powershell
.\aqua.bat
```

## 🛡️ 预防措施

### 1. **使用最新版本**
确保使用包含修复的AQUA版本（2025-08-01之后的提交）：

```powershell
git pull origin osx-to-windows-sync
```

### 2. **验证修复效果**
运行以下脚本验证占位符解析是否正常：

```powershell
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
platform_info = loader.get_platform_info()
print('平台信息:', platform_info['system'])

# 测试日志路径解析
log_config = loader.get_logging_config('dev')
log_path = loader.expand_cross_platform_path(log_config['file_path'])
print('日志路径解析结果:', log_path)

# 检查是否包含未解析占位符
if '{' in str(log_path) and '}' in str(log_path):
    print('❌ 仍存在未解析占位符')
else:
    print('✅ 占位符解析正常')
"
```

**预期输出：**
```
平台信息: Windows
日志路径解析结果: D:\Data\duckdb\AQUA\DataCenter\logs\aqua_dev_20250801.log
✅ 占位符解析正常
```

### 3. **配置环境变量**
设置正确的环境变量确保路径解析：

```powershell
# 设置AQUA环境
setx AQUA_ENV "dev"

# 验证环境变量
echo %AQUA_ENV%
```

## 🔧 技术细节

### 修复内容

**1. 增强ConfigLoader占位符解析**
- 为Windows平台添加正确的D:/Data路径默认值
- 添加双重解析机制：旧系统失败时使用新路径系统
- 增加未解析占位符检测和目录创建阻断

**2. 错误处理改进**
- 详细的错误日志和调试信息
- 优雅的失败处理，避免创建错误目录
- 自动fallback到备用解析系统

**3. 清理工具**
- 智能检测各种占位符目录模式
- 安全的备份和清理流程
- 交互式用户界面，支持不同清理策略

### 相关文件

- `src/utils/config_loader.py` - 核心配置加载器修复
- `scripts/fix_windows_placeholder_dirs.py` - 自动修复脚本
- `src/utils/paths.py` - 新路径管理系统（备用）

## 📞 获取帮助

如果修复后仍然出现问题，请：

1. **收集日志信息**
```powershell
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
config = loader.get_config('dev')
"
```

2. **检查项目状态**
```powershell
git status
git log --oneline -5
```

3. **提交Issue**
在项目仓库中提交Issue，包含：
- Windows版本信息
- 错误日志
- 占位符目录截图
- 运行的具体命令

---

**修复版本**: v2.0.1 (2025-08-01)  
**维护状态**: ✅ 已解决  
**测试平台**: Windows 11 21H2+