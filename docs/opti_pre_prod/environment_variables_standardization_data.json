{"summary": {"total_variables": 89, "variables_need_standardization": 66, "files_affected": 168}, "standardization_actions": [{"original_name": "TUSHARE_TOKEN", "standard_name": "AQUA_TUSHARE_TOKEN", "reason": "预定义标准化规则", "affected_files": ["docs/PRODUCTION_READY_REFACTOR_PLAN.md", "logs/tests/OSX_DEEP_TEST_PLAN.md", "src/aqua/cli/health_checker.py", "tests/test_settings_config_values.py", "src/utils/config_validator.py", "src/cli/commands/init.py", "tests/integration/test_tushare_incremental.py", "logs/tests/backup_20250804_110259/config_backup/settings.toml", "docs/PRECOMMIT_OPTIMIZATION.md", "DATA_TEST_GEMINI.md", "src/data_import/trading_calendar_manager.py", "src/aqua/cli/setup_wizard.py", "src/data_import/extractors/simple_extractor_factory.py", "src/data_import/extractors/tushare_extractor.py", "config/settings.toml", "docs/handbook/InitStartCLI/best_practices.md", "docs/kanban/看板初始化启动前的重构.md", "docs/tasks/MVP_Data_Collection_CLI_Design.md", "docs/opti_pre_prod/environment_variables_audit_data.json", "tests/test_config_cross_platform.py", "tests/test_config_validator_functions.py", "src/utils/config_loader.py", "docs/handbook/InitStartCLI/configuration.md", "docs/opti_pre_prod/environment_variables_audit_report.md", "docs/tasks/Dev_Tasks_EPIC2_V3_Refactored.md", "src/config/data_source_config.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])TUSHARE_TOKEN(['\"])", "replacement": "os.environ.get(\\1AQUA_TUSHARE_TOKEN\\2)", "type": "regex"}, {"pattern": "os.environ[['\"]]TUSHARE_TOKEN[['\"]]", "replacement": "os.environ[\"AQUA_TUSHARE_TOKEN\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]TUSHARE_TOKEN[['\"]]", "replacement": "os.environ[\"AQUA_TUSHARE_TOKEN\"]", "type": "regex"}, {"pattern": "TUSHARE_TOKEN=", "replacement": "AQUA_TUSHARE_TOKEN=", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "TUSHARE_TOKEN=", "replacement": "AQUA_TUSHARE_TOKEN=", "type": "literal"}, {"pattern": "TUSHARE_TOKEN=", "replacement": "AQUA_TUSHARE_TOKEN=", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "TUSHARE_TOKEN=", "replacement": "AQUA_TUSHARE_TOKEN=", "type": "literal"}, {"pattern": "TUSHARE_TOKEN=", "replacement": "AQUA_TUSHARE_TOKEN=", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}, {"pattern": "${TUSHARE_TOKEN}", "replacement": "${AQUA_TUSHARE_TOKEN}", "type": "literal"}]}, {"original_name": "TEST_SET_VAR", "standard_name": "AQUA_TEST_SET_VAR", "reason": "缺少项目前缀", "affected_files": ["tests/test_env_manager.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])TEST_SET_VAR(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_SET_VAR\\2)", "type": "regex"}]}, {"original_name": "TEST_CONVENIENCE", "standard_name": "AQUA_TEST_CONVENIENCE", "reason": "缺少项目前缀", "affected_files": ["tests/test_env_manager.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])TEST_CONVENIENCE(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_CONVENIENCE\\2)", "type": "regex"}]}, {"original_name": "REQUIRED_VAR", "standard_name": "VITE_REQUIRED_VAR", "reason": "缺少项目前缀", "affected_files": ["tests/test_env_manager.py"], "replacement_patterns": [{"pattern": "os.environ[['\"]]REQUIRED_VAR[['\"]]", "replacement": "os.environ[\"VITE_REQUIRED_VAR\"]", "type": "regex"}]}, {"original_name": "TEST_LIST", "standard_name": "AQUA_TEST_LIST", "reason": "缺少项目前缀", "affected_files": ["tests/test_env_manager.py"], "replacement_patterns": [{"pattern": "os.environ[['\"]]TEST_LIST[['\"]]", "replacement": "os.environ[\"AQUA_TEST_LIST\"]", "type": "regex"}]}, {"original_name": "GREEN", "standard_name": "AQUA_GREEN", "reason": "缺少项目前缀", "affected_files": ["scripts/switch-precommit-mode.sh", "docs/opti_pre_prod/environment_variables_audit_data.json", "docs/PRECOMMIT_OPTIMIZATION.md"], "replacement_patterns": [{"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "GREEN=", "replacement": "AQUA_GREEN=", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "${GREEN}", "replacement": "${AQUA_GREEN}", "type": "literal"}, {"pattern": "GREEN=", "replacement": "AQUA_GREEN=", "type": "literal"}]}, {"original_name": "NC", "standard_name": "AQUA_NC", "reason": "变量名过短，不符合命名规范", "affected_files": ["scripts/switch-precommit-mode.sh", "docs/opti_pre_prod/environment_variables_audit_data.json", "docs/PRECOMMIT_OPTIMIZATION.md"], "replacement_patterns": [{"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "${NC}", "replacement": "${AQUA_NC}", "type": "literal"}, {"pattern": "NC=", "replacement": "AQUA_NC=", "type": "literal"}]}, {"original_name": "YELLOW", "standard_name": "AQUA_YELLOW", "reason": "缺少项目前缀", "affected_files": ["scripts/switch-precommit-mode.sh", "docs/opti_pre_prod/environment_variables_audit_data.json", "docs/PRECOMMIT_OPTIMIZATION.md"], "replacement_patterns": [{"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "YELLOW=", "replacement": "AQUA_YELLOW=", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "${YELLOW}", "replacement": "${AQUA_YELLOW}", "type": "literal"}, {"pattern": "YELLOW=", "replacement": "AQUA_YELLOW=", "type": "literal"}]}, {"original_name": "SENSITIVE_PATTERNS", "standard_name": "AQUA_SENSITIVE_PATTERNS", "reason": "缺少项目前缀", "affected_files": ["docs/PRECOMMIT_OPTIMIZATION.md"], "replacement_patterns": [{"pattern": "SENSITIVE_PATTERNS=", "replacement": "AQUA_SENSITIVE_PATTERNS=", "type": "literal"}]}, {"original_name": "RED", "standard_name": "AQUA_RED", "reason": "缺少项目前缀", "affected_files": ["scripts/switch-precommit-mode.sh", "docs/opti_pre_prod/environment_variables_audit_data.json", "docs/PRECOMMIT_OPTIMIZATION.md"], "replacement_patterns": [{"pattern": "RED=", "replacement": "AQUA_RED=", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "${RED}", "replacement": "${AQUA_RED}", "type": "literal"}, {"pattern": "RED=", "replacement": "AQUA_RED=", "type": "literal"}]}, {"original_name": "PYTHONPATH", "standard_name": "AQUA_PYTHONPATH", "reason": "缺少项目前缀", "affected_files": ["docs/claude_tools_guide.md"], "replacement_patterns": [{"pattern": "${PYTHONPATH}", "replacement": "${AQUA_PYTHONPATH}", "type": "literal"}]}, {"original_name": "BACKUP_DATE", "standard_name": "AQUA_BACKUP_DATE", "reason": "缺少项目前缀", "affected_files": ["docs/gitee.md"], "replacement_patterns": [{"pattern": "${BACKUP_DATE}", "replacement": "${AQUA_BACKUP_DATE}", "type": "literal"}, {"pattern": "${BACKUP_DATE}", "replacement": "${AQUA_BACKUP_DATE}", "type": "literal"}, {"pattern": "BACKUP_DATE=", "replacement": "AQUA_BACKUP_DATE=", "type": "literal"}]}, {"original_name": "REPO_URL", "standard_name": "AQUA_REPO_URL", "reason": "缺少项目前缀", "affected_files": ["docs/gitee.md"], "replacement_patterns": [{"pattern": "REPO_URL=", "replacement": "AQUA_REPO_URL=", "type": "literal"}]}, {"original_name": "SSH_URL", "standard_name": "AQUA_SSH_URL", "reason": "缺少项目前缀", "affected_files": ["docs/gitee.md"], "replacement_patterns": [{"pattern": "SSH_URL=", "replacement": "AQUA_SSH_URL=", "type": "literal"}]}, {"original_name": "LOG_FILE", "standard_name": "AQUA_LOG_FILE", "reason": "缺少项目前缀", "affected_files": ["docs/gitee.md"], "replacement_patterns": [{"pattern": "LOG_FILE=", "replacement": "AQUA_LOG_FILE=", "type": "literal"}]}, {"original_name": "TIMESTAMP", "standard_name": "AQUA_TIMESTAMP", "reason": "缺少项目前缀", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "scripts/backup_database.sh", "scripts/backup_frontend.sh", "docs/tasks/Dev_Taks_Epic0_Prompt.md", "docs/gitee.md", "scripts/backup_backend.sh"], "replacement_patterns": [{"pattern": "TIMESTAMP=", "replacement": "AQUA_TIMESTAMP=", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "TIMESTAMP=", "replacement": "AQUA_TIMESTAMP=", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "TIMESTAMP=", "replacement": "AQUA_TIMESTAMP=", "type": "literal"}, {"pattern": "${TIMESTAMP}", "replacement": "${AQUA_TIMESTAMP}", "type": "literal"}, {"pattern": "TIMESTAMP=", "replacement": "AQUA_TIMESTAMP=", "type": "literal"}]}, {"original_name": "COMMIT_HASH", "standard_name": "AQUA_COMMIT_HASH", "reason": "缺少项目前缀", "affected_files": ["docs/gitee.md"], "replacement_patterns": [{"pattern": "COMMIT_HASH=", "replacement": "AQUA_COMMIT_HASH=", "type": "literal"}]}, {"original_name": "REUSE_STATUS", "standard_name": "AQUA_REUSE_STATUS", "reason": "缺少项目前缀", "affected_files": ["docs/gitee.md"], "replacement_patterns": [{"pattern": "REUSE_STATUS=", "replacement": "AQUA_REUSE_STATUS=", "type": "literal"}]}, {"original_name": "FEATURE", "standard_name": "AQUA_FEATURE", "reason": "缺少项目前缀", "affected_files": ["docs/FAQ.md"], "replacement_patterns": [{"pattern": "FEATURE=", "replacement": "AQUA_FEATURE=", "type": "literal"}]}, {"original_name": "PORT", "standard_name": "AQUA_PORT", "reason": "缺少项目前缀", "affected_files": ["docs/FAQ.md"], "replacement_patterns": [{"pattern": "PORT=", "replacement": "AQUA_PORT=", "type": "literal"}]}, {"original_name": "DEBUG", "standard_name": "AQUA_DEBUG", "reason": "缺少项目前缀", "affected_files": ["docs/FAQ.md", "src/utils/simple_config.py"], "replacement_patterns": [{"pattern": "DEBUG=", "replacement": "AQUA_DEBUG=", "type": "literal"}]}, {"original_name": "MYSQL_PASSWORD", "standard_name": "AQUA_MYSQL_PASSWORD", "reason": "缺少项目前缀", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "docs/opti_pre_prod/environment_variables_audit_report.md", "docs/kanban/看板初始化启动前的重构.md", "src/cli/commands/init.py", "src/config/data_source_config.py"], "replacement_patterns": [{"pattern": "${MYSQL_PASSWORD}", "replacement": "${AQUA_MYSQL_PASSWORD}", "type": "literal"}, {"pattern": "${MYSQL_PASSWORD}", "replacement": "${AQUA_MYSQL_PASSWORD}", "type": "literal"}, {"pattern": "${MYSQL_PASSWORD}", "replacement": "${AQUA_MYSQL_PASSWORD}", "type": "literal"}, {"pattern": "${MYSQL_PASSWORD}", "replacement": "${AQUA_MYSQL_PASSWORD}", "type": "literal"}, {"pattern": "${MYSQL_PASSWORD}", "replacement": "${AQUA_MYSQL_PASSWORD}", "type": "literal"}, {"pattern": "${MYSQL_PASSWORD}", "replacement": "${AQUA_MYSQL_PASSWORD}", "type": "literal"}, {"pattern": "${MYSQL_PASSWORD}", "replacement": "${AQUA_MYSQL_PASSWORD}", "type": "literal"}]}, {"original_name": "TEST_MODE", "standard_name": "AQUA_TEST_MODE", "reason": "缺少项目前缀", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "scripts/env_init.py", "deprecated/env_init.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])TEST_MODE(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_MODE\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])TEST_MODE(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_MODE\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])TEST_MODE(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_MODE\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])TEST_MODE(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_MODE\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])TEST_MODE(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_MODE\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])TEST_MODE(['\"])", "replacement": "os.environ.get(\\1AQUA_TEST_MODE\\2)", "type": "regex"}]}, {"original_name": "NO_COLOR", "standard_name": "AQUA_NO_COLOR", "reason": "缺少项目前缀", "affected_files": ["src/cli/utils/platform.py", "docs/opti_pre_prod/environment_variables_audit_data.json", "src/utils/cli_ui.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])NO_COLOR(['\"])", "replacement": "os.environ.get(\\1AQUA_NO_COLOR\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])NO_COLOR(['\"])", "replacement": "os.environ.get(\\1AQUA_NO_COLOR\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])NO_COLOR(['\"])", "replacement": "os.environ.get(\\1AQUA_NO_COLOR\\2)", "type": "regex"}]}, {"original_name": "FORCE_COLOR", "standard_name": "AQUA_FORCE_COLOR", "reason": "缺少项目前缀", "affected_files": ["src/cli/utils/platform.py", "docs/opti_pre_prod/environment_variables_audit_data.json", "src/utils/cli_ui.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])FORCE_COLOR(['\"])", "replacement": "os.environ.get(\\1AQUA_FORCE_COLOR\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])FORCE_COLOR(['\"])", "replacement": "os.environ.get(\\1AQUA_FORCE_COLOR\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])FORCE_COLOR(['\"])", "replacement": "os.environ.get(\\1AQUA_FORCE_COLOR\\2)", "type": "regex"}]}, {"original_name": "SHELL", "standard_name": "AQUA_SHELL", "reason": "缺少项目前缀", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "src/cli/utils/completion.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])SHELL(['\"])", "replacement": "os.environ.get(\\1AQUA_SHELL\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])SHELL(['\"])", "replacement": "os.environ.get(\\1AQUA_SHELL\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])SHELL(['\"])", "replacement": "os.environ.get(\\1AQUA_SHELL\\2)", "type": "regex"}]}, {"original_name": "fpath", "standard_name": "AQUA_fpath", "reason": "缺少项目前缀", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "src/cli/utils/completion.py"], "replacement_patterns": [{"pattern": "os.environ.get(['\"])fpath(['\"])", "replacement": "os.environ.get(\\1AQUA_fpath\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])fpath(['\"])", "replacement": "os.environ.get(\\1AQUA_fpath\\2)", "type": "regex"}, {"pattern": "os.environ.get(['\"])fpath(['\"])", "replacement": "os.environ.get(\\1AQUA_fpath\\2)", "type": "regex"}]}, {"original_name": "PYTHONIOENCODING", "standard_name": "AQUA_PYTHONIOENCODING", "reason": "缺少项目前缀", "affected_files": ["src/aqua/cli/windows_compat.py", "src/utils/service_manager.py", "docs/opti_pre_prod/environment_variables_audit_data.json", "deprecated/start.py"], "replacement_patterns": [{"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONIOENCODING[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONIOENCODING\"]", "type": "regex"}]}, {"original_name": "PYTHONUTF8", "standard_name": "AQUA_PYTHONUTF8", "reason": "缺少项目前缀", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "src/aqua/main.py", "docs/handbook/InitStartCLI/troubleshooting.md", "src/aqua/cli/windows_compat.py", "deprecated/start.py"], "replacement_patterns": [{"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUTF8[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUTF8\"]", "type": "regex"}]}, {"original_name": "PYTHONUNBUFFERED", "standard_name": "AQUA_PYTHONUNBUFFERED", "reason": "缺少项目前缀", "affected_files": ["src/utils/service_manager.py", "docs/opti_pre_prod/environment_variables_audit_data.json"], "replacement_patterns": [{"pattern": "os.environ[['\"]]PYTHONUNBUFFERED[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUNBUFFERED\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUNBUFFERED[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUNBUFFERED\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]PYTHONUNBUFFERED[['\"]]", "replacement": "os.environ[\"AQUA_PYTHONUNBUFFERED\"]", "type": "regex"}]}, {"original_name": "CSV_DATA_PATH", "standard_name": "AQUA_CSV_DATA_PATH", "reason": "预定义标准化规则", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "src/aqua/cli/setup_wizard.py", "src/config/data_source_config.py", "src/aqua/cli/health_checker.py"], "replacement_patterns": []}, {"original_name": "MYSQL_HOST", "standard_name": "AQUA_MYSQL_HOST", "reason": "缺少项目前缀", "affected_files": ["docs/kanban/看板初始化启动前的重构.md", "docs/opti_pre_prod/environment_variables_audit_data.json", "src/config/data_source_config.py"], "replacement_patterns": []}, {"original_name": "MYSQL_PORT", "standard_name": "AQUA_MYSQL_PORT", "reason": "缺少项目前缀", "affected_files": ["docs/kanban/看板初始化启动前的重构.md", "docs/opti_pre_prod/environment_variables_audit_data.json", "src/config/data_source_config.py"], "replacement_patterns": []}, {"original_name": "MYSQL_USER", "standard_name": "AQUA_MYSQL_USER", "reason": "缺少项目前缀", "affected_files": ["docs/kanban/看板初始化启动前的重构.md", "docs/opti_pre_prod/environment_variables_audit_data.json", "src/config/data_source_config.py", "src/cli/commands/init.py"], "replacement_patterns": [{"pattern": "${MYSQL_USER}", "replacement": "${AQUA_MYSQL_USER}", "type": "literal"}, {"pattern": "${MYSQL_USER}", "replacement": "${AQUA_MYSQL_USER}", "type": "literal"}, {"pattern": "${MYSQL_USER}", "replacement": "${AQUA_MYSQL_USER}", "type": "literal"}, {"pattern": "${MYSQL_USER}", "replacement": "${AQUA_MYSQL_USER}", "type": "literal"}, {"pattern": "${MYSQL_USER}", "replacement": "${AQUA_MYSQL_USER}", "type": "literal"}]}, {"original_name": "MYSQL_DATABASE", "standard_name": "AQUA_MYSQL_DATABASE", "reason": "缺少项目前缀", "affected_files": ["docs/kanban/看板初始化启动前的重构.md", "docs/opti_pre_prod/environment_variables_audit_data.json", "src/config/data_source_config.py"], "replacement_patterns": []}, {"original_name": "VIRTUAL_ENV", "standard_name": "AQUA_VIRTUAL_ENV", "reason": "缺少项目前缀", "affected_files": ["src/utils/env_detector.py", "docs/opti_pre_prod/environment_variables_audit_data.json"], "replacement_patterns": []}, {"original_name": "USERNAME", "standard_name": "AQUA_USERNAME", "reason": "缺少项目前缀", "affected_files": ["src/aqua/cli/windows_compat.py", "docs/opti_pre_prod/environment_variables_audit_data.json"], "replacement_patterns": []}, {"original_name": "VAR", "standard_name": "AQUA_VAR", "reason": "缺少项目前缀", "affected_files": ["src/utils/config_validator.py", "docs/opti_pre_prod/environment_variables_audit_data.json", "scripts/audit_environment_variables.py"], "replacement_patterns": [{"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "process.env.VAR", "replacement": "process.env.AQUA_VAR", "type": "literal"}, {"pattern": "process.env.VAR", "replacement": "process.env.AQUA_VAR", "type": "literal"}, {"pattern": "import.meta.env.VAR", "replacement": "import.meta.env.AQUA_VAR", "type": "literal"}, {"pattern": "import.meta.env.VAR", "replacement": "import.meta.env.AQUA_VAR", "type": "literal"}, {"pattern": "os.environ.get(['\"])VAR(['\"])", "replacement": "os.environ.get(\\1AQUA_VAR\\2)", "type": "regex"}, {"pattern": "os.environ[['\"]]VAR[['\"]]", "replacement": "os.environ[\"AQUA_VAR\"]", "type": "regex"}, {"pattern": "os.environ[['\"]]VAR[['\"]]", "replacement": "os.environ[\"AQUA_VAR\"]", "type": "regex"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "process.env.VAR", "replacement": "process.env.AQUA_VAR", "type": "literal"}, {"pattern": "import.meta.env.VAR", "replacement": "import.meta.env.AQUA_VAR", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}, {"pattern": "${VAR}", "replacement": "${AQUA_VAR}", "type": "literal"}]}, {"original_name": "BLUE", "standard_name": "AQUA_BLUE", "reason": "缺少项目前缀", "affected_files": ["scripts/switch-precommit-mode.sh", "docs/opti_pre_prod/environment_variables_audit_data.json"], "replacement_patterns": [{"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "${BLUE}", "replacement": "${AQUA_BLUE}", "type": "literal"}, {"pattern": "BLUE=", "replacement": "AQUA_BLUE=", "type": "literal"}]}, {"original_name": "COMP_CWORD", "standard_name": "AQUA_COMP_CWORD", "reason": "缺少项目前缀", "affected_files": ["docs/opti_pre_prod/environment_variables_audit_data.json", "src/cli/utils/completion.py"], "replacement_patterns": [{"pattern": "${COMP_CWORD}", "replacement": "${AQUA_COMP_CWORD}", "type": "literal"}, {"pattern": "${COMP_CWORD}", "replacement": "${AQUA_COMP_CWORD}", "type": "literal"}, {"pattern": "${COMP_CWORD}", "replacement": "${AQUA_COMP_CWORD}", "type": "literal"}]}, {"original_name": "ENVIRONMENT", "standard_name": "AQUA_ENVIRONMENT", "reason": "缺少项目前缀", "affected_files": ["docs/handbook/InitStartCLI/best_practices.md"], "replacement_patterns": [{"pattern": "ENVIRONMENT=", "replacement": "AQUA_ENVIRONMENT=", "type": "literal"}]}, {"original_name": "VERSION", "standard_name": "AQUA_VERSION", "reason": "缺少项目前缀", "affected_files": ["docs/handbook/InitStartCLI/best_practices.md"], "replacement_patterns": [{"pattern": "VERSION=", "replacement": "AQUA_VERSION=", "type": "literal"}]}, {"original_name": "DEPLOY_TIME", "standard_name": "AQUA_DEPLOY_TIME", "reason": "缺少项目前缀", "affected_files": ["docs/handbook/InitStartCLI/best_practices.md"], "replacement_patterns": [{"pattern": "DEPLOY_TIME=", "replacement": "AQUA_DEPLOY_TIME=", "type": "literal"}]}, {"original_name": "BACKUP_DIR", "standard_name": "AQUA_BACKUP_DIR", "reason": "预定义标准化规则", "affected_files": ["scripts/backup_backend.sh", "logs/tests/rollback_script.sh", "scripts/backup_frontend.sh", "scripts/backup_database.sh"], "replacement_patterns": [{"pattern": "BACKUP_DIR=", "replacement": "AQUA_BACKUP_DIR=", "type": "literal"}, {"pattern": "BACKUP_DIR=", "replacement": "AQUA_BACKUP_DIR=", "type": "literal"}, {"pattern": "BACKUP_DIR=", "replacement": "AQUA_BACKUP_DIR=", "type": "literal"}, {"pattern": "BACKUP_DIR=", "replacement": "AQUA_BACKUP_DIR=", "type": "literal"}]}, {"original_name": "PROJECT_ROOT", "standard_name": "AQUA_PROJECT_ROOT", "reason": "预定义标准化规则", "affected_files": ["scripts/restore_backend.sh", "scripts/backup_database.sh", "scripts/backup_frontend.sh", "scripts/restore_database.sh", "scripts/restore_frontend.sh", "scripts/backup_backend.sh", "scripts/monitor_frontend.sh"], "replacement_patterns": [{"pattern": "PROJECT_ROOT=", "replacement": "AQUA_PROJECT_ROOT=", "type": "literal"}, {"pattern": "PROJECT_ROOT=", "replacement": "AQUA_PROJECT_ROOT=", "type": "literal"}, {"pattern": "PROJECT_ROOT=", "replacement": "AQUA_PROJECT_ROOT=", "type": "literal"}, {"pattern": "PROJECT_ROOT=", "replacement": "AQUA_PROJECT_ROOT=", "type": "literal"}, {"pattern": "PROJECT_ROOT=", "replacement": "AQUA_PROJECT_ROOT=", "type": "literal"}, {"pattern": "PROJECT_ROOT=", "replacement": "AQUA_PROJECT_ROOT=", "type": "literal"}, {"pattern": "PROJECT_ROOT=", "replacement": "AQUA_PROJECT_ROOT=", "type": "literal"}]}, {"original_name": "SETTINGS_PATH", "standard_name": "AQUA_SETTINGS_PATH", "reason": "预定义标准化规则", "affected_files": ["scripts/restore_backend.sh", "scripts/backup_database.sh", "scripts/backup_frontend.sh", "scripts/restore_database.sh", "scripts/restore_frontend.sh", "scripts/backup_backend.sh", "scripts/monitor_frontend.sh"], "replacement_patterns": [{"pattern": "SETTINGS_PATH=", "replacement": "AQUA_SETTINGS_PATH=", "type": "literal"}, {"pattern": "SETTINGS_PATH=", "replacement": "AQUA_SETTINGS_PATH=", "type": "literal"}, {"pattern": "SETTINGS_PATH=", "replacement": "AQUA_SETTINGS_PATH=", "type": "literal"}, {"pattern": "SETTINGS_PATH=", "replacement": "AQUA_SETTINGS_PATH=", "type": "literal"}, {"pattern": "SETTINGS_PATH=", "replacement": "AQUA_SETTINGS_PATH=", "type": "literal"}, {"pattern": "SETTINGS_PATH=", "replacement": "AQUA_SETTINGS_PATH=", "type": "literal"}, {"pattern": "SETTINGS_PATH=", "replacement": "AQUA_SETTINGS_PATH=", "type": "literal"}]}, {"original_name": "LOG_PATH", "standard_name": "AQUA_LOG_PATH", "reason": "预定义标准化规则", "affected_files": ["scripts/restore_backend.sh", "scripts/backup_database.sh", "scripts/backup_frontend.sh", "scripts/restore_database.sh", "scripts/restore_frontend.sh", "scripts/backup_backend.sh", "scripts/monitor_frontend.sh"], "replacement_patterns": [{"pattern": "LOG_PATH=", "replacement": "AQUA_LOG_PATH=", "type": "literal"}, {"pattern": "LOG_PATH=", "replacement": "AQUA_LOG_PATH=", "type": "literal"}, {"pattern": "LOG_PATH=", "replacement": "AQUA_LOG_PATH=", "type": "literal"}, {"pattern": "LOG_PATH=", "replacement": "AQUA_LOG_PATH=", "type": "literal"}, {"pattern": "LOG_PATH=", "replacement": "AQUA_LOG_PATH=", "type": "literal"}, {"pattern": "LOG_PATH=", "replacement": "AQUA_LOG_PATH=", "type": "literal"}, {"pattern": "LOG_PATH=", "replacement": "AQUA_LOG_PATH=", "type": "literal"}]}, {"original_name": "FRONTEND_PORT", "standard_name": "AQUA_FRONTEND_PORT", "reason": "预定义标准化规则", "affected_files": ["scripts/monitor_frontend.sh"], "replacement_patterns": [{"pattern": "FRONTEND_PORT=", "replacement": "AQUA_FRONTEND_PORT=", "type": "literal"}, {"pattern": "FRONTEND_PORT=", "replacement": "AQUA_FRONTEND_PORT=", "type": "literal"}]}, {"original_name": "FRONTEND_HOST", "standard_name": "AQUA_FRONTEND_HOST", "reason": "预定义标准化规则", "affected_files": ["scripts/monitor_frontend.sh"], "replacement_patterns": [{"pattern": "FRONTEND_HOST=", "replacement": "AQUA_FRONTEND_HOST=", "type": "literal"}, {"pattern": "FRONTEND_HOST=", "replacement": "AQUA_FRONTEND_HOST=", "type": "literal"}]}, {"original_name": "INTERVAL", "standard_name": "AQUA_INTERVAL", "reason": "缺少项目前缀", "affected_files": ["scripts/monitor_frontend.sh"], "replacement_patterns": [{"pattern": "INTERVAL=", "replacement": "AQUA_INTERVAL=", "type": "literal"}]}, {"original_name": "{standard}", "standard_name": "AQUA_{standard}", "reason": "缺少项目前缀", "affected_files": ["scripts/standardize_env_variables.py"], "replacement_patterns": [{"pattern": "os.environ[['\"]]{standard}[['\"]]", "replacement": "os.environ[\"AQUA_{standard}\"]", "type": "regex"}]}, {"original_name": "BACKUP_FILE", "standard_name": "AQUA_BACKUP_FILE", "reason": "缺少项目前缀", "affected_files": ["scripts/restore_backend.sh", "scripts/restore_database.sh", "scripts/restore_frontend.sh"], "replacement_patterns": [{"pattern": "BACKUP_FILE=", "replacement": "AQUA_BACKUP_FILE=", "type": "literal"}, {"pattern": "BACKUP_FILE=", "replacement": "AQUA_BACKUP_FILE=", "type": "literal"}, {"pattern": "BACKUP_FILE=", "replacement": "AQUA_BACKUP_FILE=", "type": "literal"}]}, {"original_name": "DB_PATH", "standard_name": "AQUA_DB_PATH", "reason": "缺少项目前缀", "affected_files": ["scripts/restore_database.sh", "scripts/backup_database.sh"], "replacement_patterns": [{"pattern": "DB_PATH=", "replacement": "AQUA_DB_PATH=", "type": "literal"}, {"pattern": "DB_PATH=", "replacement": "AQUA_DB_PATH=", "type": "literal"}, {"pattern": "DB_PATH=", "replacement": "AQUA_DB_PATH=", "type": "literal"}, {"pattern": "DB_PATH=", "replacement": "AQUA_DB_PATH=", "type": "literal"}, {"pattern": "DB_PATH=", "replacement": "AQUA_DB_PATH=", "type": "literal"}, {"pattern": "DB_PATH=", "replacement": "AQUA_DB_PATH=", "type": "literal"}]}, {"original_name": "DB_BAK_NAME", "standard_name": "AQUA_DB_BAK_NAME", "reason": "缺少项目前缀", "affected_files": ["scripts/backup_database.sh"], "replacement_patterns": [{"pattern": "DB_BAK_NAME=", "replacement": "AQUA_DB_BAK_NAME=", "type": "literal"}]}, {"original_name": "DB_BAK_TARGET", "standard_name": "AQUA_DB_BAK_TARGET", "reason": "缺少项目前缀", "affected_files": ["scripts/backup_database.sh"], "replacement_patterns": [{"pattern": "DB_BAK_TARGET=", "replacement": "AQUA_DB_BAK_TARGET=", "type": "literal"}]}, {"original_name": "FRONTEND_NAME", "standard_name": "VITE_FRONTEND_NAME", "reason": "缺少项目前缀", "affected_files": ["scripts/backup_frontend.sh"], "replacement_patterns": [{"pattern": "FRONTEND_NAME=", "replacement": "VITE_FRONTEND_NAME=", "type": "literal"}]}, {"original_name": "FRONTEND_TARGET", "standard_name": "VITE_FRONTEND_TARGET", "reason": "缺少项目前缀", "affected_files": ["scripts/backup_frontend.sh"], "replacement_patterns": [{"pattern": "FRONTEND_TARGET=", "replacement": "VITE_FRONTEND_TARGET=", "type": "literal"}]}, {"original_name": "SCRIPT_DIR", "standard_name": "AQUA_SCRIPT_DIR", "reason": "缺少项目前缀", "affected_files": ["scripts/deploy.sh", "deprecated/entry_files/aqua.sh"], "replacement_patterns": [{"pattern": "SCRIPT_DIR=", "replacement": "AQUA_SCRIPT_DIR=", "type": "literal"}, {"pattern": "SCRIPT_DIR=", "replacement": "AQUA_SCRIPT_DIR=", "type": "literal"}]}, {"original_name": "BASE_DIR", "standard_name": "AQUA_BASE_DIR", "reason": "缺少项目前缀", "affected_files": ["scripts/deploy.sh"], "replacement_patterns": [{"pattern": "BASE_DIR=", "replacement": "AQUA_BASE_DIR=", "type": "literal"}]}, {"original_name": "ENV", "standard_name": "AQUA_ENV", "reason": "缺少项目前缀", "affected_files": ["scripts/deploy.sh"], "replacement_patterns": [{"pattern": "ENV=", "replacement": "AQUA_ENV=", "type": "literal"}]}, {"original_name": "BACKEND_NAME", "standard_name": "AQUA_BACKEND_NAME", "reason": "缺少项目前缀", "affected_files": ["scripts/backup_backend.sh"], "replacement_patterns": [{"pattern": "BACKEND_NAME=", "replacement": "AQUA_BACKEND_NAME=", "type": "literal"}]}, {"original_name": "BACKEND_TARGET", "standard_name": "AQUA_BACKEND_TARGET", "reason": "缺少项目前缀", "affected_files": ["scripts/backup_backend.sh"], "replacement_patterns": [{"pattern": "BACKEND_TARGET=", "replacement": "AQUA_BACKEND_TARGET=", "type": "literal"}]}, {"original_name": "TERM", "standard_name": "AQUA_TERM", "reason": "缺少项目前缀", "affected_files": ["src/utils/cli_ui.py"], "replacement_patterns": []}, {"original_name": "JUPYTER_RUNTIME_DIR", "standard_name": "AQUA_JUPYTER_RUNTIME_DIR", "reason": "缺少项目前缀", "affected_files": ["src/utils/simple_config.py"], "replacement_patterns": []}, {"original_name": "LOG_LEVEL", "standard_name": "AQUA_LOG_LEVEL", "reason": "缺少项目前缀", "affected_files": ["src/utils/simple_config.py"], "replacement_patterns": []}, {"original_name": "PYTEST_CURRENT_TEST", "standard_name": "AQUA_PYTEST_CURRENT_TEST", "reason": "缺少项目前缀", "affected_files": ["src/utils/env_detector.py"], "replacement_patterns": []}], "migration_guide": [{"step": "将 TUSHARE_TOKEN 重命名为 AQUA_TUSHARE_TOKEN", "description": "原因: 预定义标准化规则", "affected_files": 26, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 TUSHARE_TOKEN", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 TEST_SET_VAR 重命名为 AQUA_TEST_SET_VAR", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 TEST_SET_VAR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 TEST_CONVENIENCE 重命名为 AQUA_TEST_CONVENIENCE", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 TEST_CONVENIENCE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 REQUIRED_VAR 重命名为 VITE_REQUIRED_VAR", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 REQUIRED_VAR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 TEST_LIST 重命名为 AQUA_TEST_LIST", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 TEST_LIST", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 GREEN 重命名为 AQUA_GREEN", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 GREEN", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 NC 重命名为 AQUA_NC", "description": "原因: 变量名过短，不符合命名规范", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 NC", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 YELLOW 重命名为 AQUA_YELLOW", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 YELLOW", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 SENSITIVE_PATTERNS 重命名为 AQUA_SENSITIVE_PATTERNS", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 SENSITIVE_PATTERNS", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 RED 重命名为 AQUA_RED", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 RED", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 PYTHONPATH 重命名为 AQUA_PYTHONPATH", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONPATH", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 BACKUP_DATE 重命名为 AQUA_BACKUP_DATE", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 BACKUP_DATE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 REPO_URL 重命名为 AQUA_REPO_URL", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 REPO_URL", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 SSH_URL 重命名为 AQUA_SSH_URL", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 SSH_URL", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 LOG_FILE 重命名为 AQUA_LOG_FILE", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 LOG_FILE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 TIMESTAMP 重命名为 AQUA_TIMESTAMP", "description": "原因: 缺少项目前缀", "affected_files": 6, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 TIMESTAMP", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 COMMIT_HASH 重命名为 AQUA_COMMIT_HASH", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 COMMIT_HASH", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 REUSE_STATUS 重命名为 AQUA_REUSE_STATUS", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 REUSE_STATUS", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 FEATURE 重命名为 AQUA_FEATURE", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 FEATURE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 PORT 重命名为 AQUA_PORT", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 PORT", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 DEBUG 重命名为 AQUA_DEBUG", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 DEBUG", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 MYSQL_PASSWORD 重命名为 AQUA_MYSQL_PASSWORD", "description": "原因: 缺少项目前缀", "affected_files": 5, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_PASSWORD", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 TEST_MODE 重命名为 AQUA_TEST_MODE", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 TEST_MODE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 NO_COLOR 重命名为 AQUA_NO_COLOR", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 NO_COLOR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 FORCE_COLOR 重命名为 AQUA_FORCE_COLOR", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 FORCE_COLOR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 SHELL 重命名为 AQUA_SHELL", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 SHELL", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 fpath 重命名为 AQUA_fpath", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 fpath", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 PYTHONIOENCODING 重命名为 AQUA_PYTHONIOENCODING", "description": "原因: 缺少项目前缀", "affected_files": 4, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONIOENCODING", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 PYTHONUTF8 重命名为 AQUA_PYTHONUTF8", "description": "原因: 缺少项目前缀", "affected_files": 5, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONUTF8", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 PYTHONUNBUFFERED 重命名为 AQUA_PYTHONUNBUFFERED", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONUNBUFFERED", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 CSV_DATA_PATH 重命名为 AQUA_CSV_DATA_PATH", "description": "原因: 预定义标准化规则", "affected_files": 4, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 CSV_DATA_PATH", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 MYSQL_HOST 重命名为 AQUA_MYSQL_HOST", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_HOST", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 MYSQL_PORT 重命名为 AQUA_MYSQL_PORT", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_PORT", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 MYSQL_USER 重命名为 AQUA_MYSQL_USER", "description": "原因: 缺少项目前缀", "affected_files": 4, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_USER", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 MYSQL_DATABASE 重命名为 AQUA_MYSQL_DATABASE", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_DATABASE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 VIRTUAL_ENV 重命名为 AQUA_VIRTUAL_ENV", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 VIRTUAL_ENV", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 USERNAME 重命名为 AQUA_USERNAME", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 USERNAME", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 VAR 重命名为 AQUA_VAR", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 VAR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 BLUE 重命名为 AQUA_BLUE", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 BLUE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 COMP_CWORD 重命名为 AQUA_COMP_CWORD", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 COMP_CWORD", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 ENVIRONMENT 重命名为 AQUA_ENVIRONMENT", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 ENVIRONMENT", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 VERSION 重命名为 AQUA_VERSION", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 VERSION", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 DEPLOY_TIME 重命名为 AQUA_DEPLOY_TIME", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 DEPLOY_TIME", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 BACKUP_DIR 重命名为 AQUA_BACKUP_DIR", "description": "原因: 预定义标准化规则", "affected_files": 4, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 BACKUP_DIR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 PROJECT_ROOT 重命名为 AQUA_PROJECT_ROOT", "description": "原因: 预定义标准化规则", "affected_files": 7, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 PROJECT_ROOT", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 SETTINGS_PATH 重命名为 AQUA_SETTINGS_PATH", "description": "原因: 预定义标准化规则", "affected_files": 7, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 SETTINGS_PATH", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 LOG_PATH 重命名为 AQUA_LOG_PATH", "description": "原因: 预定义标准化规则", "affected_files": 7, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 LOG_PATH", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 FRONTEND_PORT 重命名为 AQUA_FRONTEND_PORT", "description": "原因: 预定义标准化规则", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_PORT", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 FRONTEND_HOST 重命名为 AQUA_FRONTEND_HOST", "description": "原因: 预定义标准化规则", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_HOST", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 INTERVAL 重命名为 AQUA_INTERVAL", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 INTERVAL", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 {standard} 重命名为 AQUA_{standard}", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 {standard}", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 BACKUP_FILE 重命名为 AQUA_BACKUP_FILE", "description": "原因: 缺少项目前缀", "affected_files": 3, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 BACKUP_FILE", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 DB_PATH 重命名为 AQUA_DB_PATH", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 DB_PATH", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 DB_BAK_NAME 重命名为 AQUA_DB_BAK_NAME", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 DB_BAK_NAME", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 DB_BAK_TARGET 重命名为 AQUA_DB_BAK_TARGET", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 DB_BAK_TARGET", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 FRONTEND_NAME 重命名为 VITE_FRONTEND_NAME", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_NAME", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 FRONTEND_TARGET 重命名为 VITE_FRONTEND_TARGET", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_TARGET", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 SCRIPT_DIR 重命名为 AQUA_SCRIPT_DIR", "description": "原因: 缺少项目前缀", "affected_files": 2, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 SCRIPT_DIR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 BASE_DIR 重命名为 AQUA_BASE_DIR", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 BASE_DIR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 ENV 重命名为 AQUA_ENV", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 ENV", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 BACKEND_NAME 重命名为 AQUA_BACKEND_NAME", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 BACKEND_NAME", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 BACKEND_TARGET 重命名为 AQUA_BACKEND_TARGET", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 BACKEND_TARGET", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 TERM 重命名为 AQUA_TERM", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 TERM", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 JUPYTER_RUNTIME_DIR 重命名为 AQUA_JUPYTER_RUNTIME_DIR", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 JUPYTER_RUNTIME_DIR", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 LOG_LEVEL 重命名为 AQUA_LOG_LEVEL", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 LOG_LEVEL", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}, {"step": "将 PYTEST_CURRENT_TEST 重命名为 AQUA_PYTEST_CURRENT_TEST", "description": "原因: 缺少项目前缀", "affected_files": 1, "manual_steps": ["1. 更新环境变量定义文件(.env, .env.*)中的 PYTEST_CURRENT_TEST", "2. 更新部署配置中的环境变量设置", "3. 通知团队成员更新本地环境配置"]}], "validation_steps": ["1. 运行所有单元测试确保功能正常", "2. 检查环境变量管理器的验证功能", "3. 验证前端构建过程中的环境变量", "4. 测试不同环境(dev/test/prod)的配置", "5. 确认CI/CD流程中的环境变量设置"]}