
# 依赖自动安装报告

## 安装概览
- **执行时间**: 2025-08-05 11:20:03
- **平台**: Darwin
- **模式**: 试运行

## 安装结果

### Python依赖
- pywin32>=306; sys_platform: ✅ 成功
  命令: `pip install pywin32>=306; sys_platform=="win32"`

### Node.js依赖
- @cypress/vite-dev-server: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @eslint/config-helpers: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @eslint/js: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @types/node: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @typescript-eslint/eslint-plugin: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @typescript-eslint/parser: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @vitejs/plugin-vue: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @vue/test-utils: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- cypress: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- eslint: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- eslint-config-prettier: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- eslint-define-config: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- eslint-plugin-prettier: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- eslint-plugin-vue: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- jsdom: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- prettier: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- vite: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- vitest: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- vue: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- vue-tsc: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @typescript-eslint/eslint-plugin: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @typescript-eslint/parser: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @vitejs/plugin-vue: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @vitest/coverage-v8: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- @vue/tsconfig: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- vite: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`
- vitest: ✅ 成功
  命令: `npm install @cypress/vite-dev-server@6.0.3 @eslint/config-helpers@0.3.0 @eslint/js@9.30.1 @types/node@24.0.10 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vue/test-utils@2.4.6 cypress@14.5.1 eslint@9.30.1 eslint-config-prettier@10.1.5 eslint-define-config@2.1.0 eslint-plugin-prettier@5.5.1 eslint-plugin-vue@10.3.0 jsdom@26.1.0 prettier@3.6.2 vite@7.0.0 vitest@3.2.4 vue@3.5.17 vue-tsc@3.0.0 @typescript-eslint/eslint-plugin@8.35.1 @typescript-eslint/parser@8.35.1 @vitejs/plugin-vue@6.0.0 @vitest/coverage-v8@3.2.4 @vue/tsconfig@0.7.0 vite@7.0.0 vitest@3.2.4`

### 系统依赖
- psql: ✅ 成功
  命令: `brew install postgresql`
- wget: ✅ 成功
  命令: `brew install wget`
