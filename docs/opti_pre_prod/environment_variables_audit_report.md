# AQUA项目环境变量使用审计报告
生成时间: 2025-08-05 07:34:24

## 📊 审计摘要
- 扫描文件总数: 2995
- 包含环境变量的文件: 58
- 发现的环境变量总数: 77
- 发现的问题总数: 45

## 🔧 发现的环境变量
### AQUA_DEBUG
使用次数: 4
- aqua.py:86
- aqua.py:117
- tests/unit/test_simple_config.py:220
- ... 还有 1 个使用位置

### AQUA_ENV
使用次数: 33
- main.py:86
- tests/test_ai_mvp_simple.py:16
- tests/test_ai_mvp.py:21
- ... 还有 30 个使用位置

### VITE_API_BASE_URL
使用次数: 3
- frontend/vite.config.js:16
- frontend/src/api/index.ts:5
- tests/test_hardcoded_path_replacement.py:140

### NODE_ENV
使用次数: 7
- frontend/tests/unit/DataCenterPageImport.test.ts:489
- frontend/tests/unit/DataCenterPageImport.test.ts:490
- frontend/tests/unit/DataCenterPageImport.test.ts:516
- ... 还有 4 个使用位置

### HOME
使用次数: 2
- frontend/src/utils/platform/CrossPlatformHelper.ts:64
- tests/integration/test_cross_platform.py:142

### USERPROFILE
使用次数: 2
- frontend/src/utils/platform/CrossPlatformHelper.ts:64
- tests/integration/test_cross_platform.py:142

### TUSHARE_TOKEN
使用次数: 25
- config/settings.toml:169
- tests/test_config_cross_platform.py:46
- tests/test_settings_config_values.py:51
- ... 还有 22 个使用位置

### AQUA_LOG_LEVEL
使用次数: 3
- tests/test_ai_mvp_simple.py:17
- tests/unit/test_simple_config.py:219
- src/utils/simple_config.py:201

### CI
使用次数: 2
- tests/unit/test_simple_config.py:172
- src/utils/simple_config.py:145

### AQUA_TEST_MODE
使用次数: 1
- tests/integration/conftest.py:23

### PATH
使用次数: 1
- tests/integration/test_cross_platform.py:141

### PSV
使用次数: 6
- tests/integration/test_windows_environment_plan.py:456
- tests/integration/test_windows_environment_plan.py:456
- tests/integration/test_windows_environment_plan.py:457
- ... 还有 3 个使用位置

### P
使用次数: 4
- tests/integration/test_windows_environment_plan.py:463
- tests/integration/test_windows_environment_plan.py:464
- tests/integration/test_windows_environment_plan.py:465
- ... 还有 1 个使用位置

### T
使用次数: 8
- tests/integration/test_windows_environment_plan.py:477
- tests/integration/test_windows_environment_plan.py:478
- tests/integration/test_windows_environment_plan.py:479
- ... 还有 5 个使用位置

### L
使用次数: 3
- tests/integration/test_windows_environment_plan.py:488
- tests/integration/test_windows_environment_plan.py:490
- tests/integration/test_windows_environment_plan.py:517

### _
使用次数: 1
- tests/integration/test_windows_environment_plan.py:493

### E
使用次数: 3
- tests/integration/test_windows_environment_plan.py:498
- tests/integration/test_windows_environment_plan.py:499
- tests/integration/test_windows_environment_plan.py:519

### UPSTASH_VECTOR_REST_URL
使用次数: 1
- .claude/settings.local.json:7

### BACKUP_DIR
使用次数: 11
- logs/tests/rollback_script.sh:8
- logs/tests/rollback_script.sh:11
- logs/tests/rollback_script.sh:12
- ... 还有 8 个使用位置

### PROJECT_ROOT
使用次数: 27
- scripts/monitor_frontend.sh:13
- scripts/monitor_frontend.sh:14
- scripts/restore_backend.sh:11
- ... 还有 24 个使用位置

### LOG_PATH
使用次数: 15
- scripts/monitor_frontend.sh:17
- scripts/monitor_frontend.sh:29
- scripts/monitor_frontend.sh:33
- ... 还有 12 个使用位置

### SETTINGS_PATH
使用次数: 4
- scripts/monitor_frontend.sh:20
- scripts/monitor_frontend.sh:21
- scripts/backup_database.sh:20
- ... 还有 1 个使用位置

### FRONTEND_HOST
使用次数: 3
- scripts/monitor_frontend.sh:37
- scripts/monitor_frontend.sh:39
- scripts/monitor_frontend.sh:41

### FRONTEND_PORT
使用次数: 3
- scripts/monitor_frontend.sh:37
- scripts/monitor_frontend.sh:39
- scripts/monitor_frontend.sh:41

### INTERVAL
使用次数: 1
- scripts/monitor_frontend.sh:43

### VAR
使用次数: 12
- scripts/audit_environment_variables.py:27
- scripts/audit_environment_variables.py:28
- scripts/audit_environment_variables.py:123
- ... 还有 9 个使用位置

### BACKUP_FILE
使用次数: 18
- scripts/restore_backend.sh:27
- scripts/restore_backend.sh:27
- scripts/restore_backend.sh:28
- ... 还有 15 个使用位置

### TIMESTAMP
使用次数: 3
- scripts/backup_database.sh:24
- scripts/backup_frontend.sh:20
- scripts/backup_backend.sh:20

### DB_PATH
使用次数: 8
- scripts/backup_database.sh:22
- scripts/backup_database.sh:39
- scripts/backup_database.sh:40
- ... 还有 5 个使用位置

### DB_BAK_NAME
使用次数: 1
- scripts/backup_database.sh:25

### DB_BAK_TARGET
使用次数: 3
- scripts/backup_database.sh:45
- scripts/backup_database.sh:46
- scripts/backup_database.sh:48

### FRONTEND_NAME
使用次数: 1
- scripts/backup_frontend.sh:21

### FRONTEND_TARGET
使用次数: 3
- scripts/backup_frontend.sh:36
- scripts/backup_frontend.sh:37
- scripts/backup_frontend.sh:39

### TEST_MODE
使用次数: 2
- scripts/env_init.py:797
- deprecated/env_init.py:790

### SCRIPT_DIR
使用次数: 3
- scripts/deploy.sh:25
- deprecated/entry_files/aqua.sh:7
- deprecated/entry_files/aqua.sh:22

### BASE_DIR
使用次数: 1
- scripts/deploy.sh:26

### ENV
使用次数: 5
- scripts/deploy.sh:41
- scripts/deploy.sh:56
- scripts/deploy.sh:58
- ... 还有 2 个使用位置

### FRONTEND_PID
使用次数: 3
- scripts/deploy.sh:70
- scripts/deploy.sh:71
- scripts/deploy.sh:73

### BACKEND_PID
使用次数: 3
- scripts/deploy.sh:70
- scripts/deploy.sh:71
- scripts/deploy.sh:73

### BLUE
使用次数: 3
- scripts/switch-precommit-mode.sh:11
- scripts/switch-precommit-mode.sh:19
- scripts/switch-precommit-mode.sh:90

### NC
使用次数: 19
- scripts/switch-precommit-mode.sh:11
- scripts/switch-precommit-mode.sh:16
- scripts/switch-precommit-mode.sh:17
- ... 还有 16 个使用位置

### GREEN
使用次数: 4
- scripts/switch-precommit-mode.sh:16
- scripts/switch-precommit-mode.sh:66
- scripts/switch-precommit-mode.sh:70
- ... 还有 1 个使用位置

### YELLOW
使用次数: 7
- scripts/switch-precommit-mode.sh:17
- scripts/switch-precommit-mode.sh:71
- scripts/switch-precommit-mode.sh:75
- ... 还有 4 个使用位置

### RED
使用次数: 5
- scripts/switch-precommit-mode.sh:18
- scripts/switch-precommit-mode.sh:84
- scripts/switch-precommit-mode.sh:99
- ... 还有 2 个使用位置

### VENV_PYTHON
使用次数: 1
- scripts/env_diagnose.sh:40

### BACKEND_NAME
使用次数: 1
- scripts/backup_backend.sh:21

### BACKEND_TARGET
使用次数: 3
- scripts/backup_backend.sh:36
- scripts/backup_backend.sh:37
- scripts/backup_backend.sh:39

### CSV_DATA_PATH
使用次数: 6
- src/config/data_source_config.py:40
- src/aqua/cli/setup_wizard.py:96
- src/aqua/cli/setup_wizard.py:266
- ... 还有 3 个使用位置

### MYSQL_HOST
使用次数: 1
- src/config/data_source_config.py:45

### MYSQL_PORT
使用次数: 1
- src/config/data_source_config.py:50

### MYSQL_USER
使用次数: 2
- src/config/data_source_config.py:55
- src/cli/commands/init.py:160

### MYSQL_PASSWORD
使用次数: 2
- src/config/data_source_config.py:60
- src/cli/commands/init.py:161

### MYSQL_DATABASE
使用次数: 1
- src/config/data_source_config.py:65

### AQUA_MYSQL_PASSWORD
使用次数: 1
- src/utils/config_loader.py:105

### AQUA_ENABLE_DATA_PROCESSOR
使用次数: 1
- src/utils/config_loader.py:304

### AQUA_DATA_PROCESSOR_FAIL_SAFE
使用次数: 1
- src/utils/config_loader.py:305

### AQUA_SHOW_QUALITY_STATS
使用次数: 1
- src/utils/config_loader.py:306

### PYTHONIOENCODING
使用次数: 4
- src/utils/service_manager.py:90
- src/aqua/cli/windows_compat.py:83
- src/aqua/cli/windows_compat.py:373
- ... 还有 1 个使用位置

### PYTHONUNBUFFERED
使用次数: 1
- src/utils/service_manager.py:91

### NO_COLOR
使用次数: 2
- src/utils/cli_ui.py:110
- src/cli/utils/platform.py:98

### FORCE_COLOR
使用次数: 2
- src/utils/cli_ui.py:113
- src/cli/utils/platform.py:101

### TERM
使用次数: 1
- src/utils/cli_ui.py:117

### JUPYTER_RUNTIME_DIR
使用次数: 1
- src/utils/simple_config.py:147

### LOG_LEVEL
使用次数: 1
- src/utils/simple_config.py:201

### DEBUG
使用次数: 1
- src/utils/simple_config.py:206

### AQUA_MEMORY_LIMIT_MB
使用次数: 1
- src/utils/simple_config.py:216

### PYTEST_CURRENT_TEST
使用次数: 1
- src/utils/env_detector.py:341

### VIRTUAL_ENV
使用次数: 2
- src/utils/env_detector.py:355
- src/utils/env_detector.py:360

### SHELL
使用次数: 1
- src/cli/utils/completion.py:36

### fpath
使用次数: 1
- src/cli/utils/completion.py:105

### COMP_CWORD
使用次数: 1
- src/cli/utils/completion.py:179

### PYTHONUTF8
使用次数: 4
- src/aqua/main.py:109
- src/aqua/cli/windows_compat.py:82
- src/aqua/cli/windows_compat.py:372
- ... 还有 1 个使用位置

### USERNAME
使用次数: 1
- src/aqua/cli/windows_compat.py:165

### PROFILE
使用次数: 1
- src/aqua/cli/windows_compat.py:271

### O
使用次数: 1
- src/aqua/cli/windows_compat.py:283

### PYTHON_EXE
使用次数: 1
- deprecated/entry_files/aqua.sh:25

### VITE_WS_BASE_URL
使用次数: 1
- deprecated/data_import_legacy/frontend/modules/data-center/components/UnifiedDataImport.vue:2228

## ⚠️ 发现的问题
🔴 **hardcoded_token** - config/settings.toml:169
   发现硬编码敏感信息: token = "${TUSHARE_TOKEN}"

🟡 **no_default_value** - tests/test_ai_mvp_simple.py:16
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/test_ai_mvp_simple.py:17
   环境变量 AQUA_LOG_LEVEL 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/test_ai_mvp.py:21
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🔴 **hardcoded_password** - tests/unit/test_mysql_importer.py:50
   发现硬编码敏感信息: password='test_password'

🟡 **no_default_value** - tests/unit/test_simple_config.py:161
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/unit/test_simple_config.py:172
   环境变量 CI 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/unit/test_simple_config.py:219
   环境变量 AQUA_LOG_LEVEL 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/unit/test_simple_config.py:220
   环境变量 AQUA_DEBUG 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/unit/test_paths.py:35
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/unit/test_paths.py:37
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/unit/test_paths.py:74
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/unit/test_paths.py:90
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/integration/conftest.py:22
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - tests/integration/conftest.py:23
   环境变量 AQUA_TEST_MODE 缺少默认值，可能导致KeyError

🔴 **hardcoded_token** - logs/tests/backup_20250804_110259/config_backup/settings.toml:169
   发现硬编码敏感信息: token = "${TUSHARE_TOKEN}"

🔴 **hardcoded_token** - scripts/create_kanban_project.py:257
   发现硬编码敏感信息: token = "your_gitee_token"

🟡 **no_default_value** - scripts/audit_environment_variables.py:28
   环境变量 VAR 缺少默认值，可能导致KeyError

🟡 **no_default_value** - scripts/audit_environment_variables.py:123
   环境变量 VAR 缺少默认值，可能导致KeyError

🔴 **hardcoded_token** - scripts/dev_setup.sh:42
   发现硬编码敏感信息: TOKEN='your_token_here'

🟡 **no_default_value** - scripts/env_init.py:1729
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - scripts/env_init.py:1742
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - scripts/env_init.py:1748
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🔴 **hardcoded_password** - src/config/data_source_config.py:110
   发现硬编码敏感信息: PASSWORD='your_password'

🔴 **hardcoded_token** - src/config/data_source_config.py:95
   发现硬编码敏感信息: TOKEN='your_token_here'

🟡 **no_default_value** - src/utils/service_manager.py:90
   环境变量 PYTHONIOENCODING 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/utils/service_manager.py:91
   环境变量 PYTHONUNBUFFERED 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/utils/simple_config.py:35
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/utils/simple_config.py:41
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/utils/simple_config.py:43
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/utils/simple_config.py:59
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/utils/simple_config.py:65
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/utils/simple_config.py:67
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🔴 **hardcoded_token** - src/cli/commands/collect.py:93
   发现硬编码敏感信息: TOKEN='your_token_here'

🔴 **hardcoded_password** - src/cli/commands/init.py:161
   发现硬编码敏感信息: password = "${MYSQL_PASSWORD}"

🔴 **hardcoded_token** - src/cli/commands/init.py:117
   发现硬编码敏感信息: token = "${TUSHARE_TOKEN}"

🔴 **hardcoded_token** - src/cli/commands/init.py:151
   发现硬编码敏感信息: token = "${TUSHARE_TOKEN}"

🟡 **no_default_value** - src/aqua/main.py:109
   环境变量 PYTHONUTF8 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/aqua/cli/windows_compat.py:82
   环境变量 PYTHONUTF8 缺少默认值，可能导致KeyError

🟡 **no_default_value** - src/aqua/cli/windows_compat.py:83
   环境变量 PYTHONIOENCODING 缺少默认值，可能导致KeyError

🟡 **no_default_value** - deprecated/start.py:33
   环境变量 PYTHONIOENCODING 缺少默认值，可能导致KeyError

🟡 **no_default_value** - deprecated/start.py:34
   环境变量 PYTHONUTF8 缺少默认值，可能导致KeyError

🟡 **no_default_value** - deprecated/env_init.py:1690
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - deprecated/env_init.py:1703
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

🟡 **no_default_value** - deprecated/env_init.py:1709
   环境变量 AQUA_ENV 缺少默认值，可能导致KeyError

## 💡 改进建议
🟢 **naming_prefix**: 建议为环境变量 NODE_ENV 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 HOME 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 USERPROFILE 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 TUSHARE_TOKEN 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 CI 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PATH 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PSV 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 P 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 T 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 L 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_convention**: 环境变量 _ 应该使用大写字母
🟢 **naming_prefix**: 建议为环境变量 _ 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 E 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 UPSTASH_VECTOR_REST_URL 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 BACKUP_DIR 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PROJECT_ROOT 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 LOG_PATH 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 SETTINGS_PATH 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 FRONTEND_HOST 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 FRONTEND_PORT 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 INTERVAL 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 VAR 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 BACKUP_FILE 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 TIMESTAMP 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 DB_PATH 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 DB_BAK_NAME 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 DB_BAK_TARGET 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 FRONTEND_NAME 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 FRONTEND_TARGET 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 TEST_MODE 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 SCRIPT_DIR 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 BASE_DIR 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 ENV 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 FRONTEND_PID 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 BACKEND_PID 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 BLUE 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 NC 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 GREEN 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 YELLOW 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 RED 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 VENV_PYTHON 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 BACKEND_NAME 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 BACKEND_TARGET 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 CSV_DATA_PATH 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 MYSQL_HOST 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 MYSQL_PORT 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 MYSQL_USER 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 MYSQL_PASSWORD 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 MYSQL_DATABASE 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PYTHONIOENCODING 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PYTHONUNBUFFERED 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 NO_COLOR 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 FORCE_COLOR 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 TERM 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 JUPYTER_RUNTIME_DIR 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 LOG_LEVEL 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 DEBUG 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PYTEST_CURRENT_TEST 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 VIRTUAL_ENV 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 SHELL 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_convention**: 环境变量 fpath 应该使用大写字母
🟢 **naming_prefix**: 建议为环境变量 fpath 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 COMP_CWORD 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PYTHONUTF8 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 USERNAME 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PROFILE 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 O 添加项目前缀 (AQUA_ 或 VITE_)
🟢 **naming_prefix**: 建议为环境变量 PYTHON_EXE 添加项目前缀 (AQUA_ 或 VITE_)
