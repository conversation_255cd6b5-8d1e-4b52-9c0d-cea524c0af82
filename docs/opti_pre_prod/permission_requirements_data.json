{"platform_info": {"system": "<PERSON>", "version": "Darwin Kernel Version 21.6.0: Mon Jun 24 00:56:10 PDT 2024; root:xnu-8020.240.18.709.2~1/RELEASE_X86_64", "architecture": ["64bit", ""], "user_info": {"username": "<PERSON><PERSON><PERSON>", "uid": 501, "gid": 20, "groups": ["staff", "everyone", "localaccounts", "_appserverusr", "admin", "_appserveradm", "_lpadmin", "access_bpf", "_appstore", "_lpoperator", "_developer", "_analyticsusers", "com.apple.access_ftp", "com.apple.access_screensharing", "com.apple.access_ssh", "com.apple.access_remote_ae", "com.apple.sharepoint.group.1"], "is_admin": true}, "permission_model": {"type": "POSIX", "supports_unix_permissions": true, "supports_file_attributes": true, "supports_inheritance": false, "default_umask": "0o22"}, "security_features": ["SIP", "Gatekeeper", "Code_Signing", "Sandboxing", "POSIX_Permissions"]}, "directory_permissions": {".": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA", "exists": true, "is_directory": true, "permissions": {"octal": "0o700", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": false, "group_write": false, "group_execute": false, "other_read": false, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "src": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/src", "exists": true, "is_directory": true, "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "config": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/config", "exists": true, "is_directory": true, "permissions": {"octal": "0o700", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": false, "group_write": false, "group_execute": false, "other_read": false, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "data": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/data", "exists": true, "is_directory": true, "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "logs": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/logs", "exists": true, "is_directory": true, "permissions": {"octal": "0o700", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": false, "group_write": false, "group_execute": false, "other_read": false, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "cache": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/cache", "exists": true, "is_directory": true, "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "data/backup": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/data/backup", "exists": true, "is_directory": true, "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "tests": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/tests", "exists": true, "is_directory": true, "permissions": {"octal": "0o700", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": false, "group_write": false, "group_execute": false, "other_read": false, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "docs": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/docs", "exists": true, "is_directory": true, "permissions": {"octal": "0o700", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": false, "group_write": false, "group_execute": false, "other_read": false, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}, "scripts": {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/scripts", "exists": true, "is_directory": true, "permissions": {"octal": "0o700", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": false, "group_write": false, "group_execute": false, "other_read": false, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "issues": [], "recommendations": []}}, "file_permissions": {"src/aqua/main.py": {"path": "src/aqua/main.py", "exists": true, "is_file": true, "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "is_executable": false, "issues": [], "recommendations": []}, "src/cli/main.py": {"path": "src/cli/main.py", "exists": true, "is_file": true, "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "is_executable": false, "issues": [], "recommendations": []}, "scripts/env_init.py": {"path": "scripts/env_init.py", "exists": true, "is_file": true, "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "is_executable": false, "issues": [], "recommendations": []}, "config/settings.toml": {"path": "config/settings.toml", "exists": true, "is_file": true, "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "is_executable": false, "issues": ["敏感配置文件对其他用户可读"], "recommendations": ["限制文件权限为600 (仅所有者可读写)"]}, "requirements.txt": {"path": "requirements.txt", "exists": true, "is_file": true, "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "is_executable": false, "issues": [], "recommendations": []}, "package.json": {"path": "package.json", "exists": true, "is_file": true, "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "owner": {"uid": 501, "gid": 20, "username": "<PERSON><PERSON><PERSON>", "groupname": "staff"}, "is_executable": false, "issues": [], "recommendations": []}}, "executable_permissions": {"demo_taskqueue.py": {"path": "demo_taskqueue.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "aqua.py": {"path": "aqua.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "test_cli_database.py": {"path": "test_cli_database.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "test_import.py": {"path": "test_import.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "main.py": {"path": "main.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "test_database_path_fix.py": {"path": "test_database_path_fix.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_ai_mvp_simple.py": {"path": "tests/test_ai_mvp_simple.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_config_cross_platform.py": {"path": "tests/test_config_cross_platform.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_incremental_collection_helper.py": {"path": "tests/test_incremental_collection_helper.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_hardcoded_path_replacement.py": {"path": "tests/test_hardcoded_path_replacement.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_cross_platform_paths.py": {"path": "tests/test_cross_platform_paths.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/__init__.py": {"path": "tests/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_env_manager.py": {"path": "tests/test_env_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_trading_calendar_manager.py": {"path": "tests/test_trading_calendar_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_settings_config_values.py": {"path": "tests/test_settings_config_values.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_points_calculator.py": {"path": "tests/test_points_calculator.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_ai_mvp.py": {"path": "tests/test_ai_mvp.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_ai_mvp_standalone.py": {"path": "tests/test_ai_mvp_standalone.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_cross_platform_env_vars.py": {"path": "tests/test_cross_platform_env_vars.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_config_validator_functions.py": {"path": "tests/test_config_validator_functions.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_database_config_fix.py": {"path": "tests/test_database_config_fix.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_rate_limiter.py": {"path": "tests/test_rate_limiter.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/test_tushare_extractor.py": {"path": "tests/test_tushare_extractor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_performance_monitor.py": {"path": "tests/unit/test_performance_monitor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_anomaly_detection_engine.py": {"path": "tests/unit/test_anomaly_detection_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_cache_manager.py": {"path": "tests/unit/test_cache_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_duckdb_init_check.py": {"path": "tests/unit/test_duckdb_init_check.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_cli_ui.py": {"path": "tests/unit/test_cli_ui.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_performance_optimizer.py": {"path": "tests/unit/test_performance_optimizer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_env_detector.py": {"path": "tests/unit/test_env_detector.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_performance_router.py": {"path": "tests/unit/test_performance_router.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_smart_routing_engine.py": {"path": "tests/unit/test_smart_routing_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_config_loader.py": {"path": "tests/unit/test_config_loader.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_csv_importer_unified.py": {"path": "tests/unit/test_csv_importer_unified.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_ai_task_manager.py": {"path": "tests/unit/test_ai_task_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_user_adapter.py": {"path": "tests/unit/test_user_adapter.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_conflict_resolution_engine.py": {"path": "tests/unit/test_conflict_resolution_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_unified_api.py": {"path": "tests/unit/test_unified_api.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_unified_storage_manager.py": {"path": "tests/unit/test_unified_storage_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_data_quality_controller.py": {"path": "tests/unit/test_data_quality_controller.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_nlp_query_processor.py": {"path": "tests/unit/test_nlp_query_processor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_dep_manager.py": {"path": "tests/unit/test_dep_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_task_queue_engine.py": {"path": "tests/unit/test_task_queue_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_cli_framework.py": {"path": "tests/unit/test_cli_framework.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_simple_error.py": {"path": "tests/unit/test_simple_error.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_mysql_importer.py": {"path": "tests/unit/test_mysql_importer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_exceptions.py": {"path": "tests/unit/test_exceptions.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/__init__.py": {"path": "tests/unit/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_data_mapping_engine.py": {"path": "tests/unit/test_data_mapping_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_connection_manager.py": {"path": "tests/unit/test_connection_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_data_router.py": {"path": "tests/unit/test_data_router.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_strategy_optimizer.py": {"path": "tests/unit/test_strategy_optimizer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_interactive_features.py": {"path": "tests/unit/test_interactive_features.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_aqua_cli.py": {"path": "tests/unit/test_aqua_cli.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_init_helpers_refactored.py": {"path": "tests/unit/test_init_helpers_refactored.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_aqua_cli_enhanced.py": {"path": "tests/unit/test_aqua_cli_enhanced.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_time_utils.py": {"path": "tests/unit/test_time_utils.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_simple_config.py": {"path": "tests/unit/test_simple_config.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_paths.py": {"path": "tests/unit/test_paths.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_init_helpers.py": {"path": "tests/unit/test_init_helpers.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_intelligent_report_generator.py": {"path": "tests/unit/test_intelligent_report_generator.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_tushare_data_processor.py": {"path": "tests/unit/test_tushare_data_processor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_collect_command.py": {"path": "tests/unit/test_collect_command.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_env_init.py": {"path": "tests/unit/test_env_init.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_unified_csv_importer.py": {"path": "tests/unit/test_unified_csv_importer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_service_manager.py": {"path": "tests/unit/test_service_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_logger.py": {"path": "tests/unit/test_logger.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/unit/test_math_utils.py": {"path": "tests/unit/test_math_utils.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_data_import_integration.py": {"path": "tests/integration/test_data_import_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_data_processor_enhanced.py": {"path": "tests/integration/test_data_processor_enhanced.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/conftest.py": {"path": "tests/integration/conftest.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_unified_import_system.py": {"path": "tests/integration/test_unified_import_system.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_csv_layer_extractor.py": {"path": "tests/integration/test_csv_layer_extractor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/__init__.py": {"path": "tests/integration/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_performance_optimization_integration.py": {"path": "tests/integration/test_performance_optimization_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_file_status_tracker.py": {"path": "tests/integration/test_file_status_tracker.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_cross_platform.py": {"path": "tests/integration/test_cross_platform.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_f6_cross_platform.py": {"path": "tests/integration/test_f6_cross_platform.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_real_datasource_integration.py": {"path": "tests/integration/test_real_datasource_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_duckdb_layered_architecture.py": {"path": "tests/integration/test_duckdb_layered_architecture.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_aqua_cli_integration.py": {"path": "tests/integration/test_aqua_cli_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_database_integration.py": {"path": "tests/integration/test_database_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_windows_environment_plan.py": {"path": "tests/integration/test_windows_environment_plan.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_f6_real_integration.py": {"path": "tests/integration/test_f6_real_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_cli_integration.py": {"path": "tests/integration/test_cli_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_data_center_integration.py": {"path": "tests/integration/test_data_center_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_tushare_incremental.py": {"path": "tests/integration/test_tushare_incremental.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_collect_service_dataprocessor.py": {"path": "tests/integration/test_collect_service_dataprocessor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_aqua_optimization.py": {"path": "tests/integration/test_aqua_optimization.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_end_to_end_layered_import.py": {"path": "tests/integration/test_end_to_end_layered_import.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_cli.py": {"path": "tests/integration/test_cli.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_data_import_manager.py": {"path": "tests/integration/test_data_import_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_f6_performance.py": {"path": "tests/integration/test_f6_performance.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/integration/test_cli_real_data_end_to_end.py": {"path": "tests/integration/test_cli_real_data_end_to_end.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/api/routers/test_data_router_import_history.py": {"path": "tests/api/routers/test_data_router_import_history.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/api/routers/test_websocket_integration.py": {"path": "tests/api/routers/test_websocket_integration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/fixtures/__init__.py": {"path": "tests/fixtures/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "tests/fixtures/csv_test_data.py": {"path": "tests/fixtures/csv_test_data.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/database_init_complete.py": {"path": "scripts/database_init_complete.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/run_tests.py": {"path": "scripts/run_tests.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/backup_data.py": {"path": "scripts/backup_data.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/standardize_env_variables.py": {"path": "scripts/standardize_env_variables.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/reset_database.py": {"path": "scripts/reset_database.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/create_kanban_project.py": {"path": "scripts/create_kanban_project.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/quick_start.py": {"path": "scripts/quick_start.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/audit_environment_variables.py": {"path": "scripts/audit_environment_variables.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/demo_sync_flow.py": {"path": "scripts/demo_sync_flow.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/sync_to_gitee.py": {"path": "scripts/sync_to_gitee.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/demo_windows_init.py": {"path": "scripts/demo_windows_init.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/one_click_sync.py": {"path": "scripts/one_click_sync.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/create_unified_tables_v4.py": {"path": "scripts/create_unified_tables_v4.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/migration_cli.py": {"path": "scripts/migration_cli.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/env_init.py": {"path": "scripts/env_init.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/analyze_permission_requirements.py": {"path": "scripts/analyze_permission_requirements.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/fix_windows_placeholder_dirs.py": {"path": "scripts/fix_windows_placeholder_dirs.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/sync_via_branch.py": {"path": "scripts/sync_via_branch.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/run_integration_tests.py": {"path": "scripts/run_integration_tests.py", "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "is_executable": true, "should_be_executable": true, "issues": [], "recommendations": []}, "scripts/china_network_optimizer.py": {"path": "scripts/china_network_optimizer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/sync_from_gitee.py": {"path": "scripts/sync_from_gitee.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/common/health_utils.py": {"path": "scripts/common/health_utils.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/common/cli_utils.py": {"path": "scripts/common/cli_utils.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/common/__init__.py": {"path": "scripts/common/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/common/progress_utils.py": {"path": "scripts/common/progress_utils.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "templates/claude/test_templates/unit_test_template.py": {"path": "templates/claude/test_templates/unit_test_template.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "templates/claude/error_patterns/error_handling_decorators.py": {"path": "templates/claude/error_patterns/error_handling_decorators.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "templates/claude/implementation_templates/class_template.py": {"path": "templates/claude/implementation_templates/class_template.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/__init__.py": {"path": "src/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/database/__init__.py": {"path": "src/database/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/database/data_dictionary_schema.py": {"path": "src/database/data_dictionary_schema.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/database/duckdb_init_check.py": {"path": "src/database/duckdb_init_check.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/database/connection_manager.py": {"path": "src/database/connection_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/database/performance_optimizer.py": {"path": "src/database/performance_optimizer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/core/__init__.py": {"path": "src/core/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/core/task_queue_engine.py": {"path": "src/core/task_queue_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cache/cache_manager.py": {"path": "src/cache/cache_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/config/data_source_config.py": {"path": "src/config/data_source_config.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/tushare/points_calculator.py": {"path": "src/tushare/points_calculator.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/tushare/rate_limiter.py": {"path": "src/tushare/rate_limiter.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/tushare/__init__.py": {"path": "src/tushare/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/dep_manager.py": {"path": "src/utils/dep_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/config_loader.py": {"path": "src/utils/config_loader.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/smart_mirror_manager.py": {"path": "src/utils/smart_mirror_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/math_utils.py": {"path": "src/utils/math_utils.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/paths.py": {"path": "src/utils/paths.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/env_manager.py": {"path": "src/utils/env_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/config_validator.py": {"path": "src/utils/config_validator.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/mysql_config_manager.py": {"path": "src/utils/mysql_config_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/service_manager.py": {"path": "src/utils/service_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/performance_monitor.py": {"path": "src/utils/performance_monitor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/__init__.py": {"path": "src/utils/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/time_utils.py": {"path": "src/utils/time_utils.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/logger.py": {"path": "src/utils/logger.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/cli_ui.py": {"path": "src/utils/cli_ui.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/simple_error.py": {"path": "src/utils/simple_error.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/simple_config.py": {"path": "src/utils/simple_config.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/exceptions.py": {"path": "src/utils/exceptions.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/env_detector.py": {"path": "src/utils/env_detector.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/user_adapter.py": {"path": "src/utils/user_adapter.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/utils/config_loader_v2.py": {"path": "src/utils/config_loader_v2.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/__init__.py": {"path": "src/cli/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/main.py": {"path": "src/cli/main.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/utils/themes.py": {"path": "src/cli/utils/themes.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/utils/formatters.py": {"path": "src/cli/utils/formatters.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/utils/completion.py": {"path": "src/cli/utils/completion.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/utils/__init__.py": {"path": "src/cli/utils/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/utils/platform.py": {"path": "src/cli/utils/platform.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/utils/progress.py": {"path": "src/cli/utils/progress.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/utils/colors.py": {"path": "src/cli/utils/colors.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/commands/collect.py": {"path": "src/cli/commands/collect.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/commands/config.py": {"path": "src/cli/commands/config.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/commands/__init__.py": {"path": "src/cli/commands/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/commands/export.py": {"path": "src/cli/commands/export.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/commands/init.py": {"path": "src/cli/commands/init.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/commands/analyze.py": {"path": "src/cli/commands/analyze.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/commands/status.py": {"path": "src/cli/commands/status.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/services/wizard_service.py": {"path": "src/cli/services/wizard_service.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/services/status_service.py": {"path": "src/cli/services/status_service.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/services/__init__.py": {"path": "src/cli/services/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/cli/services/collect_service.py": {"path": "src/cli/services/collect_service.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/storage/unified_storage_manager.py": {"path": "src/storage/unified_storage_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/storage/priority_queue_manager.py": {"path": "src/storage/priority_queue_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/storage/__init__.py": {"path": "src/storage/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/storage/conflict_resolution_engine.py": {"path": "src/storage/conflict_resolution_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/ai_agent/nlp_query_processor.py": {"path": "src/ai_agent/nlp_query_processor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/ai_agent/ai_strategy.py": {"path": "src/ai_agent/ai_strategy.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/ai_agent/__init__.py": {"path": "src/ai_agent/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/ai_agent/ai_task_manager.py": {"path": "src/ai_agent/ai_task_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/ai_agent/report_generator.py": {"path": "src/ai_agent/report_generator.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/ai_agent/anomaly_detection_engine.py": {"path": "src/ai_agent/anomaly_detection_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/mysql_importer_logic.py": {"path": "src/data_import/mysql_importer_logic.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/data_processor.py": {"path": "src/data_import/data_processor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/unified_csv_importer.py": {"path": "src/data_import/unified_csv_importer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/table_migration.py": {"path": "src/data_import/table_migration.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/__init__.py": {"path": "src/data_import/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/task_control_manager.py": {"path": "src/data_import/task_control_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/trading_calendar_manager.py": {"path": "src/data_import/trading_calendar_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/mysql_importer.py": {"path": "src/data_import/mysql_importer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/incremental_collection_helper.py": {"path": "src/data_import/incremental_collection_helper.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/fromc2c_importer_logic.py": {"path": "src/data_import/fromc2c_importer_logic.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/csv_importer.py": {"path": "src/data_import/csv_importer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/import_history_manager.py": {"path": "src/data_import/import_history_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/fromC2C_csv_main_contract_importer.py": {"path": "src/data_import/fromC2C_csv_main_contract_importer.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/websocket_manager.py": {"path": "src/data_import/websocket_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/status/__init__.py": {"path": "src/data_import/status/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/status/file_tracker.py": {"path": "src/data_import/status/file_tracker.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/field_mapper.py": {"path": "src/data_import/processors/field_mapper.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/data_quality_controller.py": {"path": "src/data_import/processors/data_quality_controller.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/data_mapping_engine.py": {"path": "src/data_import/processors/data_mapping_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/data_validator.py": {"path": "src/data_import/processors/data_validator.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/quality_metrics.py": {"path": "src/data_import/processors/quality_metrics.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/tushare_data_processor.py": {"path": "src/data_import/processors/tushare_data_processor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/hybrid_rule_engine.py": {"path": "src/data_import/processors/hybrid_rule_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/processors/type_converter.py": {"path": "src/data_import/processors/type_converter.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/mappers/business_table_mapper.py": {"path": "src/data_import/mappers/business_table_mapper.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/mappers/__init__.py": {"path": "src/data_import/mappers/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/mappers/data_dictionary_mapper.py": {"path": "src/data_import/mappers/data_dictionary_mapper.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/validators/__init__.py": {"path": "src/data_import/validators/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/validators/csv_validator.py": {"path": "src/data_import/validators/csv_validator.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/extractors/simple_extractor.py": {"path": "src/data_import/extractors/simple_extractor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/extractors/simple_extractor_factory.py": {"path": "src/data_import/extractors/simple_extractor_factory.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/extractors/csv_layer_extractor.py": {"path": "src/data_import/extractors/csv_layer_extractor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/extractors/tushare_extractor.py": {"path": "src/data_import/extractors/tushare_extractor.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/extractors/__init__.py": {"path": "src/data_import/extractors/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/loaders/data_loader.py": {"path": "src/data_import/loaders/data_loader.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/data_import/loaders/__init__.py": {"path": "src/data_import/loaders/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/config.py": {"path": "src/aqua/config.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/__init__.py": {"path": "src/aqua/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/main.py": {"path": "src/aqua/main.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/init_helpers.py": {"path": "src/aqua/cli/init_helpers.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/dev_tools.py": {"path": "src/aqua/cli/dev_tools.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/mirror_command.py": {"path": "src/aqua/cli/mirror_command.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/personal_kanban_command.py": {"path": "src/aqua/cli/personal_kanban_command.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/service_manager.py": {"path": "src/aqua/cli/service_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/__init__.py": {"path": "src/aqua/cli/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/setup_wizard.py": {"path": "src/aqua/cli/setup_wizard.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/windows_compat.py": {"path": "src/aqua/cli/windows_compat.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/enhanced_ui.py": {"path": "src/aqua/cli/enhanced_ui.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/health_checker.py": {"path": "src/aqua/cli/health_checker.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/aqua/cli/task_queue_command.py": {"path": "src/aqua/cli/task_queue_command.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/__init__.py": {"path": "src/api/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/main.py": {"path": "src/api/main.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/routers/data_router.py": {"path": "src/api/routers/data_router.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/routers/__init__.py": {"path": "src/api/routers/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/routers/performance_router.py": {"path": "src/api/routers/performance_router.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/routers/unified_data_router.py": {"path": "src/api/routers/unified_data_router.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/models/__init__.py": {"path": "src/api/models/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/api/models/data_models.py": {"path": "src/api/models/data_models.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/routing/__init__.py": {"path": "src/routing/__init__.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/routing/smart_routing_engine.py": {"path": "src/routing/smart_routing_engine.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "src/routing/routing_strategy.py": {"path": "src/routing/routing_strategy.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "deprecated/start.py": {"path": "deprecated/start.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "deprecated/env_init.py": {"path": "deprecated/env_init.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "deprecated/entry_files/start_services.py": {"path": "deprecated/entry_files/start_services.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "deprecated/data_import_legacy/scripts/data_import_manager.py": {"path": "deprecated/data_import_legacy/scripts/data_import_manager.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "deprecated/data_import_legacy/scripts/fromC2C_import_cli.py": {"path": "deprecated/data_import_legacy/scripts/fromC2C_import_cli.py", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "logs/tests/rollback_script.sh": {"path": "logs/tests/rollback_script.sh", "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "is_executable": true, "should_be_executable": true, "issues": [], "recommendations": []}, "scripts/monitor_frontend.sh": {"path": "scripts/monitor_frontend.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/restore_backend.sh": {"path": "scripts/restore_backend.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/dev_setup.sh": {"path": "scripts/dev_setup.sh", "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "is_executable": true, "should_be_executable": true, "issues": [], "recommendations": []}, "scripts/restore_frontend.sh": {"path": "scripts/restore_frontend.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/backup_database.sh": {"path": "scripts/backup_database.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/backup_frontend.sh": {"path": "scripts/backup_frontend.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/restore_database.sh": {"path": "scripts/restore_database.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/deploy.sh": {"path": "scripts/deploy.sh", "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "is_executable": true, "should_be_executable": true, "issues": [], "recommendations": []}, "scripts/switch-precommit-mode.sh": {"path": "scripts/switch-precommit-mode.sh", "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "is_executable": true, "should_be_executable": true, "issues": [], "recommendations": []}, "scripts/env_diagnose.sh": {"path": "scripts/env_diagnose.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "scripts/backup_backend.sh": {"path": "scripts/backup_backend.sh", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": true, "issues": ["应该可执行但不可执行"], "recommendations": ["添加执行权限"]}, "deprecated/entry_files/aqua.sh": {"path": "deprecated/entry_files/aqua.sh", "permissions": {"octal": "0o755", "readable": true, "writable": true, "executable": true, "owner_read": true, "owner_write": true, "owner_execute": true, "group_read": true, "group_write": false, "group_execute": true, "other_read": true, "other_write": false, "other_execute": true}, "is_executable": true, "should_be_executable": true, "issues": [], "recommendations": []}, "aqua.bat": {"path": "aqua.bat", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": false, "issues": [], "recommendations": []}, "scripts/dev_setup.bat": {"path": "scripts/dev_setup.bat", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": false, "issues": [], "recommendations": []}, "scripts/deploy.bat": {"path": "scripts/deploy.bat", "permissions": {"octal": "0o644", "readable": true, "writable": true, "executable": false, "owner_read": true, "owner_write": true, "owner_execute": false, "group_read": true, "group_write": false, "group_execute": false, "other_read": true, "other_write": false, "other_execute": false}, "is_executable": false, "should_be_executable": false, "issues": [], "recommendations": []}}, "network_permissions": {"required_ports": [8000, 3306], "outbound_connections": [{"host": "api.tushare.pro", "port": 443, "protocol": "HTTPS", "purpose": "Tushare数据API"}, {"host": "pypi.org", "port": 443, "protocol": "HTTPS", "purpose": "Python包下载"}, {"host": "registry.npmjs.org", "port": 443, "protocol": "HTTPS", "purpose": "NPM包下载"}], "firewall_requirements": [], "issues": [], "recommendations": []}, "system_permissions": {"file_system_access": [{"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/data", "access": "read/write", "purpose": "数据存储"}, {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/logs", "access": "read/write", "purpose": "日志记录"}, {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/cache", "access": "read/write", "purpose": "缓存存储"}, {"path": "/Users/<USER>/Documents/AQUA/Dev/AQUA/data/backup", "access": "read/write", "purpose": "备份存储"}], "process_permissions": [{"permission": "create_process", "purpose": "启动子进程"}, {"permission": "network_bind", "purpose": "绑定网络端口"}, {"permission": "file_lock", "purpose": "文件锁定"}], "registry_access": [], "service_permissions": [], "issues": [], "recommendations": []}, "permission_issues": [], "recommendations": []}