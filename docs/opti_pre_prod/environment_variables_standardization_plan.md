# AQUA项目环境变量标准化计划
生成时间: 2025-08-05 07:39:17

## 📊 标准化摘要
- 发现的环境变量总数: 89
- 需要标准化的变量: 66
- 受影响的文件数: 168

## 🔧 标准化行动
### 1. TUSHARE_TOKEN → AQUA_TUSHARE_TOKEN
**原因**: 预定义标准化规则
**受影响文件**: 26 个
**文件列表**:
- docs/PRODUCTION_READY_REFACTOR_PLAN.md
- logs/tests/OSX_DEEP_TEST_PLAN.md
- src/aqua/cli/health_checker.py
- tests/test_settings_config_values.py
- src/utils/config_validator.py
- ... 还有 21 个文件

### 2. TEST_SET_VAR → AQUA_TEST_SET_VAR
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- tests/test_env_manager.py

### 3. TEST_CONVENIENCE → AQUA_TEST_CONVENIENCE
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- tests/test_env_manager.py

### 4. REQUIRED_VAR → VITE_REQUIRED_VAR
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- tests/test_env_manager.py

### 5. TEST_LIST → AQUA_TEST_LIST
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- tests/test_env_manager.py

### 6. GREEN → AQUA_GREEN
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- scripts/switch-precommit-mode.sh
- docs/opti_pre_prod/environment_variables_audit_data.json
- docs/PRECOMMIT_OPTIMIZATION.md

### 7. NC → AQUA_NC
**原因**: 变量名过短，不符合命名规范
**受影响文件**: 3 个
**文件列表**:
- scripts/switch-precommit-mode.sh
- docs/opti_pre_prod/environment_variables_audit_data.json
- docs/PRECOMMIT_OPTIMIZATION.md

### 8. YELLOW → AQUA_YELLOW
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- scripts/switch-precommit-mode.sh
- docs/opti_pre_prod/environment_variables_audit_data.json
- docs/PRECOMMIT_OPTIMIZATION.md

### 9. SENSITIVE_PATTERNS → AQUA_SENSITIVE_PATTERNS
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/PRECOMMIT_OPTIMIZATION.md

### 10. RED → AQUA_RED
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- scripts/switch-precommit-mode.sh
- docs/opti_pre_prod/environment_variables_audit_data.json
- docs/PRECOMMIT_OPTIMIZATION.md

### 11. PYTHONPATH → AQUA_PYTHONPATH
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/claude_tools_guide.md

### 12. BACKUP_DATE → AQUA_BACKUP_DATE
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/gitee.md

### 13. REPO_URL → AQUA_REPO_URL
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/gitee.md

### 14. SSH_URL → AQUA_SSH_URL
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/gitee.md

### 15. LOG_FILE → AQUA_LOG_FILE
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/gitee.md

### 16. TIMESTAMP → AQUA_TIMESTAMP
**原因**: 缺少项目前缀
**受影响文件**: 6 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- scripts/backup_database.sh
- scripts/backup_frontend.sh
- docs/tasks/Dev_Taks_Epic0_Prompt.md
- docs/gitee.md
- ... 还有 1 个文件

### 17. COMMIT_HASH → AQUA_COMMIT_HASH
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/gitee.md

### 18. REUSE_STATUS → AQUA_REUSE_STATUS
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/gitee.md

### 19. FEATURE → AQUA_FEATURE
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/FAQ.md

### 20. PORT → AQUA_PORT
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/FAQ.md

### 21. DEBUG → AQUA_DEBUG
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- docs/FAQ.md
- src/utils/simple_config.py

### 22. MYSQL_PASSWORD → AQUA_MYSQL_PASSWORD
**原因**: 缺少项目前缀
**受影响文件**: 5 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- docs/opti_pre_prod/environment_variables_audit_report.md
- docs/kanban/看板初始化启动前的重构.md
- src/cli/commands/init.py
- src/config/data_source_config.py

### 23. TEST_MODE → AQUA_TEST_MODE
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- scripts/env_init.py
- deprecated/env_init.py

### 24. NO_COLOR → AQUA_NO_COLOR
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- src/cli/utils/platform.py
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/utils/cli_ui.py

### 25. FORCE_COLOR → AQUA_FORCE_COLOR
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- src/cli/utils/platform.py
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/utils/cli_ui.py

### 26. SHELL → AQUA_SHELL
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/cli/utils/completion.py

### 27. fpath → AQUA_fpath
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/cli/utils/completion.py

### 28. PYTHONIOENCODING → AQUA_PYTHONIOENCODING
**原因**: 缺少项目前缀
**受影响文件**: 4 个
**文件列表**:
- src/aqua/cli/windows_compat.py
- src/utils/service_manager.py
- docs/opti_pre_prod/environment_variables_audit_data.json
- deprecated/start.py

### 29. PYTHONUTF8 → AQUA_PYTHONUTF8
**原因**: 缺少项目前缀
**受影响文件**: 5 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/aqua/main.py
- docs/handbook/InitStartCLI/troubleshooting.md
- src/aqua/cli/windows_compat.py
- deprecated/start.py

### 30. PYTHONUNBUFFERED → AQUA_PYTHONUNBUFFERED
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- src/utils/service_manager.py
- docs/opti_pre_prod/environment_variables_audit_data.json

### 31. CSV_DATA_PATH → AQUA_CSV_DATA_PATH
**原因**: 预定义标准化规则
**受影响文件**: 4 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/aqua/cli/setup_wizard.py
- src/config/data_source_config.py
- src/aqua/cli/health_checker.py

### 32. MYSQL_HOST → AQUA_MYSQL_HOST
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- docs/kanban/看板初始化启动前的重构.md
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/config/data_source_config.py

### 33. MYSQL_PORT → AQUA_MYSQL_PORT
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- docs/kanban/看板初始化启动前的重构.md
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/config/data_source_config.py

### 34. MYSQL_USER → AQUA_MYSQL_USER
**原因**: 缺少项目前缀
**受影响文件**: 4 个
**文件列表**:
- docs/kanban/看板初始化启动前的重构.md
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/config/data_source_config.py
- src/cli/commands/init.py

### 35. MYSQL_DATABASE → AQUA_MYSQL_DATABASE
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- docs/kanban/看板初始化启动前的重构.md
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/config/data_source_config.py

### 36. VIRTUAL_ENV → AQUA_VIRTUAL_ENV
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- src/utils/env_detector.py
- docs/opti_pre_prod/environment_variables_audit_data.json

### 37. USERNAME → AQUA_USERNAME
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- src/aqua/cli/windows_compat.py
- docs/opti_pre_prod/environment_variables_audit_data.json

### 38. VAR → AQUA_VAR
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- src/utils/config_validator.py
- docs/opti_pre_prod/environment_variables_audit_data.json
- scripts/audit_environment_variables.py

### 39. BLUE → AQUA_BLUE
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- scripts/switch-precommit-mode.sh
- docs/opti_pre_prod/environment_variables_audit_data.json

### 40. COMP_CWORD → AQUA_COMP_CWORD
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- docs/opti_pre_prod/environment_variables_audit_data.json
- src/cli/utils/completion.py

### 41. ENVIRONMENT → AQUA_ENVIRONMENT
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/handbook/InitStartCLI/best_practices.md

### 42. VERSION → AQUA_VERSION
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/handbook/InitStartCLI/best_practices.md

### 43. DEPLOY_TIME → AQUA_DEPLOY_TIME
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- docs/handbook/InitStartCLI/best_practices.md

### 44. BACKUP_DIR → AQUA_BACKUP_DIR
**原因**: 预定义标准化规则
**受影响文件**: 4 个
**文件列表**:
- scripts/backup_backend.sh
- logs/tests/rollback_script.sh
- scripts/backup_frontend.sh
- scripts/backup_database.sh

### 45. PROJECT_ROOT → AQUA_PROJECT_ROOT
**原因**: 预定义标准化规则
**受影响文件**: 7 个
**文件列表**:
- scripts/restore_backend.sh
- scripts/backup_database.sh
- scripts/backup_frontend.sh
- scripts/restore_database.sh
- scripts/restore_frontend.sh
- ... 还有 2 个文件

### 46. SETTINGS_PATH → AQUA_SETTINGS_PATH
**原因**: 预定义标准化规则
**受影响文件**: 7 个
**文件列表**:
- scripts/restore_backend.sh
- scripts/backup_database.sh
- scripts/backup_frontend.sh
- scripts/restore_database.sh
- scripts/restore_frontend.sh
- ... 还有 2 个文件

### 47. LOG_PATH → AQUA_LOG_PATH
**原因**: 预定义标准化规则
**受影响文件**: 7 个
**文件列表**:
- scripts/restore_backend.sh
- scripts/backup_database.sh
- scripts/backup_frontend.sh
- scripts/restore_database.sh
- scripts/restore_frontend.sh
- ... 还有 2 个文件

### 48. FRONTEND_PORT → AQUA_FRONTEND_PORT
**原因**: 预定义标准化规则
**受影响文件**: 1 个
**文件列表**:
- scripts/monitor_frontend.sh

### 49. FRONTEND_HOST → AQUA_FRONTEND_HOST
**原因**: 预定义标准化规则
**受影响文件**: 1 个
**文件列表**:
- scripts/monitor_frontend.sh

### 50. INTERVAL → AQUA_INTERVAL
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/monitor_frontend.sh

### 51. {standard} → AQUA_{standard}
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/standardize_env_variables.py

### 52. BACKUP_FILE → AQUA_BACKUP_FILE
**原因**: 缺少项目前缀
**受影响文件**: 3 个
**文件列表**:
- scripts/restore_backend.sh
- scripts/restore_database.sh
- scripts/restore_frontend.sh

### 53. DB_PATH → AQUA_DB_PATH
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- scripts/restore_database.sh
- scripts/backup_database.sh

### 54. DB_BAK_NAME → AQUA_DB_BAK_NAME
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/backup_database.sh

### 55. DB_BAK_TARGET → AQUA_DB_BAK_TARGET
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/backup_database.sh

### 56. FRONTEND_NAME → VITE_FRONTEND_NAME
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/backup_frontend.sh

### 57. FRONTEND_TARGET → VITE_FRONTEND_TARGET
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/backup_frontend.sh

### 58. SCRIPT_DIR → AQUA_SCRIPT_DIR
**原因**: 缺少项目前缀
**受影响文件**: 2 个
**文件列表**:
- scripts/deploy.sh
- deprecated/entry_files/aqua.sh

### 59. BASE_DIR → AQUA_BASE_DIR
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/deploy.sh

### 60. ENV → AQUA_ENV
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/deploy.sh

### 61. BACKEND_NAME → AQUA_BACKEND_NAME
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/backup_backend.sh

### 62. BACKEND_TARGET → AQUA_BACKEND_TARGET
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- scripts/backup_backend.sh

### 63. TERM → AQUA_TERM
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- src/utils/cli_ui.py

### 64. JUPYTER_RUNTIME_DIR → AQUA_JUPYTER_RUNTIME_DIR
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- src/utils/simple_config.py

### 65. LOG_LEVEL → AQUA_LOG_LEVEL
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- src/utils/simple_config.py

### 66. PYTEST_CURRENT_TEST → AQUA_PYTEST_CURRENT_TEST
**原因**: 缺少项目前缀
**受影响文件**: 1 个
**文件列表**:
- src/utils/env_detector.py

## 📋 迁移指南
### 步骤 1: 将 TUSHARE_TOKEN 重命名为 AQUA_TUSHARE_TOKEN
**说明**: 原因: 预定义标准化规则
**影响文件**: 26 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 TUSHARE_TOKEN
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 2: 将 TEST_SET_VAR 重命名为 AQUA_TEST_SET_VAR
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 TEST_SET_VAR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 3: 将 TEST_CONVENIENCE 重命名为 AQUA_TEST_CONVENIENCE
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 TEST_CONVENIENCE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 4: 将 REQUIRED_VAR 重命名为 VITE_REQUIRED_VAR
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 REQUIRED_VAR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 5: 将 TEST_LIST 重命名为 AQUA_TEST_LIST
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 TEST_LIST
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 6: 将 GREEN 重命名为 AQUA_GREEN
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 GREEN
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 7: 将 NC 重命名为 AQUA_NC
**说明**: 原因: 变量名过短，不符合命名规范
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 NC
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 8: 将 YELLOW 重命名为 AQUA_YELLOW
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 YELLOW
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 9: 将 SENSITIVE_PATTERNS 重命名为 AQUA_SENSITIVE_PATTERNS
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 SENSITIVE_PATTERNS
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 10: 将 RED 重命名为 AQUA_RED
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 RED
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 11: 将 PYTHONPATH 重命名为 AQUA_PYTHONPATH
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONPATH
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 12: 将 BACKUP_DATE 重命名为 AQUA_BACKUP_DATE
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 BACKUP_DATE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 13: 将 REPO_URL 重命名为 AQUA_REPO_URL
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 REPO_URL
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 14: 将 SSH_URL 重命名为 AQUA_SSH_URL
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 SSH_URL
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 15: 将 LOG_FILE 重命名为 AQUA_LOG_FILE
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 LOG_FILE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 16: 将 TIMESTAMP 重命名为 AQUA_TIMESTAMP
**说明**: 原因: 缺少项目前缀
**影响文件**: 6 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 TIMESTAMP
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 17: 将 COMMIT_HASH 重命名为 AQUA_COMMIT_HASH
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 COMMIT_HASH
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 18: 将 REUSE_STATUS 重命名为 AQUA_REUSE_STATUS
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 REUSE_STATUS
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 19: 将 FEATURE 重命名为 AQUA_FEATURE
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 FEATURE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 20: 将 PORT 重命名为 AQUA_PORT
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 PORT
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 21: 将 DEBUG 重命名为 AQUA_DEBUG
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 DEBUG
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 22: 将 MYSQL_PASSWORD 重命名为 AQUA_MYSQL_PASSWORD
**说明**: 原因: 缺少项目前缀
**影响文件**: 5 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_PASSWORD
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 23: 将 TEST_MODE 重命名为 AQUA_TEST_MODE
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 TEST_MODE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 24: 将 NO_COLOR 重命名为 AQUA_NO_COLOR
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 NO_COLOR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 25: 将 FORCE_COLOR 重命名为 AQUA_FORCE_COLOR
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 FORCE_COLOR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 26: 将 SHELL 重命名为 AQUA_SHELL
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 SHELL
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 27: 将 fpath 重命名为 AQUA_fpath
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 fpath
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 28: 将 PYTHONIOENCODING 重命名为 AQUA_PYTHONIOENCODING
**说明**: 原因: 缺少项目前缀
**影响文件**: 4 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONIOENCODING
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 29: 将 PYTHONUTF8 重命名为 AQUA_PYTHONUTF8
**说明**: 原因: 缺少项目前缀
**影响文件**: 5 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONUTF8
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 30: 将 PYTHONUNBUFFERED 重命名为 AQUA_PYTHONUNBUFFERED
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 PYTHONUNBUFFERED
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 31: 将 CSV_DATA_PATH 重命名为 AQUA_CSV_DATA_PATH
**说明**: 原因: 预定义标准化规则
**影响文件**: 4 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 CSV_DATA_PATH
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 32: 将 MYSQL_HOST 重命名为 AQUA_MYSQL_HOST
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_HOST
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 33: 将 MYSQL_PORT 重命名为 AQUA_MYSQL_PORT
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_PORT
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 34: 将 MYSQL_USER 重命名为 AQUA_MYSQL_USER
**说明**: 原因: 缺少项目前缀
**影响文件**: 4 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_USER
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 35: 将 MYSQL_DATABASE 重命名为 AQUA_MYSQL_DATABASE
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 MYSQL_DATABASE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 36: 将 VIRTUAL_ENV 重命名为 AQUA_VIRTUAL_ENV
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 VIRTUAL_ENV
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 37: 将 USERNAME 重命名为 AQUA_USERNAME
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 USERNAME
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 38: 将 VAR 重命名为 AQUA_VAR
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 VAR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 39: 将 BLUE 重命名为 AQUA_BLUE
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 BLUE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 40: 将 COMP_CWORD 重命名为 AQUA_COMP_CWORD
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 COMP_CWORD
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 41: 将 ENVIRONMENT 重命名为 AQUA_ENVIRONMENT
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 ENVIRONMENT
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 42: 将 VERSION 重命名为 AQUA_VERSION
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 VERSION
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 43: 将 DEPLOY_TIME 重命名为 AQUA_DEPLOY_TIME
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 DEPLOY_TIME
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 44: 将 BACKUP_DIR 重命名为 AQUA_BACKUP_DIR
**说明**: 原因: 预定义标准化规则
**影响文件**: 4 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 BACKUP_DIR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 45: 将 PROJECT_ROOT 重命名为 AQUA_PROJECT_ROOT
**说明**: 原因: 预定义标准化规则
**影响文件**: 7 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 PROJECT_ROOT
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 46: 将 SETTINGS_PATH 重命名为 AQUA_SETTINGS_PATH
**说明**: 原因: 预定义标准化规则
**影响文件**: 7 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 SETTINGS_PATH
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 47: 将 LOG_PATH 重命名为 AQUA_LOG_PATH
**说明**: 原因: 预定义标准化规则
**影响文件**: 7 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 LOG_PATH
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 48: 将 FRONTEND_PORT 重命名为 AQUA_FRONTEND_PORT
**说明**: 原因: 预定义标准化规则
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_PORT
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 49: 将 FRONTEND_HOST 重命名为 AQUA_FRONTEND_HOST
**说明**: 原因: 预定义标准化规则
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_HOST
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 50: 将 INTERVAL 重命名为 AQUA_INTERVAL
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 INTERVAL
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 51: 将 {standard} 重命名为 AQUA_{standard}
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 {standard}
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 52: 将 BACKUP_FILE 重命名为 AQUA_BACKUP_FILE
**说明**: 原因: 缺少项目前缀
**影响文件**: 3 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 BACKUP_FILE
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 53: 将 DB_PATH 重命名为 AQUA_DB_PATH
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 DB_PATH
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 54: 将 DB_BAK_NAME 重命名为 AQUA_DB_BAK_NAME
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 DB_BAK_NAME
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 55: 将 DB_BAK_TARGET 重命名为 AQUA_DB_BAK_TARGET
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 DB_BAK_TARGET
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 56: 将 FRONTEND_NAME 重命名为 VITE_FRONTEND_NAME
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_NAME
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 57: 将 FRONTEND_TARGET 重命名为 VITE_FRONTEND_TARGET
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 FRONTEND_TARGET
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 58: 将 SCRIPT_DIR 重命名为 AQUA_SCRIPT_DIR
**说明**: 原因: 缺少项目前缀
**影响文件**: 2 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 SCRIPT_DIR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 59: 将 BASE_DIR 重命名为 AQUA_BASE_DIR
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 BASE_DIR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 60: 将 ENV 重命名为 AQUA_ENV
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 ENV
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 61: 将 BACKEND_NAME 重命名为 AQUA_BACKEND_NAME
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 BACKEND_NAME
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 62: 将 BACKEND_TARGET 重命名为 AQUA_BACKEND_TARGET
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 BACKEND_TARGET
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 63: 将 TERM 重命名为 AQUA_TERM
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 TERM
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 64: 将 JUPYTER_RUNTIME_DIR 重命名为 AQUA_JUPYTER_RUNTIME_DIR
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 JUPYTER_RUNTIME_DIR
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 65: 将 LOG_LEVEL 重命名为 AQUA_LOG_LEVEL
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 LOG_LEVEL
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

### 步骤 66: 将 PYTEST_CURRENT_TEST 重命名为 AQUA_PYTEST_CURRENT_TEST
**说明**: 原因: 缺少项目前缀
**影响文件**: 1 个
**手动操作**:
   1. 更新环境变量定义文件(.env, .env.*)中的 PYTEST_CURRENT_TEST
   2. 更新部署配置中的环境变量设置
   3. 通知团队成员更新本地环境配置

## ✅ 验证步骤
- 1. 运行所有单元测试确保功能正常
- 2. 检查环境变量管理器的验证功能
- 3. 验证前端构建过程中的环境变量
- 4. 测试不同环境(dev/test/prod)的配置
- 5. 确认CI/CD流程中的环境变量设置
