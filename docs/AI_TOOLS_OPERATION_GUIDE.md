# AQUA项目AI工具操作指南
## 确保Claude Code/Gemini完全遵照宪法执行

> **适用工具**: <PERSON> Code, Gemini CLI, Cursor, 其他AI编程助手
> **目标**: 100%遵守CLAUDE.md宪法和项目规范

---

## 🚀 第一步：启动AI工具时的操作

### 1.1 打开Claude Code时必须执行

```bash
# 进入AQUA项目目录
cd /Users/<USER>/Documents/AQUA/Dev/AQUA

# 🏛️ 步骤1: 运行自检程序
python scripts/claude_self_check.py

# 如果自检通过，会看到：
# ✅ Claude Code自检通过 - 处于完全合规状态
# 📄 承诺书已保存至: logs/claude_compliance_pledge.md
```

### 1.2 打开Gemini CLI时必须执行

```bash
# 进入AQUA项目目录
cd /Users/<USER>/Documents/AQUA/Dev/AQUA

# 🔍 检查项目合规性
python scripts/compliance_checker.py

# 📋 向Gemini发送初始化指令
echo "请严格遵循以下宪法文件执行所有操作：
1. /Users/<USER>/Documents/AQUA/Dev/AQUA/CLAUDE.md
2. /Users/<USER>/Documents/AQUA/Dev/AQUA/docs/ENHANCED_EXECUTION_INSTRUCTIONS.md

在每次操作前必须声明：
合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则"
```

### 1.3 开启任何AI编程工具的标准流程

```
📋 发送给AI工具的初始化消息模板：

🏛️ AQUA项目宪法执行初始化

我正在使用AQUA量化交易系统开发项目，请严格遵循以下规范：

📜 核心宪法文件：
- /Users/<USER>/Documents/AQUA/Dev/AQUA/CLAUDE.md (最高优先级)
- /Users/<USER>/Documents/AQUA/Dev/AQUA/docs/ENHANCED_EXECUTION_INSTRUCTIONS.md

🚨 强制要求：
1. 每次操作前必须声明："合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则"
2. 测试文件必须放在 tests/ 目录
3. 文档文件必须放在 docs/ 目录  
4. 脚本文件必须放在 scripts/ 目录
5. 绝不在根目录创建上述类型文件
6. 发现更好方案时必须暂停并请求我的批准

⚠️ 违规后果：
严重违规将导致操作立即终止，需要重新开始。

请确认您理解并将严格遵循以上规范。
```

---

## 🔄 第二步：开发过程中的操作流程

### 2.1 每次给AI工具分配任务时

**📝 标准任务分配模板：**

```
🎯 [任务描述] 

📋 执行要求：
1. 合规声明：请先声明"合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则"
2. 预执行检查：分析现有代码，评估复用机会
3. 文件放置验证：确认所有新文件都放在正确目录
4. TDD要求：如涉及代码修改，需先写测试用例

⚠️ 违规阻断：
如需在根目录创建文件或发现更好方案，请立即暂停并请求批准。

开始执行前请确认理解以上要求。
```

### 2.2 监控AI工具执行过程

**🔍 实时监控命令：**

```bash
# 终端1: 监控宪法执行日志
tail -f logs/constitutional_enforcement.jsonl

# 终端2: 监控审批请求
tail -f logs/approval_requests.jsonl

# 终端3: 定期检查项目合规性
watch -n 60 "python scripts/compliance_checker.py"
```

### 2.3 AI工具请求审批时的处理

当AI工具暂停并显示类似信息时：

```
🤖 AI工具请求人类审批
============================================================
📋 决策类型: 替代方案
📝 当前计划: [原计划]
💡 建议替代方案: [更好方案]
🧠 推理过程: [分析过程]

请回复: approve / reject / modify: [具体要求]
============================================================
```

**您的回复选项：**

```bash
# ✅ 批准AI工具的建议
echo "approve"

# ❌ 拒绝，坚持原计划
echo "reject"  

# 🔄 要求修改方案
echo "modify: 请在tests/integration/目录而不是tests/unit/目录创建测试文件"

# 📋 要求更多信息
echo "explain: 请详细说明为什么这个方案更好，有什么风险？"
```

---

## 🚫 第三步：违规检测和处理

### 3.1 发现AI工具违规时的处理

**立即执行：**

```bash
# 🔍 检查违规情况
python scripts/compliance_checker.py

# 🚨 查看具体违规记录
python -c "
from scripts.claude_constitutional_enforcer import ClaudeConstitutionalEnforcer
enforcer = ClaudeConstitutionalEnforcer()
print(enforcer.generate_compliance_report())
"

# 🔧 自动修正违规问题
python scripts/compliance_checker.py  # 选择 'y' 自动修正
```

**向AI工具发送警告：**

```
🚨 检测到违规行为

您刚才的操作违反了AQUA项目宪法：
[具体违规描述]

请立即：
1. 停止当前操作
2. 重新声明合规承诺
3. 按照正确规范重新执行

⚠️ 继续违规将导致会话终止。
```

### 3.2 重置AI工具状态

如果AI工具持续违规：

```bash
# 🔄 重置项目合规状态  
python scripts/claude_self_check.py

# 📋 向AI工具发送重置指令
echo "
🏛️ 宪法合规重置

由于检测到违规行为，现在重置您的执行状态：

1. 请重新阅读宪法文件：/Users/<USER>/Documents/AQUA/Dev/AQUA/CLAUDE.md
2. 重新声明合规承诺
3. 从预执行检查开始重新执行任务

所有之前的违规操作已被撤销，请严格按照规范重新开始。
"
```

---

## 📊 第四步：日常监控和维护

### 4.1 每日检查清单

**☀️ 每天开始开发前：**

```bash  
# 1. 检查项目整体合规性
python scripts/compliance_checker.py

# 2. 查看AI工具操作日志
cat logs/constitutional_enforcement.jsonl | tail -10

# 3. 处理待审批请求
cat logs/approval_requests.jsonl | tail -5

# 4. 确认没有违规文件
ls -la | grep -E "(test_.*\.py|.*_REPORT\.md|.*_script\.py)" || echo "✅ 根目录清洁"
```

**🌙 每天结束开发后：**

```bash
# 1. 生成当日合规报告
python -c "
from scripts.claude_constitutional_enforcer import ClaudeConstitutionalEnforcer
enforcer = ClaudeConstitutionalEnforcer()
print('📊 今日合规报告：')
print(enforcer.generate_compliance_report())
" > logs/daily_compliance_$(date +%Y%m%d).md

# 2. 清理临时文件
find . -name "*.tmp" -o -name "temp_*" -delete

# 3. 备份重要日志
cp logs/constitutional_enforcement.jsonl logs/backup/
```

### 4.2 周期性维护

**🗓️ 每周执行：**

```bash
# 分析AI工具行为模式
python -c "
import json
from collections import Counter

violations = []
with open('logs/constitutional_enforcement.jsonl', 'r') as f:
    for line in f:
        try:
            entry = json.loads(line)
            if entry.get('violations'):
                violations.extend(entry['violations'])
        except:
            continue

print('📊 本周违规模式分析：')
for violation, count in Counter(violations).most_common(5):
    print(f'  {count}次: {violation}')
"

# 更新合规规则（如需要）
# 根据违规模式调整 claude_constitutional_enforcer.py 中的规则
```

---

## 🎯 第五步：高级操作和定制

### 5.1 自定义约束规则

如需添加新的约束规则：

```python
# 编辑 scripts/claude_constitutional_enforcer.py
# 在 _load_constitutional_rules() 方法中添加：

"custom_rules": {
    "database_operations": [
        "所有数据库操作必须使用connection_manager",
        "禁止直接执行SQL DDL语句"
    ],
    "api_development": [
        "所有API路由必须有对应的测试用例",
        "API文档必须同步更新"
    ]
}
```

### 5.2 集成到IDE中

**Visual Studio Code集成：**

```json
// .vscode/tasks.json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "AQUA Compliance Check",
            "type": "shell",
            "command": "python",
            "args": ["scripts/compliance_checker.py"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always"
            }
        },
        {
            "label": "Claude Self Check", 
            "type": "shell",
            "command": "python",
            "args": ["scripts/claude_self_check.py"],
            "group": "build"
        }
    ]
}
```

**快捷键设置：**

```json
// .vscode/keybindings.json
[
    {
        "key": "ctrl+shift+c",
        "command": "workbench.action.tasks.runTask",
        "args": "AQUA Compliance Check"
    }
]
```

---

## 🔧 故障排除和常见问题

### Q1: AI工具不响应合规要求怎么办？

```bash
# 强制重置方案
echo "
🚨 强制合规重置

检测到您没有遵循AQUA项目宪法。现在执行强制重置：

1. 请立即停止所有操作
2. 重新阅读：/Users/<USER>/Documents/AQUA/Dev/AQUA/CLAUDE.md
3. 声明：'合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则'
4. 等待进一步指示

⚠️ 如不配合，将终止当前会话。
"
```

### Q2: 如何处理AI工具的"创新"建议？

```bash
# 标准回应模板
echo "
🤖 关于您的建议

感谢您的创新思路，但请遵循以下流程：

1. 暂停当前操作
2. 详细说明：
   - 当前方案是什么？
   - 您建议的方案是什么？
   - 为什么认为更好？
   - 预期的改进效果？
3. 等待我的明确批准
4. 获得批准后再执行

请不要自行决定采用新方案。
"
```

### Q3: 多个AI工具同时工作时如何协调？

```bash
# 建立协调机制
mkdir -p logs/ai_coordination

# 为每个AI工具创建独立日志
touch logs/ai_coordination/claude_code.log
touch logs/ai_coordination/gemini.log  
touch logs/ai_coordination/cursor.log

# 监控脚本
cat > scripts/ai_coordination_monitor.py << 'EOF'
#!/usr/bin/env python3
import time
import json
from pathlib import Path

def monitor_ai_tools():
    """监控多个AI工具的协调状态"""
    coordination_dir = Path("logs/ai_coordination")
    
    while True:
        active_tools = []
        for log_file in coordination_dir.glob("*.log"):
            if log_file.stat().st_mtime > time.time() - 300:  # 5分钟内活跃
                active_tools.append(log_file.stem)
        
        if len(active_tools) > 1:
            print(f"⚠️ 检测到多个AI工具同时活跃: {active_tools}")
            print("建议协调工作避免冲突")
        
        time.sleep(60)

if __name__ == "__main__":
    monitor_ai_tools()
EOF
```

---

## 📋 快速参考卡片

### 🏛️ 每次启动AI工具必须执行

```bash
# 1. 进入项目目录
cd /Users/<USER>/Documents/AQUA/Dev/AQUA

# 2. 运行自检
python scripts/claude_self_check.py

# 3. 发送初始化指令给AI工具
[发送上述初始化消息模板]
```

### 🔍 每次分配任务必须包含

```
1. 合规声明要求
2. 预执行检查要求  
3. 文件放置验证
4. TDD规则要求
5. 违规阻断警告
```

### 🚨 发现违规时立即执行

```bash
python scripts/compliance_checker.py
[向AI工具发送违规警告]
```

### 📊 日常监控命令

```bash
# 实时日志监控
tail -f logs/constitutional_enforcement.jsonl

# 合规状态检查
python scripts/compliance_checker.py

# 生成合规报告
python scripts/claude_constitutional_enforcer.py
```

---

通过严格遵循这套操作流程，您可以确保所有AI工具完全按照AQUA项目宪法执行，同时保持对所有重要决策的最终控制权。