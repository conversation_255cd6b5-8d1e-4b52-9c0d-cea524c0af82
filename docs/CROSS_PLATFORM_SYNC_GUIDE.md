# AQUA 跨平台全量同步指南

> **目标**: 实现OS X本地代码全量推送到Gitee，然后在Windows环境下全量拉取覆盖

## 🎯 使用场景

- 在OS X环境下开发完成，需要切换到Windows环境继续工作
- 需要在多个平台间保持代码完全同步
- 解决跨平台开发中的代码一致性问题

## 📋 前提条件

### OS X端
- Git仓库状态干净（无未提交更改）
- 有Gitee仓库的推送权限
- Python 3.x环境

### Windows端  
- 已克隆相同的Git仓库
- 有Gitee仓库的拉取权限
- Python 3.x环境

## 🚀 同步方案

### 方案1: 强制同步（快速但需谨慎）

**⚡ 适用场景**: 
- 确定Windows端没有重要的本地更改
- 需要快速完全同步
- 对Git历史要求不高

#### OS X端操作

```bash
# 方法1: 使用脚本（推荐）
python scripts/sync_to_gitee.py

# 方法2: 手动执行
git add .
git commit -m "Before full sync to Windows"
git push --force-with-lease origin main  # 替换main为你的分支名
```

#### Windows端操作

```bash
# 方法1: 使用脚本（推荐）
python scripts/sync_from_gitee.py

# 方法2: 手动执行
git fetch --all
git reset --hard origin/main  # 替换main为你的分支名
git clean -fd
```

### 方案2: 分支同步（安全推荐）

**🛡️ 适用场景**: 
- 需要审查代码差异
- Windows端可能有本地更改
- 需要保持Git历史完整

#### OS X端操作

```bash
# 创建并推送同步分支
python scripts/sync_via_branch.py --push

# 或手动执行
git checkout -b osx-to-windows-sync
git push --force origin osx-to-windows-sync
git checkout main  # 返回原分支
```

#### Windows端操作

```bash
# 拉取并合并同步分支
python scripts/sync_via_branch.py --pull

# 或手动执行
git fetch origin
git checkout osx-to-windows-sync
git pull origin osx-to-windows-sync
# 检查差异
git diff main osx-to-windows-sync
# 确认后合并
git checkout main
git merge osx-to-windows-sync
```

## 🔧 脚本功能详解

### sync_to_gitee.py (OS X端)

**功能**:
- 自动检查工作区状态
- 创建备份分支防止意外
- 强制推送当前分支到远程
- 生成Windows端操作指令

**安全措施**:
- 操作前用户确认
- 自动创建时间戳备份分支
- 推送备份分支到远程
- 提供完整的恢复指令

### sync_from_gitee.py (Windows端)

**功能**:
- 检查并处理本地未提交更改
- 创建本地备份分支
- 强制重置到远程分支
- 验证同步结果

**处理策略**:
- **有本地更改**: 提供stash或丢弃选项
- **工作区干净**: 直接执行重置
- **同步验证**: 确保本地和远程一致

### sync_via_branch.py (分支模式)

**功能**:
- 创建专用同步分支
- 支持差异预览
- 安全的合并流程
- 自动平台检测

## ⚠️ 重要注意事项

### 数据安全
1. **备份重要数据**: 操作前确保重要代码已提交
2. **使用备份分支**: 脚本会自动创建备份，记住备份分支名
3. **测试环境优先**: 建议先在测试仓库验证流程

### 平台差异处理
1. **换行符**: Git会自动处理CRLF/LF转换
2. **文件权限**: Windows和Unix权限差异会被忽略
3. **路径分隔符**: 代码中使用Path.joinpath()处理

### 冲突解决
1. **合并冲突**: 分支模式下如有冲突需手动解决
2. **文件锁定**: Windows下确保没有程序占用文件
3. **权限问题**: 确保对仓库目录有足够权限

## 🔄 常见使用流程

### 日常开发流程

```bash
# OS X端开发完成
git add .
git commit -m "Feature complete on macOS"

# 全量推送到Gitee
python scripts/sync_to_gitee.py

# 切换到Windows环境
# Windows端全量拉取
python scripts/sync_from_gitee.py

# 继续在Windows端开发...
```

### 定期同步流程

```bash
# 每日/每周同步
# OS X端
python scripts/sync_via_branch.py --push

# Windows端
python scripts/sync_via_branch.py --pull
```

## 🛠️ 故障排除

### 常见问题

**Q: 推送被拒绝（rejected）**
```bash
# 解决方法
git fetch origin
git rebase origin/main  # 或使用 --force-with-lease
python scripts/sync_to_gitee.py
```

**Q: Windows端文件被占用**
```bash
# 解决方法
# 1. 关闭所有相关程序（IDE、编辑器等）
# 2. 使用管理员权限运行命令行
# 3. 重启后重试
```

**Q: 同步后编码问题**
```bash
# 设置Git编码配置
git config core.autocrlf true    # Windows
git config core.autocrlf input   # macOS/Linux
```

### 恢复操作

**从备份分支恢复**:
```bash
# 查看备份分支
git branch -a | grep backup

# 恢复到备份分支
git checkout backup_before_sync_20240101_120000
git branch -D main
git checkout -b main
git push --force origin main
```

**从stash恢复**:
```bash
# 查看stash列表
git stash list

# 恢复stash
git stash pop
# 或指定特定stash
git stash apply stash@{0}
```

## 💡 最佳实践

1. **定期提交**: 避免一次性同步过多更改
2. **分支管理**: 使用功能分支减少冲突
3. **测试验证**: 同步后运行测试确保功能正常
4. **备份策略**: 保留重要的备份分支
5. **文档更新**: 记录重要的同步节点

## 📞 支持

如果遇到问题：
1. 检查Git状态: `git status`
2. 查看日志: `git log --oneline -10`
3. 备份当前状态后重试
4. 必要时从备份分支恢复

---

**版本**: v1.0  
**更新日期**: 2025-01-01  
**适用环境**: AQUA项目跨平台开发