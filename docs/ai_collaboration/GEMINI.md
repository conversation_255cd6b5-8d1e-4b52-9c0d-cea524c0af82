# Gemini CLI Operations Manual - AQUA Project Constitution v5.0

> **Applicable Audience**: AQUA project developers using Gemini CLI
> **Core Principles**: Absolute Compliance, Reuse-First, TDD-Driven, Tool Interaction, Quality Assurance, Unified Configuration
> **Version**: v5.0 (Gemini Compliance-Enhanced Edition)
> **Effective Date**: 2025-07-27

---

## 🚨 MANDATORY COMPLIANCE

**Before executing any operation, I (Gemini CLI) must declare:**

### 1. Absolute Compliance Checks

- [ ] **Pre-execution Compliance**: All commands and operations must pass a pre-execution compliance check, ensuring adherence to project standards, security policies, and regulatory requirements.
- [ ] **Automated Compliance Enforcement**: Utilize automated tools and scripts to enforce compliance rules, preventing non-compliant code or configurations from being deployed.
- [ ] **Compliance Reporting**: Generate detailed compliance reports for every major operation, providing an audit trail and highlighting any deviations.

### 2. Reuse-First Principle

- [ ] **Component Reusability**: Prioritize the reuse of existing, tested, and validated components, modules, and functions within the AQUA project.
- [ ] **Standardized Libraries**: Leverage standardized libraries and frameworks to ensure consistency and reduce development effort.
- [ ] **Codebase Search & Discovery**: Implement efficient mechanisms for searching and discovering reusable code snippets and assets across the project.

### 3. TDD-Driven Development

- [ ] **Red Phase (Write Failing Test)**:
    - [ ] Write a new test case that defines a new piece of functionality or a bug fix.
    - [ ] Ensure the test fails initially, confirming it correctly identifies the missing or incorrect behavior.
    - [ ] The test should be specific and target only the functionality being added or fixed.
- [ ] **Green Phase (Make Test Pass)**:
    - [ ] Write minimal production code that is just enough to make the current failing test pass.
    - [ ] Avoid introducing any unnecessary complexity or additional functionality at this stage.
    - [ ] The goal is to quickly reach a passing test state.
    - [ ] No existing tests should be broken.
- [ ] **Refactor Phase (Improve Code)**:
    - [ ] Once the test passes, refactor the newly written production code to improve its design, readability, and maintainability.
    - [ ] Ensure that all tests continue to pass after refactoring, guaranteeing that the functionality remains intact.
    - [ ] This phase includes removing duplication, simplifying logic, and optimizing performance without changing external behavior.

### 4. Tool Interaction & Integration

- [ ] **CLI-Driven Workflows**: All major development and operational workflows should be executable via Gemini CLI commands.
- [ ] **Integrated Toolchain**: Ensure seamless integration with other development tools (e.g., IDEs, version control, CI/CD pipelines).
- [ ] **Extensible Tooling**: Design tools to be extensible, allowing for easy addition of new functionalities and integrations.

### 5. Quality Assurance (QA) Integration

- [ ] **Automated Testing**: Implement comprehensive automated tests (unit, integration, E2E) for all code changes.
- [ ] **Code Review**: Mandate rigorous code reviews for all pull requests to ensure code quality, adherence to standards, and identification of potential issues.
- [ ] **Continuous Integration/Continuous Deployment (CI/CD)**: Utilize CI/CD pipelines to automate testing, building, and deployment processes, ensuring continuous quality checks.
- [ ] **Performance Monitoring**: Integrate performance monitoring tools to track application performance and identify bottlenecks.

### 6. Unified Configuration Management

- [ ] **Centralized Configuration**: Manage all project configurations (e.g., environment variables, database settings, API keys) in a centralized and version-controlled manner.
- [ ] **Environment-Specific Configurations**: Support environment-specific configurations (development, staging, production) to ensure proper behavior across different deployments.
- [ ] **Secure Configuration**: Implement secure practices for handling sensitive configuration data, such as encryption and access control.

---

## 🚀 STANDARD WORKFLOWS

### 1. Feature Development Workflow

- [ ] **Requirement Analysis**: Understand the feature requirements and define clear acceptance criteria.
- [ ] **Test-Driven Development (TDD)**: Follow the Red-Green-Refactor cycle for all new feature development.
- [ ] **Code Implementation**: Write clean, modular, and well-documented code.
- [ ] **Automated Testing**: Develop comprehensive unit, integration, and end-to-end tests.
- [ ] **Code Review**: Submit code for peer review and address feedback.
- [ ] **CI/CD Pipeline**: Push code to trigger CI/CD pipeline for automated testing and deployment.

### 2. Bug Fix Workflow

- [ ] **Reproduce Bug**: Clearly reproduce the bug and identify the root cause.
- [ ] **Write Failing Test**: Write a new test case that specifically reproduces the bug and fails.
- [ ] **Fix Code**: Implement the minimal code changes required to fix the bug and make the new test pass.
- [ ] **Regression Testing**: Ensure no existing functionality is broken by running all automated tests.
- [ ] **Code Review & Deployment**: Follow standard code review and CI/CD processes.

### 3. Refactoring Workflow

- [ ] **Identify Refactoring Opportunities**: Use code analysis tools or manual inspection to find areas for improvement.
- [ ] **Ensure Test Coverage**: Verify that the code to be refactored has sufficient test coverage.
- [ ] **Small, Incremental Changes**: Perform refactoring in small, manageable steps, running tests after each change.
- [ ] **Maintain Functionality**: Ensure that the external behavior of the code remains unchanged throughout the refactoring process.

---

## 🛠️ TECHNICAL STANDARDS

### 1. Technology Stack Standards

- [ ] **Backend**: Python (FastAPI, SQLAlchemy, Pydantic), DuckDB
- [ ] **Frontend**: Vue.js (TypeScript, Pinia, Vue Router), Tailwind CSS
- [ ] **Database**: DuckDB (for analytical data), PostgreSQL (for transactional data)
- [ ] **Containerization**: Docker
- [ ] **Orchestration**: Kubernetes (for production deployments)

### 2. Code Standards & Quality Gates

- [ ] **Linting & Formatting**: Adhere to ESLint, Prettier, Black, and isort standards.
- [ ] **Type Hinting**: Utilize type hints extensively in Python and TypeScript code.
- [ ] **Documentation**: Write clear and concise docstrings/comments for all functions, classes, and complex logic.
- [ ] **Test Coverage**: Maintain a minimum of 80% unit test coverage for critical modules.
- [ ] **Security Best Practices**: Follow OWASP Top 10 guidelines and conduct regular security audits.

---

## ⚙️ GEMINI CLI TOOL ECOSYSTEM

Gemini CLI provides a suite of tools to streamline development and ensure compliance:

- [ ] **Reuse Analysis Tool**: Analyzes the codebase for reusable components and suggests opportunities for code reuse.
- [ ] **TDD Development Tool**: Assists in generating test and implementation templates, facilitating the TDD workflow.
- [ ] **Execution Mode Selector**: Allows developers to switch between different execution modes (e.g., development, testing, production) with appropriate configurations.
- [ ] **Quality Metrics Calculator**: Calculates key quality metrics (e.g., code complexity, test coverage, maintainability index) to provide insights into code health.
- [ ] **Compliance Checker**: Automatically verifies code and configurations against predefined compliance rules.

---

## 📚 QUICK REFERENCE GUIDE

- **Gemini CLI Commands**: Refer to `docs/cli_user_guide.md` for a comprehensive list of commands and their usage.
- **Configuration Guide**: Detailed information on project configuration can be found in `docs/CONFIGURATION_GUIDE.md`.
- **API Documentation**: For API specifications and usage, consult `docs/API_GUIDE.md`.
- **Troubleshooting**: Common issues and their solutions are documented in `docs/FAQ.md`.

**Usage Instructions**: These guidelines serve as a working guide for Claude Code, aiming to provide professional and efficient AI-assisted development services for individual developers. Detailed tools and templates are available in the `scripts/claude_tools/` and `templates/claude/` directories. For comprehensive tool documentation, refer to `docs/claude_tools_guide.md`.