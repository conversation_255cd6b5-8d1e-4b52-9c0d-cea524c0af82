# AQUA 项目原子化任务提示词 - Epic 0

> 说明：本文件所有任务、提示词、依赖、规则等，均以 docs/AQUA_GUIDE.md 为唯一权威事实源。所有引用的旧文档内容已迁移至 AQUA_GUIDE.md，后续仅以 AQUA_GUIDE.md 为唯一权威事实源。

---

## 目录
- [Task 0.1.1：配置 config/settings.toml 基础参数](#task-011配置-configsettingstoml-基础参数)
- [Task 0.1.2：创建环境变量模板文件](#task-012创建环境变量模板文件)
- [Task 0.1.3：完善环境初始化脚本核心功能](#task-013完善环境初始化脚本核心功能)
- [Task 0.1.4：编写项目启动脚本](#task-014编写项目启动脚本)
- [Task 0.1.5：创建初始依赖文件](#task-015创建初始依赖文件)
- [Task 0.1.6：设定 Node.js 版本](#task-016设定-nodejs-版本)
- [Task 0.1.7：创建项目结构](#task-017创建项目结构)
- [Task 0.1.8：迁移并完善单一事实源文档](#task-018迁移并完善单一事实源文档)
- [Task 0.1.9：创建项目测试用例](#task-019创建项目测试用例)
- [Task 0.1.10：创建项目部署脚本](#task-0110创建项目部署脚本)
- [Task 0.1.11：创建项目监控脚本](#task-0111创建项目监控脚本)
- [Task 0.1.12：创建项目备份脚本](#task-0112创建项目备份脚本)
- [Task 0.1.13：创建项目恢复脚本](#task-0113创建项目恢复脚本)
- [Task 0.2.1：深入研读权威文档](#task-021深入研读权威文档)
- [Task 0.2.2：初始化任务管理文件](#task-022初始化任务管理文件)
- [Task 0.2.3：明确日志文件模板与规范](#task-023明确日志文件模板与规范)
- [Task 0.2.4：建立结构化-ai-提示词库](#task-024建立结构化-ai-提示词库)
- [Task 0.2.5：明确-ai-协作合规性要求](#task-025明确-ai-协作合规性要求)
- [Task 0.3.1：安装和配置-pre-commit-框架](#task-031安装和配置-pre-commit-框架)
- [Task 0.3.2：配置-pre-commit-configyaml](#task-032配置-pre-commit-configyaml)
- [Task 0.3.3：配置-ide编辑器集成](#task-033配置-ide编辑器集成)
- [Task 0.3.4：编写基础单元测试用例](#task-034编写基础单元测试用例)
- [Task 0.4.1：创建功能模块定义文件](#task-041创建功能模块定义文件)
- [Task 0.4.2：初步定义核心功能-api-接口](#task-042初步定义核心功能-api-接口)
- [Task 0.4.3：利用-ai-辅助草拟接口定义并审查](#task-043利用-ai-辅助草拟接口定义并审查)
- [Task 0.5.1：遵循-git-分支管理规范](#task-051遵循-git-分支管理规范)
- [Task 0.5.2：明确-commit-message-规范](#task-052明确-commit-message-规范)
- [Task 0.5.3：确保任务与日志实时更新](#task-053确保任务与日志实时更新)
- [Task 0.5.4：编写脚本自动检查-commit-message](#task-054编写脚本自动检查-commit-message)

---

<!-- 以下为各任务内容，已按标准结构化，0.5.x部分已补全为Markdown -->

<!-- 其余内容保持原有分节和编号，仅对0.5.x部分做结构化修正 -->

### **原子化任务提示词 - Task 0.1.1：配置 `config/settings.toml` 基础参数**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.1
  * **任务描述:** 初始化 `config/settings.toml` 文件，包含数据库路径、日志路径、项目名称等基本配置项。
  * **相关文件/代码:**
      * `config/settings.toml` (待创建/修改)
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`、`docs/AQUA_GUIDE.md#[GUIDE-07]`、`0302_REQUEST.md`、`@/rules/development_standards.mdc`

-----

## 2 任务目标与预期输出

  * **具体目标:** 在 `config/` 文件夹中创建或初始化 `settings.toml`，填充必要的全局配置项。
  * **预期输出类型:** 配置代码 (TOML)
  * **预期输出文件/位置:** `config/settings.toml`
  * **输出格式要求:** 完整TOML，含注释，UTF-8，无BOM。

-----

## 3 约束与强制规范

  * **技术栈:** Python 配置 (TOML)
  * **项目规范:** 配置驱动、snake_case命名、注释清晰、必须提交版本库。
  * **特定限制:** 仅含基础配置（[project]、[database]、[logging]），路径为相对路径。

-----

## 4 辅助信息与示例

  * **项目名称:** AQUA躺赢之路
  * **示例结构:**
    ```toml
    [project]
    name = "AQUA"
    version = "0.1.0"
    description = "AQUA 躺赢之路"

    [database]
    path = "data/aqua.duckdb"

    [logging]
    level = "INFO"
    file_path = "logs/application.log"
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T01
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.2：创建环境变量模板文件**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.2
  * **任务描述:** 创建 `config/.env.example` 文件，作为敏感配置和本地覆盖的模板，并加入 `.gitignore`。
  * **相关文件/代码:**
      * `config/.env.example` (待创建)
      * `.gitignore` (需修改)
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`、`docs/AQUA_GUIDE.md#[GUIDE-07]`、`0302_REQUEST.md`、`@/rules/development_standards.mdc`

-----

## 2 任务目标与预期输出

  * **具体目标:** 在 `config/` 下创建 `.env.example`，并确保 `.gitignore` 忽略实际 `.env` 文件。
  * **预期输出类型:** 环境变量模板文件
  * **预期输出文件/位置:** `config/.env.example`
  * **输出格式要求:** 仅包含非敏感示例变量，注释说明用途。

-----

## 3 约束与强制规范

  * **技术栈:** Python/Node.js 环境变量
  * **项目规范:** 不得包含真实敏感信息，变量命名需清晰，注释说明。
  * **特定限制:** 仅示例变量，实际敏感信息本地维护。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```env
    # 数据库连接字符串
    DATABASE_URL=duckdb:///data/aqua.duckdb
    # 日志级别
    LOG_LEVEL=INFO
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T02
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.3：完善环境初始化脚本核心功能**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.3
  * **任务描述:** 完善 `scripts/env_init.py`，实现自动虚拟环境、依赖、数据库初始化。
  * **相关文件/代码:**
      * `scripts/env_init.py` (待完善)
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`、`docs/AQUA_GUIDE.md#[GUIDE-07]`、`0302_REQUEST.md`、`@/rules/development_standards.mdc`

-----

## 2 任务目标与预期输出

  * **具体目标:** 使 `env_init.py` 支持自动创建虚拟环境、安装依赖、初始化数据库。
  * **预期输出类型:** Python 脚本
  * **预期输出文件/位置:** `scripts/env_init.py`
  * **输出格式要求:** 结构清晰，注释完整，兼容Win/OSX。

-----

## 3 约束与强制规范

  * **技术栈:** Python
  * **项目规范:** 脚本需跨平台，路径用os.path，异常处理和日志。
  * **特定限制:** 依赖项、数据库路径均从 `config/settings.toml` 读取。

-----

## 4 辅助信息与示例

  * **示例结构:**
    - 自动检测/创建.venv
    - pip/uv依赖安装
    - 数据库文件初始化

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T03
  * **task_status:** pending_review
  
---

### **原子化任务提示词 - Task 0.1.4：编写项目启动脚本**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.4
  * **任务描述:** 创建用于启动后端和前端服务的脚本，并强制在启动时调用 `env_init.py` 进行环境校验。
  * **相关文件/代码:**
      * `start_backend.sh`、`start_frontend.sh`、`start_backend.bat`、`start_frontend.bat`（待创建）
      * `scripts/env_init.py`（需调用）
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`、`0301_BASE.md`、`0302_REQUEST.md`
      * @/rules/development_standards.mdc、project_management_standards.mdc、mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 创建跨平台启动脚本，自动校验并初始化环境，确保后端/前端服务可一键启动。
  * **预期输出类型:** Shell/Bat 脚本
  * **预期输出文件/位置:** 项目根目录下
  * **输出格式要求:**
    - 脚本命名采用小写snake_case
    - 注释为中文，说明每步操作目的
    - 必须调用 `env_init.py` 校验环境
    - 路径、参数均从 `config/settings.toml` 读取
    - 禁止硬编码敏感信息
    - 结构清晰，便于自动化集成

-----

## 3 约束与强制规范

  * 你必须确保脚本兼容Win11和OSX，路径拼接用变量，禁止写死绝对路径。
  * 脚本需先调用 `env_init.py`，如环境异常应中止并输出结构化错误信息。
  * 所有依赖、端口、环境变量等参数，必须从 `config/settings.toml` 读取。
  * 脚本需有详细中文注释，说明每步操作的业务目的。
  * 变更脚本后，需同步更新 `logs/dev_log.md`，记录变更内容、影响范围、验证状态。
  * 脚本执行日志需结构化输出，含北京时间（UTC+8）时间戳、级别、模块、消息。
  * 禁止在脚本中暴露任何敏感信息。
  * 脚本变更需同步登记 `Dev_Tasks_Epic0.md`。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```sh
    #!/bin/bash
    # 启动前端服务，自动校验环境
    python scripts/env_init.py || exit 1
    pnpm run dev
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T04
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.5：创建初始依赖文件**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.5
  * **任务描述:** 为后端 Python 依赖和前端 Node.js 依赖创建初始清单文件。
  * **相关文件/代码:**
      * `requirements.txt`（待创建）
      * `package.json`（待创建）
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`、`0301_BASE.md`、`0302_REQUEST.md`
      * @/rules/development_standards.mdc、project_management_standards.mdc、mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 创建后端和前端的依赖清单，确保依赖管理自动化、可追溯。
  * **预期输出类型:** 依赖清单文件
  * **预期输出文件/位置:** 项目根目录下
  * **输出格式要求:**
    - requirements.txt、package.json 命名规范
    - 注释说明依赖用途（如支持）
    - 禁止包含未使用或敏感依赖
    - 结构清晰，便于自动化集成

-----

## 3 约束与强制规范

  * 依赖项需与实际项目技术栈、PRD、数据字典一致。
  * 禁止硬编码敏感信息。
  * 依赖变更需同步更新 logs/dev_log.md，记录变更内容、影响范围、验证状态。
  * 依赖管理需支持自动化（如uv/pnpm），并兼容Win11/OSX。
  * 依赖清单变更需同步登记 Dev_Tasks_Epic0.md。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```txt
    polars==0.19.0  # 高性能数据处理
    duckdb==0.8.0   # 内嵌数据库
    ```
    ```json
    {
      "name": "aqua-frontend",
      "dependencies": {
        "react": "^18.0.0"
      }
    }
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T05
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.6：设定 Node.js 版本**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.6
  * **任务描述:** 明确项目所需的 Node.js 版本，以确保开发环境一致性。
  * **相关文件/代码:**
      * `.nvmrc`（待创建）
      * `package.json`（engines字段，待补充）
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`、`0301_BASE.md`、`0302_REQUEST.md`
      * @/rules/development_standards.mdc、project_management_standards.mdc、mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 明确并文档化Node.js版本，保证团队环境一致。
  * **预期输出类型:** 版本声明文件/字段
  * **预期输出文件/位置:** 项目根目录下
  * **输出格式要求:**
    - .nvmrc内容为Node.js主版本号
    - package.json中engines字段需同步
    - 注释说明版本选择依据

-----

## 3 约束与强制规范

  * Node.js版本需与前端依赖、CI/CD环境兼容。
  * 版本声明变更需同步更新 logs/dev_log.md。
  * 禁止随意更改主版本，需评估兼容性。
  * 变更需同步登记 Dev_Tasks_Epic0.md。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```txt
    18.16.0
    ```
    ```json
    {
      "engines": {
        "node": ">=18.16.0"
      }
    }
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T06
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.7：创建项目结构**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.7
  * **任务描述:** 创建项目结构，包括后端、前端、数据存储等。
  * **相关文件/代码:**
      * 后端代码结构
      * 前端代码结构
      * 数据存储结构
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`、`0301_BASE.md`、`0302_REQUEST.md`
      * @/rules/development_standards.mdc、project_management_standards.mdc、mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 创建完整项目结构，包括后端、前端、数据存储等。
  * **预期输出类型:** 项目结构
  * **预期输出文件/位置:** 项目根目录下
  * **输出格式要求:** 结构清晰，目录命名规范，文件命名清晰。

-----

## 3 约束与强制规范

  * **技术栈:** Python、Node.js
  * **项目规范:** 项目结构需符合项目管理标准，目录命名规范，文件命名清晰。
  * **特定限制:** 项目结构需包含后端、前端、数据存储等。

-----

## 4 辅助信息与示例

  * **示例结构:**
    - 后端代码结构
    - 前端代码结构
    - 数据存储结构

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T07
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.8：迁移并完善单一事实源文档**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.8
  * **任务描述:** 根据 `logs/re_doc_list.md` 的计划，将原有分散的文档内容迁移、整合并完善到 `docs/AQUA_GUIDE.md` 中，建立项目的唯一事实源。
  * **相关文件/代码:**
      * `docs/AQUA_GUIDE.md` (待创建/完善)
      * `docs/old/*` (待归档的旧文档)
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`, `docs/AQUA_GUIDE.md#[GUIDE-07]`
      * `@/rules/*.mdc`

-----

## 2 任务目标与预期输出

  * **具体目标:** 产出统一、无冗余、可被AI和脚本精确引用的单一事实源文档 `AQUA_GUIDE.md`。
  * **预期输出类型:** Markdown 文档
  * **预期输出文件/位置:** `docs/AQUA_GUIDE.md`
  * **输出格式要求:**
    - 文档采用多级标题组织，内容清晰，包含中文注释。
    - 结构和锚点严格遵循 `[GUIDE-XX]` 规范。
    - 强制采用 UTF-8 编码无BOM，，LF 换行。

-----

## 3 约束与强制规范

  * **技术栈:** Markdown, Python, Node.js（文档需涵盖相关技术栈）
  * **项目规范:** 必须遵循 `logs/re_doc_list.md` 的任务拆解，确保内容迁移无遗漏，且符合所有项目规则。
  * **特定限制:** 最终 `AQUA_GUIDE.md` 需完整包含项目介绍、业务场景、目录结构、技术栈、功能模块与API、数据字典、规则与规范、变更历史和FAQ等所有核心信息。

-----

## 4 辅助信息与示例

  * **参考文件:**
    - `logs/re_doc_list.md` (迁移任务清单)
    - `docs/AQUA_GUIDE.md` (最终产出模板)

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T08

---

### **原子化任务提示词 - Task 0.1.9：创建项目测试用例**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.9
  * **任务描述:** 创建初始测试用例，包括单元测试、集成测试和端到端测试，确保测试框架（Pytest 和 Jest/Vitest）可正常运行。注意在后端必须使用UV管理虚拟环境。
  * **相关文件/代码:**
      * tests/unit/（单元测试代码，如 test_utils.py）
      * tests/integration/（集成测试代码）
      * tests/e2e/（端到端测试代码）
      * scripts/env_init.py
      * scripts/logger.py
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`, `0301_BASE.md`, `0302_REQUEST.md`
      * @/rules/development_standards.mdc, project_management_standards.mdc, mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 验证测试框架的集成并为后续测试开发奠定基础，确保测试用例覆盖核心功能。
  * **预期输出类型:** Python/TypeScript 测试代码
  * **预期输出文件/位置:** tests/unit/, tests/integration/, tests/e2e/
  * **输出格式要求:**
    - 测试文件命名规范（如 test_module.py, module.test.ts）。
    - 包含至少一个通过的简单断言，带中文注释。
    - 强制采用 UTF-8 编码无BOM，，LF 换行。

-----

## 3 约束与强制规范

  * **技术栈:** Python (Pytest), Node.js (Jest/Vitest)
  * **项目规范:** 测试用例需符合 testing_standards.mdc，代码结构清晰，提交版本控制。
  * **特定限制:** 测试用例仅覆盖基础功能，避免复杂业务逻辑。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```python
    # tests/unit/test_utils.py
    # -*- coding: utf-8 -*-
    def test_addition():
        """测试简单的加法功能"""
        assert 1 + 1 == 2
    ```
    ```typescript
    // frontend/tests/unit/example.test.ts
    // 测试简单的乘法功能
    test('multiplies 2 * 3 to equal 6', () => {
      expect(2 * 3).toBe(6);
    });
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T09
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.10：创建项目部署脚本**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.10
  * **任务描述:** 创建统一的、参数化的跨平台部署脚本（`deploy.sh`, `deploy.bat`），通过接收环境参数（`local`, `test`, `prod`）来执行对应环境的部署流程，并强制调用 `env_init.py` 进行环境校验。
  * **相关文件/代码:**
      * `scripts/deploy.sh` (待创建, 支持 `local`, `test`, `prod` 参数)
      * `scripts/deploy.bat` (待创建, 支持 `local`, `test`, `prod` 参数)
      * `scripts/env_init.py`（需调用）
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`, `0301_BASE.md`, `0302_REQUEST.md`
      * @/rules/development_standards.mdc, project_management_standards.mdc, mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 创建统一、可维护、可扩展的跨平台部署脚本，通过参数实现不同环境（本地开发、测试、生产）的一键部署。
  * **预期输出类型:** Shell/Bat 脚本
  * **预期输出文件/位置:** `scripts/deploy.sh`, `scripts/deploy.bat`
  * **输出格式要求:**
    - 脚本命名采用小写 snake_case。
    - 包含中文注释，说明每步操作目的。
    - 必须调用 env_init.py 校验环境。
    - 参数从 config/settings.toml 读取，禁止硬编码敏感信息。
    - 强制采用 UTF-8 编码无BOM，，LF 换行。

-----

## 3 约束与强制规范

  * **技术栈:** Python, Shell, Batch
  * **项目规范:** 脚本需兼容 Win11 和 macOS，路径拼接用变量，异常处理和日志记录。
  * **特定限制:** 脚本需先调用 env_init.py，环境异常时中止并输出结构化错误信息。变更需更新 logs/dev_log.md 和 Dev_Tasks_Epic0.md。

-----

## 4 辅助信息与示例

  * **调用示例:**
    ```sh
    # 本地环境部署 (macOS/Linux)
    ./scripts/deploy.sh local

    # 生产环境部署 (Windows)
    scripts\deploy.bat prod
    ```
  * **脚本内部结构示例 (`deploy.sh`):**
    ```sh
    #!/bin/bash
    #
    # 统一部署脚本
    # 用法: ./deploy.sh [local|test|prod]
    #
    set -e # 任何命令失败则退出

    # 1. 解析环境参数
    ENV=$1
    if [[ -z "$ENV" ]]; then
        echo "错误: 未提供环境参数 (local, test, or prod)"
        exit 1
    fi

    # 2. 强制环境校验
    echo "正在校验环境..."
    python3 scripts/env_init.py || { echo "环境校验失败，中止部署。"; exit 1; }
    echo "环境校验通过。"

    # 3. 根据环境执行不同部署逻辑
    case "$ENV" in
        local)
            echo "正在启动本地开发环境..."
            # 复用开发启动脚本
            ./start_frontend.sh & ./start_backend.sh
            ;;
        test|prod)
            echo "正在为 $ENV 环境构建和部署..."
            # 此处为测试/生产环境的构建和启动逻辑
            # 例如: pnpm build, gunicorn ...
            echo "生产环境部署逻辑待实现。"
            ;;
        *)
            echo "错误: 无效的环境参数 '$ENV'。请使用 'local', 'test', or 'prod'."
            exit 1
            ;;
    esac

    echo "部署脚本执行完毕。"
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T10
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.11：创建项目监控脚本**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.11
  * **任务描述:** 创建监控脚本，针对后端、前端和数据库的状态监控，确保系统运行可观测。
  * **相关文件/代码:**
      * scripts/monitor_backend.py（后端服务监控）
      * scripts/monitor_frontend.sh（前端服务监控）
      * scripts/monitor_database.py（数据库状态监控）
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`, `0301_BASE.md`, `0302_REQUEST.md`
      * @/rules/development_standards.mdc, project_management_standards.mdc, mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 提供监控脚本，实时检查后端、前端和数据库的运行状态，输出结构化日志。
  * **预期输出类型:** Python/Shell 脚本
  * **预期输出文件/位置:** scripts/ 目录下
  * **输出格式要求:**
    - 脚本命名采用小写 snake_case。
    - 包含中文注释，说明监控逻辑。
    - 日志输出含北京时间（UTC+8）时间戳、级别、模块、消息。
    - 强制采用 UTF-8 编码无BOM，，LF 换行。

-----

## 3 约束与强制规范

  * **技术栈:** Python, Shell
  * **项目规范:** 脚本需兼容 Win11 和 macOS，路径从 config/settings.toml 读取，包含异常处理。
  * **特定限制:** 变更需更新 logs/dev_log.md 和 Dev_Tasks_Epic0.md，禁止硬编码敏感信息。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```python
    # scripts/monitor_backend.py
    # -*- coding: utf-8 -*-
    import logging
    import time
    # 配置日志
    logging.basicConfig(filename="logs/monitor.log", level=logging.INFO)
    def monitor_backend():
        """监控后端服务状态"""
        logging.info(f"{time.strftime('%Y-%m-%d %H:%M:%S+08:00')} - Backend status: running")
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T11
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.12：创建项目备份脚本**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.F极简ature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.12
  * **任务描述:** 创建备份脚本，支持后端、前端和数据库的备份，覆盖本地、测试和生产环境，确保数据和代码安全。
  * **相关文件/代码:**
      * scripts/backup_backend.sh（后端代码备份）
      * scripts/backup_frontend.sh（前端代码备份）
      * scripts/backup_database.sh（数据库备份）
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`, `0301_BASE.md`, `0302_REQUEST.md`
      * @/rules/development_standards.mdc, project_management_standards.mdc, mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 提供跨平台备份脚本，自动化备份后端、前端和数据库，支持不同环境（本地、测试、生产）。
  * **预期输出类型:** Shell 脚本
  * **预期输出文件/位置:** scripts/ 目录下
  * **输出格式要求:**
    - 脚本命名采用小写 snake_case。
    - 包含中文注释，说明备份逻辑和环境。
    - 日志输出含北京时间（UTC+8）时间戳、级别、模块、消息。
    - 强制采用 UTF-8 编码无BOM，，LF 换行。

-----

## 3 约束与强制规范

  * **技术栈:** Shell, Python（可选）
  * **项目规范:** 脚本需兼容 Win11 和 macOS，路径从 config/settings.toml 读取，包含异常处理和日志记录。
  * **特定限制:** 备份需支持全量和增量备份，变更需更新 logs/dev_log.md 和 Dev_Tasks_Epic0.md，禁止硬编码敏感信息。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```sh
    # scripts/backup_database.sh
    #!/bin/bash
    # 备份数据库到指定路径
    source config/.env
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="backups/database_${TIMESTAMP}.bak"
    duckdb $DATABASE_URL "BACKUP TO '$BACKUP_PATH'"
    echo "$(date +%Y-%m-%dT%H:%M:%S+08:00) - INFO - Database - Backup created at $BACKUP_PATH" >> logs/backup.log
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T12
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.1.13：创建项目恢复脚本**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.1: 项目环境与依赖管理初始化
  * **任务 ID:** Task 0.1.13
  * **任务描述:** 创建恢复脚本，支持从备份中恢复后端、前端和数据库，覆盖本地、测试和生产环境，要求使用TDD模式进行。
  * **相关文件/代码:**
      * scripts/restore_backend.sh（后端代码恢复）
      * scripts/restore_frontend.sh（前端代码恢复）
      * scripts/restore_database.sh（数据库恢复）
  * **核心关联文档和必须遵守的规则:**
      * `docs/AQUA_GUIDE.md#[GUIDE-01]`, `0301_BASE.md`, `0302_REQUEST.md`
      * @/rules/development_standards.mdc, project_management_standards.mdc, mvp_rules.mdc

-----

## 2 任务目标与预期输出

  * **具体目标:** 提供跨平台恢复脚本，自动化从备份恢复系统，适用于不同环境。
  * **预期输出类型:** Shell 脚本
  * **预期输出文件/位置:** scripts/ 目录下
  * **输出格式要求:**
    - 脚本命名采用小写 snake_case。
    - 包含中文注释，说明恢复逻辑和环境。
    - 日志输出含北京时间（UTC+8）时间戳、级别、模块、消息。
    - 强制采用 UTF-8 编码无BOM，，LF 换行。

-----

## 3 约束与强制规范

  * **技术栈:** Shell, Python（可选）
  * **项目规范:** 脚本需兼容 Win11 和 macOS，路径从 config/settings.toml 读取，包含异常处理和日志记录。
  * **特定限制:** 恢复脚本需验证备份完整性，变更需更新 logs/dev_log.md 和 Dev_Tasks_Epic0.md，禁止硬编码敏感信息。

-----

## 4 辅助信息与示例

  * **示例结构:**
    ```sh
    # scripts/restore_database.sh
    #!/bin/bash
    # 从备份恢复数据库
    source config/.env
    BACKUP_PATH=$1
    duckdb $DATABASE_URL "RESTORE FROM '$BACKUP_PATH'"
    echo "$(date +%Y-%m-%dT%H:%M:%S+08:00) - INFO - Database - Restored from $BACKUP_PATH" >> logs/restore.log
    ```

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F01-T13
  * **task_status:** pending_review


---

### **原子化任务提示词 - Task 0.2.1：深入研读权威文档**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.2: 个人任务管理与 AI 协作流建立
  * **任务 ID:** Task 0.2.1
  * **任务描述:** 认真阅读《项目蓝图》、《产品需求文档》、《功能模块定义》及所有 `.rules` 规则文件，这是您与 AI 协作的基础。
  * **相关文件/代码:**
      * `docs/AQUA_GUIDE.md`
      * `docs/01_PRD.md`
      * `docs/AQUA_GUIDE.md#[GUIDE-03]`
      * `@/rules/*.mdc` (所有规则文件)
  * **核心关联文档和必须遵守的规则:**
      * project_management_standards.mdc (角色与行为)
      * development_standards.mdc (AI/自动化合规性)

-----

## 2 任务目标与预期输出

  * **具体目标:** 全面理解项目核心文档和所有强制规则，形成统一的认知基准，确保与AI协作的准确性。
  * **预期输出类型:** 个人知识理解（无需代码产出）
  * **预期输出文件/位置:** N/A
  * **输出格式要求:** N/A

-----

## 3 约束与强制规范

  * 你必须至少通读一遍所有列出的权威文档和规则文件，并理解其核心要义。
  * 你必须主动识别文档中可能存在的冲突或模糊之处，并提问澄清（参考 [MODE: RESEARCH] 提问范式）。
  * 你必须在后续的任务执行中，严格遵循这些文档和规则的要求，将其内化为开发习惯。
  * 你必须确保AI协作的每次交互都基于这些权威文档，并能及时指出AI的非合规输出。

-----

## 4 辅助信息与示例

  * **建议研读策略:**
    - 优先阅读 `development_standards.mdc` (优先级最高)。
    - 其次阅读 `project_management_standards.mdc`、`data_standards.mdc`、`testing_standards.mdc`、`deployment_standards.mdc`、`mvp_rules.mdc`。
    - 结合 `docs/AQUA_GUIDE.md#[GUIDE-01]`、`01_PRD.md`、`docs/AQUA_GUIDE.md#[GUIDE-03]` 理解项目全貌和功能细节。

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F02-T01
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.2.2：初始化任务管理文件**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.2: 个人任务管理与 AI 协作流建立
  * **任务 ID:** Task 0.2.2
  * **任务描述:** 创建 `Dev_Tasks_Epic0.md` 文件，并录入初步的 Epic 和 Feature 结构，遵循多级标题和元数据格式。
  * **相关文件/代码:**
      * `docs/tasks/Dev_Tasks_Epic0.md` (待创建)
  * **核心关联文档和必须遵守的规则:**
      * project_management_standards.mdc (任务流与进度追踪、任务/进度登记模板)

-----

## 2 任务目标与预期输出

  * **具体目标:** 建立项目任务的唯一结构化登记源，便于管理、追溯和AI辅助更新。
  * **预期输出类型:** Markdown 文档
  * **预期输出文件/位置:** `docs/tasks/Dev_Tasks_Epic0.md`
  * **输出格式要求:**
    - 必须严格遵循 `project_management_standards.mdc` 中"任务/进度登记模板"的格式和字段。
    - 任务名称、负责人、状态、预计工时、开始日期、目标等字段必须完整。
    - 使用多级标题（`#` Epic, `##` Feature, `###` Task, `####` Sub-task）清晰组织。
    - 需包含文件头部的说明性文字，例如"任务流唯一登记源，所有主线-支线-子任务-原子化任务均需结构化登记，便于追溯与自动化。"
    - 强制采用UTF-8编码，LF换行。

-----

## 3 约束与强制规范

  * 你必须严格按照 `project_management_standards.mdc` 中定义的任务登记模板来初始化 `Dev_Tasks_Epic0.md` 的结构。
  * 你必须确保 `Dev_Tasks_Epic0.md` 中的所有任务都可追溯到 Epic 和 Feature。
  * 你必须确保任务状态流转（待开始→进行中→已完成→阻断）在文档中清晰体现。
  * 你必须将 `Dev_Tasks_Epic0.md` 纳入版本控制。
  * 你必须在完成此任务后，同步更新 `logs/dev_log.md`，记录此次文件的创建与初始化，并注明 `prompt_id`。

-----

## 4 辅助信息与示例

  * **参考模板:** `project_management_standards.mdc` 中"任务/进度登记模板"部分。
  * **初始结构建议:** 可仅录入 Epic 0 的顶层结构及 Feature 0.1、0.2、0.3、0.4、0.5 的名称。

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F02-T02
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.2.3：明确日志文件模板与规范**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.2: 个人任务管理与 AI 协作流建立
  * **任务 ID:** Task 0.2.3
  * **任务描述:** 创建 `logs/dev_log.md` 文件，并设置其结构化日志模板，确保符合 `project_management_standards.mdc` 的日志规范。
  * **相关文件/代码:**
      * `logs/dev_log.md` (待创建)
  * **核心关联文档和必须遵守的规则:**
      * project_management_standards.mdc (日志与时间戳、日志记录模板、日志文件命名、变更日志模板)
      * development_standards.mdc (日志与时间戳)

-----

## 2 任务目标与预期输出

  * **具体目标:** 建立统一的开发日志记录标准，确保所有开发、修复、优化过程可追溯、可审计。
  * **预期输出类型:** Markdown 文档 (结构化日志)
  * **预期输出文件/位置:** `logs/dev_log.md`
  * **输出格式要求:**
    - 必须严格遵循 `project_management_standards.mdc` 中"变更日志模板"的格式。
    - 字段包括：`日期`、`类型`（功能/修复/重构）、`描述`、`影响模块`、`验证状态`。
    - `日期` 字段必须使用北京时间（UTC+8）ISO 8601 格式，精确到秒。
    - 需包含文件头部的说明性文字。
    - 强制采用UTF-8编码，LF换行。

-----

## 3 约束与强制规范

  * 你必须严格按照 `project_management_standards.mdc` 中定义的变更日志模板来初始化 `logs/dev_log.md`。
  * 你必须确保所有开发、修复、优化操作，在完成后立即同步更新此日志文件。
  * 你必须确保日志内容简洁、清晰，准确描述变更，并注明影响模块和验证状态。
  * 你必须将 `logs/dev_log.md` 纳入版本控制。
  * 你必须确保日志文件命名遵循 `project_management_standards.mdc` 规范（`logs/app_{YYYYMMDD}.log` 为运行时日志，`logs/dev_log.md` 为开发变更日志）。
  * 你必须在完成此任务后，同步更新 `Dev_Tasks_Epic0.md`，并注明 `prompt_id`。

-----

## 4 辅助信息与示例

  * **参考模板:** `project_management_standards.mdc` 中"变更日志模板"部分。
  * **初始内容建议:** 可先添加一条记录，说明本文件的创建和规范初始化。

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F02-T03
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.2.4：建立结构化 AI 提示词库**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.2: 个人任务管理与 AI 协作流建立
  * **任务 ID:** Task 0.2.4
  * **任务描述:** 创建 `docs/ai_prompts/` 目录，并开始维护用于指导 AI 协作的结构化提示词模板。
  * **相关文件/代码:**
      * `docs/ai_prompts/` (待创建目录)
  * **核心关联文档和必须遵守的规则:**
      * project_management_standards.mdc (AI 协作元数据)
      * development_standards.mdc (AI/自动化合规性)

-----

## 2 任务目标与预期输出

  * **具体目标:** 建立AI提示词的统一管理库，确保AI协作效率、可控性和产出质量。
  * **预期输出类型:** 目录结构，以及未来可能填充的Markdown/文本文件
  * **预期输出文件/位置:** `docs/ai_prompts/` 目录
  * **输出格式要求:**
    - 目录命名采用小写snake_case。
    - 提示词模板文件命名应清晰反映其用途。
    - 模板内容应结构化，包含任务背景、目标、约束、示例等，便于AI理解和执行。
    - 强制采用UTF-8编码。

-----

## 3 约束与强制规范

  * 你必须创建 `docs/ai_prompts/` 目录，并确保其可被版本控制。
  * 你必须确保未来添加的提示词模板遵循统一的结构和规范，如 `chating.md` 中的原子化任务提示词模板。
  * 你必须在完成此任务后，同步更新 `logs/dev_log.md`，记录目录的创建，并注明 `prompt_id`。
  * 你必须确保AI在生成/推理/编辑/提交前后，自动校验是否符合本规则及权威文档，并使用此提示词库来指导AI行为。

-----

## 4 辅助信息与示例

  * **初始内容建议:** 目录创建后可暂时为空，待后续具体任务再填充提示词模板。
  * **未来使用场景:** 用于为AI提供特定任务的详细指令，如数据采集、代码重构、测试用例编写等。

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F02-T04
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.2.5：明确 AI 协作合规性要求**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.2: 个人任务管理与 AI 协作流建立
  * **任务 ID:** Task 0.2.5
  * **任务描述:** 仔细理解并确认 AI 输出的审查流程和 Git Hooks 校验机制，确保 AI 协作可控。
  * **相关文件/代码:**
      * `@/rules/development_standards.mdc` (AI/自动化合规性)
      * Git Hooks 配置 (如 `.git/hooks/pre-commit`)
  * **核心关联文档和必须遵守的规则:**
      * development_standards.mdc (AI/自动化合规性)
      * project_management_standards.mdc (AI 协作元数据)

-----

## 2 任务目标与预期输出

  * **具体目标:** 建立人与AI协同开发的清晰界限和审查机制，确保AI产出始终符合项目规范和质量要求，并可追溯。
  * **预期输出类型:** 个人知识理解与行为规范（无需代码产出）
  * **预期输出文件/位置:** N/A
  * **输出格式要求:** N/A

-----

## 3 约束与强制规范

  * 你必须明确AI生成/推理/编辑/提交前后的自动校验流程，理解AI在何时会暂停操作并输出警告。
  * 你必须理解Git Hooks在代码提交前的强制校验作用，特别是对Commit Message、代码格式、Lint等方面的约束。
  * 你必须在每次AI生成重要代码或文档时，进行人工审查，确保其符合所有强制规范和业务逻辑。
  * 你必须记录无法自动校验或需要人工审核的AI操作，并将其记录到 `logs/dev_log.md`。
  * 你必须确保任何AI产出或AI协作产生的变更都可追溯，并符合版本管理策略。
  * 你必须在完成此任务后，同步更新 `logs/dev_log.md` 和 `Dev_Tasks_Epic0.md`。

-----

## 4 辅助信息与示例

  * **审查要点:** 命名规范、中文注释、类型注解、异常处理、日志记录、配置驱动、数据结构对齐、测试覆盖率等。
  * **Git Hooks:** 可参考 `pre-commit` 框架及其配置方式。

-----

## 5 AI 协作元数据

  * **ai_generated:** true
  * **prompt_id:** Epic0-F02-T05
  * **task_status:** pending_review

---

### **原子化任务提示词 - Task 0.3.1：安装和配置 `pre-commit` 框架**

## 1 任务背景与上下文

  * **所属 Epic/Feature:** Epic 0.Feature 0.3: 强制规范与自动化校验机制部署
  * **任务 ID:** Task 0.3.1
  * **任务描述:** 在项目中安装 `pre-commit` 工具，并进行初步设置。
  * **相关文件/代码:**
      * `.pre-commit-config.yaml` (待创建)
      * `.git/hooks/pre-commit` (自动生成/修改)
  * **核心关联文档和必须遵守的规则:**
      * development_standards.mdc (自动化与跨平台、AI/自动化合规性)
      * testing_standards.mdc (自动化与持续集成)

-----

## 2 任务目标与预期输出

  * **具体目标:** 在项目版本控制中集成代码提交前自动校验框架，强制执行代码规范。
  * **预期输出类型:** `pre-commit` 配置和安装结果
  * **预期输出文件/位置:** 项目根目录及 `.git/hooks/` 目录
  * **输出格式要求:**
    - `pre-commit`


    以下是为每个未完成任务设计的专业提示词模板，严格遵循您要求的格式和分析逻辑：

---

### 任务提示词 - Task 0.1.10：创建参数化部署脚本

1. **任务背景与上下文**  
   - 所属 Epic/Feature: Epic 0 / Feature 0.1  
   - 任务 ID: Task 0.1.10  
   - 任务描述：创建跨平台部署脚本（.sh/.bat），通过环境参数（local/test/prod）区分部署逻辑，强制调用env_init.py进行环境校验，当前需完成集成测试  
   - 相关文件/代码：  
     - `scripts/deploy.sh`（待完成）  
     - `scripts/deploy.bat`（待完成）  
     - `tests/integration/test_deployment.py`（待创建）  
   - 核心关联文档：  
     - `AQUA_GUIDE.md#[GUIDE-04]`（环境管理规范）  
     - `0302_REQUEST.md#部署规范`  
     - `@/rules/deployment_standards.mdc`  

2. **任务目标与预期输出**  
   - 具体目标：  
     1. 实现参数化部署逻辑（local：开发环境，test：测试环境，prod：生产环境）  
     2. 集成环境校验（强制调用env_init.py --env $1）  
     3. 编写验证三种环境的集成测试用例  
   - 预期输出文件：  
     - `scripts/deploy.sh`（支持参数校验和分支逻辑）  
     - `scripts/deploy.bat`（Windows兼容实现）  
     - `tests/integration/test_deployment.py`（覆盖率≥80%）  
   - 输出格式要求：  
     - 部署脚本含中文注释和错误处理  
     - 测试用例使用pytest+unittest.mock架构  

3. **约束与强制规范**  
   - 技术栈：Shell/Python（跨平台兼容）  
   - 项目规范：  
     - 禁止硬编码路径（使用settings.toml配置）  
     - 生产环境操作需二次确认  
     - 错误日志含北京时区时间戳  
   - 特定限制：  
     - local环境仅启动服务  
     - prod环境需包含备份流程  

4. **辅助信息与示例**  
   ```bash
   # deploy.sh 示例片段
   case "$1" in
     local)
       python scripts/env_init.py --env local || exit 1
       echo "启动本地开发服务..."
       ;;
     prod)
       read -p "确认部署生产环境？(y/n)" confirm
       [ "$confirm" != "y" ] && exit 0
       python scripts/backup_all.sh  # 先备份
       # 部署逻辑...
       ;;
   esac
   ```

5. **AI 协作元数据**  
   - ai_generated: true  
   - prompt_id: Epic0-F01-T10  
   - task_status: 进行中  

---

### 任务提示词 - Task 0.4.1：创建功能模块定义文件

1. **任务背景与上下文**  
   - 所属 Epic/Feature: Epic 0 / Feature 0.4  
   - 任务 ID: Task 0.4.1  
   - 任务描述：创建API契约唯一事实源文件，定义核心功能模块  
   - 相关文件/代码：  
     - `docs/docs/AQUA_GUIDE.md#[GUIDE-03]`（已经存在，请阅读）  
     - `01_PRD.md`（输入参考）  
   - 核心关联文档：  
     - `AQUA_GUIDE.md#[API-CONTRACT]`  
     - `@/rules/api_design_standards.mdc`  

2. **任务目标与预期输出**  
   - 具体目标：  
     1. 结构化定义两大核心模块：  
        - 数据导入模块  （#### 010101.数据导入 (data_import)）
        - 数据浏览模块  （### 0102.数据浏览 (data_view)）
     2. 明确各模块前后端职责边界  
   - 预期更新文件：`docs/AQUA_GUIDE.md#[GUIDE-04]`  
   - 输出格式要求：  
     - Markdown格式三级标题结构  
     - 模块定义包含：功能描述、输入/输出、错误代码  

3. **约束与强制规范**  
   - 技术栈：OpenAPI 3.0规范  
   - 项目规范：  
     - 必须使用DuckDB数据类型  
     - 错误代码遵循HTTP状态码扩展  
   - 特定限制：  
     - 优先定义readonly接口  

4. **辅助信息与示例**  
   ```markdown
   ### 数据导入模块
   **前端职责**：
   - 提供文件上传组件（支持CSV/Parquet）
   - 调用POST /api/import接口
   
   **后端契约**：
   ```endpoint
   POST /api/import
   Request: {file: base64, format: enum[csv,parquet]}
   Response: {job_id: string, estimated_time: int}
   Error 400: INVALID_FORMAT
   ```
   ```

5. **AI 协作元数据**  
   - ai_generated: true  
   - prompt_id: Epic0-F04-T01  
   - task_status: 待开始  

---

### 任务提示词 - Task 0.4.2：初步定义核心功能API接口

1. **任务背景与上下文**  
   - 所属 Epic/Feature: Epic 0 / Feature 0.4  
   - 任务 ID: Task 0.4.2  
   - 任务描述：基于docs/AQUA_GUIDE.md#[GUIDE-03]框架，定义具体API接口规范  
   - 相关文件/代码：  
     - `docs/docs/AQUA_GUIDE.md#[GUIDE-03]`（待修改）  
     - `database_schema.sql`（输入参考）  
   - 核心关联文档：  
     - `AQUA_GUIDE.md#[REST-STANDARD]`  
     - `@/rules/data_model_standards.mdc`  

2. **任务目标与预期输出**  
   - 具体目标：  
     1. 完成6个核心接口定义：  
        - 数据导入/元数据查询/回测执行等  
     2. 明确定义：  
        - 路径参数/查询参数/请求体  
        - 响应字段类型和示例  
   - 预期输出：更新`docs/AQUA_GUIDE.md#[GUIDE-03]`  
   - 输出格式要求：  
     - 使用JSON Schema描述数据结构  
     - 包含成功/错误响应示例  

3. **约束与强制规范**  
   - 技术栈：FastAPI兼容规范  
   - 项目规范：  
     - 所有时间字段用ISO8601格式  
     - 分页参数统一用?limit=10&offset=0  
   - 特定限制：  
     - 禁止定义前端渲染相关字段  

4. **辅助信息与示例**  
   ```markdown
   ### 数据库浏览模块
   ```schema
   GET /api/tables/{table_name}
   Parameters: 
     - name: columns
       in: query
       schema: [string]
   Response 200:
   {
     "columns": [
       {"name": "date", "type": "DATE"},
       {"name": "value", "type": "FLOAT"}
     ],
     "sample_data": [...] 
   }
   ```
   ```

5. **AI 协作元数据**  
   - ai_generated: true  
   - prompt_id: Epic0-F04-T02  
   - task_status: 待开始  

---

（由于字数限制，以下任务提示词采用精简格式）

### 任务提示词 - Task 0.4.3：利用AI辅助草拟接口定义

1. **背景**：基于PRD生成API草稿，人工审查修正  
2. **目标**：  
   - 输入：`01_PRD.md`需求描述  
   - 输出：API草案（Markdown表格形式）  
   - 审查要点：参数完备性/响应结构/错误覆盖  
3. **约束**：  
   - 必须生成3个备选方案  
   - 草案需标注“AI生成-待审查”水印  
4. **示例输入**：  
   “用户需能查询表结构元数据”  
5. **AI元数据**：  
   - prompt_id: Epic0-F04-T03  
   - task_status: 待开始  

---

### 任务提示词 - Task 0.5.1：遵循Git分支管理规范

1. **背景**：实施Gitflow精简版工作流  
2. **目标**：  
   - 创建分支命名规范文档  
   - 在README.md添加分支管理示意图  
3. **约束**：  
   - 特性分支前缀：feature/  
   - hotfix分支生命周期≤24小时  
4. **辅助工具**：  
   ```bash
   # 创建特性分支
   git checkout -b feature/epic0-feature0.4
   ```
5. **AI元数据**：  
   - prompt_id: Epic0-F05-T01  
   - task_status: 待开始  

---

### 任务提示词 - Task 0.5.2：明确Commit Message规范

1. **背景**：建立可追溯的提交记录  
2. **目标**：  
   - 在AQUA_GUIDE.md新增[COMMIT-STANDARD]章节  
   - 编写提交消息模板  
3. **强制格式**：  
   ```
   <type>: <summary> #T_ID_<task_id>
   [空行]
   <detail>
   ```
4. **类型枚举**：  
   feat/fix/docs/style/refactor/test/chore  
5. **AI元数据**：  
   - prompt_id: Epic0-F05-T02  
   - task_status: 待开始  

---

### 任务提示词 - Task 0.5.3：确保任务与日志实时更新

1. **背景**：建立开发过程可追溯机制  
2. **目标**：  
   - 在pre-commit钩子中添加校验规则  
   - 创建日志更新检查脚本  
3. **校验逻辑**：  
   ```python
   # 检查任务文件修改时间
   if task_modified and not log_updated:
       raise Exception("检测到任务更新但日志未同步")
   ```
4. **日志模板**：  
   ```markdown
   ## YYYY-MM-DD HH:mm
   - TASK: #T_ID_010201 
   - ACTION: 完成API草案
   - IMPACT: 影响docs/AQUA_GUIDE.md#[GUIDE-03]
   ```
5. **AI元数据**：  
   - prompt_id: Epic0-F05-T03  
   - task_status: 待开始  

---

### 任务提示词 - Task 0.5.4：编写脚本自动检查Commit Message

1. **背景**：通过自动化保障提交规范  
2. **目标**：  
   - 创建commit-msg钩子脚本  
   - 集成到pre-commit框架  
3. **校验规则**：  
   - 正则验证：`^(feat|fix|docs)😕.+#T_ID_\d{6}$`  
   - 校验失败时阻止提交  
4. **错误处理**：  
   ```python
   def validate_message(msg):
       pattern = r"^(\w+): .+ #T_ID_\d{6}$"
       if not re.match(pattern, msg):
           print("错误：不符合提交格式规范")
           return 1
   ```
5. **AI元数据**：  
   - prompt_id: Epic0-F05-T04  
   - task_status: 待开始  

---

所有提示词均包含：
1. 精准任务背景定位
2. 可量化输出目标
3. 显性化约束条件
4. 实际代码示例辅助
5. 严格关联权威文档
6. 明确AI协作元数据

特别强化技术约束：
- 部署脚本强调环境参数校验和备份机制
- API定义强制JSON Schema和错误代码规范
- Git规范绑定具体分支前缀和生命周期
- 自动化校验通过正则和钩子脚本实现