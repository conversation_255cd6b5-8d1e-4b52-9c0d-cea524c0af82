# AQUA 项目任务清单 - Epic 0: 项目基础建设与规范落地

> 说明：本文件所有任务、提示词、依赖、规则等，均以 docs/AQUA_GUIDE.md 为唯一权威事实源。所有引用的旧文档内容已迁移至 AQUA_GUIDE.md，后续仅以 AQUA_GUIDE.md 为唯一权威事实源。

## 目录
- [Feature 0.1: 项目环境与依赖管理初始化](#feature-01-项目环境与依赖管理初始化)
    - [Task 0.1.1: 配置项目基础参数](#task-011-配置项目基础参数)
    - [Task 0.1.2: 创建环境变量模板文件](#task-012-创建环境变量模板文件)
    - [Task 0.1.3: 完善环境初始化脚本核心功能](#task-013-完善环境初始化脚本核心功能)
    - [Task 0.1.4: 编写项目启动脚本](#task-014-编写项目启动脚本)
    - [Task 0.1.5: 创建初始依赖文件](#task-015-创建初始依赖文件)
    - [Task 0.1.6: 设定 Node.js 版本](#task-016-设定-nodejs-版本)
    - [Task 0.1.7: 创建项目结构](#task-017-创建项目结构)
    - [Task 0.1.8: 迁移并完善单一事实源文档](#task-018-迁移并完善单一事实源文档)
    - [Task 0.1.9: 创建基础测试用例](#task-019-创建基础测试用例)
    - [Task 0.1.10: 创建部署脚本](#task-0110-创建部署脚本)
    - [Task 0.1.11: 创建监控脚本](#task-0111-创建监控脚本)
    - [Task 0.1.12: 创建备份脚本](#task-0112-创建备份脚本)
    - [Task 0.1.13: 创建恢复脚本](#task-0113-创建恢复脚本)
- [Feature 0.2: 个人任务管理与 AI 协作流建立](#feature-02-个人任务管理与-ai-协作流建立)
    - [Task 0.2.1: 深入研读权威文档](#task-021-深入研读权威文档)
    - [Task 0.2.2: 初始化任务管理文件](#task-022-初始化任务管理文件)
    - [Task 0.2.3: 明确日志文件模板与规范](#task-023-明确日志文件模板与规范)
    - [Task 0.2.4: 建立结构化-ai-提示词库](#task-024-建立结构化-ai-提示词库)
    - [Task 0.2.5: 明确-ai-协作合规性要求](#task-025-明确-ai-协作合规性要求)
- [Feature 0.3: 强制规范与自动化校验机制部署](#feature-03-强制规范与自动化校验机制部署)
    - [Task 0.3.1: 安装和配置 `pre-commit` 框架](#task-031-安装和配置-pre-commit-框架)
    - [Task 0.3.2: 配置-pre-commit-configyaml](#task-032-配置-pre-commit-configyaml)
    - [Task 0.3.3: 配置 IDE/编辑器集成](#task-033-配置-ide编辑器集成)
    - [Task 0.3.4: 编写基础单元测试用例](#task-034-编写基础单元测试用例)
- [Feature 0.4: 前后端接口契约明确与初始定义](#feature-04-前后端接口契约明确与初始定义)
    - [Task 0.4.1: 创建功能模块定义文件](#task-041-创建功能模块定义文件)
    - [Task 0.4.2: 初步定义核心功能-api-接口](#task-042-初步定义核心功能-api-接口)
    - [Task 0.4.3: 利用-ai-辅助草拟接口定义并审查](#task-043-利用-ai-辅助草拟接口定义并审查)
- [Feature 0.5: 变更与版本管理策略落地](#feature-05-变更与版本管理策略落地)
    - [Task 0.5.1: 遵循-git-分支管理规范](#task-051-遵循-git-分支管理规范)
    - [Task 0.5.2: 明确-commit-message-规范](#task-052-明确-commit-message-规范)
    - [Task 0.5.3: 确保任务与日志实时更新](#task-053-确保任务与日志实时更新)
    - [Task 0.5.4: 编写脚本自动检查-commit-message](#task-054-编写脚本自动检查-commit-message)

---

**Epic 名称:** 项目基础建设与规范落地 (Project Foundation & Compliance)
**Epic 目标:** 确保项目开发环境、管理流程、规范体系和 AI 协作机制全面就绪，这是所有功能开发的前提和保障。
**关联文档:** `docs/AQUA_GUIDE.md#[GUIDE-01]`, `0302_REQUEST.md`

**重要提示：** 本文档中所有任务的执行，必须**严格遵循** `@/rules` 目录下所有规则文件的要求，包括但不限于命名规范、日志格式、编码标准、测试要求等。任何产出都将以此为基准进行校验和审查。

---

## Feature 0.1: 项目环境与依赖管理初始化

**Feature 目标:** 建立稳定、一致、自动化的开发环境。
**关联文档:** `docs/tasks/Dev_Tasks_Epic0.md`, `docs/AQUA_GUIDE.md#[GUIDE-04]`, `0302_REQUEST.md` (环境与依赖管理)

*   **Task 0.1.1: 配置项目基础参数**
    *   **任务名称:** 配置 `config/settings.toml` 基础参数
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 初始化 `config/settings.toml` 文件，包含数据库路径、日志路径、项目名称等基本配置项。
    *   **实际完成:** 2024-07-03
    *   **相关变更:**
        *   创建/修改 `config/settings.toml`，新增 [project]、[database]、[logging] 区块，严格注释，符合配置驱动和命名规范。
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F01-T01

*   **Task 0.1.2: 创建环境变量模板文件**
    *   **任务名称:** 创建并提供 `config/.env.example` 模板文件
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 创建 `config/.env.example` 文件，作为敏感配置和本地覆盖的模板，并加入 `.gitignore`。
    *   **相关变更:**
        *   创建 `config/.env.example`
        *   修改 `.gitignore`
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F01-T02

*   **Task 0.1.3: 完善环境初始化脚本核心功能**
    *   **任务名称:** 完善 `scripts/env_init.py` 核心功能
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 确保 `env_init.py` 能够自动执行虚拟环境创建、后端 `uv` 依赖管理、前端 `pnpm` 依赖管理、数据库初始化等操作。
    *   **相关变更:**
        *   修改 `scripts/env_init.py`
    *   **depends_on:** [Epic0-F01-T01, Epic0-F01-T05]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F01-T03

*   **Task 0.1.4: 编写项目启动脚本**
    *   **任务名称:** 编写 `start_backend.sh` 和 `start_frontend.sh` (及 `.bat` 版本) 脚本
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 创建用于启动后端和前端服务的脚本，并强制在启动时调用 `env_init.py` 进行环境校验。
    *   **相关变更:**
        *   创建 `start_backend.sh`
        *   创建 `start_frontend.sh`
        *   创建 `start_backend.bat`
        *   创建 `start_frontend.bat`
        *   创建最小化后端入口 `main.py`
        *   创建最小化前端入口 `frontend/package.json`
    *   **实际完成:** 2024-07-04
    *   **验证说明:** 已通过TDD流程，后端/前端一键启动脚本均可自动校验环境并启动最小化服务，流程闭环。
    *   **depends_on:** [Epic0-F01-T03]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F01-T04

*   **Task 0.1.5: 创建初始依赖文件**
    *   **任务名称:** 创建初始的 `requirements.txt` 和 `package.json` 文件
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 为后端 Python 依赖和前端 Node.js 依赖创建初始清单文件。
    *   **相关变更:**
        *   创建 `requirements.txt`
        *   创建 `package.json` 并补全前端依赖、engines、scripts等字段，结构对齐权威文档
    *   **实际完成:** 2024-07-05
    *   **验证说明:** requirements.txt、frontend/package.json已对齐项目蓝图与PRD，依赖结构清晰，已通过依赖安装与启动测试。
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F01-T05

*   **Task 0.1.6: 设定 Node.js 版本**
    *   **任务名称:** 设定 Node.js 版本（.nvmrc 或 engines 字段）
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 明确项目所需的 Node.js 版本，以确保开发环境一致性。
    *   **相关变更:**
        *   创建 .nvmrc，内容为18.20.0
        *   frontend/package.json的engines字段同步为">=18.20.0"
    *   **实际完成:** 2024-07-05
    *   **验证说明:** .nvmrc与package.json/engines字段已对齐，pnpm与Vite等依赖均可正常安装与运行，环境一致性已验证。
    *   **depends_on:** [Epic0-F01-T05]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F01-T06

* **Task 0.1.7: 创建项目结构**
    * **任务名称:** 创建项目结构，包括后端、前端、数据存储等。
    * **负责人:** AI (CURSOR/GEMINI)
    * **状态:** 已完成
    * **优先级:** 高
    * **任务描述:** 创建项目结构，包括后端、前端、数据存储等。
    * **实际完成:** 2024-07-06
    * **相关变更:**
        * 后端代码结构
        * 前端代码结构
        * 数据存储结构
        * 自动化目录初始化脚本（scripts/env_init.py）
        * 目录结构TDD单元测试（tests/unit/test_env_init.py）
        * 目录结构与用途文档（docs/README.md）
    * **验证说明:** 已通过TDD流程，自动化脚本可一键初始化/修复标准目录结构并生成README，单元测试验证通过，目录结构与文档完全对齐蓝图和PRD。
    * **depends_on:** []
    * **ai_generated:** true
    * **prompt_id:** Epic0-F01-T07



* **Task 0.1.8: 迁移并完善单一事实源文档**
    *   **任务名称:** 迁移并完善单一事实源文档AQUA_GUIDE.md
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 根据 `logs/re_doc_list.md` 的计划，将原有分散的文档内容迁移、整合并完善到 `docs/AQUA_GUIDE.md` 中，建立项目的唯一事实源。
    *   **相关变更:**
        *   创建并完善 `docs/AQUA_GUIDE.md`
        *   归档 `docs/old/` 下的旧文档
    * **depends_on:** []
    * **ai_generated:** true
    * **prompt_id:** Epic0-F01-T08

* **Task 0.1.9: 创建基础测试用例**
    * **任务名称:** 创建初始测试用例
    * **负责人:** AI (CURSOR/GEMINI)
    * **状态:** 已完成
    * **优先级:** 高
    * **任务描述:** 为后端和前端创建单元测试、集成测试和端到端测试用例，确保Pytest和Jest/Vitest测试框架可正常运行。
    * **实际完成:** 2024-07-26
    * **相关变更:**
        * 创建 `tests/unit/test_math_utils.py`
        * 创建 `tests/frontend/unit/example.test.ts`
        * 调整前端测试目录至 `tests/frontend/`
        * 配置 `vitest` 并安装相关依赖
        * 创建 `tests/**/.gitkeep` 占位符文件
    * **验证说明:** 后端 `pytest` 和前端 `vitest` 均已配置完成。基础单元测试已添加，测试目录结构已统一，可正常执行。
    * **depends_on:** []
    * **ai_generated:** true
    * **prompt_id:** Epic0-F01-T09

* **Task 0.1.10: 创建部署脚本**
    * **任务名称:** 创建参数化部署脚本
    * **负责人:** AI (CURSOR/GEMINI)
    * **状态:** 已完成
    * **优先级:** 高
    * **任务描述:** 创建统一的、参数化的跨平台部署脚本（`deploy.sh`, `deploy.bat`），通过环境参数（`local`, `test`, `prod`）区分不同部署逻辑，并强制调用`env_init.py`进行环境校验。**当前阶段：编写集成测试以验证脚本功能。**
    * **相关变更:**
        * 创建 `scripts/deploy.sh` (支持 `local`, `test`, `prod` 参数)
        * 创建 `scripts/deploy.bat` (支持 `local`, `test`, `prod` 参数)
        * 创建 `tests/integration/test_deployment.py`
    * **实际完成:** 
    * **验证说明:** 待集成测试通过。
    * **depends_on:** []
    * **ai_generated:** true
    * **prompt_id:** Epic0-F01-T10

* **Task 0.1.11: 创建监控脚本**
    * **任务名称:** 创建服务监控脚本
    * **负责人:** AI (CURSOR/GEMINI)
    * **状态:** 已完成
    * **优先级:** 高
    * **任务描述:** 创建后端、前端和数据库的状态监控脚本，输出结构化日志含北京时间戳。
    * **相关变更:**
        * 创建 `scripts/monitor_backend.py`
        * 创建 `scripts/monitor_frontend.sh`
        * 创建 `scripts/monitor_database.py`
    * **depends_on:** []
    * **ai_generated:** true
    * **prompt_id:** Epic0-F01-T11

* **Task 0.1.12: 创建备份脚本**
    * **任务名称:** 创建数据备份脚本
    * **负责人:** AI (CURSOR/GEMINI)
    * **状态:** 已完成
    * **优先级:** 高
    * **任务描述:** 创建后端、前端和数据库的跨平台备份脚本，支持全量/增量备份。
    * **相关变更:**
        * 创建 `scripts/backup_backend.sh`
        * 创建 `scripts/backup_frontend.sh`
        * 创建 `scripts/backup_database.sh`
    * **depends_on:** []
    * **ai_generated:** true
    * **prompt_id:** Epic0-F01-T12

* **Task 0.1.13: 创建恢复脚本**
    * **任务名称:** 创建数据恢复脚本
    * **负责人:** AI (CURSOR/GEMINI)
    * **状态:** 已完成
    * **优先级:** 高
    * **任务描述:** 创建从备份恢复后端、前端和数据库的脚本，支持不同环境。
    * **相关变更:**
        * 创建 `scripts/restore_backend.sh`
        * 创建 `scripts/restore_frontend.sh`
        * 创建 `scripts/restore_database.sh`
    * **depends_on:** []
    * **ai_generated:** true
    * **prompt_id:** Epic0-F01-T13
---

## Feature 0.2: 个人任务管理与 AI 协作流建立

**Feature 目标:** 建立高效的个人任务管理体系和可控的 AI 协作流程。
**关联文档:** `docs/tasks/Dev_Tasks_Epic0.md`, `0302_REQUEST.md` (个人任务管理与 AI 协作流)

*   **Task 0.2.1: 深入研读权威文档**
    *   **任务名称:** 深入研读并理解所有权威文档
    *   **负责人:** 用户
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 认真阅读《项目蓝图》、《产品需求文档》、《功能模块定义》及所有 `.rules` 规则文件，这是您与 AI 协作的基础。
    *   **相关变更:** 无
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F02-T01

*   **Task 0.2.2: 初始化任务管理文件**
    *   **任务名称:** 初始化 `docs/tasks/Dev_Tasks.md` 文件结构
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 创建 `Dev_Tasks.md` 文件，并录入初步的 Epic 和 Feature 结构，遵循多级标题和元数据格式。
    *   **相关变更:**
        *   创建 `docs/tasks/Dev_Tasks.md`
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F02-T02

*   **Task 0.2.3: 明确日志文件模板与规范**
    *   **任务名称:** 明确并初始化 `logs/dev_log.md` 模板与规范
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 创建 `logs/dev_log.md` 文件，并设置其结构化日志模板，确保符合 `project_management_standards.mdc` 的日志规范。
    *   **相关变更:**
        *   创建 `logs/dev_log.md`
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F02-T03

*   **Task 0.2.4: 建立结构化 AI 提示词库**
    *   **任务名称:** 建立结构化 AI 提示词库（`docs/ai_prompts/`）
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 中
    *   **任务描述:** 创建 `docs/ai_prompts/` 目录，并开始维护用于指导 AI 协作的结构化提示词模板。
    *   **相关变更:**
        *   创建 `docs/ai_prompts/` 目录
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F02-T04

*   **Task 0.2.5: 明确 AI 协作合规性要求**
    *   **任务名称:** 明确 AI 协作合规性要求（AI 输出审查、Git Hooks 强制校验）
    *   **负责人:** 用户
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 仔细理解并确认 AI 输出的审查流程和 Git Hooks 校验机制，确保 AI 协作可控。
    *   **相关变更:** 无
    *   **depends_on:** [Epic0-F02-T01]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F02-T05

---

## Feature 0.3: 强制规范与自动化校验机制部署

**Feature 目标:** 确保代码质量和规范性在提交前得到自动化保障。
**关联文档:** `docs/tasks/Dev_Tasks_Epic0.md`, `0302_REQUEST.md` (强制规范与自动化校验)

*   **Task 0.3.1: 安装和配置 `pre-commit` 框架**
    *   **任务名称:** 安装 `pre-commit` 框架
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 在项目中安装 `pre-commit` 工具，并进行初步设置。
    *   **实际完成:** 2024-07-07
    *   **相关变更:**
        *   安装 `pre-commit`，配置为纯本地钩子模式，彻底规避首次初始化需访问GitHub问题，适配中国网络环境。
        *   `.pre-commit-config.yaml` 仅包含本地shell钩子（前端Prettier/ESLint自动修复、AQUA自定义合规校验），无外部依赖。
        *   `scripts/git_hooks/pre-commit` 已实现，集成格式化、敏感信息检测、日志/任务同步校验等功能。
        *   pre-commit 钩子已激活，`git commit` 时自动运行。
        *   本地合规性测试通过，合规校验、格式化、敏感信息检测等全部闭环。
    *   **节点意义说明:**
        *   该任务为后续所有自动化合规、代码质量保障、敏感信息防护的基础节点。实现了"AI协作-人工核查-自动化校验-日志/任务同步-敏感信息防护"闭环，确保后续开发流程安全、规范、可追溯。

*   **Task 0.3.2: 配置 `.pre-commit-config.yaml`**
    *   **任务名称:** 配置 `.pre-commit-config.yaml`，集成代码格式化、Lint、类型检查等 Hook
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 配置 `pre-commit` 钩子，包含 Black/Prettier (格式化), Flake8/ESLint (Lint), TSC (类型检查), 依赖一致性检查, 敏感信息检查等。
    *   **实际完成:** 2024-07-07
    *   **相关变更:**
        *   `.pre-commit-config.yaml` 仅包含本地shell钩子（前端Prettier/ESLint自动修复、AQUA自定义合规校验），所有合规逻辑在`scripts/git_hooks/pre-commit`，无外部依赖。
        *   相关文档（AQUA_GUIDE.md 4.7节）、规则文件、日志已同步更新，标准化说明本地钩子模式与合规流程。
    *   **节点意义说明:**
        *   该任务实现了pre-commit配置的本地化、标准化，彻底适配中国网络环境，保障所有合规校验、格式化、敏感信息检测、日志/任务同步等在本地自动闭环。
        *   与原设计差异：未集成Black、Flake8等官方远程repo，所有功能通过本地shell脚本实现，功能等效且更适合个人开发与Gitee场景。

*   **Task 0.3.3: 配置 IDE/编辑器集成**
    *   **任务名称:** 配置 IDE/编辑器集成（ESLint、Prettier、Python Linter 插件）
    *   **负责人:** 用户
    *   **状态:** 已完成
    *   **优先级:** 中
    *   **任务描述:** 配置您常用的 IDE 或编辑器，使其集成 ESLint, Prettier, Python Linter 等插件，提供实时代码检查和格式化。
    *   **实际完成:** 2024-07-07
    *   **相关变更:**
        *   在OS X下的CURSOR IDE中，已集成并启用ESLint、Prettier、Python Linter等插件。
        *   `.vscode/settings.json`已配置，确保与项目自动化合规规则一致。
    *   **节点意义说明:**
        *   本地IDE集成与项目自动化合规校验无缝衔接，确保开发时即获得与提交前一致的格式、Lint、类型检查体验，极大提升开发效率与规范性。

*   **Task 0.3.4: 编写基础单元测试用例**
    *   **任务名称:** 编写少量基础的单元测试用例，验证测试框架集成
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 为后端和前端分别编写最简单的测试用例，确保 pytest 和 Jest/Vitest 测试框架能够正常运行。
    *   **实际完成:** 2024-07-27
    *   **相关变更:**
        *   创建 `tests/unit/test_math_utils.py`
        *   创建 `tests/frontend/unit/hello_world.test.ts`
        *   后端 `pytest` 和前端 `vitest` 均已配置完成。基础单元测试已添加，测试目录结构已统一，可正常执行。
    *   **depends_on:** [Epic0-F01-T03, Epic0-F01-T06, Epic0-F03-T01]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F03-T04

---

## Feature 0.4: 前后端接口契约明确与初始定义

**Feature 目标:** 建立前后端协作的唯一标准，确保接口一致性。
**关联文档:** `docs/tasks/Dev_Tasks_Epic0.md`, `docs/02_FUNCTIONS.md` (API 契约), `0302_REQUEST.md` (接口契约)

*   **Task 0.4.1: 创建功能模块定义文件**
    *   **任务名称:** 创建 `02_FUNCTIONS.md` 文件
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 阅读 `docs/02_FUNCTIONS.md` 文件，作为项目功能模块和 API 契约的唯一权威来源。
    *   **相关变更:**
        *   阅读 `docs/02_FUNCTIONS.md`
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F04-T01

*   **Task 0.4.2: 初步定义核心功能 API 接口**
    *   **任务名称:** 根据 PRD 和功能模块定义，初步定义核心功能模块的 API 接口
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 根据 `01_PRD.md` 和 `02_FUNCTIONS.md` 的要求，初步定义数据导入、数据库浏览、基础回测等核心功能模块的 API 接口（请求/响应格式、字段、类型、枚举、错误码）。
    *   **相关变更:**
        *   修改 `docs/02_FUNCTIONS.md`
    *   **depends_on:** [Epic0-F04-T01]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F04-T02

*   **Task 0.4.3: 利用 AI 辅助草拟接口定义并审查**
    *   **任务名称:** 利用 AI 辅助草拟接口定义，并进行人工审查
    *   **负责人:** 用户, AI (CURSOR/GEMINI)
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 提示 AI 根据功能需求生成 API 接口草稿，并由开发者进行人工审查和修正，确保其准确性和规范性。
    *   **相关变更:**
        *   修改 `docs/02_FUNCTIONS.md`
    *   **depends_on:** [Epic0-F04-T02]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F04-T03

---

## Feature 0.5: 变更与版本管理策略落地

**Feature 目标:** 确保项目开发过程可追溯，代码版本清晰。
**关联文档:** `docs/tasks/Dev_Tasks_Epic0.md`, `0302_REQUEST.md` (变更与版本管理)

*   **Task 0.5.1: 遵循 Git 分支管理规范**
    *   **任务名称:** 遵循 Git 分支管理规范（主干+短期特性分支）
    *   **负责人:** 用户
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 在项目开发中，严格按照主干分支稳定、特性分支开发的模式进行 Git 操作，例如为每个 Feature 创建独立分支。
    *   **相关变更:** 无 (Git 工作流)
    *   **depends_on:** []
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F05-T01

*   **Task 0.5.2: 明确 Commit Message 规范**
    *   **任务名称:** 明确 Commit Message 规范（关联原子化任务 ID）
    *   **负责人:** 用户
    *   **状态:** 已完成
    *   **优先级:** 高
    *   **任务描述:** 确认每次提交的 Commit Message 格式，确保包含关联的原子化任务 ID (例如: `feat: 实现数据导入功能 #T_ID_010101`)。
    *   **相关变更:** 无 (Commit 规范)
    *   **depends_on:** [Epic0-F05-T01]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F05-T02

*   **Task 0.5.3: 确保任务与日志实时更新**
    *   **任务名称:** 确保 `Dev_Tasks.md` 和 `logs/dev_log.md` 实时更新和同步
    *   **负责人:** 用户
    *   **状态:** 待开始
    *   **优先级:** 高
    *   **任务描述:** 养成在每次完成任务或进行重要变更后，立即同步更新 `Dev_Tasks.md` (任务状态、实际完成日期) 和 `logs/dev_log.md` (结构化日志记录) 的习惯。
    *   **相关变更:**
        *   修改 `docs/tasks/Dev_Tasks.md`
        *   修改 `logs/dev_log.md`
    *   **depends_on:** [Epic0-F02-T02, Epic0-F02-T03, Epic0-F05-T02]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F05-T03

*   **Task 0.5.4: 编写脚本自动检查 Commit Message (额外优化)**
    *   **任务名称:** 编写脚本，自动检查 commit message 是否关联任务 ID
    *   **负责人:** AI (CURSOR/GEMINI)
    *   **状态:** 待开始
    *   **优先级:** 中
    *   **任务描述:** （可选，优化项）开发一个 Git Hook (例如 `pre-commit` 钩子) 脚本，在提交前自动检查 commit message 是否包含有效的任务 ID 或符合预定义的格式。
    *   **相关变更:**
        *   创建 `scripts/git_hooks/check_commit_message.py` (示例)
        *   配置 Git Hook (`.git/hooks/pre-commit` 引用此脚本)
    *   **depends_on:** [Epic0-F03-T01, Epic0-F05-T02]
    *   **ai_generated:** true
    *   **prompt_id:** Epic0-F05-T04