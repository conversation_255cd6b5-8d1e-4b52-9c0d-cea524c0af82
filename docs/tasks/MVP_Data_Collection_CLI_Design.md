# AQUA 数据采集MVP-CLI重新设计方案

## 📋 项目概述

**项目名称**: AQUA数据采集MVP-CLI重新设计  
**版本**: V4.0 统一业务表架构适配版  
**目标用户**: 个人量化开发者  
**支持平台**: Windows 10/11 + macOS (跨平台)  
**核心数据源**: TUSHARE Pro (2100积分额度)  
**架构基础**: V4.0统一业务表架构  
**开发原则**: 配置驱动、TDD开发、实际数据测试优先  

### 项目背景
基于Phase 2文档统一化+代码清理的完成，现需重新设计数据采集MVP-CLI系统，以充分利用TUSHARE Pro的高质量数据源，为个人开发者提供简洁、高效、跨平台的数据采集解决方案。

---

## 🎯 Stage 1: 需求确定阶段

### 1.1 个人开发者特征分析

#### 用户画像
- **技术水平**: 有一定Python基础，熟悉量化交易概念
- **资源限制**: 单机环境，内存8-16GB，存储500GB-2TB
- **时间约束**: 业余时间开发，需要高效的工具链
- **成本敏感**: 需要最大化利用TUSHARE 2100积分价值
- **平台需求**: 开发环境可能在Windows/macOS之间切换

#### 核心需求矩阵

| 需求类别 | 具体需求 | 优先级 | 平台要求 |
|---------|---------|--------|----------|
| **数据获取** | 期货主力合约日线/分钟线数据 | HIGH | 跨平台 |
| **数据获取** | 股票日线数据和基本信息 | HIGH | 跨平台 |
| **数据获取** | 历史数据批量回填 | MEDIUM | 跨平台 |
| **成本控制** | 积分使用效率最大化 | HIGH | 跨平台 |
| **成本控制** | 避免重复数据获取 | HIGH | 跨平台 |
| **易用性** | 一键式数据采集命令 | HIGH | 跨平台 |
| **易用性** | 清晰的进度显示和错误提示 | MEDIUM | 跨平台 |
| **可靠性** | 断点续传和自动重试 | MEDIUM | 跨平台 |
| **维护性** | 配置驱动，最小化代码修改 | HIGH | 跨平台 |

### 1.2 跨平台需求规格

#### Windows平台特殊需求
- 配置文件位置: `%APPDATA%\AQUA\config\`
- 数据存储位置: `%USERPROFILE%\Documents\AQUA\data\`
- 日志文件位置: `%APPDATA%\AQUA\logs\`
- 路径分隔符处理: 使用`pathlib`统一处理
- 中文路径支持: UTF-8编码确保

#### macOS平台特殊需求
- 配置文件位置: `~/Library/Application Support/AQUA/config/`
- 数据存储位置: `~/Documents/AQUA/data/`
- 日志文件位置: `~/Library/Logs/AQUA/`
- 权限管理: 确保目录创建和文件写入权限
- 系统通知集成: 支持macOS原生通知

#### 统一跨平台需求
- 环境变量统一: `AQUA_CONFIG_DIR`, `AQUA_DATA_DIR`
- 配置文件格式: TOML格式，人类友好
- 字符编码: 全程UTF-8
- 依赖管理: 纯Python依赖，避免C扩展兼容问题

### 1.3 TUSHARE集成需求

#### API使用策略
- **积分预算**: 2100积分高效分配
- **频率控制**: ≤200次/分钟，智能退避
- **数据优先级**: 期货主力合约 > 股票日线 > 其他数据
- **成本优化**: 批量获取，避免单条查询

#### 数据质量要求
- **权威性**: TUSHARE作为最高优先级数据源
- **完整性**: 支持历史数据和增量更新
- **时效性**: T+1数据及时获取
- **准确性**: 数据验证和异常检测

---

## 🏗️ Stage 2: 架构规划阶段

### 2.1 整体系统架构

```
AQUA数据采集MVP-CLI架构 (个人开发者跨平台版 + 增量采集)
┌─────────────────────────────────────────────────────────────┐
│                   CLI用户界面层                              │
│    (Click + Rich + 跨平台路径处理 + 增量采集支持)            │
├─────────────────────────────────────────────────────────────┤
│                 配置管理层 (跨平台)                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │平台检测器   │  │配置加载器   │  │环境变量管理 │          │
│  │(Win/macOS)  │  │(TOML+覆盖)  │  │(.env支持)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│               增量采集管理层 (新增)                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │增量采集助手 │  │交易日历管理 │  │历史状态追踪 │          │
│  │(范围计算)   │  │(交易日验证) │  │(最后采集)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                 数据源管理层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │🏆TUSHARE核心│  │ CSV兼容器   │  │ MySQL扩展器 │          │
│  │(积分管理)   │  │(FromC2C等)  │  │(第三方数据) │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│               频率控制和积分管理层                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 积分追踪器  │  │ 频率限制器  │  │ 智能重试器  │          │
│  │(2100预算)   │  │(200/分钟)   │  │(指数退避)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│              DataProcessor数据处理层 (V2.0)                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 数据清洗器  │  │ 重复检测器  │  │ 质量评分器  │          │
│  │(标准化)     │  │(去重处理)   │  │(多源优先级) │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                 存储管理层                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 表路由器    │  │ 元数据管理  │  │ 索引优化器  │          │
│  │(业务表映射) │  │(数据来源)   │  │(查询加速)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│              DuckDB存储层 (V4.0 + 交易日历)                 │
│   期货业务表(5) + 股票业务表(5) + 公共业务表(2)             │
│   + market_trading_calendar表 + 统一元数据管理              │
│            跨平台文件管理 + 增量采集状态追踪                 │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件设计 (基于现有架构复用)

#### ✅ 现有基础架构复用分析
**发现**: 经过深度技术分析，AQUA项目已具备85%的TUSHARE基础架构：
- `SimpleExtractor`基类和工厂模式 ✅ (完成)
- 配置驱动系统 ✅ (完成)
- V4.0统一业务表设计 ✅ (完成)
- 跨平台路径管理 ✅ (完成)
- `DataProcessor` V2.0数据处理引擎 ✅ (完成)
- `UnifiedStorageManager`统一存储管理 ✅ (完成)
- `ImportHistoryManager`导入历史管理 ✅ (完成)

**缺失核心**:
- TushareExtractor具体实现类 (0%完成)
- 增量采集功能模块 (0%完成)
- 交易日历管理器 (0%完成)

#### 主控制器: AquaDataCollectionCLI (继承现有架构)
```python
class AquaDataCollectionCLI:
    """个人开发者跨平台数据采集CLI主控制器"""

    # 复用现有ConfigLoader
    def __init__(self, platform: str = None):
        self.config = ConfigLoader()  # 已实现的配置管理器
        self.platform_info = self.config.get_platform_info()
        self.data_processor = None  # DataProcessor集成
        self.incremental_helper = None  # 增量采集助手

    # TUSHARE数据采集命令组
    def tushare_collect(self, data_type: str, date_range: str, incremental: bool = False):
        # 复用SimpleExtractorFactory框架
        extractor = SimpleExtractorFactory.create_extractor('tushare')

        # 增量采集逻辑
        if incremental:
            incremental_range = self._calculate_incremental_range(data_type, date_range)
            if not incremental_range:
                return {'success': True, 'message': '无新数据需要采集'}
            date_range = incremental_range

        # 数据采集
        raw_data = extractor.extract(data_type, date_range)

        # DataProcessor数据处理
        if self.data_processor and raw_data is not None:
            processed_result = self.data_processor.process(raw_data, data_type, "TUSHARE")
            return processed_result.clean_data

        return raw_data

    # 积分使用监控 (基于现有配置)
    def check_points_usage(self):
        return self._get_tushare_points_status()

    # 复用现有跨平台配置
    def init_platform_config(self):
        return self.config.load_config()  # 已支持跨平台

    # 增量采集范围计算
    def _calculate_incremental_range(self, data_type: str, date_range: str):
        if not self.incremental_helper:
            from src.data_import.incremental_collection_helper import IncrementalCollectionHelper
            self.incremental_helper = IncrementalCollectionHelper()
        return self.incremental_helper.calculate_incremental_range(data_type, date_range)
```

#### TUSHARE核心适配器: TushareExtractor (新实现类)
```python
class TushareExtractor(SimpleExtractor):
    """TUSHARE Pro API适配器 - 继承现有SimpleExtractor基类"""

    # 继承基类，符合现有架构标准
    def __init__(self, config: Dict):
        super().__init__(config)
        self.pro = ts.pro_api(token=config['tushare']['token'])
        self.points_budget = 2100
        self.rate_limiter = RateLimiter(200)  # 200次/分钟
        self.trading_calendar_manager = TradingCalendarManager()  # 交易日历管理

    # 实现抽象方法extract()
    def extract(self, data_type: str, **kwargs) -> DataFrame:
        # 增量采集支持
        if kwargs.get('incremental', False):
            kwargs = self._adjust_for_incremental(data_type, kwargs)

        # 具体实现TUSHARE数据获取逻辑
        if data_type == 'futures':
            return self.collect_futures_data(kwargs.get('contracts', []), **kwargs)
        elif data_type == 'stocks':
            return self.collect_stocks_data(kwargs.get('codes', []), **kwargs)
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

    # 期货数据采集 (映射到V4.0业务表)
    def collect_futures_data(self, contracts: List[str], **kwargs):
        # 支持增量采集的期货数据获取
        return self._map_to_futures_daily_table(contracts, **kwargs)

    # 股票数据采集 (映射到V4.0业务表)
    def collect_stocks_data(self, codes: List[str], **kwargs):
        # 支持增量采集的股票数据获取
        return self._map_to_stock_daily_table(codes, **kwargs)

    # 增量采集参数调整
    def _adjust_for_incremental(self, data_type: str, kwargs: Dict) -> Dict:
        # 基于交易日历计算增量时间范围
        if 'start_date' not in kwargs or 'end_date' not in kwargs:
            incremental_range = self.trading_calendar_manager.calculate_incremental_range(
                data_type, kwargs.get('symbols', [])
            )
            kwargs.update(incremental_range)
        return kwargs
```

#### 跨平台配置管理: ConfigLoader (现有组件复用)
```python
# 直接复用 /src/utils/config_loader.py
# 已实现功能:
# - 平台自动检测 ✅
# - 跨平台路径处理 ✅  
# - 环境变量自动覆盖 ✅
# - TUSHARE_TOKEN支持 ✅

# 无需新实现，直接使用现有ConfigLoader类
```

#### DataProcessor集成: 数据处理引擎 (现有组件复用)
```python
# 直接复用 /src/data_import/data_processor.py
# 已实现功能:
# - 数据清洗和标准化 ✅
# - 重复数据检测和去重 ✅
# - 数据质量评分 ✅
# - 多数据源优先级处理 ✅
# - 15万+条/秒处理性能 ✅

# DataProcessor与TUSHARE数据集成
class TushareDataProcessor:
    """TUSHARE数据专用处理器"""

    def __init__(self):
        from src.data_import.data_processor import DataProcessor
        self.processor = DataProcessor()

    def process_tushare_data(self, raw_data: DataFrame, data_type: str) -> ProcessingResult:
        """处理TUSHARE原始数据"""
        # 设置TUSHARE数据源标识
        processing_config = {
            'data_source': 'TUSHARE',
            'source_quality': 'HIGH',
            'quality_score': 95.0,
            'data_type': data_type
        }

        # 调用现有DataProcessor
        result = self.processor.process(raw_data, processing_config)

        # TUSHARE特定的后处理
        if data_type == 'futures':
            result = self._post_process_futures(result)
        elif data_type == 'stocks':
            result = self._post_process_stocks(result)

        return result

    def _post_process_futures(self, result: ProcessingResult) -> ProcessingResult:
        """期货数据后处理"""
        # 合约代码标准化 (RB2501.SHF -> RB2501)
        # 金额单位转换 (万元 -> 元)
        # 交易日验证
        return result

    def _post_process_stocks(self, result: ProcessingResult) -> ProcessingResult:
        """股票数据后处理"""
        # 股票代码标准化
        # 价格数据验证
        # 交易日验证
        return result
```

#### 增量采集管理器: IncrementalCollectionHelper (新实现)
```python
class IncrementalCollectionHelper:
    """轻量级增量采集助手 - 基于交易日历的精准增量"""

    def __init__(self):
        from src.data_import.import_history_manager import ImportHistoryManager
        self.history_manager = ImportHistoryManager()
        self.trading_calendar = TradingCalendarManager()

    def calculate_incremental_range(self, data_type: str, symbols: List[str],
                                  source: str = 'TUSHARE') -> Optional[Dict[str, str]]:
        """计算严格无重叠的增量时间范围"""

        # 查询最后采集时间
        last_dates = {}
        for symbol in symbols:
            last_date = self.history_manager.get_last_import_date(
                symbol, data_type, source
            )
            last_dates[symbol] = last_date

        # 如果没有历史记录，使用settings.toml默认配置
        if not any(last_dates.values()):
            return self._get_default_range_from_settings(data_type)

        # 计算增量范围（基于交易日历）
        min_last_date = min(date for date in last_dates.values() if date)

        # 获取下一个交易日
        market_type = 'STOCK' if data_type == 'stocks' else 'FUTURES'
        exchange_code = self._get_exchange_code(symbols[0], data_type)

        next_trading_date = self.trading_calendar.get_next_trading_date(
            min_last_date, market_type, exchange_code
        )

        if not next_trading_date:
            return None  # 已经是最新数据

        today = datetime.now().strftime('%Y-%m-%d')

        return {
            'start_date': next_trading_date,
            'end_date': today
        }

    def _get_default_range_from_settings(self, data_type: str) -> Dict[str, str]:
        """从settings.toml获取默认时间范围"""
        from src.utils.config_loader import ConfigLoader
        config = ConfigLoader()

        # 读取默认配置
        default_days = config.get(f'data_collection.default_days.{data_type}', 30)
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=default_days)).strftime('%Y-%m-%d')

        return {'start_date': start_date, 'end_date': end_date}

    def _get_exchange_code(self, symbol: str, data_type: str) -> str:
        """根据标的代码获取交易所代码"""
        if data_type == 'stocks':
            return 'SSE' if symbol.endswith('.SH') else 'SZSE'
        else:
            # 期货交易所映射
            futures_exchange_map = {
                'RB': 'SHFE', 'CU': 'SHFE', 'AL': 'SHFE',
                'IF': 'CFFEX', 'IC': 'CFFEX', 'IH': 'CFFEX'
            }
            product_code = ''.join([c for c in symbol if c.isalpha()])
            return futures_exchange_map.get(product_code, 'SHFE')
```

#### 交易日历管理器: TradingCalendarManager (新实现)
```python
class TradingCalendarManager:
    """交易日历管理器 - 基于market_trading_calendar表"""

    def __init__(self):
        from src.database.unified_storage_manager import UnifiedStorageManager
        self.storage_manager = UnifiedStorageManager()
        self._cache = {}  # 内存缓存

    def is_trading_day(self, date: str, market_type: str, exchange_code: str) -> bool:
        """判断是否为交易日"""
        cache_key = f"{date}_{market_type}_{exchange_code}"

        if cache_key in self._cache:
            return self._cache[cache_key]

        query = """
        SELECT is_trading_day FROM market_trading_calendar
        WHERE market_type = ? AND exchange_code = ? AND cal_date = ?
        """

        result = self.storage_manager.execute_query(
            query, [market_type, exchange_code, date]
        )

        is_trading = bool(result[0][0]) if result else False
        self._cache[cache_key] = is_trading
        return is_trading

    def get_next_trading_date(self, date: str, market_type: str,
                            exchange_code: str) -> Optional[str]:
        """获取下一个交易日"""
        query = """
        SELECT MIN(cal_date) as next_trading_date
        FROM market_trading_calendar
        WHERE market_type = ?
          AND exchange_code = ?
          AND cal_date > ?
          AND is_trading_day = TRUE
        """

        result = self.storage_manager.execute_query(
            query, [market_type, exchange_code, date]
        )

        return result[0][0] if result and result[0][0] else None

    def get_trading_dates_range(self, start_date: str, end_date: str,
                              market_type: str, exchange_code: str) -> List[str]:
        """获取时间范围内的所有交易日"""
        query = """
        SELECT cal_date FROM market_trading_calendar
        WHERE market_type = ?
          AND exchange_code = ?
          AND is_trading_day = TRUE
          AND cal_date BETWEEN ? AND ?
        ORDER BY cal_date
        """

        result = self.storage_manager.execute_query(
            query, [market_type, exchange_code, start_date, end_date]
        )

        return [row[0] for row in result] if result else []

    def initialize_trading_calendar(self):
        """初始化交易日历数据（从TUSHARE获取）"""
        # 使用TUSHARE的trade_cal接口初始化交易日历
        # 这个方法在系统首次运行时调用
        pass
```

### 2.3 数据流设计

#### 完整数据采集流程 (包含DataProcessor和增量采集)
```
用户命令 → 增量检查 → 平台检测 → 配置加载 → 积分检查 → 数据采集 → DataProcessor → 存储写入 → 历史更新
    ↓         ↓         ↓         ↓         ↓         ↓         ↓           ↓         ↓
CLI解析   增量范围   Win/macOS   TOML配置   剩余积分   API调用   数据处理     DuckDB    导入记录
          计算                                                  ↓
                                                            清洗+去重
                                                            质量评分
                                                            格式标准化
```

#### 增量采集专用流程
```
--incremental → IncrementalHelper → 交易日历查询 → 历史记录查询 → 增量范围计算 → 数据采集
     ↓              ↓                  ↓             ↓             ↓           ↓
  参数标识      增量助手           TradingCalendar  ImportHistory   时间范围    API调用
                                      ↓             ↓             ↓
                                  交易日验证      最后采集日期    无重叠范围
```

#### DataProcessor集成流程
```
原始数据 → 数据清洗 → 重复检测 → 质量评分 → 格式转换 → 元数据添加 → 存储准备
   ↓         ↓         ↓         ↓         ↓         ↓         ↓
TUSHARE   字段标准化   去重处理   质量打分   统一格式   来源标记   存储对象
```

#### 跨平台兼容处理
```
配置请求 → 平台判断 → 路径解析 → 目录创建 → 权限检查 → 配置写入
    ↓         ↓         ↓         ↓         ↓         ↓
统一接口   系统识别   路径标准   目录结构   文件权限   格式统一
```

---

## 🔧 Stage 3: 深化设计阶段

### 3.1 技术栈选择 (跨平台优化)

#### 核心技术栈
```python
TECH_STACK = {
    "python_version": "3.11+",          # 现代Python特性
    "cli_framework": "click==8.1.7",    # 成熟CLI框架
    "ui_enhancement": "rich==13.7.0",   # 跨平台终端美化
    "data_processing": "polars==0.20.0", # 高性能数据处理
    "database": "duckdb==0.9.0",        # 跨平台OLAP数据库
    "tushare_sdk": "tushare==1.4.21",   # 官方SDK
    "config_parsing": "toml==0.10.2",   # 配置文件解析
    "path_handling": "pathlib",         # 标准库路径处理
    "platform_dirs": "platformdirs==4.1.0", # 跨平台目录管理
    "env_management": "python-dotenv==1.0.0", # 环境变量管理
    "retry_logic": "tenacity==8.2.3",   # 智能重试
    "validation": "pydantic==2.5.0",    # 数据验证
    "async_support": "asyncio",         # 异步处理
}
```

#### 跨平台兼容性库
```python
CROSS_PLATFORM_LIBS = {
    "path_management": "pathlib + platformdirs",
    "file_encoding": "UTF-8 强制",
    "config_location": "platformdirs.user_config_dir()",
    "data_location": "platformdirs.user_documents_dir()",
    "log_location": "platformdirs.user_log_dir()",
    "temp_location": "platformdirs.user_cache_dir()",
}
```

### 3.2 性能目标 (个人开发者优化升级版)

#### 个人开发者资源友好目标
```python
PERSONAL_DEV_TARGETS = {
    # 内存使用优化 (个人电脑友好)
    "memory_usage": {
        "peak": "≤1.5GB",                    # 峰值内存使用
        "avg": "≤800MB",                     # 平均内存使用
        "startup": "≤200MB",                 # 启动内存占用
        "gc_threshold": "每50MB触发GC"        # 垃圾回收策略
    },
    
    # 启动性能优化 (开发效率友好)
    "startup_performance": {
        "cold_start": "≤2秒",                # 冷启动时间
        "warm_start": "≤1秒",                # 热启动时间
        "config_load": "≤0.5秒",             # 配置加载时间
        "dependency_check": "≤0.3秒"         # 依赖检查时间
    },
    
    # 网络资源控制 (积分预算友好)
    "network_efficiency": {
        "concurrent_limit": 2,                # 并发连接数(保守)
        "connection_reuse": "强制复用",       # 连接复用策略
        "timeout_progressive": "3s→10s→30s",  # 渐进式超时
        "retry_intelligent": "指数退避+抖动"   # 智能重试策略
    },
    
    # 数据处理效率 (个人规模优化)
    "data_processing": {
        "batch_size": 500,                   # 批处理大小(内存友好)
        "processing_speed": "≥3000条/秒",    # 处理速度目标
        "memory_streaming": "流式处理",      # 大数据集处理方式
        "cache_hit_rate": "≥80%"             # 缓存命中率
    }
}
```

#### 跨平台一致性强化目标
```python
CROSS_PLATFORM_CONSISTENCY = {
    # 性能一致性 (严格控制)
    "performance_variance": {
        "startup_time": "≤5%差异",           # 启动时间差异
        "data_processing": "≤3%差异",        # 数据处理差异
        "memory_usage": "≤8%差异",           # 内存使用差异
        "network_latency": "平台无关"        # 网络延迟控制
    },
    
    # 功能一致性 (用户体验)
    "feature_parity": {
        "cli_commands": "100%一致",          # 命令行接口
        "config_behavior": "100%一致",       # 配置行为
        "error_messages": "100%一致",        # 错误消息
        "progress_display": "100%一致"       # 进度显示
    },
    
    # 文件系统一致性 (跨平台兼容)
    "filesystem_consistency": {
        "path_handling": "pathlib统一",      # 路径处理
        "file_encoding": "UTF-8强制",        # 文件编码
        "line_endings": "自动转换",          # 行尾符处理
        "case_sensitivity": "智能处理"       # 大小写敏感性
    }
}
```

### 3.3 配置系统设计

#### 主配置文件: aqua_data_collection.toml
```toml
[platform]
auto_detect = true
data_dir = "auto"  # 自动选择平台适合的位置
config_dir = "auto"
log_dir = "auto"

[tushare]
enabled = true
token = "${TUSHARE_TOKEN}"  # 环境变量
points_budget = 2100
points_daily_limit = 200
api_timeout = 30
retry_attempts = 3

[tushare.rate_limiting]
calls_per_minute = 200
points_per_minute = 2000
intelligent_backoff = true
burst_mode = false

[data_collection]
batch_size = 1000
parallel_workers = 2  # 个人开发者保守设置
memory_limit_mb = 1024
temp_cleanup = true

[cross_platform]
path_separator = "auto"
encoding = "utf-8"
newline = "auto"
case_sensitive = "auto"

[logging]
level = "INFO"
format = "structured"
rotation = "daily"
retention_days = 30
```

#### 环境变量管理
```bash
# Windows (.env文件)
TUSHARE_TOKEN=fce8ebc522c21b44c75ea26210d1205a05fe81409f8f916cbfe0ba4a
AQUA_CONFIG_DIR=%APPDATA%\AQUA\config
AQUA_DATA_DIR=%USERPROFILE%\Documents\AQUA\data

# macOS (.env文件) 
TUSHARE_TOKEN=fce8ebc522c21b44c75ea26210d1205a05fe81409f8f916cbfe0ba4a
AQUA_CONFIG_DIR=~/Library/Application Support/AQUA/config
AQUA_DATA_DIR=~/Documents/AQUA/data
```

### 3.4 错误处理和容错机制设计 (个人开发者友好版)

#### 异常分类和处理策略
```python
ERROR_HANDLING_STRATEGY = {
    # 网络相关异常 (个人网络环境考虑)
    "network_errors": {
        "connection_timeout": {
            "strategy": "渐进式重试",
            "timeouts": [3, 10, 30],           # 3次渐进超时
            "user_message": "网络连接超时，正在重试...",
            "fallback": "离线模式提示"
        },
        "api_rate_limit": {
            "strategy": "智能退避",
            "backoff": "指数退避+随机抖动",
            "user_message": "API调用频率受限，自动等待中...",
            "progress_show": "倒计时显示"
        },
        "network_unreachable": {
            "strategy": "自动检测",
            "detection": "ping测试+DNS解析",
            "user_message": "网络不可达，请检查网络连接",
            "guide": "提供网络诊断指南"
        }
    },
    
    # TUSHARE相关异常 (积分预算保护)
    "tushare_errors": {
        "insufficient_points": {
            "strategy": "预算保护",
            "prevention": "事前检查+余量预留",
            "user_message": "积分不足，停止数据采集",
            "recovery": "显示积分使用情况+重置建议"
        },
        "invalid_token": {
            "strategy": "配置引导",
            "detection": "启动时验证",
            "user_message": "TUSHARE Token无效",
            "guide": "详细的Token配置指南"
        },
        "api_unavailable": {
            "strategy": "服务检测",
            "fallback": "缓存数据+降级服务",
            "user_message": "TUSHARE服务暂不可用",
            "alternative": "建议使用历史缓存数据"
        }
    },
    
    # 平台相关异常 (跨平台兼容)
    "platform_errors": {
        "permission_denied": {
            "windows": "提示管理员权限",
            "macos": "提示文件系统权限",
            "strategy": "自动权限检查+修复建议",
            "user_message": "文件权限不足，需要修复"
        },
        "disk_space_low": {
            "detection": "启动时+运行时检查",
            "threshold": "可用空间<1GB",
            "strategy": "自动清理+用户选择",
            "user_message": "磁盘空间不足，建议清理"
        },
        "memory_pressure": {
            "detection": "内存使用>80%",
            "strategy": "流式处理+批次缩小",
            "user_message": "内存压力大，启用节约模式",
            "optimization": "自动调整批处理大小"
        }
    }
}
```

#### 断点续传状态管理机制
```python
RESUME_MECHANISM = {
    # 状态持久化 (跨平台一致)
    "state_persistence": {
        "state_file": "{config_dir}/resume_states.json",
        "backup_file": "{config_dir}/resume_states.backup.json",
        "compression": "gzip压缩",
        "atomic_write": "原子写入防止损坏"
    },
    
    # 任务状态跟踪
    "task_tracking": {
        "granularity": "数据批次级别",
        "state_fields": [
            "task_id", "start_time", "progress_percent",
            "completed_batches", "failed_batches", 
            "points_consumed", "last_checkpoint"
        ],
        "checkpoint_frequency": "每100条记录",
        "auto_cleanup": "7天后清理完成任务"
    },
    
    # 恢复策略 (个人开发者友好)
    "recovery_strategy": {
        "auto_resume": "启动时自动检测",
        "user_confirmation": "显示进度+用户选择",
        "data_validation": "断点处数据完整性检查",
        "conflict_resolution": "timestamp比较+用户决策"
    }
}
```

#### 积分预算超额降级策略
```python
POINTS_BUDGET_PROTECTION = {
    # 多级预算控制 (个人开发者保护)
    "budget_levels": {
        "conservative": {
            "daily_limit": "30积分/天",      # 保守模式
            "buffer": "10%安全余量",
            "priority": "核心数据优先"
        },
        "normal": {
            "daily_limit": "50积分/天",      # 正常模式  
            "buffer": "15%安全余量",
            "priority": "均衡数据获取"
        },
        "aggressive": {
            "daily_limit": "80积分/天",      # 激进模式
            "buffer": "20%安全余量",
            "priority": "最大化数据获取"
        }
    },
    
    # 降级策略 (智能调整)
    "degradation_strategy": {
        "level_1": "减少数据频率 (日线→周线)",
        "level_2": "缩小数据范围 (减少合约数)",
        "level_3": "启用缓存模式 (优先使用历史数据)",
        "level_4": "只读模式 (停止新数据获取)",
        "emergency": "完全停止 (积分耗尽保护)"
    },
    
    # 用户交互 (透明化管理)
    "user_interaction": {
        "budget_display": "实时积分余额显示",
        "warning_thresholds": [80, 90, 95],    # 预警阈值
        "user_choice": "降级策略用户确认",
        "recommendation": "智能化使用建议"
    }
}
```

### 3.5 监控界面设计规范 (个人开发者可视化)

#### 实时积分使用显示界面
```python
POINTS_MONITORING_UI = {
    # 控制台显示设计 (Rich UI)
    "console_display": {
        "layout": "顶部状态栏+主内容区+底部进度条",
        "refresh_rate": "1秒更新",
        "color_scheme": {
            "safe": "绿色 (积分充足)",
            "warning": "黄色 (积分紧张)", 
            "danger": "红色 (积分不足)",
            "info": "蓝色 (一般信息)"
        }
    },
    
    # 积分使用仪表板
    "points_dashboard": {
        "total_budget": "2100积分总预算",
        "daily_used": "今日已用积分",
        "estimated_remaining": "预估剩余可用天数",
        "efficiency_score": "积分使用效率评分",
        "cost_breakdown": "按数据类型成本分解"
    },
    
    # 跨平台显示一致性
    "cross_platform_ui": {
        "terminal_width": "自动适配终端宽度",
        "encoding": "UTF-8字符显示",
        "fallback": "ASCII字符兼容模式",
        "color_support": "自动检测颜色支持"
    }
}
```

#### 数据采集进度可视化方案
```python
PROGRESS_VISUALIZATION = {
    # 多级进度显示 (层次化信息)
    "progress_levels": {
        "task_level": "整体任务进度 (0-100%)",
        "module_level": "模块进度 (期货/股票)",
        "batch_level": "批次进度 (当前批次状态)",
        "item_level": "条目进度 (当前处理项目)"
    },
    
    # 实时状态指示器
    "status_indicators": {
        "network_status": "网络连接状态",
        "api_status": "TUSHARE API状态",
        "database_status": "数据库写入状态",
        "memory_usage": "内存使用状态",
        "processing_speed": "数据处理速度"
    },
    
    # 个人开发者友好特性
    "dev_friendly_features": {
        "eta_calculation": "智能ETA计算",
        "throughput_display": "实时吞吐量显示",
        "error_summary": "错误汇总统计",
        "resource_usage": "资源使用监控",
        "pause_resume": "暂停/恢复控制"
    }
}
```

### 3.6 版本兼容性策略设计 (长期维护友好)

#### TUSHARE API版本检测机制
```python
API_VERSION_MANAGEMENT = {
    # 版本检测策略
    "version_detection": {
        "check_frequency": "每日首次启动",
        "detection_method": "API响应头+功能探测",
        "cache_duration": "24小时",
        "fallback_strategy": "本地版本映射表"
    },
    
    # 兼容性处理
    "compatibility_handling": {
        "backward_compatible": "支持1个主版本向后兼容",
        "forward_compatible": "字段增量兼容",
        "breaking_changes": "自动检测+用户警告",
        "migration_guide": "自动生成迁移建议"
    },
    
    # 个人开发者维护友好
    "maintenance_friendly": {
        "auto_update_check": "可选的自动更新检查",
        "manual_override": "手动版本锁定选项",
        "rollback_support": "配置回滚支持",
        "debug_info": "详细的版本兼容性信息"
    }
}
```

#### 配置文件版本管理策略
```python
CONFIG_VERSION_MANAGEMENT = {
    # 版本控制机制
    "version_control": {
        "version_field": "config_version",
        "semantic_versioning": "主版本.次版本.修订版本",
        "compatibility_matrix": "版本兼容性矩阵",
        "auto_backup": "升级前自动备份"
    },
    
    # 平滑升级机制 (个人开发者无感知)
    "smooth_upgrade": {
        "incremental_migration": "增量配置迁移",
        "default_preservation": "保留用户自定义配置",
        "validation_check": "迁移后配置验证",
        "rollback_option": "一键回滚选项"
    },
    
    # 跨平台一致性保证
    "cross_platform_consistency": {
        "path_normalization": "路径格式自动转换",
        "encoding_handling": "字符编码统一处理",
        "permission_adaptation": "权限设置平台适配",
        "backup_location": "平台相关备份位置"
    }
}
```

### 3.7 数据映射规格

#### TUSHARE期货数据映射
```python
TUSHARE_FUTURES_MAPPING = {
    # API字段 -> V4.0统一字段
    "ts_code": {
        "target": "contract_code", 
        "transform": "remove_exchange_suffix",  # RB2501.SHF -> RB2501
        "validation": "contract_code_format"
    },
    "trade_date": {
        "target": "trade_datetime",
        "transform": "date_to_timestamp",
        "timezone": "Asia/Shanghai"
    },
    "open": {"target": "open_price", "type": "DECIMAL(10,4)"},
    "high": {"target": "high_price", "type": "DECIMAL(10,4)"},
    "low": {"target": "low_price", "type": "DECIMAL(10,4)"},
    "close": {"target": "close_price", "type": "DECIMAL(10,4)"},
    "settle": {"target": "settle_price", "type": "DECIMAL(10,4)"},
    "vol": {"target": "volume", "type": "BIGINT"},
    "amount": {
        "target": "amount", 
        "transform": "wan_yuan_to_yuan",  # 万元 -> 元
        "type": "BIGINT"
    },
    "oi": {"target": "open_interest", "type": "BIGINT"},
    
    # 元数据字段自动填充
    "_metadata": {
        "data_source": "TUSHARE",
        "source_quality": "HIGH", 
        "quality_score": 95.0,
        "data_status": "ACTIVE",
        "import_timestamp": "auto",
        "platform": "auto"  # Windows/macOS标记
    }
}
```

#### TUSHARE股票数据映射
```python
TUSHARE_STOCKS_MAPPING = {
    "ts_code": {"target": "stock_code", "validation": "stock_code_format"},
    "trade_date": {"target": "trade_date", "type": "DATE"},
    "open": {"target": "open_price", "type": "DECIMAL(10,3)"},
    "high": {"target": "high_price", "type": "DECIMAL(10,3)"},
    "low": {"target": "low_price", "type": "DECIMAL(10,3)"},
    "close": {"target": "close_price", "type": "DECIMAL(10,3)"},
    "pre_close": {"target": "prev_close", "type": "DECIMAL(10,3)"},
    "vol": {"target": "volume", "type": "BIGINT"},
    "amount": {"target": "amount", "type": "BIGINT"},  # 已经是元单位
    
    "_metadata": {
        "data_source": "TUSHARE",
        "source_quality": "HIGH",
        "quality_score": 95.0,
        "data_status": "ACTIVE"
    }
}
```

---

## ⚛️ Stage 4: 原子化任务拆解

### 4.1 任务组织原则
- 每个任务1-3天完成 (个人开发者时间考虑)
- 任务间依赖关系清晰
- 支持跨平台并行验证
- 每个任务有明确的验收标准

### 4.2 任务拆解详表

#### Module 1: 个人开发者跨平台基础设施 (3天 - 深度优化版)

**✅ 现有架构复用优势** (85%已完成):
- ConfigLoader类已实现完整跨平台支持 ✅
- 环境变量管理 (TUSHARE_TOKEN) 已完成 ✅  
- 路径处理和目录管理已完成 ✅
- 配置文件TOML解析已完成 ✅

**🚀 个人开发者优化增强**:

**Task 1.1: 个人开发者配置优化** (1天) **[最高优先级]**
- **描述**: 针对个人开发者优化配置系统，激活TUSHARE并添加个人友好特性
- **输入**: 现有配置文件 + 个人开发者需求分析
- **输出**: 个人开发者优化的配置系统
- **验收标准**: 
  - `[datasources.api.tushare] enabled = true`
  - 个人开发者资源友好配置 (内存≤1.5GB)
  - 多级积分预算控制 (保守/正常/激进模式)
  - 跨平台一致性配置验证
- **个人开发者特性**:
  - 启动时间≤2秒优化
  - 智能资源检测和调整
  - 用户友好的错误提示
- **风险**: 低 (基于现有配置扩展)
- **依赖**: 无

**Task 1.2: 跨平台错误处理框架** (1天) **[高优先级]**
- **描述**: 实现个人开发者友好的跨平台错误处理和恢复机制
- **输入**: 错误处理策略设计 + 平台差异分析
- **输出**: 统一错误处理框架
- **验收标准**:
  - Windows/macOS错误消息100%一致
  - 网络异常智能重试 (渐进式超时)
  - 积分预算保护机制
  - 断点续传状态管理
- **个人开发者特性**:
  - 错误自动诊断和修复建议
  - 透明的恢复进度显示
  - 一键问题解决指引
- **依赖**: Task 1.1

**Task 1.3: 个人开发者监控界面** (1天) **[高优先级]**
- **描述**: 实现个人开发者可视化监控界面，积分使用和进度跟踪
- **输入**: 监控界面设计规范 + Rich UI框架
- **输出**: 个人开发者友好的监控系统
- **验收标准**:
  - 实时积分使用仪表板
  - 多级进度可视化 (任务/模块/批次/条目)
  - 跨平台界面一致性 (Windows/macOS)
  - 资源使用状态监控
- **个人开发者特性**:
  - 终端宽度自动适配
  - 颜色主题智能检测
  - 暂停/恢复控制
  - ETA智能计算
- **依赖**: Task 1.1, Task 1.2

#### Module 2: TUSHARE核心集成 (5天 - 关键模块)

**🎯 复用现有SimpleExtractor架构**:
- SimpleExtractor基类已定义 ✅
- SimpleExtractorFactory已实现 ✅
- 需实现TushareExtractor具体类 ❌ (核心缺失)

**Task 2.1: TushareExtractor基础实现** (2天) **[最高优先级]**
- **描述**: 实现TushareExtractor继承SimpleExtractor基类
- **输入**: SimpleExtractor基类、TUSHARE配置
- **输出**: TushareExtractor类，基础extract()方法
- **验收标准**:
  - 继承SimpleExtractor基类正确
  - Token认证和API连接成功  
  - SimpleExtractorFactory能正确创建实例
  - 基础错误处理完善
- **风险**: API限制、网络稳定性
- **依赖**: Task 1.1

**Task 2.2: 积分管理和频率控制** (2天) **[高优先级]**
- **描述**: 在TushareExtractor中实现积分预算和频率控制
- **输入**: TUSHARE API响应、配置的限制参数
- **输出**: 集成的积分跟踪和频率限制机制
- **验收标准**:
  - 2100积分预算严格控制
  - 200次/分钟频率限制有效
  - 积分消耗准确计算
  - 超限自动阻止和重试
- **依赖**: Task 2.1

**Task 2.3: 数据采集方法实现** (1天) **[高优先级]**
- **描述**: 实现期货和股票数据的具体采集方法
- **输入**: 合约/股票代码列表、日期参数
- **输出**: 期货和股票数据采集功能
- **验收标准**:
  - fut_daily API正确调用
  - daily API (股票)正确调用
  - 数据批量获取有效
  - 返回标准DataFrame格式
- **依赖**: Task 2.2

#### Module 3: 数据处理和映射 (6天)

**Task 3.1: TUSHARE数据字段映射** (2天)
- **描述**: TUSHARE字段到V4.0统一业务表的映射转换
- **输入**: TUSHARE原始数据、映射规则配置
- **输出**: DataMapper类、字段映射配置
- **验收标准**:
  - 期货和股票字段映射准确
  - 数据类型转换正确
  - 单位转换无误 (万元→元)
  - 合约代码标准化正确
- **依赖**: Task 2.3, Task 2.4

**Task 3.2: 数据验证和质量控制** (2天)
- **描述**: 数据完整性验证和质量评分
- **输入**: 映射后的数据
- **输出**: DataValidator类、质量评分算法
- **验收标准**:
  - 数据完整性检查准确
  - 异常数据识别有效
  - 质量评分算法合理
  - 数据修复策略正确
- **依赖**: Task 3.1

**Task 3.3: 元数据管理和数据源追溯** (2天)
- **描述**: 统一元数据字段管理和数据来源追溯
- **输入**: 处理后的数据、数据源信息
- **输出**: MetadataManager类
- **验收标准**:
  - 元数据字段自动填充
  - 数据来源准确追溯
  - 平台信息正确记录
  - 导入时间戳准确
- **依赖**: Task 3.2

#### Module 4: 存储管理 (4天)

**Task 4.1: DuckDB表路由和映射** (2天)
- **描述**: 数据到V4.0统一业务表的路由和写入
- **输入**: 验证后的数据、表映射配置
- **输出**: TableRouter类、存储管理器
- **验收标准**:
  - 期货数据正确写入对应业务表
  - 股票数据正确写入对应业务表
  - 表结构与DATA_DICTIONARY.md一致
  - 批量写入性能达标
- **依赖**: Task 3.3

**Task 4.2: 数据冲突处理和优先级管理** (2天)
- **描述**: 多数据源冲突解决，TUSHARE最高优先级
- **输入**: 冲突数据记录、优先级配置
- **输出**: ConflictResolver类
- **验收标准**:
  - TUSHARE数据自动覆盖低质量数据
  - 冲突解决策略正确执行
  - 数据版本管理正确
- **依赖**: Task 4.1

#### Module 5: CLI用户界面 (4天)

**Task 5.1: CLI命令框架设计** (2天)
- **描述**: Click基础CLI命令结构和参数设计
- **输入**: CLI设计规范
- **输出**: CLI主程序、命令组结构
- **验收标准**:
  - 命令结构清晰直观
  - 参数验证完善
  - 帮助信息详细
  - 跨平台命令一致
- **依赖**: 无

**Task 5.2: Rich界面和进度显示** (2天)
- **描述**: 终端美化和进度监控显示
- **输入**: 数据采集进度、状态信息
- **输出**: UI组件、进度显示器
- **验收标准**:
  - 进度条显示准确
  - 积分使用实时显示
  - 错误信息清晰友好
  - 跨平台界面一致
- **依赖**: Task 5.1

#### Module 6: 集成和优化 (4天)

**Task 6.1: 端到端集成** (2天)
- **描述**: 所有模块集成和完整数据流测试
- **输入**: 所有功能模块
- **输出**: 完整的CLI工具
- **验收标准**:
  - 完整数据采集流程正常
  - 跨平台功能一致
  - 性能指标达标
- **依赖**: 所有前置任务

**Task 6.2: 性能优化和错误处理** (2天)
- **描述**: 性能调优和异常场景处理
- **输入**: 集成测试结果
- **输出**: 优化后的系统
- **验收标准**:
  - 内存使用≤2GB
  - 积分利用率≥95%
  - 错误恢复时间≤30秒
- **依赖**: Task 6.1

### 4.3 任务依赖关系图

```
Module 1 (基础设施)    Module 2 (TUSHARE)     Module 3 (数据处理)
┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│ Task 1.1     │────→ │ Task 2.1     │      │              │
│ Task 1.2     │────→ │ Task 2.2     │────→ │ Task 3.1     │
│ Task 1.3     │      │ Task 2.3     │────→ │ Task 3.2     │
│ Task 1.4     │      │ Task 2.4     │────→ │ Task 3.3     │
└──────────────┘      └──────────────┘      └──────────────┘
                                                   │
Module 5 (CLI界面)     Module 4 (存储)        │
┌──────────────┐      ┌──────────────┐      │
│ Task 5.1     │      │ Task 4.1     │←─────┘
│ Task 5.2     │      │ Task 4.2     │
└──────────────┘      └──────────────┘
       │                     │
       └──────┬──────────────┘
              │
      Module 6 (集成)
      ┌──────────────┐
      │ Task 6.1     │
      │ Task 6.2     │
      └──────────────┘
```

---

## 🧪 Stage 5: TDD开发计划 (实际数据优先)

### 5.1 测试策略 (实际数据为主)

#### 核心原则
- **实际数据优先**: 所有测试优先使用真实TUSHARE数据
- **积分预算控制**: 每日测试积分消耗≤50积分
- **跨平台验证**: Windows和macOS并行测试
- **渐进式测试**: 从小数据集到完整数据流

#### 测试数据管理策略
```python
TEST_DATA_STRATEGY = {
    "real_data_budget": {
        "daily_points": 50,           # 每日测试积分预算
        "single_test_limit": 5,       # 单次测试积分上限
        "cache_strategy": "aggressive", # 激进缓存策略
        "refresh_cycle": "weekly"     # 测试数据刷新周期
    },
    
    "test_data_categories": {
        "mini_sample": {
            "futures_contracts": ["RB2501", "CU2501"],  # 2个合约
            "stocks_codes": ["000001.SZ", "600000.SH"], # 2只股票
            "date_range": "1_day",                       # 1天数据
            "points_cost": 2                             # 积分消耗
        },
        "standard_sample": {
            "futures_contracts": ["RB2501", "CU2501", "AL2501"], # 3个合约
            "stocks_codes": ["000001.SZ", "600000.SH", "000002.SZ"], # 3只股票  
            "date_range": "1_week",                      # 1周数据
            "points_cost": 15
        },
        "integration_sample": {
            "futures_contracts": "main_contracts_top10", # 主力合约前10
            "stocks_codes": "hs300_sample_20",           # 沪深300样本20只
            "date_range": "1_month",                     # 1个月数据
            "points_cost": 100
        }
    }
}
```

### 5.2 分层测试设计

#### Layer 1: 单元测试 (Real Data Mini Samples)
```python
# 使用真实数据的最小样本进行单元测试
class TestTushareDataCollector:
    """TUSHARE数据采集器单元测试 - 真实数据"""
    
    @pytest.fixture
    def real_mini_futures_data(self):
        """获取2个期货合约1天的真实数据"""
        collector = TushareDataCollector(points_budget=5)
        return collector.get_futures_daily(
            ts_code="RB2501.SHF,CU2501.SHF", 
            trade_date="20241201"
        )
    
    def test_futures_data_structure(self, real_mini_futures_data):
        """验证期货数据结构 - 基于真实数据"""
        assert len(real_mini_futures_data) >= 1
        assert 'ts_code' in real_mini_futures_data.columns
        assert 'open' in real_mini_futures_data.columns
        # ... 基于真实数据验证完整字段结构
    
    def test_data_field_mapping(self, real_mini_futures_data):
        """验证字段映射转换 - 真实数据"""
        mapper = DataMapper()
        mapped_data = mapper.map_futures_data(real_mini_futures_data)
        
        # 验证真实的合约代码转换
        assert all('.' not in code for code in mapped_data['contract_code'])
        # 验证真实的价格数据
        assert all(price > 0 for price in mapped_data['open_price'])
```

#### Layer 2: 集成测试 (Real Data Standard Samples)
```python
class TestDataCollectionIntegration:
    """完整数据采集流程集成测试 - 真实数据"""
    
    @pytest.fixture(scope="session")
    def real_standard_sample(self):
        """获取标准真实数据样本 - 会话级别缓存"""
        collector = AquaDataCollectionCLI()
        return collector.collect_data(
            data_types=['futures', 'stocks'],
            date_range='2024-12-01,2024-12-07',  # 1周真实数据
            contracts=['RB2501', 'CU2501', 'AL2501'],
            stocks=['000001.SZ', '600000.SH', '000002.SZ']
        )
    
    def test_end_to_end_data_flow(self, real_standard_sample):
        """端到端数据流测试 - 真实数据验证"""
        # 验证数据采集
        assert real_standard_sample['success'] == True
        
        # 验证数据库写入
        db_records = self.query_database_records()
        assert len(db_records) > 0
        
        # 验证元数据记录
        metadata_records = self.query_metadata_records()
        assert all(record['data_source'] == 'TUSHARE' for record in metadata_records)
        
        # 验证数据质量
        quality_scores = [record['quality_score'] for record in metadata_records]
        assert all(score >= 95.0 for score in quality_scores)
```

#### Layer 3: 跨平台测试 (Real Data Cross-Platform)
```python
class TestCrossPlatformCompatibility:
    """跨平台兼容性测试 - 真实数据"""
    
    @pytest.mark.parametrize("platform", ["Windows", "macOS"])
    def test_platform_specific_data_collection(self, platform):
        """平台特定数据采集测试"""
        
        # 模拟平台环境
        with mock_platform(platform):
            collector = AquaDataCollectionCLI()
            
            # 使用真实数据进行跨平台测试
            result = collector.collect_futures_data(
                contracts=['RB2501'],
                date_range='2024-12-01',
                points_budget=5
            )
            
            # 验证跨平台结果一致性
            assert result['success'] == True
            assert len(result['data']) > 0
            
            # 验证平台特定路径处理
            config_path = collector.get_config_path()
            assert self.validate_platform_path(config_path, platform)
```

### 5.3 TDD开发循环 (Real Data Driven)

#### Red Phase: 基于真实数据结构编写失败测试
```python
def test_tushare_futures_api_integration():
    """红色阶段: 基于真实TUSHARE API数据结构的测试"""
    
    # 这个测试会失败，因为还没有实现
    collector = TushareDataCollector()
    
    # 使用真实的TUSHARE API调用
    real_data = collector.get_futures_daily(
        ts_code="RB2501.SHF",
        trade_date="20241201"
    )
    
    # 基于真实数据结构的断言（会失败）
    assert real_data is not None
    assert 'ts_code' in real_data.columns
    assert real_data['ts_code'].iloc[0] == "RB2501.SHF"
    assert real_data['open'].iloc[0] > 0
```

#### Green Phase: 实现最小可行代码使真实数据测试通过
```python
class TushareDataCollector:
    """绿色阶段: 实现最小代码使真实数据测试通过"""
    
    def __init__(self):
        import tushare as ts
        self.pro = ts.pro_api(token=os.getenv('TUSHARE_TOKEN'))
    
    def get_futures_daily(self, ts_code: str, trade_date: str):
        """最小实现：获取真实期货日线数据"""
        try:
            df = self.pro.fut_daily(
                ts_code=ts_code,
                trade_date=trade_date
            )
            return df
        except Exception as e:
            logger.error(f"获取期货数据失败: {e}")
            return None
```

#### Refactor Phase: 基于真实数据优化和重构
```python
class TushareDataCollector:
    """重构阶段: 基于真实数据使用模式优化代码"""
    
    def __init__(self, points_budget: int = 2100):
        self.points_budget = points_budget
        self.points_used = 0
        self.rate_limiter = RateLimiter(calls_per_minute=200)
        
        import tushare as ts
        self.pro = ts.pro_api(token=os.getenv('TUSHARE_TOKEN'))
    
    @rate_limited
    @retry_on_failure
    def get_futures_daily(self, ts_code: str, trade_date: str) -> pd.DataFrame:
        """重构后: 增加频率控制、重试、积分管理"""
        
        # 积分预算检查
        if self.points_used + 5 > self.points_budget:
            raise PointsBudgetExceededError()
        
        try:
            df = self.pro.fut_daily(ts_code=ts_code, trade_date=trade_date)
            self.points_used += 5  # 期货日线数据消耗5积分
            
            # 数据质量验证（基于真实数据特征）
            if df.empty:
                raise DataNotFoundError(f"未找到数据: {ts_code}, {trade_date}")
            
            return df
            
        except Exception as e:
            logger.error(f"获取期货数据失败: {e}")
            raise
```

### 5.4 测试数据管理和缓存策略

#### 真实数据缓存系统
```python
class RealDataCache:
    """真实数据缓存管理 - 减少积分重复消耗"""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = cache_dir
        self.cache_ttl = timedelta(days=7)  # 缓存7天
    
    def get_cached_data(self, data_key: str) -> Optional[pd.DataFrame]:
        """获取缓存的真实数据"""
        cache_file = self.cache_dir / f"{data_key}.parquet"
        
        if cache_file.exists():
            cache_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
            if datetime.now() - cache_time < self.cache_ttl:
                return pd.read_parquet(cache_file)
        
        return None
    
    def cache_real_data(self, data_key: str, data: pd.DataFrame):
        """缓存真实数据以供后续测试使用"""
        cache_file = self.cache_dir / f"{data_key}.parquet"
        data.to_parquet(cache_file)
        logger.info(f"缓存真实数据: {data_key}, {len(data)}条记录")
```

#### 积分使用监控
```python
class TestPointsMonitor:
    """测试积分使用监控"""
    
    def __init__(self):
        self.daily_limit = 50
        self.used_today = 0
        self.test_calls = []
    
    def before_test_call(self, test_name: str, expected_points: int):
        """测试前积分检查"""
        if self.used_today + expected_points > self.daily_limit:
            pytest.skip(f"今日测试积分预算不足: {test_name}")
    
    def after_test_call(self, test_name: str, actual_points: int):
        """测试后积分记录"""
        self.used_today += actual_points
        self.test_calls.append({
            'test_name': test_name,
            'points_used': actual_points,
            'timestamp': datetime.now()
        })
```

---

## ✅ Stage 6: 交付验收

### 6.1 MVP验收标准

#### 6.1.1 功能完整性验收

**核心功能必达标准**:
- ✅ **TUSHARE集成**: 期货和股票数据完整采集，API调用成功率≥99%
- ✅ **跨平台兼容**: Windows 10/11和macOS功能完全一致
- ✅ **数据映射**: TUSHARE数据准确映射到V4.0统一业务表
- ✅ **积分管理**: 2100积分高效利用，使用效率≥95%
- ✅ **配置驱动**: 全程配置文件驱动，无硬编码常量

**数据质量验收**:
- ✅ **数据准确性**: TUSHARE数据与官方源100%一致
- ✅ **数据完整性**: 历史数据回填无遗漏，增量更新及时
- ✅ **数据一致性**: 跨平台数据存储格式完全一致
- ✅ **元数据完整**: 数据来源、质量评分、导入时间准确记录

#### 6.1.2 性能验收标准

**个人开发者环境性能目标**:
```python
PERFORMANCE_ACCEPTANCE = {
    "system_resources": {
        "memory_usage": "≤2GB",           # 个人电脑内存友好
        "disk_space": "≤100MB程序体积",   # 轻量级安装包
        "startup_time": "≤3秒",           # CLI启动速度
        "shutdown_time": "≤1秒"           # 清理和退出时间
    },
    
    "api_performance": {
        "tushare_response": "≤5秒平均",    # API响应时间
        "rate_limit_compliance": "100%",   # 频率限制遵守
        "points_efficiency": "≥95%",       # 积分利用效率
        "error_rate": "≤0.5%"             # API调用错误率
    },
    
    "data_processing": {
        "mapping_speed": "≥5000条/秒",     # 数据映射速度
        "validation_speed": "≥8000条/秒",  # 数据验证速度
        "storage_speed": "≥10000条/秒",    # 数据库写入速度
        "query_response": "≤100ms"        # 数据库查询响应
    },
    
    "cross_platform": {
        "performance_variance": "≤10%",    # 跨平台性能差异
        "config_load_time": "≤1秒",        # 配置加载时间
        "path_resolution": "≤50ms"        # 路径解析时间
    }
}
```

#### 6.1.3 易用性验收标准

**用户体验标准**:
- ✅ **安装简易**: 单命令安装，依赖自动解决
- ✅ **配置简单**: 仅需配置TUSHARE_TOKEN，其他自动检测
- ✅ **命令直观**: CLI命令符合直觉，帮助信息详细
- ✅ **错误友好**: 错误信息清晰，提供具体解决建议
- ✅ **进度透明**: 数据采集进度实时显示，积分消耗可见

**跨平台一致性标准**:
- ✅ **界面一致**: Windows和macOS界面显示完全一致
- ✅ **命令一致**: 所有CLI命令和参数跨平台通用
- ✅ **配置一致**: 配置文件格式和位置自动适配
- ✅ **数据一致**: 采集的数据格式和存储结构完全一致

### 6.2 测试验收标准

#### 6.2.1 实际数据测试验收

**真实数据测试覆盖率**:
```python
REAL_DATA_TEST_COVERAGE = {
    "unit_tests": {
        "real_data_ratio": "≥80%",        # 单元测试80%使用真实数据
        "mock_data_ratio": "≤20%",        # 模拟数据仅限边界测试
        "points_budget": "≤30积分/天"      # 单元测试积分预算
    },
    
    "integration_tests": {
        "real_data_ratio": "100%",        # 集成测试100%真实数据
        "data_scenarios": [               # 必测数据场景
            "期货主力合约日线数据",
            "期货分钟线数据采集",
            "股票日线数据采集", 
            "股票基础信息获取",
            "历史数据批量回填",
            "增量数据更新"
        ],
        "points_budget": "≤100积分/测试周期"
    },
    
    "cross_platform_tests": {
        "platforms": ["Windows_10", "Windows_11", "macOS_12+"],
        "test_data": "相同真实数据集",
        "result_consistency": "100%一致性"
    }
}
```

**数据质量验证**:
- ✅ **数据准确性**: 与TUSHARE官方数据100%一致性验证
- ✅ **数据完整性**: 缺失数据自动检测和报告
- ✅ **数据时效性**: T+1数据及时性验证
- ✅ **数据格式**: 字段类型和格式严格验证

#### 6.2.2 测试覆盖率标准

**代码覆盖率要求**:
```python
COVERAGE_REQUIREMENTS = {
    "overall_coverage": "≥90%",           # 整体代码覆盖率
    
    "module_specific": {
        "tushare_integration": "≥95%",    # TUSHARE集成模块
        "data_mapping": "≥95%",           # 数据映射模块
        "cross_platform": "≥90%",        # 跨平台兼容模块
        "cli_interface": "≥85%",          # CLI界面模块
        "error_handling": "≥90%"          # 错误处理模块
    },
    
    "test_categories": {
        "unit_tests": "≥200个测试用例",
        "integration_tests": "≥50个测试用例",
        "cross_platform_tests": "≥30个测试用例",
        "performance_tests": "≥20个基准测试"
    }
}
```

### 6.3 交付物清单

#### 6.3.1 软件交付物

**1. 可执行程序**
- `aqua-data-cli.exe` (Windows版本)
- `aqua-data-cli` (macOS版本) 
- 跨平台Python包 `aqua-data-collection`

**2. 配置模板和示例**
- `aqua_data_collection.toml` - 主配置文件模板
- `.env.example` - 环境变量配置示例
- `tushare_config_example.toml` - TUSHARE专用配置示例

**3. 安装和部署包**  
- `requirements.txt` - Python依赖清单
- `setup.py` - 安装脚本
- `install_windows.bat` - Windows一键安装脚本
- `install_macos.sh` - macOS一键安装脚本

#### 6.3.2 文档交付物

**1. 用户文档**
- `用户使用手册.md` - 完整使用指南
- `Windows平台安装指南.md` - Windows特定说明
- `macOS平台安装指南.md` - macOS特定说明
- `TUSHARE集成配置指南.md` - TUSHARE专用配置
- `常见问题解答.md` - FAQ和故障排除

**2. 技术文档**
- `系统架构设计.md` - 详细技术架构说明
- `API接口文档.md` - 内部API接口规范
- `数据映射规范.md` - TUSHARE到统一表的映射规则
- `跨平台兼容性设计.md` - 平台兼容实现说明
- `性能优化指南.md` - 性能调优建议

**3. 开发者文档**
- `开发环境搭建.md` - 开发环境配置指南
- `代码贡献指南.md` - 代码提交和review规范
- `扩展开发指南.md` - 插件和扩展开发说明
- `测试指南.md` - 测试策略和实践

#### 6.3.3 质量保证交付物

**1. 测试报告**
- `实际数据测试报告.md` - 真实数据测试结果
- `跨平台兼容性测试报告.md` - 平台测试结果
- `性能基准测试报告.md` - 性能测试数据
- `积分使用效率分析报告.md` - TUSHARE积分使用分析

**2. 代码质量报告**
- `代码覆盖率报告.html` - 测试覆盖率详细报告
- `静态代码分析报告.md` - 代码质量分析
- `安全性检查报告.md` - 安全漏洞扫描结果

### 6.4 验收流程和里程碑

#### 6.4.1 验收流程

**Phase 1: 内部验收 (2天)**
- 功能完整性自检
- 性能指标验证  
- 跨平台兼容性测试
- 代码质量review

**Phase 2: 用户验收测试 (3天)**
- 真实用户场景测试
- 个人开发者环境验证
- 易用性和文档完整性检查
- 用户反馈收集和问题修复

**Phase 3: 生产就绪验证 (2天)**
- 生产环境部署测试
- 长时间稳定性验证
- 资源使用监控
- 最终验收确认

#### 6.4.2 里程碑时间表

```
Week 1-2: 跨平台基础设施 + TUSHARE核心集成
├── 里程碑1: 平台检测和配置管理完成
├── 里程碑2: TUSHARE API连接和认证成功
└── 里程碑3: 基础数据采集功能可用

Week 3-4: 数据处理映射 + 存储管理  
├── 里程碑4: 数据映射和验证完成
├── 里程碑5: DuckDB存储集成完成
└── 里程碑6: 元数据管理功能可用

Week 5: CLI界面 + 集成优化
├── 里程碑7: CLI用户界面完成
├── 里程碑8: 端到端集成测试通过
└── 里程碑9: 性能优化达标

Week 6: 测试完善 + 文档交付
├── 里程碑10: 实际数据测试全部通过
├── 里程碑11: 跨平台兼容性验证完成
└── 里程碑12: 文档和交付物完整

Week 7: 用户验收 + 生产部署
├── 里程碑13: 用户验收测试通过
├── 里程碑14: 生产环境验证成功
└── 里程碑15: 正式交付和部署
```

### 6.5 成功标准定义

#### 最低可接受标准 (MVP)
- ✅ TUSHARE期货和股票数据采集功能正常
- ✅ Windows和macOS平台基本功能一致
- ✅ 积分使用效率≥90%
- ✅ 系统稳定运行无崩溃
- ✅ 用户文档完整可用

#### 理想成功标准  
- ✅ 所有性能指标100%达标
- ✅ 跨平台功能和性能完全一致
- ✅ 积分使用效率≥95%
- ✅ 用户体验优秀，易用性高
- ✅ 代码质量优秀，可维护性强

#### 超预期标准
- ✅ 性能指标超过目标20%以上
- ✅ 用户反馈满意度≥95%
- ✅ 零关键bug，极少一般问题
- ✅ 支持更多数据类型和扩展
- ✅ 社区贡献和生态建设启动

---

## 📊 项目风险评估和缓解策略

### 技术风险
1. **TUSHARE API变更风险**: 建立版本兼容性检查机制
2. **跨平台兼容性风险**: 早期并行开发和测试验证
3. **积分超额使用风险**: 严格的预算控制和监控机制
4. **性能不达标风险**: 持续的性能基准测试

### 进度风险  
1. **个人开发者时间限制**: 合理的任务拆解和优先级管理
2. **跨平台测试复杂性**: 自动化测试和CI/CD流程
3. **实际数据测试成本**: 积分预算管理和缓存策略

### 质量风险
1. **真实数据测试覆盖不足**: 强制性实际数据测试要求
2. **用户体验不佳**: 早期用户反馈和迭代优化
3. **文档不完整**: 文档优先和review机制

---

## 🎯 项目成功关键因素

### 技术成功因素
- TUSHARE Pro API的深度集成和优化使用
- 跨平台兼容性的全面验证和支持  
- V4.0统一业务表架构的准确映射
- 个人开发者友好的性能和资源使用

### 用户成功因素
- 极简的安装和配置流程
- 直观清晰的CLI用户界面
- 完整详细的使用文档
- 快速响应的技术支持

### 项目成功因素
- 严格的TDD开发流程执行
- 基于实际数据的测试策略
- 持续的质量监控和改进
- 及时的风险识别和缓解

---

---

## 🔄 Stage 7: 增量采集功能设计 (新增)

### 7.1 增量采集核心需求

#### 业务需求
- **严格无重叠**: 从最后采集日期的下一个交易日开始采集
- **防止数据遗漏**: 基于交易日历确保时间连续性
- **智能回退**: 首次使用时自动使用settings.toml默认配置
- **跨数据源支持**: TUSHARE、CSV、MySQL三种数据源统一支持

#### 技术需求
- **轻量级集成**: 最小化对现有代码的修改
- **高性能**: 增量查询和计算性能优化
- **容错性**: 增量失败时自动回退到全量采集
- **跨平台一致性**: Windows和macOS行为完全一致

### 7.2 增量采集架构设计

#### 核心组件关系
```
CLI --incremental → CollectService → IncrementalHelper → TradingCalendar
                                          ↓                    ↓
                                   ImportHistory         交易日验证
                                          ↓                    ↓
                                   增量范围计算 ← ← ← ← ← ← ← ← ←
                                          ↓
                                   现有数据采集流程
```

#### 数据流设计
```
增量参数 → 历史查询 → 交易日历 → 范围计算 → 数据采集 → DataProcessor → 存储 → 状态更新
   ↓         ↓         ↓         ↓         ↓         ↓           ↓       ↓
--incremental 最后日期  下个交易日  时间范围   API调用   数据处理    DuckDB  历史记录
```

### 7.3 交易日历集成设计

#### market_trading_calendar表结构应用
```sql
-- 增量采集中的交易日历查询
SELECT MIN(cal_date) as next_trading_date
FROM market_trading_calendar
WHERE market_type = 'STOCK'  -- 或 'FUTURES'
  AND exchange_code = 'SSE'   -- 或其他交易所
  AND cal_date > '2024-12-01' -- 最后采集日期
  AND is_trading_day = TRUE;
```

#### 交易日历数据初始化策略
- **数据源**: TUSHARE的trade_cal接口
- **初始化时机**: 系统首次运行或交易日历为空时
- **更新策略**: 每年初自动更新下一年的交易日历
- **缓存机制**: 内存缓存常用查询，提升性能

### 7.4 增量采集实现策略

#### A1: 严格无重叠策略
```python
def calculate_incremental_range(self, symbol: str, data_type: str) -> Dict[str, str]:
    """严格无重叠增量范围计算"""

    # 1. 查询最后采集日期
    last_date = self.history_manager.get_last_import_date(symbol, data_type, 'TUSHARE')

    # 2. 如果没有历史记录，使用settings.toml默认配置
    if not last_date:
        return self._get_default_range_from_settings(data_type)

    # 3. 基于交易日历获取下一个交易日
    market_type = 'STOCK' if data_type == 'stocks' else 'FUTURES'
    exchange_code = self._get_exchange_code(symbol, data_type)

    next_trading_date = self.trading_calendar.get_next_trading_date(
        last_date, market_type, exchange_code
    )

    # 4. 如果没有新的交易日，返回None
    if not next_trading_date or next_trading_date > datetime.now().strftime('%Y-%m-%d'):
        return None

    # 5. 返回严格的增量范围
    return {
        'start_date': next_trading_date,
        'end_date': datetime.now().strftime('%Y-%m-%d')
    }
```

#### B2: 智能交易日计算
```python
def get_next_trading_date(self, date: str, market_type: str, exchange_code: str) -> str:
    """基于交易日历的智能计算"""

    # 优先从数据库查询
    query = """
    SELECT MIN(cal_date) as next_trading_date
    FROM market_trading_calendar
    WHERE market_type = ? AND exchange_code = ?
      AND cal_date > ? AND is_trading_day = TRUE
    """

    result = self.db_manager.execute_query(query, [market_type, exchange_code, date])

    if result and result[0][0]:
        return result[0][0]

    # 如果数据库没有数据，使用简化逻辑（排除周末）
    next_date = datetime.strptime(date, '%Y-%m-%d') + timedelta(days=1)

    # 跳过周末
    while next_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
        next_date += timedelta(days=1)

    return next_date.strftime('%Y-%m-%d')
```

#### C2: settings.toml默认配置
```toml
[data_collection.default_days]
stocks = 30      # 股票数据默认采集30天
futures = 60     # 期货数据默认采集60天
minutes = 7      # 分钟数据默认采集7天

[data_collection.incremental]
enabled = true                    # 启用增量采集
overlap_protection = true         # 重叠保护（严格无重叠）
fallback_to_full = true          # 失败时回退到全量采集
cache_trading_calendar = true     # 缓存交易日历
```

### 7.5 集成点设计

#### CollectService集成
```python
def collect_data(self, symbols: List[str], source: str = 'tushare',
                data_type: str = 'stock', freq: str = 'daily',
                preview: bool = False, incremental: bool = False, **kwargs) -> Dict[str, Any]:

    # 新增：增量采集逻辑分支
    if incremental:
        incremental_helper = self._get_incremental_helper()

        # 计算增量范围
        incremental_ranges = incremental_helper.calculate_incremental_range(
            symbols, data_type, freq, source
        )

        if not incremental_ranges:
            return {'success': True, 'message': '无新数据需要采集', 'records_processed': 0}

        # 更新时间范围参数
        kwargs.update(incremental_ranges)
        self.logger.info(f"使用增量采集范围: {incremental_ranges}")

    # 原有逻辑保持完全不变
    try:
        if source == 'tushare':
            return self._collect_from_tushare(symbols, data_type, freq, preview, **kwargs)
        elif source == 'csv':
            return self._collect_from_csv(symbols, data_type, freq, preview, **kwargs)
        elif source == 'mysql':
            return self._collect_from_mysql(symbols, data_type, freq, preview, **kwargs)
        else:
            raise ValueError(f"不支持的数据源: {source}")

    except Exception as e:
        if incremental:
            self.logger.warning(f"增量采集失败，回退到全量采集: {e}")
            # 移除增量参数，重新执行全量采集
            kwargs.pop('start_date', None)
            kwargs.pop('end_date', None)
            return self.collect_data(symbols, source, data_type, freq, preview, False, **kwargs)
        else:
            raise
```

### 7.6 性能优化设计

#### 缓存策略
- **交易日历缓存**: 内存缓存常用的交易日查询
- **历史记录缓存**: 缓存最近查询的导入历史
- **范围计算缓存**: 缓存计算结果，避免重复计算

#### 数据库优化
```sql
-- 为增量采集优化的索引
CREATE INDEX idx_import_history_incremental
ON data_import_history(symbol, data_type, source, import_date DESC);

CREATE INDEX idx_trading_calendar_incremental
ON market_trading_calendar(market_type, exchange_code, cal_date, is_trading_day);
```

### 7.7 测试策略

#### 增量采集专项测试
```python
class TestIncrementalCollection:
    """增量采集功能测试"""

    def test_first_time_incremental(self):
        """首次使用增量采集测试"""
        # 清空历史记录
        # 执行增量采集
        # 验证使用settings.toml默认配置

    def test_strict_no_overlap(self):
        """严格无重叠测试"""
        # 模拟已有历史数据
        # 执行增量采集
        # 验证时间范围严格连续

    def test_trading_calendar_integration(self):
        """交易日历集成测试"""
        # 测试交易日验证
        # 测试节假日跳过
        # 测试跨年处理

    def test_incremental_fallback(self):
        """增量采集回退测试"""
        # 模拟增量采集失败
        # 验证自动回退到全量采集
        # 验证数据完整性
```

---

**本设计方案已完成所有七个阶段的详细规划，包含完整的DataProcessor集成和增量采集功能设计，等待用户确认后开始具体的开发工作实施。**