# AQUA V4.0 统一业务表架构总览

> **文档版本**: V4.0  
> **创建日期**: 2025-07-28  
> **架构类型**: 统一业务表架构  
> **文档状态**: 权威架构文档

---

## 📋 架构概述

### 核心理念

AQUA V4.0采用**统一业务表架构**，这是一种业务导向的数据组织方式：

- **业务驱动**: 以业务表为核心，通过元数据字段管理数据来源
- **查询简化**: 应用层直接访问统一业务表，无需复杂表关联
- **数据透明**: 数据源差异在底层处理，应用层统一视图
- **扩展灵活**: 通过元数据字段支持多数据源无缝集成

### 架构特点

```
统一业务表架构设计理念
┌─────────────────────────────────────────────────────────────┐
│                    应用业务层                                 │
│           直接查询统一业务表，业务逻辑简单清晰                  │
├─────────────────────────────────────────────────────────────┤
│                  统一业务表层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 期货业务表  │  │ 股票业务表  │  │ 公共业务表  │          │
│  │ 包含元数据  │  │ 包含元数据  │  │ 包含元数据  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                   数据源适配层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ CSV适配器   │  │ MySQL适配器 │  │ API适配器   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    DuckDB存储层                              │
│              高性能OLAP存储引擎                              │
└─────────────────────────────────────────────────────────────┘
```

---

## 🏗️ 核心组件

### 数据表结构

基于`DATA_DICTIONARY.md`的权威定义，V4.0架构包含以下核心业务表：

#### 期货业务表 (5个表)
1. **fut_daily_kline** - 期货日线数据
2. **fut_15min_kline** - 期货15分钟线数据  
3. **fut_5min_kline** - 期货5分钟线数据
4. **fut_main_contract_kline_15min** - 期货主力合约15分钟线
5. **fut_main_contract_info** - 期货主力合约信息

#### 股票业务表 (5个表) 
1. **stock_daily_kline** - 股票日线数据
2. **stock_15min_kline** - 股票15分钟线数据
3. **stock_5min_kline** - 股票5分钟线数据
4. **stock_basic_info** - 股票基本信息
5. **stock_trade_calendar** - 股票交易日历

#### 公共业务表 (2个表)
1. **trading_calendar** - 通用交易日历
2. **market_info** - 市场基础信息

### 统一元数据设计

每个业务表都包含统一的元数据字段：

```sql
-- 统一元数据字段设计
data_source VARCHAR,        -- 数据来源(csv/mysql/api)
import_time TIMESTAMP,      -- 数据导入时间
data_quality_score FLOAT,  -- 数据质量评分
update_frequency VARCHAR,  -- 数据更新频次
source_file_path VARCHAR,  -- 源文件路径(如适用)
```

---

## 🔄 数据流架构

### 数据采集流程

```
数据源 → 适配器 → 验证器 → 业务表映射器 → 统一业务表
  ↓        ↓        ↓           ↓            ↓
CSV文件  格式转换  质量检查   表结构映射    DuckDB存储
MySQL   字段映射  数据清洗   元数据填充    索引优化
API接口  类型转换  完整性校验 业务规则应用  查询加速
```

### 查询访问模式

```
应用查询 → 统一业务表 → 直接结果返回
    ↓         ↓           ↓
业务请求   单表查询    数据响应
条件过滤   索引加速    格式化输出
排序分页   性能优化    元数据包装
```

---

## ⚙️ 技术规格

### 存储引擎

- **DuckDB**: OLAP优化的列式存储引擎  
- **索引策略**: 基于查询模式的智能索引设计
- **压缩优化**: 自动数据压缩和存储优化
- **查询加速**: 向量化执行和并行处理

### 性能目标

| 指标 | 目标值 | 说明 |
|------|-------|------|
| 数据导入速度 | ≥20,000条/秒 | 批量导入性能 |
| 查询响应时间 | ≤100ms | 单表查询平均响应 |
| 存储压缩比 | 60%+ | 相比原始数据的压缩比 |
| 并发查询数 | 50+ | 同时支持的查询连接数 |

### 配置管理

基于`config/settings.toml`的统一配置：

```toml
[database]
engine = "duckdb"
path = "data/aqua_v4.duckdb"
memory_limit = "8GB"
threads = 8

[import]
batch_size = 5000
validation_level = "strict"
auto_create_tables = true
error_handling = "skip_and_log"

[performance]
enable_parallel_processing = true
cache_size = "2GB"
optimize_for_olap = true
```

---

## 🛡️ 数据质量保证

### 验证机制

1. **数据类型验证**: 自动检测和转换数据类型
2. **完整性检查**: 必需字段完整性验证
3. **业务规则校验**: 数据范围和逻辑一致性检查
4. **重复数据处理**: 智能去重和冲突解决

### 质量评分

每条数据记录都包含质量评分(0.0-1.0)：
- **1.0**: 完美数据，无任何质量问题
- **0.8-0.9**: 高质量数据，微小缺陷已修复
- **0.6-0.7**: 中等质量，存在可接受的缺陷
- **<0.6**: 低质量数据，需要人工审查

---

## 🔧 扩展设计

### 数据源扩展

支持插件化数据源适配器：

```python
class DataSourceAdapter:
    """数据源适配器基类"""
    
    def extract(self) -> Iterator[Dict]:
        """提取数据"""
        raise NotImplementedError
    
    def transform(self, record: Dict) -> Dict:
        """转换数据格式"""
        raise NotImplementedError
    
    def get_metadata(self) -> Dict:
        """获取数据源元数据"""
        raise NotImplementedError
```

### 业务表扩展

新业务表只需：
1. 在`DATA_DICTIONARY.md`中定义表结构
2. 创建对应的业务表映射器
3. 配置数据源适配规则

---

## 📊 监控和运维

### 健康检查

- **连接状态**: 数据库连接健康监控
- **表结构**: 业务表结构一致性检查
- **数据完整性**: 关键数据完整性验证
- **性能指标**: 查询性能和资源使用监控

### 运维工具

- **数据库初始化**: `DuckDBInitChecker`
- **健康报告**: 自动生成系统健康报告
- **性能分析**: 查询性能分析和优化建议
- **数据修复**: 自动数据修复和一致性恢复

---

## 🎯 架构优势

### 简化复杂度

- ✅ **查询简单**: 应用层直接查询业务表，无需复杂JOIN
- ✅ **维护容易**: 统一的表结构和数据管理方式
- ✅ **扩展便捷**: 新数据源只需添加适配器

### 性能优化

- ✅ **查询高效**: DuckDB OLAP优化，查询性能优秀
- ✅ **存储节约**: 列式存储和智能压缩
- ✅ **并发支持**: 支持高并发查询访问

### 数据质量

- ✅ **质量可控**: 统一的数据质量评分和管理
- ✅ **来源可追溯**: 完整的数据来源和处理历史
- ✅ **一致性保证**: 统一的数据格式和业务规则

---

## 📚 相关文档

- **数据字典**: `/docs/database/DATA_DICTIONARY.md` - 权威表结构定义
- **配置管理**: `/config/settings.toml` - 统一配置文件
- **实现脚本**: `/scripts/create_unified_tables_v4.py` - 具体实现代码
- **任务规划**: `/docs/tasks/Dev_Tasks_EPIC2_V2.md` - 重构任务计划

---

**文档维护者**: AQUA项目团队  
**最后更新**: 2025-07-28  
**架构版本**: V4.0统一业务表架构