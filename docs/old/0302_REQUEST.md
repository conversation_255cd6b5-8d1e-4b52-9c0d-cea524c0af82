# AQUA 项目开发启动前置任务详尽落地实施要求与建议（个人开发者 & AI 协作优化版）

**目标：** 在 AQUA 项目正式编码工作开始前，作为个人开发者，与 AI（如 CURSOR/GEMINI）高效协作，建立一套稳健、规范、可追溯的基础环境与自我管理体系。

-----

## 1. 核心理念与环境兼容性

  * **配置驱动**：所有参数、路径、环境变量以 `config/settings.toml` 为唯一事实来源，敏感/本地覆盖用 `.env`，并提供 `.env.example` 模板。
  * **规则先行**：所有开发、测试、变更、配置、接口、mock、任务流等，必须对齐权威文档（蓝图/PRD/功能定义）和 `.rules` 规则，发现冲突时以权威文档为准。
  * **自动化**：所有依赖、配置、环境、数据库结构、测试等初始化和校验，必须通过 `scripts/env_init.py` 和启动脚本自动完成，禁止手动跳过。
  * **跨平台**：所有工具、脚本、依赖、配置、路径、换行符等，必须兼容 Win11/OSX，禁止硬编码。
  * **前后端一体化**：前后端接口、字段、类型、枚举等严格对齐，接口契约唯一来源为 `02_FUNCTIONS.md`。
  * **MVP 演进**：以最小可行产品为起点，分阶段推进，所有变更、任务、接口、mock、测试等如有调整，必须同步更新 `logs/dev_log.md` 和 `Dev_Tasks.md`，保证全流程可追溯。

-----

## 2. 个人任务管理与 AI 协作流建立

  * **2.1 核心要求：** 任务管理重点在于个人思考的结构化、AI 协作的可控性与可追溯性。所有任务（包括 AI 协助产生的子任务）仍需遵循"主线（Epic）-支线（Feature）-子任务（Task）-原子化任务（Sub-task）"四级分层，并统一登记于 `docs/tasks/Dev_Tasks.md` 文件中。AI 生成的代码或任务，必须通过本地校验。

  * **2.2 落地实施建议：**

      * **角色定位与责任：**

          * **您（个人开发者）是核心决策者和主导者：** 负责项目的整体规划、Epic 和 Feature 的确定、关键技术选型、复杂问题的最终决策、以及对 AI 输出的最终审查与整合。
          * **AI (CURSOR/GEMINI) 是您的智能助手：** 负责根据您的结构化提示词进行代码生成、文档撰写、问题分析、测试用例编写等原子化任务的执行。AI 的每一次生成、编辑或提交，都必须服从您预设的规则和审查。

      * **思维结构化与任务分解（核心环节）：**

          * **深入理解权威文档：** 再次强调深入研读《项目蓝图》、《产品需求文档》、《功能模块定义》及所有 `.rules` 规则文件。这是您与 AI 协作的基础，您需要将这些规则内化，并以此指导 AI。
          * **Epic & Feature 定义：** 由您主导，根据项目蓝图和产品需求，首先确定项目的各个主线（Epic）和支线（Feature）。这些是您高层次的目标。
          * **Task 与 Sub-task 细化（重点 AI 协作区）：**
              * **人工引导，AI 辅助细化：** 对于每个 Feature，您作为开发者，给出初步的 Task 构想。然后，利用 AI（通过结构化提示词）来协助您进一步拆解为更细粒度的子任务（Task）和原子化任务（Sub-task）。例如，您可以提示 AI："请根据用户认证 Feature，列出其后端和前端的所有原子化开发任务，包括数据模型、API、UI 组件等。"
              * **AI 生成任务元数据：** 指导 AI 在生成任务时，尽可能包含任务名、父任务、建议负责人（默认为您自己）、建议工时、状态、描述、ai_generated、prompt_id、ai_version等元数据。
              * **人工审查与调整：** AI 提炼出的任务清单，您必须仔细审查，确保其符合您的理解和项目实际，并对工时、依赖关系等进行最终调整。

      * **`docs/tasks/Dev_Tasks.md` 文件初始化与维护：**

          * **唯一源与结构化：** 确保所有任务，无论是您手动添加还是 AI 辅助生成，都统一录入到 `docs/tasks/Dev_Tasks.md`。严格遵循手册规定的多级标题和元数据格式。
          * **AI 任务关联与提示词库：** 如果某个原子化任务完全由 AI 生成或主要由 AI 完成，可以在任务描述中明确注明，记录所使用的结构化提示词的 ID 或版本。建议在 `docs/ai_prompts/` 下维护结构化提示词库，每次AI协作引用具体版本，便于复现和持续优化AI行为。
          * **状态流转与 `commit ID` 关联：** 严格遵循任务状态流转，并在每次代码提交时关联原子化任务 ID。即使是 AI 提交的代码，也应通过您的 Git 账户提交，并在 commit message 中包含任务 ID。
          * **任务优先级与依赖管理**：在任务元数据中增加`priority`、`depends_on`字段，结合可视化工具（如mermaid.js）自动生成任务流图，便于全局把控。

      * **日志与变更追踪 (`logs/dev_log.md`)：**

          * **个人开发日志：** 将 `logs/dev_log.md` 视为您的开发日记。每次重要的开发、修复、优化、测试或配置变更，都应结构化记录在此，并引用对应的任务 ID。
          * **AI 协作日志自动化：** 明确记录您与 AI 之间重要的交互，AI生成任务、代码、文档等均需结构化自查报告，自动归档到日志，便于后续追溯和知识沉淀。
          * **日志格式规范：** 严格对齐 `.rules` 和蓝图要求的日志格式、命名和内容。

      * **AI 协作合规性（重中之重）：**

          * **结构化提示词与AI自查：** 维护结构化提示词模板库，AI生成内容需结构化自查（如命名、类型、依赖、敏感信息、接口对齐等），人工快速确认。
          * **AI输出审查与三重保障：**
              * **AI自查报告**：AI生成代码后自动输出结构化自查报告。
              * **人工快速审查**：开发者重点关注AI报告中的高风险项。
              * **自动化校验**：pre-commit hook自动执行格式、类型、依赖、敏感信息等检查，未通过禁止提交。
          * **敏捷与安全兼顾**：原型/探索性代码可允许"跳过部分校验"或"仅警告不阻断"，但需在commit message中注明"实验/待完善"；关键功能、对外接口、数据处理等必须强制通过全部校验。
          * **AI代码审查流程自动化**：AI审查prompt模板自动生成结构化审查清单，审查结果自动归档到logs/dev_log.md。

-----

## 3. 环境与依赖管理的极致自动化与高效自检

  * **3.1 现状与能力：**
      * `scripts/env_init.py` 已支持一键检测/安装UV、唯一激活.venv、依赖多轮修复、pip自愈、结构化日志、兼容Win11/OSX、`--check-only`参数等。
      * 自动检测并清理多余虚拟环境，自动读取`config/settings.toml`，依赖编译/同步均有重试和详细输出。
      * 检查虚拟环境激活状态，支持自举到.venv。
  * **3.2 优化建议：**
      * **前端依赖集成**：建议在env_init.py中增加前端pnpm依赖检测与同步，或在`scripts/check_all.sh`中统一自检。
      * **数据库结构/配置完整性检测**：增加对数据库文件、表结构、settings.toml关键字段的检测。
      * **依赖变更自动提醒**：依赖变更时自动输出提醒，建议开发者同步更新requirements.txt、package.json等。
      * **一键全栈自检脚本**：增加`scripts/check_all.sh`，串联env_init.py、前端依赖、数据库、端口等自检，结构化输出。
      * **自定义扩展点**：预留自定义检测/初始化脚本钩子，便于灵活扩展。
  * **3.3 敏捷与准确的平衡：**
      * 保留极致自动化能力，但允许"跳过/仅检测/自定义"，以适应个人开发者的灵活需求。
      * 检测结果结构化输出，便于AI/脚本自动分析和归档。
      * 依赖变更、环境异常自动提醒开发者同步文档和日志，形成闭环。

-----

## 4. Mock与测试数据、API契约的智能管理

  * **Mock数据自动同步脚本**：开发脚本自动比对`frontend/tests/mock/`与后端API契约（02_FUNCTIONS.md），发现不一致自动生成/提醒，确保前后端mock数据结构永远一致。
  * **测试用例与任务联动**：在`Dev_Tasks.md`中为每个功能/接口自动生成测试用例清单（AI辅助），并与实际测试代码、覆盖率报告自动关联。

-----

## 5. 变更与知识管理的闭环

  * **自动归档机制**：每次遇到新问题、踩坑、优化经验，AI/脚本自动提取关键内容，归档到`docs/faq.md`或`docs/knowledge_base.md`，内容结构化（问题、场景、解决方案、相关任务ID/commit ID、日期），便于后续检索和复用。
  * **知识库与变更日志联动**：变更日志（logs/dev_log.md）中如有"经验/FAQ"标签，自动同步到知识库。AI/脚本定期扫描日志和任务，自动生成知识库草稿，开发者补充细节。
  * **高效检索与复用**：FAQ/知识库支持关键词检索、标签分类，结合AI问答接口，支持自然语言检索知识库内容。

-----

## 6. 创新实践建议（可选/附录）

  * **AI协作元数据标准化**：所有AI生成任务、代码、文档，均需记录`ai_generated: true`、`prompt_id`、`ai_version`等元数据，便于溯源和质量追踪。
  * **AI提示词库版本管理**：在`docs/ai_prompts/`下维护结构化提示词库，每次AI协作引用具体版本，便于复现和持续优化AI行为。
  * **AI协作日志自动化**：开发脚本自动从`Dev_Tasks.md`和`logs/dev_log.md`中提取AI相关任务、日志，生成AI协作报告，便于定期复盘AI贡献与风险点。
  * **每日/每周小结机制**：在`logs/dev_log.md`中，建议每周/每日自动生成小结（可由AI辅助），总结进展、问题、下周计划，提升自我驱动力和复盘效率。
  * **一键全栈环境自检脚本**：在`scripts/`下增加`check_all.sh`，一键检测后端虚拟环境、前端依赖、配置文件、数据库结构、端口占用等，输出结构化报告，便于每日开发前自检。
  * **AI代码审查双重机制**：每次AI生成的关键代码，先由AI自查（结构化输出自查报告），再由开发者人工审查，最后通过Git Hooks自动化校验，三重保障。
  * **敏感信息与安全扫描自动化**：在pre-commit hook中集成敏感信息扫描（如detect-secrets）、依赖漏洞扫描（如safety、npm audit），AI生成代码也必须通过。
  * **Mock数据与API契约自动同步**：自动比对前端mock与后端API契约，发现不一致自动生成/提醒，确保mock数据结构永远一致。
  * **知识沉淀机制**：每次遇到新问题、踩坑、优化经验，自动归档到FAQ/知识库，AI辅助初稿，开发者补充。
  * **变更影响分析**：每次关键变更（如接口、数据结构），AI自动分析影响范围，生成影响报告，提醒开发者同步更新相关文档、mock、测试。
  * **所有自动化流程允许人工干预和灵活配置，确保个人开发者高效、准确、无负担地推进项目。**

-----

## 7. 模拟数据与实际数据在测试中的策略

- **前端：**
  - 开发、UI调试、单元测试：大量使用模拟数据（如MSW、硬编码）。
  - 集成测试、端到端测试：逐步引入实际数据（通过Mock Server模拟真实API响应，或通过E2E测试连接真实后端和测试数据库）。
- **后端：**
  - 单元测试：强制使用Mock/模拟数据。
  - 集成测试：必须使用测试数据库提供的实际数据。
  - 端到端测试：使用真实外部服务或高度模拟的外部服务。
- **Mock与API契约自动同步**，测试用例与任务流自动联动，所有变更需同步更新相关文档和日志，确保测试数据与业务逻辑、接口契约始终一致.

-----

> 本文档为AQUA项目开发启动前置任务的权威操作指引，所有开发、测试、变更、配置、接口、mock、任务流、AI协作等，必须严格遵循本手册及权威文档、规则体系，确保项目高效、规范、可追溯推进.