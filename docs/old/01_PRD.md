# 【已归档，仅作历史参考】
# 本文件已迁移至AQUA_GUIDE.md，唯一权威源为AQUA_GUIDE.md。

# AQUA 产品需求文档

---

## 1. 产品愿景与目标 (Product Vision & Goals)

**愿景：** 成为个人量化开发者和专业投资者信赖的一站式期货与A股数据处理、策略研究与交易模拟平台。

**目标：**
* **数据整合与高质量提供：** 实现多源（CSV、未来API）数据的高效、自动化采集、清洗、复权与永久化存储，提供准确、完整的期货与A股历史数据。
* **量化研究赋能：** 提供直观、灵活的策略构建、回测分析工具，支持用户快速验证投资理念。
* **模拟交易与风险管理：** 提供仿真实时交易环境，帮助用户熟悉市场，验证策略在真实环境下的表现。
* **AI 辅助洞察：** 利用人工智能技术，提供数据洞察、异常发现、策略优化建议等增值服务。
* **易用性与零运维：** 简化部署与日常维护，降低用户使用门槛，提供配置驱动的自动化解决方案。
* **技术栈先进性与可扩展性：** 采用现代化技术栈，确保系统高性能、高内聚、低耦合，易于未来功能扩展和迭代。

---

## 2. 目标用户与核心价值 (Target Users & Core Value Proposition)

### 2.1 目标用户 (Target Users)
* **个人量化开发者：** 追求高效、自动化数据处理流程，需要稳定、高质量的本地数据源进行策略开发与回测。
* **策略研究员：** 需要便捷的工具进行策略构建、因子分析、回测性能评估，以便快速迭代投资理念。
* **专业投资者：** 希望通过系统化工具，辅助其进行市场分析、交易决策和风险控制。

### 2.2 核心价值 (Core Value Proposition)
* **数据驱动决策：** 提供清洗、复权后的高质量数据，支持数据驱动的投资分析和决策。
* **效率提升：** 自动化数据处理流程，缩短从数据到策略验证的时间。
* **智能辅助：** 引入 AI 技术，提供更深层次的市场洞察和策略优化建议。
* **本地化与私密性：** 数据本地存储，确保用户数据安全与隐私。
* **定制化与灵活性：** 提供可配置的模块和开放的接口，满足不同用户的定制化需求。

---

## 3. 功能需求 (Functional Requirements)

本文档旨在详细阐述 AQUA 系统的主要功能需求。本系统将涵盖数据管理、策略回测、模拟交易及AI辅助洞察等核心功能，旨在提升量化研究效率和决策质量。

### 3.1 数据仓库 (Data Warehouse)
* **数据管理：**
    * **数据导入：** 支持 CSV 等多种格式文件的批量导入，包括自动目录扫描和文件名智能解析。需支持期货和A股数据导入。
    * **数据库浏览：** 提供直观的界面，浏览和查询本地 DuckDB 数据库中的所有表和数据内容。
    * **数据清洗与处理：** 自动执行数据清洗、去重、复权（针对期货主力合约换月和A股除权）、主力合约判别、缺失值处理等。
    * **数据库管理：** 基本的数据库结构、表、索引管理（非面向最终用户，但需通过后台功能支持）。
* **数据浏览与分析：**
    * **中国A股数据：** 浏览和分析中国A股市场的日K线、分钟K线、财报、指标等数据。
    * **中国商品期货数据：** 浏览和分析中国商品期货市场的日K线、分钟K线、持仓、成交量等数据。
* **特色数据：**
    * **行业数据：** 行业分类、行业指数、行业相关数据展示。
    * **宏观数据：** 宏观经济指标、政策、统计数据等展示。
    * **新闻爬虫：** 自动采集财经新闻、公告、资讯等（未来扩展）。

### 3.2 策略抽屉 (Strategy Drawer)
* **策略管理：** 创建、编辑、保存、加载、删除、分类管理交易策略。
* **策略参数配置：** 提供界面配置策略参数，支持参数的校验、导入导出。
* **因子管理：**
    * **A股因子：** 管理 A 股市场相关的因子库（如估值、成长、情绪等）。
    * **商品期货因子：** 管理商品期货市场相关的因子库（如基差、持仓、季节性等）。

### 3.3 回测车间 (Backtest Workshop)
* **回测任务管理：**
    * **新建回测：** 配置回测参数（品种、周期、时间范围）、选择策略、启动回测任务。
    * **回测浏览：** 浏览历史回测任务及其状态、进度。
* **回测结果分析：**
    * **绩效报表：** 展示关键绩效指标，如累计收益率、年化收益率、最大回撤、夏普比率、索提诺比率等。
    * **收益曲线：** 绘制直观的收益走势图。
    * **交易明细：** 展示回测期间的逐笔成交记录、持仓变化明细。

### 3.4 模拟交易 (Simulator)
* **交易计划管理：** 创建、修改、查看模拟交易计划。
* **实时模拟：** 根据交易计划和市场数据，模拟订单执行和资金变动。
* **模拟交易报告：** 提供模拟交易的绩效概览和交易明细。

### 3.5 AI 驱动洞察 (AI-Driven Insight)
* **数据智能查询：** 支持自然语言查询数据和指标。
* **异常检测与预警：** 自动识别数据异常、市场异动或策略潜在问题。
* **智能报告生成：** 自动生成市场分析报告或策略表现总结。
* **策略优化建议：** 基于 AI 分析，提供策略参数或规则的优化建议。

### 3.6 前端 MVP (Frontend MVP) - 核心用户界面与功能入口
前端 MVP 作为一个独立的功能模块，是 AQUA 系统的主要用户界面入口。它将提供一个直观的主页，用于导航到各个核心功能模块，并在此阶段实现数据管理、基础回测结果展示和 AI 交互的核心视图与操作。本 MVP 阶段旨在构建一个符合新蓝图的前端应用基础，并为后续功能迭代提供可消费后端数据且具备基本用户体验的平台。

**做什么 (What it does)：**

* **主页与基础导航：**
    * 提供系统主页，作为用户进入各功能模块的入口。
    * 实现核心功能的导航，用户可以从主页快速访问数据中心、回测结果查看、AI 辅助等模块。
* **数据中心视图 (`DataHub`)：**
    * 提供数据导入功能，允许用户上传本地 CSV 文件，并将数据导入到后端数据库。
    * 提供数据库内容浏览功能，用户可以查看后端数据库中的表列表和指定表的详细数据内容。
* **回测结果视图 (`BacktestResult`)：**
    * 展示回测任务的关键绩效指标，如总收益、最大回撤、夏普比率等数值型数据。
    * 以图表形式（如收益曲线）直观展示回测结果的走势。
* **AI 辅助交互视图 (`AIAgent`)：**
    * 提供与后端 AI 辅助服务进行自然语言交互的界面，用户可以输入问题或指令。
    * 展示 AI 返回的数据查询结果或规则校验报告。
* **技术框架与规范对齐：**
    * 搭建基于 Vue 3 + TypeScript 的前端应用框架。
    * 集成 Naive UI 组件库，确保界面美观性和开发效率。
    * 配置 Pinia 进行全局状态管理。
    * 确保前端代码结构、开发规范严格遵循项目蓝图和统一规则。

---

## 4. 非功能性需求 (Non-Functional Requirements)

本节概述系统的非功能性要求。具体量化指标将在后续优化阶段细化。

### 4.1 性能 (Performance)
* **数据导入速度：** 确保大数据量（例如10年历史期货数据）能够高效导入和处理。
* **数据查询响应：** 核心数据查询应具备快速响应能力。
* **回测速度：** 回测复杂策略和大数据集时，应在合理时间内完成。

### 4.2 可用性 (Usability)
* **用户界面：** 提供直观、易用的图形用户界面（GUI）。
* **用户体验：** 流程清晰，操作反馈及时。
* **错误处理：** 友好的错误提示和日志记录，便于问题定位。

### 4.3 可扩展性 (Scalability)
* **模块化设计：** 核心功能模块化，便于独立开发和未来功能扩展。
* **数据容量：** 支持未来更大的数据存储需求。
* **技术栈支持：** 选择主流且具备良好社区支持的技术栈。

### 4.4 可维护性 (Maintainability)
* **代码规范：** 严格遵循统一的编码规范和文档标准。
* **自动化测试：** 提供全面的自动化测试覆盖，确保代码质量和回归。
* **日志与监控：** 完善的日志系统和（未来）监控机制。

### 4.5 安全性 (Security)
* **数据隐私：** 用户本地数据存储，保障数据私密性。
* **配置安全：** 敏感配置（如API Key）应妥善管理，避免硬编码。
* **访问控制：** （如果涉及多用户或云部署）需考虑适当的访问权限管理。

### 4.6 兼容性 (Compatibility)
* **操作系统：** 兼容 Windows 11 和 macOS。
* **Python 版本：** 兼容 Python 3.11+。

---

## 5. 产品发布路线图 (Product Roadmap)

本路线图将严格按照"先正其心，再利其器，终成其事"的核心理念，逐步推进项目开发。前端与后端并行发展，共同构成最小可用系统。

### 阶段 0: 万象归一 (The Great Reset) - 预计用时：1-2天
**核心目标**: 彻底清除所有历史包袱和冲突，建立一个清晰、统一、无歧义的全新起点。
* **0.1: 归档所有旧文档：** 将所有旧的规划文档和规则文档物理隔离，消除信息冲突的源头。
* **0.2: 创立新规则总纲：** 创建新的、唯一的规则文件，作为新体系的容器和未来所有规则的"家"。
* **0.3: 提炼并写入通用原则与模板：** 将旧规则中的普适性原则和模板整合到新总纲中，完成规则体系的基础构建。

### 阶段 1: 规则体系重构与优化 (Rule System Restructuring and Optimization)
**核心目标**: 完善规则体系，整合前端、后端和AI规则执行机制，简化并确保规则的高效执行。
* **1.1: 重新设计规则体系结构：** 统一规则文件格式，确保各类规则（前端、后端、AI）能够协同工作。
* **1.2: 自动化规则执行机制：** 实现规则的自动化执行，减少人工干预，确保一致性。
* **1.3: AI规则协议与接口设计：** 设计AI与项目规则之间的协议，确保AI能理解并执行规则。

### 阶段 2: MVP开发与交付 (MVP Development and Delivery)
**核心目标**: 按照先行开发 MVP 的方式，逐步实现项目的核心功能，逐步推出子 MVP。本阶段将优先启动前端 MVP 的基础框架建设和核心页面开发，并与后端核心服务 MVP 同步推进，以尽快构建用户可见的系统原型。

* **2.1: 前端 MVP 基础与主页构建：**
    * **目标：** 搭建前端应用的基础框架、主页和核心导航，确保能够作为一个独立的、可访问的 UI 入口。
    * **关键任务：** 初始化 Vue 3 + TS 项目，配置 Naive UI, Pinia, Vue Router，并开发主页、统一模块注册中心以及基础的路由导航。
    * **与后端依赖：** 此阶段主要侧重前端框架和导航，对后端功能依赖较低，可先行启动。
* **2.2: 定义核心功能与子 MVP (后端)：**
    * **目标：** 明确后端核心服务（数据、回测、AI）的 MVP 范围和 API 契约，为前端提供可消费的接口。
    * **关键任务：**
        * **后端子 MVP 1: 核心数据服务 (Core Data Service):** 建立 DuckDB 数据存储，实现 CSV 数据导入与基本数据查询 API。
        * **后端子 MVP 2: 基础回测服务 (Basic Backtesting Service):** 构建历史数据加载与简单回测逻辑，提供回测任务提交与结果查询 API。
        * **后端子 MVP 3: AI 辅助服务 (AI Agent Service):** 集成 AI 能力，提供自然语言数据查询与规则校验 API。
    * **与前端依赖：** 此阶段定义的后端 API 是前端 MVP 后续功能实现的直接依赖。
* **2.3: 前端 MVP 核心功能模块集成与可视化：**
    * **目标：** 在前端主页的基础上，集成并实现数据中心、基础回测结果展示和 AI 辅助交互的核心视图与操作。
    * **关键任务：** 开发 `DataHub` 视图（文件上传、数据库浏览）、`BacktestResult` 视图（绩效报表、收益曲线）、`AIAgent` 视图（自然语言交互），并调用后端 2.2 定义的对应 API。
    * **与后端依赖：** 强依赖后端核心数据服务、基础回测服务和 AI 辅助服务提供的 API。
* **2.4: 分步开发与测试：** 根据MVP定义分阶段开发与测试。每个子MVP要有清晰的开发周期、验收标准与测试计划。

### 阶段 3: 后期优化与维护 (Post-Optimization and Maintenance)
**核心目标**: 在 MVP 交付后进行性能优化、bug 修复、代码清理等工作，确保系统高效且稳定运行。
* **3.1: 性能优化与扩展性评估：** 根据 MVP 的性能评估，进行性能优化，确保系统支持更大规模的用户和数据量。
* **3.2: 代码清理与重构：** 对代码库进行清理，重构不规范代码，提升代码可读性与可维护性。

### 其他补充与优化建议
1.  **持续集成与自动化测试：** 为了提升开发效率和代码质量，建议在每个阶段的开发过程中，增加自动化测试和持续集成工具的支持，确保每次提交的代码都经过严格测试和验证。
2.  **定期回顾与调整：** 定期对项目进展进行回顾，评估每个阶段的任务完成情况和目标实现情况，及时调整重构计划和开发节奏。

---

## 6. 文档修订历史 (Document Revision History)

| 版本 | 日期 | 修订说明 |
| :--- | :--- | :--- |
| v1.0 | 2025-06-28 | 初始版本，根据用户反馈和现有文档整合生成。 |
| v1.1 | 2025-06-28 | 整合 `MVP_DEFINITION.md` 和 `ACTION_PLAN.md` 内容，明确前端MVP功能，并严格对齐蓝图和脚手架。 |
| v1.2 | 2025-06-28 | 重新审视前端 MVP 定义，明确其作为主页入口；调整路线图，优先或并行推进前端 MVP 基础建设；细化功能需求描述，更侧重"做什么"；确保整体一致性与专业性。 |

## 顶层目录结构总览

> 目录结构唯一权威源为[AQUA_GUIDE.md](../AQUA_GUIDE.md)，本分区仅作补充或历史参考。如有冲突，以AQUA_GUIDE.md为准。

```text
AQUA_V1.2/
├── config/                 # 配置文件目录（全局配置、环境变量、多环境配置）
│   └── env/                # 多环境配置子目录（如dev、prod等）
├── data/                   # 数据存储目录（DuckDB文件、原始CSV、备份等）
│   ├── backup/             # 数据库及重要数据备份
│   ├── processed/          # 处理后数据（如清洗、复权结果）
│   └── raw/                # 原始数据（如CSV、外部采集文件）
├── docs/                   # 项目文档（蓝图、PRD、API、任务、数据字典等）
│   ├── ai_prompts/         # AI协作提示词库
│   ├── old/                # 历史归档文档，仅作参考
│   └── tasks/              # 任务流唯一登记源
├── frontend/               # 前端主目录（Vue3+TS）
│   ├── public/             # 前端静态资源
│   ├── src/                # 前端源代码（模块、组件、API、状态管理等）
│   │   ├── api/            # 后端API接口封装
│   │   ├── assets/         # 静态资源（图片、样式等）
│   │   ├── components/     # 通用UI组件
│   │   ├── modules/        # 业务页面模块
│   │   ├── router/         # 路由配置
│   │   └── stores/         # 状态管理（Pinia）
│   └── tests/              # 前端测试与mock数据
│       ├── mock/           # 前端mock/测试数据，结构与后端接口严格对齐
│       └── unit/           # 前端单元测试
├── logs/                   # 日志目录（结构化变更日志、测试报告、运行日志）
├── scripts/                # 自动化脚本（环境初始化、备份、恢复、监控、Git钩子等）
│   └── git_hooks/          # Git钩子脚本（如commit message校验）
├── src/                    # 后端主目录（核心、模块、接口、工具、定时任务、入口）
│   ├── core/               # 框架核心、全局服务、依赖注入
│   ├── interfaces/         # 公共接口与数据结构定义
│   ├── jobs/               # 定时任务、调度脚本
│   ├── modules/            # 业务功能模块
│   └── utils/              # 通用工具（logger、exceptions、time_utils等）
├── tests/                  # 后端测试用例
│   ├── e2e/                # 端到端测试
│   ├── frontend/           # 前端相关测试
│   │   ├── e2e/            # 前端端到端测试
│   │   ├── integration/    # 前端集成测试
│   │   ├── mock/           # 前端mock数据
│   │   └── unit/           # 前端单元测试
│   ├── integration/        # 后端集成测试
│   └── unit/               # 后端单元测试
├── chating.md              # AI/协作相关记录
├── eslint.config.mjs       # 前端ESLint配置
├── main.py                 # 后端主入口（如有）
├── package.json            # 前端依赖清单
├── pnpm-lock.yaml          # 前端依赖锁定文件
├── pnpm-workspace.yaml     # pnpm多包管理配置
├── pytest.ini              # 后端pytest配置
├── README.md               # 项目说明文档（快速参考，权威源为AQUA_GUIDE.md）
├── requirements.in         # Python依赖源文件
├── requirements.txt        # Python依赖清单
├── start_backend.bat       # 后端启动脚本（Windows）
├── start_backend.sh        # 后端启动脚本（Linux/Mac）
├── start_frontend.bat      # 前端启动脚本（Windows）
├── start_frontend.sh       # 前端启动脚本（Linux/Mac）
```

> 目录结构变更须同步AQUA_GUIDE.md，发现不一致时以AQUA_GUIDE.md为准。