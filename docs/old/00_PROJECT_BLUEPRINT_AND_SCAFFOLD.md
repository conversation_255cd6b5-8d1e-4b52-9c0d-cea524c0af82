# 【已归档，仅作历史参考】
# 本文件已迁移至AQUA_GUIDE.md，唯一权威源为AQUA_GUIDE.md。

# AQUA 顶层蓝图与标准脚手架（The Project Blueprint & Standard Scaffold）

---

## 1. 项目定位与核心理念 (Project Positioning & Core Philosophy)

AQUA 致力于为个人开发者与专业投资者提供高效、可扩展、易用的期货与A股数据平台。我们的核心理念是：

* **配置驱动 (Configuration-Driven)**：所有可变参数（路径、环境、API Key等）均通过集中配置文件管理。
* **规则先行 (Rules-First)**：所有开发活动严格遵循统一、明确的规则体系。
* **自动化 (Automation)**：最大限度地利用自动化脚本和工具，减少人工干预。
* **跨平台 (Cross-Platform)**：兼容主流操作系统（Win11/OSX）。
* **前后端一体化 (Full-Stack Integrated)**：前端与后端协同设计，共同构成完整系统。
* **最小可行产品 (MVP) 演进**：以最小可行产品为起点，逐步迭代和演进。

---

## 2. 技术架构与核心技术栈 (Technical Architecture & Core Stack)

AQUA 项目采用前后端分离的架构，确保模块化和可扩展性。

### 2.1 技术栈概览 (Technology Stack Overview)

* **后端 (Backend)**：
    * **语言与框架**：Python 3.11+，FastAPI (用于构建高性能 API 服务)。
    * **数据存储**：DuckDB (用于本地高性能 OLAP 数据库)。
    * **数据处理**：Polars (用于高效数据清洗、转换和分析)。
    * **架构模式**：分层架构（API/服务/DAO/工具），确保高内聚低耦合。
* **前端 (Frontend)**：
    * **框架与语言**：Vue 3 + Vite + TypeScript (现代化前端开发)。
    * **组件库**：Naive UI (提供丰富且高性能的 UI 组件)。
    * **状态管理**：Pinia (轻量级且直观的状态管理)。
    * **数据可视化**：ECharts (用于绘制复杂图表，如回测收益曲线)。
    * **架构模式**：模块注册机制，MVP 阶段聚焦 DataHub，API 契约自动生成。
* **数据流 (Data Flow)**：
    * CSV → (数据清洗/复权/主力判别等处理) → DuckDB (持久化存储) → FastAPI (API 接口) → 前端展示。
* **环境与依赖 (Environment & Dependencies)**：
    * UV 虚拟环境：轻量级且高效的 Python 虚拟环境管理。
    * `requirements.txt`：Python 依赖清单。
    * 自动化脚本：环境初始化、数据迁移、备份等。
    * 所有依赖、配置、数据路径均由 `config/settings.toml` 集中管理。
    * 当前以本地PC部署为主，未来支持容器化（如Docker）、云部署（如阿里云、AWS）、自动化CI/CD。

---

## 顶层目录结构总览

> 目录结构唯一权威源为[AQUA_GUIDE.md](../AQUA_GUIDE.md)，本分区仅作补充或历史参考。如有冲突，以AQUA_GUIDE.md为准。

```text
AQUA_V1.2/
├── config/                 # 配置文件目录（全局配置、环境变量、多环境配置）
│   └── env/                # 多环境配置子目录（如dev、prod等）
├── data/                   # 数据存储目录（DuckDB文件、原始CSV、备份等）
│   ├── backup/             # 数据库及重要数据备份
│   ├── processed/          # 处理后数据（如清洗、复权结果）
│   └── raw/                # 原始数据（如CSV、外部采集文件）
├── docs/                   # 项目文档（蓝图、PRD、API、任务、数据字典等）
│   ├── ai_prompts/         # AI协作提示词库
│   ├── old/                # 历史归档文档，仅作参考
│   └── tasks/              # 任务流唯一登记源
├── frontend/               # 前端主目录（Vue3+TS）
│   ├── public/             # 前端静态资源
│   ├── src/                # 前端源代码（模块、组件、API、状态管理等）
│   │   ├── api/            # 后端API接口封装
│   │   ├── assets/         # 静态资源（图片、样式等）
│   │   ├── components/     # 通用UI组件
│   │   ├── modules/        # 业务页面模块
│   │   ├── router/         # 路由配置
│   │   └── stores/         # 状态管理（Pinia）
│   └── tests/              # 前端测试与mock数据
│       ├── mock/           # 前端mock/测试数据，结构与后端接口严格对齐
│       └── unit/           # 前端单元测试
├── logs/                   # 日志目录（结构化变更日志、测试报告、运行日志）
├── scripts/                # 自动化脚本（环境初始化、备份、恢复、监控、Git钩子等）
│   └── git_hooks/          # Git钩子脚本（如commit message校验）
├── src/                    # 后端主目录（核心、模块、接口、工具、定时任务、入口）
│   ├── core/               # 框架核心、全局服务、依赖注入
│   ├── interfaces/         # 公共接口与数据结构定义
│   ├── jobs/               # 定时任务、调度脚本
│   ├── modules/            # 业务功能模块
│   └── utils/              # 通用工具（logger、exceptions、time_utils等）
├── tests/                  # 后端测试用例
│   ├── e2e/                # 端到端测试
│   ├── frontend/           # 前端相关测试
│   │   ├── e2e/            # 前端端到端测试
│   │   ├── integration/    # 前端集成测试
│   │   ├── mock/           # 前端mock数据
│   │   └── unit/           # 前端单元测试
│   ├── integration/        # 后端集成测试
│   └── unit/               # 后端单元测试
├── chating.md              # AI/协作相关记录
├── eslint.config.mjs       # 前端ESLint配置
├── main.py                 # 后端主入口（如有）
├── package.json            # 前端依赖清单
├── pnpm-lock.yaml          # 前端依赖锁定文件
├── pnpm-workspace.yaml     # pnpm多包管理配置
├── pytest.ini              # 后端pytest配置
├── README.md               # 项目说明文档（快速参考，权威源为AQUA_GUIDE.md）
├── requirements.in         # Python依赖源文件
├── requirements.txt        # Python依赖清单
├── start_backend.bat       # 后端启动脚本（Windows）
├── start_backend.sh        # 后端启动脚本（Linux/Mac）
├── start_frontend.bat      # 前端启动脚本（Windows）
├── start_frontend.sh       # 前端启动脚本（Linux/Mac）
```

> 目录结构变更须同步AQUA_GUIDE.md，发现不一致时以AQUA_GUIDE.md为准。

---

## 3. 标准目录结构 (Standard Directory Structure)

项目采用如下精简且权威的顶层目录结构，确保高可读性、可维护性、可追溯性，支撑个人开发、自动化、任务流与变更闭环。

```
AQUA_V1.2/
├── src/                        # 后端业务代码（Python, FastAPI, DuckDB等）
│   ├── core/                   # 框架核心、全局服务、依赖注入
│   ├── modules/                # 可插拔功能模块（如data_hub、strategy_manager等）
│   ├── interfaces/             # 公共接口定义
│   └── utils/                  # 通用工具函数
├── frontend/                   # 前端工程（Vue3+TS）
│   ├── src/
│   │   ├── modules/            # 功能模块（如DataHub、BacktestResult）
│   │   ├── components/         # 通用组件
│   │   ├── stores/             # Pinia状态管理
│   │   ├── api/                # 与后端接口交互
│   │   └── mock/               # 前端mock/测试数据，结构与真实接口一致
│   ├── tests/
│   └── dist/                   # 构建后的静态文件
├── scripts/                    # 系统初始化、数据迁移、备份等脚本
│   └── env_init.py             # 环境一键初始化脚本
├── config/                     # 配置文件
│   ├── settings.toml           # 项目主配置（唯一事实源，非敏感）
│   ├── .env.example            # 环境变量模板（敏感/本地，实际.env不提交）
│   └── env/                    # 环境配置子目录
├── docs/                       # 项目文档（权威蓝图、PRD、API、任务、数据字典等）
│   ├── 00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md   # 顶层蓝图与标准脚手架
│   ├── 01_PRD.md                              # 产品需求文档
│   ├── 02_FUNCTIONS.md                        # 功能模块与API契约
│   ├── 0301_BASE.md                           # 实施操作手册
│   ├── api.md                                 # API文档（接口定义、示例、自动生成/同步）
│   ├── data_dictionary.md                     # 数据字典（表、字段、类型、约束、接口字段对齐）
│   └── tasks/
│       └── Dev_Tasks.md                       # 任务流唯一登记源（主线-支线-子任务-原子化任务，结构化元数据）
├── logs/                        # 日志目录
│   ├── dev_log.md               # 结构化变更日志（所有变更、任务、接口、mock、测试等同步登记）
│   └── app_{YYYYMMDD}.log       # 结构化应用日志（自动轮转，含时间戳、级别、模块、上下文）
├── tests/                       # 后端测试用例
├── requirements.txt             # Python依赖清单
├── .nvmrc                       # Node.js版本锁定（前端）
├── .gitignore                   # Git忽略文件
└── README.md                    # 项目说明文档
```

- 说明：
  - 所有配置、依赖、环境变量、mock、任务、日志、数据字典、API文档等均分层明确，唯一事实源清晰。
  - 任务流、变更、mock、接口、测试等均需结构化、可追溯，所有变更同步 logs/dev_log.md 和 docs/tasks/Dev_Tasks.md。
  - 目录结构如有调整，须同步更新本蓝图、logs/dev_log.md、Dev_Tasks.md。

---

## 4. 环境与依赖管理 (Environment & Dependency Management)

**开发环境初始化**：
* 使用 `scripts/env_init.py` 脚本提供一键环境初始化功能，包括 UV 虚拟环境创建、依赖安装、配置检测、数据库初始化等，确保开发环境的一致性。

---

## 5. 规则体系与开发流程 (Rule System & Development Flow)

* **规则唯一来源**：所有项目规则统一存放于 `.cursor/rules/` 目录下，确保无冲突、无冗余、无歧义。
* **规则分层与优先级**：
    1. `development_standards.mdc` (开发/编码/结构/通用标准)：最高优先级，适用于所有项目文件和所有阶段。
    2. `project_management_standards.mdc` (项目管理/任务/文档/日志/模板)：次高优先级，适用于个人开发全流程。
    3. `data_standards.mdc` (数据处理/质量/迁移/备份)：适用于所有数据处理相关代码、SQL、DuckDB、CSV、前端数据接口等。
    4. `testing_standards.mdc` (测试/覆盖率/报告/自动化)：适用于所有测试代码。
    5. `deployment_standards.mdc` (部署/监控/事故响应)：适用于所有部署、运维、生产环境相关流程。
    6. `mvp_rules.mdc` (MVP阶段前后端规则整合)：适用于MVP阶段前后端开发、测试、交付全流程。

---

## 6. 项目流程与任务流强制规范 (Project & Task Flow Enforcement)

* **任务追踪唯一源**：所有开发任务、任务流、状态流、变更、分支、回滚，必须在 `Dev_Tasks.md` 中结构化登记和同步更新。
* **任务状态流转**：任务状态流转（待开始 → 进行中 → 已完成 → 阻断）必须同步更新文档，并记录变更说明。
* **变更可追溯**：任何任务的新增、变更、回滚、分支，均需有据可查，禁止口头或临时变更。

---

## 7. 版本与修订历史 (Version & Revision History)

| 版本  | 日期       | 修订说明                                                                                                        |
| :---- | :--------- | :-------------------------------------------------------------------------------------------------------------- |
| v1.0  | 2024-06-XX | 初始版本，整合 `00_PROJECT_BLUEPRINT.md` 和 `01_PROJECT_SCAFFOLD.md`，消除冗余、冲突，并进行结构优化。         |
| v1.1  | 2024-06-XX | (待定)                                                                                                          |

---

## 7. 前端MVP开发与能力清单/契约分阶段推进规则

1. 前端MVP开发应优先完成主入口、主页面、全局路由与导航等基础框架，严格对齐AQUA_GUIDE.md、蓝图、PRD、任务路线图等权威文档，确保产品方向和用户体验一致。
2. 能力清单与API契约（api.md）采用分阶段补全策略：初期先搭建能力清单和接口框架，后续根据后端MVP进度逐步细化字段、类型、错误码等。
3. 前端API封装、mock数据、类型定义、测试用例等，均以api.md为唯一事实源，禁止自创接口、字段或mock结构。
4. 每次接口或能力清单变更，必须同步更新api.md、02_FUNCTIONS.md、Dev_Tasks.md和logs/dev_log.md，确保前后端、mock、测试、任务流全链路一致、可追溯。
5. 推荐开发流程：主框架搭建→能力清单/契约框架建立→功能模块逐步集成→自动化校验与日志同步，形成最小可用闭环，便于敏捷迭代和个人开发节奏。

---

## 8. 变更与版本管理策略（2024极简个人敏捷版）

### 8.1 极简分支策略
- 主分支：AQUA_V1.2（始终保持可用，仅合并已测试通过的变更）
- 特性/修复分支：feature/{任务ID}-{简要描述}，如 feature/epic0-f05-t01-tdd-refactor
- 每个原子任务/需求/修复都新建分支，开发/测试/重构/文档同步在该分支完成
- 分支名必须含任务ID，便于自动化追溯
- 合并前必须通过TDD测试，合并后立即删除分支

### 8.2 极简提交规范
- commit message: [type]: 变更简述 #任务ID
  - 例：feat: 新增API契约校验 #epic0-f05-t01
- 类型：feat/fix/refactor/docs/test/chore
- 每次提交必须带任务ID，便于日志/任务/分支自动关联

### 8.3 自动化合规闭环（pre-commit极简增强）
- 分支名校验：提交前自动检测分支名是否规范（feature/…），不规范警告
- commit message校验：自动检测是否含任务ID，不含则警告
- TDD强制：提交前自动运行相关测试（pytest/vitest），未通过阻断提交
- 日志/任务同步：变更后自动提醒同步更新logs/dev_log.md和Dev_Tasks.md

### 8.4 变更与回溯极简流程
1. 新任务/需求/修复 → 新建分支（feature/…）
2. 开发/测试/重构/文档 → 均在该分支完成，TDD驱动
3. 提交 → 每次commit带任务ID，自动校验
4. 合并 → 测试通过后合并到AQUA_V1.2，合并后删除分支
5. 日志/任务同步 → 变更后立即更新logs/dev_log.md和Dev_Tasks.md

### 8.5 可执行落地步骤
- 在README.md或docs/补充分支命名规范与管理流程
- 一键分支/提交命令模板（Win+OS X通用）
- pre-commit极简增强（建议先警告，后续可阻断）
- TDD与日志/任务同步

> 本策略为AQUA项目个人敏捷开发、TDD、AI协作、自动化合规的唯一权威标准，所有开发、测试、变更、配置、接口、mock、任务流等，必须严格遵循本分区及权威文档、规则体系，确保项目高效、规范、可追溯推进。

---
