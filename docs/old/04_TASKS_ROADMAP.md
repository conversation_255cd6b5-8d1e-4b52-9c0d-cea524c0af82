# AQUA 项目主线任务-支线任务分析与建议

**核心指导思想：**
* **分层推进**：将项目分为"基础建设"、"前端骨架"和"功能开发"三大主线，确保地基稳固再建高楼。
* **MVP 优先**：优先实现核心价值功能，快速验证。
* **前后端协同**：保持前后端接口的同步推进和验证。
* **数据先行**：考虑到 AQUA 的核心是数据平台，数据相关的基础服务应先行。
* **自动化强制**：所有开发步骤，都应严格遵循 `0302_REQUEST.md` 中的自动化要求。

---

### Epic 0: 项目基础建设与规范落地 (Project Foundation & Compliance)

**目标：** 确保项目开发环境、管理流程、规范体系和 AI 协作机制全面就绪，这是所有功能开发的前提和保障。此 Epic 的任务贯穿项目启动初期，并在整个开发生命周期中持续维护和遵循。

* **Feature 0.1: 项目环境与依赖管理初始化**
    * **目标：** 建立稳定、一致、自动化的开发环境。
    * **关键任务（Task/Sub-task）：**
        * 配置 `config/settings.toml` 基础参数。
        * 创建并提供 `.env.example` 模板文件。
        * 完善 `scripts/env_init.py` 核心功能（包括虚拟环境创建、后端 `uv` 依赖管理、前端 `pnpm` 依赖管理、数据库初始化等）。
        * 编写 `start_backend.sh` 和 `start_frontend.sh` (及 `.bat` 版本) 脚本，强制调用 `env_init.py`。
        * 创建初始的 `requirements.txt` 和 `package.json` 文件。
        * 设定 Node.js 版本（`.nvmrc` 或 `engines` 字段）。
    * **关联文档：** `00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md` (技术栈), `0302_REQUEST.md` (环境与依赖管理)。

* **Feature 0.2: 个人任务管理与 AI 协作流建立**
    * **目标：** 建立高效的个人任务管理体系和可控的 AI 协作流程。
    * **关键任务（Task/Sub-task）：**
        * 深入研读并理解所有权威文档（蓝图、PRD、功能定义、规则文件）。
        * 初始化 `docs/tasks/Dev_Tasks.md` 文件结构，并录入初步的 Epic 和 Feature。
        * 明确并初始化 `logs/dev_log.md` 模板与规范。
        * 建立结构化 AI 提示词库（`docs/ai_prompts/`）。
        * 明确 AI 协作合规性要求（AI 输出审查、Git Hooks 强制校验）。
    * **关联文档：** `0302_REQUEST.md` (个人任务管理与 AI 协作流)。

* **Feature 0.3: 强制规范与自动化校验机制部署**
    * **目标：** 确保代码质量和规范性在提交前得到自动化保障。
    * **关键任务（Task/Sub-task）：**
        * 安装和配置 `pre-commit` 框架。
        * 配置 `.pre-commit-config.yaml`，集成代码格式化 (Black/Prettier)、Lint 检查 (Flake8/ESLint)、类型检查 (TSC)、依赖一致性、敏感信息检查等 Hook。
        * 配置 IDE/编辑器集成（ESLint、Prettier、Python Linter 插件）。
        * 编写少量基础的单元测试用例，验证测试框架集成。
    * **关联文档：** `0302_REQUEST.md` (强制规范与自动化校验)。

* **Feature 0.4: 前后端接口契约明确与初始定义**
    * **目标：** 建立前后端协作的唯一标准，确保接口一致性。
    * **关键任务（Task/Sub-task）：**
        * 创建 `02_FUNCTIONS.md` 文件。
        * 根据 PRD 和功能模块定义，初步定义核心功能模块的 API 接口（请求/响应格式、字段、类型、枚举、错误码）。
        * 利用 AI 辅助草拟接口定义，并进行人工审查。
    * **关联文档：** `02_FUNCTIONS.md` (API 契约), `0302_REQUEST.md` (接口契约)。

* **Feature 0.5: 变更与版本管理策略落地**
    * **目标：** 确保项目开发过程可追溯，代码版本清晰。
    * **关键任务（Task/Sub-task）：**
        * 遵循 Git 分支管理规范（主干+短期特性分支）。
        * 明确 Commit Message 规范（关联原子化任务 ID）。
        * 确保 `Dev_Tasks.md` 和 `logs/dev_log.md` 实时更新和同步。
    * **关联文档：** `0302_REQUEST.md` (变更与版本管理)。

---

### Epic 1: 前端核心骨架与导航 (Frontend Core Skeleton & Navigation)

**目标：** 搭建 AQUA 前端应用的基础框架、主页布局、全局导航和路由机制，使其成为一个可访问的空壳应用。这直接对应《01_PRD.md》中 "2.1: 构建前端 MVP 核心骨架"。

* **Feature 1.1: 前端项目初始化与基础配置**
    * **目标：** 初始化 Vue/Vite 项目，配置基础目录结构、引入 UI 框架（如 Ant Design Vue）和 Tailwind CSS，并完成基础的 `pnpm` 依赖管理。
    * **关键任务（Task/Sub-task）：**
        * 使用 Vite 创建 Vue 项目。
        * 集成 Ant Design Vue UI 组件库。
        * 配置 Tailwind CSS。
        * 调整 `package.json` 和 `vite.config.ts` 进行基础优化。
        * 确保 `env_init.py` 能够正确校验前端依赖（如 `pnpm install`）。
    * **关联文档：** `00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md` (技术栈), `0302_REQUEST.md` (环境与依赖管理)。

* **Feature 1.2: 全局布局与主页搭建**
    * **目标：** 实现应用的整体布局（Header, Sidebar, Content Area, Footer），并搭建一个基本的主页 (`/`)。
    * **关键任务（Task/Sub-task）：**
        * 创建基础的 `App.vue` 或类似入口组件。
        * 设计并实现通用的布局组件（例如 `Layout.vue`）。
        * 搭建主页（`HomePage.vue`）的初步结构和内容（如欢迎信息、项目简介）。
        * UI/UX 细节：响应式设计（初步）、主题颜色配置等。
    * **关联文档：** `01_PRD.md` (前端 MVP 核心骨架)。

* **Feature 1.3: 前端路由与导航机制**
    * **目标：** 引入并配置前端路由库（如 Vue Router），建立基础的路由映射和导航菜单，使得后续功能模块的页面可以"注册"到此导航系统中。
    * **关键任务（Task/Sub-task）：**
        * 安装和配置 Vue Router。
        * 定义基础路由（如 `/`, `/data-center`, `/backtest`, `/login` 等的占位符路由）。
        * 在侧边栏（Sidebar）或顶部导航（Header）中创建可点击的导航菜单。
        * 实现导航菜单与路由的联动。
        * 前端单元测试：路由跳转、导航高亮。
    * **关联文档：** `01_PRD.md` (前端 MVP 核心骨架), `02_FUNCTIONS.md` (未来功能模块的路由需求)。

---

### Epic 2: 核心数据服务建设 (Core Data Service Construction)

**目标：** 建立本地数据存储，实现数据导入和基本的浏览功能，作为所有后续数据分析和回测功能的基石。这对应《01_PRD.md》中 "后端子 MVP 1: 核心数据服务" 的核心功能。

* **Feature 2.1: 后端数据存储与核心模型**
    * **目标：** 初始化 DuckDB 数据库，设计并实现股票/期货数据的核心数据模型。
    * **关键任务（Task/Sub-task）：**
        * 数据库连接与配置（DuckDB）。
        * 核心数据表结构定义（如日线/分钟线数据表，考虑字段类型、索引）。
        * 数据库迁移或初始化脚本。
        * 单元测试：数据模型定义正确性。
    * **关联文档：** `00_PROJECT_BLUEPRINT_AND_SCAFFOLD.md` (技术栈), `02_FUNCTIONS.md` (0101.数据管理)。

* **Feature 2.2: CSV 数据导入核心 API (后端)**
    * **目标：** 实现 `02_FUNCTIONS.md` 中 `010101.数据导入` 定义的后端 API，支持 CSV 文件上传、解析和数据导入。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/data/import`。
        * 文件上传处理（接收 CSV）。
        * CSV 解析与校验（数据类型、格式、缺失值处理）。
        * 数据高效写入 DuckDB。
        * 错误处理与响应（文件格式错误、数据校验失败）。
        * 单元测试：文件解析、数据写入逻辑。
        * 集成测试：API 接口与数据库交互。
    * **关联文档：** `02_FUNCTIONS.md` (010101.数据导入), `0302_REQUEST.md` (测试策略)。

* **Feature 2.3: 数据库浏览核心 API (后端)**
    * **目标：** 实现 `02_FUNCTIONS.md` 中 `010102.数据库浏览` 定义的后端 API，支持获取数据库表列表和查询指定表数据。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`GET /api/data/tables` 和 `GET /api/data/{table_name}`。
        * 获取表列表功能。
        * 查询表数据功能（基础分页、过滤）。
        * 响应数据序列化。
        * 单元测试：查询逻辑。
        * 集成测试：API 接口与数据库交互。
    * **关联文档：** `02_FUNCTIONS.md` (010102.数据库浏览)。

* **Feature 2.4: 数据中心前端页面**
    * **目标：** 实现 AQUA 前端的数据中心页面，包括 CSV 文件上传组件和数据库表浏览/数据展示组件，并与后端 API 对接。此页面将集成到 Epic 1 搭建好的前端骨架中。
    * **关键任务（Task/Sub-task）：**
        * 页面路由与布局：`/data-center`。
        * 文件上传 UI 组件开发，并调用 `POST /api/data/import`。
        * 数据库表列表 UI 组件开发，并调用 `GET /api/data/tables`。
        * 数据展示 UI 组件开发（表格形式，分页），并调用 `GET /api/data/{table_name}`。
        * 前端状态管理（加载、错误、进度）。
        * 前端单元测试：组件独立功能。
        * 前端集成测试：与 Mock Server 联调。
        * 端到端测试：与真实后端联调。
    * **关联文档：** `02_FUNCTIONS.md` (API 契约), `0302_REQUEST.md` (测试策略)。

* **Feature 2.5: 特色数据集成与 API**
    * **目标：** 实现《01_PRD.md》中 `3.1 数据仓库` 下 `特色数据 (0103)` 的数据集成与后端 API。
    * **关键任务（Task/Sub-task）：**
        * 数据源接入（行业数据、宏观数据，初始可为静态文件或模拟数据）。
        * 核心数据模型与表结构定义。
        * API 路由定义：`GET /api/data/industry`, `GET /api/data/macro` 等。
        * 数据清洗、处理与存储。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (0103.特色数据)。

---

### Epic 3: 基础回测服务 (Basic Backtesting Service)

**目标：** 实现核心回测逻辑的后端 API，并提供前端回测结果的展示。这对应《01_PRD.md》中 "后端子 MVP 2: 基础回测服务" 和 "前端 MVP 核心功能模块集成与可视化" 的进一步落地。

* **Feature 3.1: 回测核心逻辑 API (后端)**
    * **目标：** 实现一个基础的回测引擎，支持简单的策略回测（如固定周期均线策略），并能够读取 DuckDB 中的数据。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/backtest/run` (回测任务提交) 和 `GET /api/backtest/results/{task_id}` (回测结果查询)。
        * 数据加载模块：从 DuckDB 加载指定历史数据。
        * 回测引擎核心实现：支持至少一种简单策略（如固定周期均线策略）。
        * 结果计算：计算盈亏、绩效指标。
        * 结果存储：将回测结果存储到 DuckDB。
        * 单元测试：回测引擎核心逻辑。
        * 集成测试：API 接口与数据库、回测引擎集成。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (0301.回测任务, 0302.回测结果分析)。

* **Feature 3.2: 回测结果展示前端页面**
    * **目标：** 开发前端回测结果展示页面，能够接收后端回测结果，并以图表和表格形式进行可视化。此页面将集成到 Epic 1 搭建好的前端骨架中。
    * **关键任务（Task/Sub-task）：**
        * 页面路由与布局：`/backtest-results`。
        * 回测任务提交 UI 组件：允许用户选择数据和策略参数，触发后端回测 API。
        * 绩效报表可视化：使用图表库展示收益曲线、回撤图，以及绩效指标表格。
        * 交易明细展示：以表格形式展示逐笔交易明细。
        * 前端状态管理（加载、错误、进度）。
        * 前端单元测试：组件独立功能。
        * 前端集成测试：与 Mock Server 联调。
        * 端到端测试：与真实后端联调。
    * **关联文档：** `02_FUNCTIONS.md` (030201.绩效报表, 030203.交易明细), `0302_REQUEST.md` (测试策略)。

---

### Epic 4: 模拟交易服务 (Simulated Trading Service)

**目标：** 实现基于回测结果的模拟交易功能，允许用户在模拟环境中执行交易操作，并包含基础的模拟资金管理。这对应《02_FUNCTIONS.md》中 `04.模拟交易 (simulator)` 模块。

* **Feature 4.1: 模拟交易与资金管理核心 API (后端)**
    * **目标：** 实现模拟交易的下单、撤单、成交回报、持仓管理，以及模拟资金的充值/提现等后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/trade/order`, `POST /api/trade/cancel`, `GET /api/trade/positions`, `POST /api/sim_account/deposit`, `POST /api/sim_account/withdraw` 等。
        * 模拟撮合引擎（简化版）。
        * 持仓管理、模拟资金变动事务处理。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (04.模拟交易)。

* **Feature 4.2: 模拟交易与资金管理前端页面**
    * **目标：** 实现前端的模拟交易界面，包括下单面板、持仓展示、交易记录，以及模拟资金充值/提现等界面。此页面将集成到 Epic 1 搭建好的前端骨架中。
    * **关键任务（Task/Sub-task）：**
        * 交易面板 UI。
        * 持仓列表 UI。
        * 交易记录 UI。
        * 模拟资金充值/提现 UI。
        * 与后端 API 对接。
        * 前端测试。
    * **关联文档：** `02_FUNCTIONS.md` (04.模拟交易)。

---

### Epic 5: 策略管理与可视化 (Strategy Management & Visualization)

**目标：** 提供策略的创建、编辑、保存和可视化功能。这对应《02_FUNCTIONS.md》中 `02.策略抽屉` 模块下的 `0201.策略列表` 和 `0202.策略管理`。

* **Feature 5.1: 策略管理 API (后端)**
    * **目标：** 实现策略的创建、保存、加载、删除等后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/strategy`, `GET /api/strategy/{id}`, `PUT /api/strategy/{id}`, `DELETE /api/strategy/{id}`。
        * 策略存储（考虑代码或配置形式）。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (0201.策略列表, 0202.策略管理)。

* **Feature 5.2: 策略编辑器前端页面**
    * **目标：** 提供一个前端策略编辑器，允许用户创建和编辑策略。此页面将集成到 Epic 1 搭建好的前端骨架中。
    * **关键任务（Task/Sub-task）：**
        * 策略列表 UI。
        * 策略创建/编辑 UI（可能集成代码编辑器或可视化配置器）。
        * 与后端 API 对接。
        * 前端测试。
    * **关联文档：** `02_FUNCTIONS.md` (0201.策略列表, 0202.策略管理)。

* **Feature 5.3: 策略可视化前端页面**
    * **目标：** 实现策略信号和交易行为在图表上的可视化。此页面将集成到 Epic 1 搭建好的前端骨架中。
    * **关键任务（Task/Sub-task）：**
        * 图表集成（K线图）。
        * 策略信号（买卖点）叠加。
        * 交易行为（开平仓）标记。
        * 与回测结果数据对接。
        * 前端测试。
    * **关联文档：** `02_FUNCTIONS.md` (0302.回测结果分析)。

* **Feature 5.4: 因子管理 (后端与前端)**
    * **目标：** 实现《01_PRD.md》中 `3.2 策略抽屉` 下 `因子管理` 的后端 API 和前端界面。
    * **关键任务（Task/Sub-task）：**
        * 因子数据模型与存储。
        * API 路由定义：`GET /api/factor/a_stock`, `GET /api/factor/futures` 等。
        * 因子计算与生成（简化版）。
        * 因子列表与详情前端 UI。
        * 单元测试、集成测试、前端测试。**强调：** 后端集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (0203.因子管理)。

---

### Epic 6: 平台管理 (Platform Management)

**目标：** 提供系统级别的管理功能，如用户账户、认证及权限管理、日志查看、系统配置等。这对应《02_FUNCTIONS.md》中 `99.平台管理` 模块及其子模块。

* **Feature 6.1: 用户账户与权限管理 API (后端)**
    * **目标：** 实现用户注册、登录、用户列表查询、用户角色/权限管理等后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/user/register`, `POST /api/user/login`, `GET /api/user/list`, `PUT /api/user/{id}/role` 等。
        * 用户认证与授权机制。
        * 用户数据模型与数据库表结构。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (990102.用户与权限)。

* **Feature 6.2: 系统配置 API (后端)**
    * **目标：** 实现系统配置的读取和修改后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`GET /api/admin/config`, `PUT /api/admin/config`。
        * 配置读取与写入逻辑。
        * 配置校验。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (990101.启动参数, 9902.功能管理, 9903.参数配置)。

* **Feature 6.3: 平台管理前端页面**
    * **目标：** 提供前端用户管理和系统配置界面。此页面将集成到 Epic 1 搭建好的前端骨架中。
    * **关键任务（Task/Sub-task）：**
        * 用户注册/登录页面 UI。
        * 用户列表管理 UI。
        * 系统配置 UI。
        * 与后端 API 对接。
        * 前端测试。
    * **关联文档：** `02_FUNCTIONS.md` (990102.用户与权限, 990101.启动参数, 9902.功能管理, 9903.参数配置)。

---

### Epic 7: AI 驱动洞察与智能辅助 (AI-Driven Insight & Intelligent Assistance)

**目标：** 实现 PRD 中定义的 AI 驱动洞察核心功能，提供自然语言交互、异常检测、智能报告和策略优化建议等服务。这对应《01_PRD.md》中 `3.5 AI 驱动洞察` 和 《02_FUNCTIONS.md》中 `05.AIGC智能体` 模块。

* **Feature 7.1: 数据智能查询 API (后端)**
    * **目标：** 实现支持自然语言查询数据和指标的后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/ai/query`。
        * 自然语言解析（集成 NLP 库或 AI 模型）。
        * 数据查询与结果生成。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (050102.AI交互管理)。

* **Feature 7.2: 异常检测与预警 API (后端)**
    * **目标：** 实现自动识别数据异常、市场异动或策略潜在问题的后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/ai/detect_anomaly`。
        * 异常检测算法实现。
        * 预警机制与通知（简化版）。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (05.AIGC智能体，作为其子功能，或新增细化模块)。

* **Feature 7.3: 智能报告生成 API (后端)**
    * **目标：** 实现自动生成市场分析报告或策略表现总结的后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/ai/generate_report`。
        * 报告内容组织与格式化。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (05.AIGC智能体，作为其子功能)。

* **Feature 7.4: 策略优化建议 API (后端)**
    * **目标：** 实现基于 AI 分析，提供策略参数或规则的优化建议的后端 API。
    * **关键任务（Task/Sub-task）：**
        * API 路由定义：`POST /api/ai/optimize_strategy`。
        * 策略优化算法实现（集成优化库或 AI 模型）。
        * 建议生成与评估。
        * 单元测试、集成测试。**强调：** 集成测试必须使用测试数据库提供的实际数据。
    * **关联文档：** `02_FUNCTIONS.md` (05.AIGC智能体，作为其子功能)。

* **Feature 7.5: AI 交互前端页面**
    * **目标：** 提供前端 AI 交互界面，支持自然语言输入、展示 AI 返回结果和报告。
    * **关键任务（Task/Sub-task）：**
        * 页面路由与布局：`/ai-agent`。
        * 自然语言输入 UI 组件。
        * AI 响应展示 UI 组件（文本、图表）。
        * 与后端 API 对接。
        * 前端测试。
    * **关联文档：** `02_FUNCTIONS.md` (0604.AI交互界面)。

---

**全栈工程师的思考与建议（补充与强调）：**

1.  **MVP 优先级：** 严格按照 PRD 中的 MVP 划分，**优先完成 Epic 0 和 Epic 1**。Epic 2 是紧随其后的核心价值功能。**强调：** `Epic 2`, `Epic 3`, `Epic 7` 是 **核心 MVP 阶段** 的关键组成部分。其他 Epics (Epic 4, 5, 6) 以及 `Feature 2.5` 属于核心 MVP 后的迭代目标。
2.  **AI 协作的深度利用：**
    * 在每个 Feature 开始前，利用 AI 协助您将 Feature 进一步拆解为详细的 Task 和 Sub-task，并填充 `Dev_Tasks.md`。**强调：** 定义原子化任务的最小粒度标准（例如：单个API实现、单个UI组件开发、单个测试用例编写），确保可快速完成。
    * 在 Task 层面，让 AI 协助生成代码骨架、单元测试用例、API 文档片段。
    * 在代码审查阶段，利用 AI 检查代码是否符合您在 `.rules` 中定义的规范，以及是否存在潜在 Bug。
    * **新增：** 制定 AI 任务元数据标准（`ai_generated`, `prompt_id`, `ai_version` 等），并规划 AI 协作日志自动化机制（将 AI 生成任务、代码、文档的自查报告自动归档到 `logs/dev_log.md`）。
    * **新增：** 探索并集成任务流可视化工具（如 Mermaid.js），确保 `Dev_Tasks.md` 可转换为任务依赖图。
3.  **持续集成/部署的思考（未来）：** 尽管是个人项目，但随着项目复杂度的增加，可以考虑引入 CI/CD 工具（如 GitHub Actions）来自动化测试、构建和部署流程，进一步减轻您的负担。这将是 Epic 0 的一个扩展 Feature。
4.  **性能与扩展性：** 在每个功能模块开发时，都要有意识地考虑性能和未来的扩展性。例如，数据导入时考虑大数据量处理、回测引擎考虑多策略并行、数据库查询考虑索引优化等。
5.  **文档的生命周期：** 强调文档不是一次性工作。随着功能的迭代和完善，`02_FUNCTIONS.md`、`Dev_Tasks.md`、`logs/dev_log.md` 以及 `.rules` 目录下的所有规则文件都必须同步更新。
6.  **错误处理与日志：** 确保每个 API 接口都有明确的错误码和错误信息返回，并且关键的业务操作和异常情况都有详细的日志记录。这对于个人排查问题至关重要。
7.  **后端集成测试的数据来源：** 在 `Epic 2.2`, `Epic 2.3`, `Epic 3.1` 以及未来所有涉及后端数据操作的 Feature 的 Task/Sub-task 中，明确强调"集成测试必须使用测试数据库提供的实际数据"。
    * **新增：** 编写脚本，自动检查 commit message 是否关联任务 ID。

---

**结构化检查总结：**

* **格式：** Markdown 标题（`###` for Epic, `*` for Features, `*` for nested tasks/sub-tasks）和列表格式统一。加粗、代码引用等基本格式应用一致。
* **编号：**
    * 所有 Epic 编号已更新为 `Epic 0` 到 `Epic 7`，保持连续性。
    * 所有 Feature 编号已更新为与其所属 Epic 编号一致（例如 `Epic 2` 下的 `Feature 2.x`），保持逻辑性。
    * 内部的关键任务（Task/Sub-task）使用嵌套的无序列表，清晰明了。
* **内容完整性：** 移除了不再独立的 Epic，将其功能合理地分配到现有 Epics 中。`02_FUNCTIONS.md` 中列出的所有主要功能模块都在相应的 Epic 中得到了体现。
* **关联文档：** 每个 Feature 都明确指出了其关联的文档，便于快速查找相关信息。
* **核心理念与指导思想：** 文档开头的核心指导思想与整个任务分解保持一致。
