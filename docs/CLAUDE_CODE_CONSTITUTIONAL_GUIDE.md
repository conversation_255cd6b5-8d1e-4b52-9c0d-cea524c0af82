# Claude Code宪法执行指南

## 🏛️ 概述

本指南提供了确保Claude Code完全遵守CLAUDE.md"宪法"和用户指令的完整解决方案。

## 🎯 核心设计原理

**"预防胜于补救，约束胜于信任，透明胜于黑盒"**

- **预执行验证**：任何操作前进行强制检查
- **违规阻断**：发现违规立即停止操作  
- **人类审批**：重大决策需要明确批准
- **透明可控**：所有决策过程可追溯

## 🔧 技术架构

### 核心组件

1. **宪法执行器** (`claude_constitutional_enforcer.py`)
   - 预执行合规检查
   - 违规行为检测和阻断
   - 人类审批流程管理
   - 合规日志记录

2. **操作模板** (`claude_operation_template.py`)  
   - 标准化操作流程
   - 强制合规声明
   - 自动合规验证

3. **合规检查器** (`compliance_checker.py`)
   - 项目目录结构验证
   - 违规文件自动检测
   - 合规报告生成

## 📋 使用流程

### 对于Claude Code操作者

**每次操作前必须执行以下步骤：**

#### 步骤1: 合规声明
```
🏛️ Claude Code 宪法合规声明
合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则
```

#### 步骤2: 预执行检查
```python
from scripts.claude_constitutional_enforcer import ClaudeConstitutionalEnforcer

enforcer = ClaudeConstitutionalEnforcer()
compliance_level, violations = enforcer.pre_execution_check(
    operation_type="file_creation",  # 或其他操作类型
    operation_details={
        "compliance_declaration": True,
        "file_path": "tests/unit/test_example.py",
        "reuse_analysis_completed": True,
        "test_first_approach": True
    }
)

if compliance_level == ComplianceLevel.CRITICAL:
    print("🚫 严重违规 - 操作终止")
    exit()
```

#### 步骤3: 人类审批（如有更好方案）
```python
if has_better_approach:
    approval = enforcer.request_human_approval(
        DecisionType.ALTERNATIVE_APPROACH,
        current_plan="当前计划描述",
        alternative_plan="更好的方案描述",
        reasoning="详细分析为什么这个方案更好"
    )
    
    if not approval:
        print("⏸️ 等待人类批准，操作暂停")
        exit()
```

### 对于用户（项目管理者）

#### 定期合规检查
```bash
# 检查项目合规性
python scripts/compliance_checker.py

# 查看Claude Code操作日志
python -c "
from scripts.claude_constitutional_enforcer import ClaudeConstitutionalEnforcer
enforcer = ClaudeConstitutionalEnforcer()
print(enforcer.generate_compliance_report())
"
```

#### 审批管理
当Claude Code请求审批时，您会看到：

```
🤖 Claude Code 请求人类审批
============================================================

📋 决策类型: 替代方案
⏰ 时间: 2025-07-27T10:30:00

📝 当前计划:
在根目录创建test_example.py测试文件

💡 建议替代方案:
在tests/unit/目录创建test_example.py，符合项目结构规范

🧠 推理过程:
根据CLAUDE.md宪法第18条，测试文件应放在tests/目录下。
这样可以：
1. 保持项目结构清晰
2. 符合开发规范
3. 便于测试管理

🎯 需要您的决策:
请回复以下选项之一：
✅ 'approve' - 批准替代方案
❌ 'reject' - 拒绝，继续原计划
🔄 'modify' - 要求修改方案
```

**回复示例：**
- `approve` - 批准Claude Code的建议
- `reject` - 坚持原计划  
- `modify: 请在tests/integration/目录创建` - 要求修改方案

## 🚫 违规处理机制

### 违规等级

1. **严重违规 (CRITICAL)** - 立即阻断操作
   - 在根目录创建测试/文档文件
   - 缺少合规声明
   - 违反核心宪法规则

2. **一般违规 (VIOLATION)** - 警告但允许继续
   - 文件放置位置不当
   - 跳过复用分析

3. **警告 (WARNING)** - 记录但不阻断
   - 未遵循TDD原则
   - 缺少完整文档

### 自动处理流程

```python
def handle_violation(violation_level, violations, context):
    if violation_level == ComplianceLevel.CRITICAL:
        print("🚫 严重违规 - 操作已阻断")
        log_violation(violations, context)
        return False  # 停止操作
    
    elif violation_level == ComplianceLevel.VIOLATION:
        print("⚠️ 发现违规 - 记录警告")
        log_violation(violations, context)
        return True  # 允许继续但记录
    
    return True
```

## 📊 监控和报告

### 实时监控
- 每次操作都会记录到 `logs/constitutional_enforcement.jsonl`
- 违规行为会触发警报
- 审批请求会保存到 `logs/approval_requests.jsonl`

### 定期报告
```bash
# 生成合规报告
python scripts/claude_constitutional_enforcer.py

# 检查项目结构合规性
python scripts/compliance_checker.py
```

### 日志分析
```python
# 查看最近的违规记录
from scripts.claude_constitutional_enforcer import ClaudeConstitutionalEnforcer

enforcer = ClaudeConstitutionalEnforcer()
violations = enforcer._load_recent_violations()
print(f"最近发现 {len(violations)} 个合规问题")
```

## 🔄 最佳实践

### 对于Claude Code

1. **永远从合规声明开始**
   ```
   合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则
   ```

2. **使用操作模板**
   ```python
   from scripts.claude_operation_template import ClaudeOperationTemplate
   template = ClaudeOperationTemplate()
   result = template.execute_with_compliance(operation_func, "file_creation", details)
   ```

3. **主动请求审批**
   - 发现更好方案时立即暂停
   - 详细说明分析过程
   - 等待明确的人类批准

4. **记录所有决策**
   - 使用结构化日志
   - 包含完整上下文信息
   - 便于后续审查

### 对于用户

1. **定期检查合规性**
   ```bash
   # 每日检查
   python scripts/compliance_checker.py
   ```

2. **及时处理审批请求**
   - 仔细阅读Claude Code的分析
   - 给出明确的决策
   - 必要时要求更多信息

3. **监控违规趋势**
   - 查看合规报告
   - 识别重复违规模式
   - 调整规则或培训

## 🛠️ 故障排除

### 常见问题

**Q: Claude Code不遵循宪法规则怎么办？**
A: 检查是否正确加载了宪法执行器，确保每次操作前都执行预检查。

**Q: 审批请求没有响应怎么处理？**  
A: 检查 `logs/approval_requests.jsonl` 文件，确认请求已记录。

**Q: 合规检查器报告大量违规怎么办？**
A: 运行 `python scripts/compliance_checker.py` 并选择自动修正。

### 调试命令

```bash
# 查看宪法执行器日志
tail -f logs/constitutional_enforcement.jsonl

# 检查最近的审批请求
cat logs/approval_requests.jsonl | jq '.'

# 验证项目结构
python scripts/compliance_checker.py
```

## 📈 持续改进

### 规则更新
- 根据实际使用情况调整宪法规则
- 添加新的违规检测模式
- 优化审批流程

### 自动化增强
- 集成到CI/CD流程
- 添加更多自动检查
- 改进违规预测

### 用户体验优化
- 简化审批界面
- 提供更清晰的违规说明
- 增加操作指导

---

## 🎯 总结

这套宪法执行框架通过技术手段确保了Claude Code的完全合规，实现了：

✅ **强制性约束** - 违规即停止，无法绕过
✅ **透明决策** - 所有操作都有记录和说明  
✅ **人类控制** - 重要决策需要明确批准
✅ **持续监控** - 实时跟踪合规状态
✅ **自动修正** - 发现问题立即处理

通过这套机制，您可以完全信任Claude Code会按照既定规则工作，同时保持对所有重要决策的最终控制权。