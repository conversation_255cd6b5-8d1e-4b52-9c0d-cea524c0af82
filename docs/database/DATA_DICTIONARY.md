# AQUA 数据字典 (v4.0 - 统一业务架构版)

> **文档地位**: 本文档是AQUA项目数据架构的**唯一、权威的蓝图**。采用**统一业务表架构**设计，通过"数据源透明化"策略处理多数据源整合，为量化投资提供简洁高效的数据解决方案。

---

## 🏗️ 架构设计原则

### 核心理念：业务导向 + 数据源透明化

AQUA采用**统一业务表架构**，按业务实体组织数据，通过元数据字段管理数据来源，实现"简单查询优先"的数据管理策略：

```text
┌─────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)           │
│     ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │
│     │  回测引擎   │  │  实时交易   │  │  数据分析   │   │
│     └─────────────┘  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────────────────────────┘
                              │
                        简单直接查询
                              │
┌─────────────────────────────────────────────────────────┐
│                   统一业务表层 (Unified Business Tables) │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │期货K线数据  │  │ 股票行情数据 │  │  基础信息   │     │
│  │单一权威表   │  │ 单一权威表   │  │  单一权威表 │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
                              │
                        数据源透明管理
                              │
┌─────────────────────────────────────────────────────────┐
│                   ETL处理层 (ETL Processing Layer)       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  CSV源      │  │ TUSHARE源   │  │  MySQL源    │     │
│  │  标准化     │  │   标准化    │  │   标准化    │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### 设计原则

**1. 业务实体优先**
- 按期货、股票、债券等业务实体组织表结构
- 每个业务实体只有一个权威数据表
- 消除数据分散和查询复杂性

**2. 字段标准化**
- 相同业务概念使用统一字段名
- 统一数据类型和精度标准
- 统一单位和时区标准

**3. 数据源透明化**
- 通过元数据字段标识数据来源
- 数据源信息不影响业务查询逻辑
- 支持数据血缘追踪和质量管理

**4. 查询优化导向**
- 90%的查询为简单单表查询
- 合理设计主键和索引
- 避免不必要的关联查询

---

## 📊 统一业务表结构设计

### 期货数据表

#### futures_main_contract_kline
> 期货主力合约K线数据统一表

**业务说明**: 存储所有来源的期货主力合约K线数据，包括FromC2C历史数据、Tushare实时数据、AKShare补充数据等。

```sql
CREATE TABLE futures_main_contract_kline (
    -- 业务主键
    contract_code     VARCHAR(20)      NOT NULL,           -- 标准化合约代码，如RB2501
    product_code      VARCHAR(10)      NOT NULL,           -- 标准化品种代码，如RB
    frequency         VARCHAR(10)      NOT NULL,           -- 数据频率：5min/15min/30min/1d
    trade_datetime    TIMESTAMP        NOT NULL,           -- 标准化交易时间（北京时间）
    
    -- 价格数据（统一单位：元，精度4位小数）
    open_price        DECIMAL(18,4)    NOT NULL,           -- 开盘价
    high_price        DECIMAL(18,4)    NOT NULL,           -- 最高价
    low_price         DECIMAL(18,4)    NOT NULL,           -- 最低价
    close_price       DECIMAL(18,4)    NOT NULL,           -- 收盘价
    settle_price      DECIMAL(18,4),                       -- 结算价（期货特有）
    
    -- 成交数据（标准化单位）
    volume           BIGINT           NOT NULL,            -- 成交量（手）
    amount           DECIMAL(22,4)    NOT NULL,            -- 成交额（元）
    open_interest    BIGINT,                               -- 持仓量（手）
    
    -- 技术指标字段（预留扩展）
    prev_close       DECIMAL(18,4),                        -- 前收盘价
    change_amount    DECIMAL(18,4),                        -- 涨跌额
    change_percent   DECIMAL(10,4),                        -- 涨跌幅(%)
    
    -- 数据源管理（透明化）
    data_source      VARCHAR(50)      NOT NULL,            -- 数据源：FROMC2C/TUSHARE/AKSHARE
    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',    -- 数据质量：HIGH/NORMAL/LOW
    source_file      VARCHAR(255),                         -- 原始文件路径（CSV源）
    source_table     VARCHAR(100),                         -- 原始表名（数据库源）
    source_api       VARCHAR(100),                         -- 原始API名称（API源）
    
    -- 数据质量管理
    last_verified    TIMESTAMP,                            -- 最后验证时间
    data_status      VARCHAR(20)      DEFAULT 'ACTIVE',    -- 数据状态：ACTIVE/DEPRECATED/ERROR
    quality_score    DECIMAL(5,2),                         -- 数据质量评分(0-100)
    
    -- 审计字段
    created_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    updated_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    
    -- 主键和索引
    PRIMARY KEY (contract_code, frequency, trade_datetime),
    INDEX idx_product_freq_time (product_code, frequency, trade_datetime),
    INDEX idx_data_source (data_source),
    INDEX idx_trade_datetime (trade_datetime),
    INDEX idx_quality (source_quality, data_status),
    INDEX idx_product_code (product_code)
);
```

#### futures_contract_basic
> 期货合约基础信息表

```sql
CREATE TABLE futures_contract_basic (
    -- 业务主键
    contract_code     VARCHAR(20)      PRIMARY KEY,        -- 合约代码，如RB2501
    product_code      VARCHAR(10)      NOT NULL,           -- 品种代码，如RB
    
    -- 合约信息
    contract_name     VARCHAR(50)      NOT NULL,           -- 合约名称
    exchange_code     VARCHAR(20)      NOT NULL,           -- 交易所代码：SHFE/DCE/CZCE/INE
    exchange_name     VARCHAR(50)      NOT NULL,           -- 交易所名称
    
    -- 交易规格
    multiplier        DECIMAL(18,4)    NOT NULL,           -- 合约乘数
    tick_size         DECIMAL(18,6)    NOT NULL,           -- 最小变动价位
    trade_unit        VARCHAR(50)      NOT NULL,           -- 交易单位
    quote_unit        VARCHAR(20)      NOT NULL,           -- 报价单位
    
    -- 时间信息
    list_date         DATE             NOT NULL,           -- 上市日期
    last_trade_date   DATE             NOT NULL,           -- 最后交易日
    delivery_month    VARCHAR(10)      NOT NULL,           -- 交割月份，如2025-01
    
    -- 保证金和限价
    margin_rate       DECIMAL(10,4),                       -- 保证金比率
    limit_up_rate     DECIMAL(10,4),                       -- 涨停板比率
    limit_down_rate   DECIMAL(10,4),                       -- 跌停板比率
    
    -- 数据源管理
    data_source      VARCHAR(50)      NOT NULL,            -- 数据源
    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
    
    -- 审计字段
    created_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    updated_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    
    -- 索引
    INDEX idx_product_code (product_code),
    INDEX idx_exchange (exchange_code),
    INDEX idx_delivery_month (delivery_month),
    INDEX idx_list_date (list_date)
);
```

#### futures_product_basic
> 期货品种基础信息表

```sql
CREATE TABLE futures_product_basic (
    -- 业务主键
    product_code      VARCHAR(10)      PRIMARY KEY,        -- 品种代码，如RB
    
    -- 品种信息
    product_name      VARCHAR(50)      NOT NULL,           -- 品种名称，如螺纹钢
    product_name_en   VARCHAR(50),                         -- 英文名称
    category          VARCHAR(30)      NOT NULL,           -- 品种分类：黑色系/有色系/能化/农产品/贵金属
    exchange_code     VARCHAR(20)      NOT NULL,           -- 主交易所
    
    -- 交易信息
    multiplier        DECIMAL(18,4)    NOT NULL,           -- 标准合约乘数
    tick_size         DECIMAL(18,6)    NOT NULL,           -- 最小变动价位
    trade_unit        VARCHAR(50)      NOT NULL,           -- 交易单位
    quote_unit        VARCHAR(20)      NOT NULL,           -- 报价单位
    
    -- 交割信息
    delivery_unit     VARCHAR(50),                         -- 交割单位
    delivery_grade    TEXT,                                -- 交割品级
    delivery_location TEXT,                                -- 交割地点
    
    -- 时间信息
    list_date         DATE,                                -- 首次上市日期
    is_active         BOOLEAN          DEFAULT TRUE,       -- 是否在交易
    
    -- 数据源管理
    data_source      VARCHAR(50)      NOT NULL,
    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
    
    -- 审计字段
    created_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    updated_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    
    -- 索引
    INDEX idx_category (category),
    INDEX idx_exchange (exchange_code),
    INDEX idx_active (is_active)
);
```

### 交易日历表

#### market_trading_calendar
> 统一交易日历表，支持股票和期货市场

**业务说明**: 存储所有市场的交易日历数据，包括股票市场（上交所、深交所）和期货市场（上期所、大商所、郑商所、中金所、能源中心）的交易日信息，为量化策略提供准确的交易时间基础。

```sql
CREATE TABLE market_trading_calendar (
    -- 业务主键
    market_type       VARCHAR(20)      NOT NULL,           -- 市场类型：STOCK/FUTURES
    exchange_code     VARCHAR(20)      NOT NULL,           -- 交易所：SSE/SZSE/SHFE/DCE/CZCE/INE/CFFEX
    cal_date          DATE             NOT NULL,           -- 日历日期
    
    -- 交易状态
    is_trading_day    BOOLEAN          NOT NULL DEFAULT FALSE, -- 是否交易日
    pretrade_date     DATE,                                -- 前一交易日
    nexttrade_date    DATE,                                -- 下一交易日
    
    -- 节假日信息
    holiday_name      VARCHAR(50),                         -- 节假日名称
    holiday_type      VARCHAR(20),                         -- 节假日类型：NATIONAL/WEEKEND/EXCHANGE
    
    -- 交易时段（期货特有）
    trading_sessions  JSON,                                -- 期货交易时段：{"morning":"09:00-10:15","night":"21:00-23:00"}
    
    -- 数据源管理
    data_source       VARCHAR(50)      NOT NULL DEFAULT 'TUSHARE',
    source_quality    VARCHAR(20)      DEFAULT 'HIGH',     -- TUSHARE官方数据质量高
    source_api        VARCHAR(100)     DEFAULT 'trade_cal',
    
    -- 数据质量管理
    last_verified     TIMESTAMP,                           -- 最后验证时间
    data_status       VARCHAR(20)      DEFAULT 'ACTIVE',   -- 数据状态：ACTIVE/DEPRECATED/ERROR
    quality_score     DECIMAL(5,2)     DEFAULT 100.00,     -- 数据质量评分(0-100)
    
    -- 审计字段
    created_at        TIMESTAMP        DEFAULT (get_beijing_time_now()),
    updated_at        TIMESTAMP        DEFAULT (get_beijing_time_now()),
    
    -- 主键和索引
    PRIMARY KEY (market_type, exchange_code, cal_date),
    INDEX idx_calendar_date (cal_date),
    INDEX idx_trading_day (is_trading_day, cal_date),
    INDEX idx_market_type (market_type),
    INDEX idx_exchange (exchange_code),
    INDEX idx_holiday (holiday_name),
    INDEX idx_quality (source_quality, data_status)
);
```

### 股票数据表

#### stocks_daily_kline
> 股票日线行情数据统一表

```sql
CREATE TABLE stocks_daily_kline (
    -- 业务主键
    stock_code        VARCHAR(20)      NOT NULL,           -- 标准化股票代码，如000001.SZ
    trade_date        DATE             NOT NULL,           -- 交易日期
    
    -- 价格数据（统一单位：元）
    open_price        DECIMAL(18,4)    NOT NULL,           -- 开盘价
    high_price        DECIMAL(18,4)    NOT NULL,           -- 最高价
    low_price         DECIMAL(18,4)    NOT NULL,           -- 最低价
    close_price       DECIMAL(18,4)    NOT NULL,           -- 收盘价
    prev_close        DECIMAL(18,4),                       -- 前收盘价
    
    -- 成交数据
    volume           BIGINT           NOT NULL,            -- 成交量（股）
    amount           DECIMAL(22,4)    NOT NULL,            -- 成交额（元）
    
    -- 技术指标
    change_amount    DECIMAL(18,4),                        -- 涨跌额
    change_percent   DECIMAL(10,4),                        -- 涨跌幅(%)
    turnover_rate    DECIMAL(10,4),                        -- 换手率(%)
    
    -- 复权因子
    adj_factor       DECIMAL(18,6),                        -- 复权因子
    
    -- 数据源管理
    data_source      VARCHAR(50)      NOT NULL,            -- TUSHARE/AKSHARE
    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
    source_api       VARCHAR(100),                         -- API接口名
    
    -- 数据质量管理
    last_verified    TIMESTAMP,
    data_status      VARCHAR(20)      DEFAULT 'ACTIVE',
    quality_score    DECIMAL(5,2),
    
    -- 审计字段
    created_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    updated_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    
    -- 主键和索引
    PRIMARY KEY (stock_code, trade_date),
    INDEX idx_trade_date (trade_date),
    INDEX idx_data_source (data_source),
    INDEX idx_quality (source_quality, data_status)
);
```

#### stocks_basic_info
> 股票基础信息表

```sql
CREATE TABLE stocks_basic_info (
    -- 业务主键
    stock_code        VARCHAR(20)      PRIMARY KEY,        -- 股票代码
    
    -- 基本信息
    stock_name        VARCHAR(50)      NOT NULL,           -- 股票名称
    stock_name_en     VARCHAR(50),                         -- 英文名称
    symbol            VARCHAR(20)      NOT NULL,           -- 交易代码（无后缀）
    market            VARCHAR(20)      NOT NULL,           -- 市场：SZ/SH
    
    -- 分类信息
    industry          VARCHAR(50),                         -- 所属行业
    area              VARCHAR(30),                         -- 所在地区
    sector            VARCHAR(50),                         -- 板块
    
    -- 上市信息
    list_date         DATE,                                -- 上市日期
    list_status       VARCHAR(20)      DEFAULT 'L',        -- 上市状态：L/D/P
    delist_date       DATE,                                -- 退市日期
    
    -- 财务信息
    total_share       BIGINT,                              -- 总股本（股）
    float_share       BIGINT,                              -- 流通股本（股）
    
    -- 数据源管理
    data_source      VARCHAR(50)      NOT NULL,
    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
    
    -- 审计字段
    created_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    updated_at       TIMESTAMP        DEFAULT (get_beijing_time_now()),
    
    -- 索引
    INDEX idx_symbol (symbol),
    INDEX idx_industry (industry),
    INDEX idx_market (market),
    INDEX idx_list_date (list_date),
    INDEX idx_list_status (list_status)
);
```

---

## 🎯 字段标准化规范

### 命名规范

| 业务概念 | 标准字段名 | 数据类型 | 说明 |
|----------|------------|----------|------|
| 合约代码 | contract_code | VARCHAR(20) | 期货：RB2501，股票：000001.SZ |
| 品种代码 | product_code | VARCHAR(10) | 期货品种：RB, AG |
| 股票代码 | stock_code | VARCHAR(20) | 统一格式：000001.SZ |
| 交易时间 | trade_datetime | TIMESTAMP | 期货分钟级数据 |
| 交易日期 | trade_date | DATE | 股票日线数据 |
| 日历日期 | cal_date | DATE | 交易日历日期 |
| 交易日标识 | is_trading_day | BOOLEAN | 是否为交易日 |
| 开盘价 | open_price | DECIMAL(18,4) | 统一精度 |
| 最高价 | high_price | DECIMAL(18,4) | 统一精度 |
| 最低价 | low_price | DECIMAL(18,4) | 统一精度 |
| 收盘价 | close_price | DECIMAL(18,4) | 统一精度 |
| 成交量 | volume | BIGINT | 期货（手）/股票（股） |
| 成交额 | amount | DECIMAL(22,4) | 统一单位：元 |

### 数据源标识规范

| 数据源 | data_source值 | 说明 |
|--------|---------------|------|
| FromC2C | FROMC2C | CSV历史数据 |
| Tushare Pro | TUSHARE | 权威金融数据 |
| AKShare | AKSHARE | 开源金融数据 |
| Wind | WIND | 万得数据 |
| 同花顺 | THS | 同花顺数据 |

### 数据质量规范

| 质量等级 | source_quality值 | 说明 |
|----------|------------------|------|
| 高质量 | HIGH | 权威机构数据，准确性≥99.9% |
| 标准质量 | NORMAL | 常规数据，准确性≥99% |
| 低质量 | LOW | 补充数据，准确性≥95% |

---

## 🔄 数据源管理策略

### ETL处理原则

**1. 数据标准化**
- 统一字段命名和数据类型
- 统一时区（北京时间）和单位
- 数据清洗和验证

**2. 重复数据处理**
- 相同时间点的数据以高质量数据源为准
- 建立数据源优先级：TUSHARE > FROMC2C > AKSHARE
- 记录数据来源用于追溯

**3. 数据质量管理**
- 自动计算质量评分
- 定期验证数据完整性
- 异常数据标记和处理

### 查询优化策略

**常用查询模式**：
```sql
-- 获取指定合约的K线数据
SELECT * FROM futures_main_contract_kline 
WHERE contract_code = 'RB2501' 
  AND frequency = '15min'
  AND trade_datetime BETWEEN '2024-01-01' AND '2024-12-31'
ORDER BY trade_datetime;

-- 获取高质量数据
SELECT * FROM futures_main_contract_kline 
WHERE product_code = 'RB' 
  AND source_quality = 'HIGH'
  AND data_status = 'ACTIVE'
ORDER BY trade_datetime DESC LIMIT 1000;

-- 按数据源查询
SELECT data_source, COUNT(*) as record_count
FROM futures_main_contract_kline 
WHERE trade_datetime >= '2024-01-01'
GROUP BY data_source;

-- 交易日历查询
SELECT is_trading_day FROM market_trading_calendar 
WHERE market_type = 'STOCK' AND cal_date = '2025-01-01';

-- 获取期货交易所最近交易日
SELECT MAX(cal_date) FROM market_trading_calendar 
WHERE market_type = 'FUTURES' AND exchange_code = 'SHFE' AND is_trading_day = TRUE;

-- 获取指定时间范围的交易日
SELECT cal_date FROM market_trading_calendar 
WHERE market_type = 'STOCK' 
  AND is_trading_day = TRUE 
  AND cal_date BETWEEN '2025-01-01' AND '2025-12-31'
ORDER BY cal_date;
```

---

## 📋 数据迁移指导

### 从分层表到统一表的映射

#### FromC2C数据迁移
```sql
-- csv_fut_main_contract_kline_15min -> futures_main_contract_kline
INSERT INTO futures_main_contract_kline (
    contract_code, product_code, frequency, trade_datetime,
    open_price, high_price, low_price, close_price,
    volume, amount, open_interest,
    data_source, source_file, source_quality,
    created_at, updated_at
)
SELECT 
    contract_code, product_code, '15min', trade_datetime,
    open, high, low, close,
    volume, amount, open_interest,
    'FROMC2C', source_file, 'NORMAL',
    created_at, updated_at
FROM csv_fut_main_contract_kline_15min;
```

#### Tushare数据迁移
```sql
-- tushare_fut_daily -> futures_main_contract_kline
INSERT INTO futures_main_contract_kline (
    contract_code, product_code, frequency, trade_datetime,
    open_price, high_price, low_price, close_price, settle_price,
    volume, amount, open_interest,
    data_source, source_quality,
    created_at, updated_at
)
SELECT 
    -- 标准化合约代码：RB2501.SHF -> RB2501
    SUBSTRING(ts_code, 1, POSITION('.' IN ts_code) - 1),
    -- 从合约代码提取品种代码
    REGEXP_EXTRACT(ts_code, '^([A-Z]+)'),
    '1d',
    CAST(trade_date AS TIMESTAMP),
    open, high, low, close, settle,
    vol, amount * 10000, oi,  -- 成交额单位转换：万元->元
    'TUSHARE', 'HIGH',
    created_at, updated_at
FROM tushare_fut_daily;
```

### 数据验证检查
```sql
-- 检查数据完整性
SELECT 
    data_source,
    COUNT(*) as total_records,
    MIN(trade_datetime) as earliest_date,
    MAX(trade_datetime) as latest_date,
    COUNT(DISTINCT contract_code) as unique_contracts
FROM futures_main_contract_kline
GROUP BY data_source;

-- 检查重复数据
SELECT 
    contract_code, frequency, trade_datetime, COUNT(*)
FROM futures_main_contract_kline
GROUP BY contract_code, frequency, trade_datetime
HAVING COUNT(*) > 1;

-- 交易日历数据初始化
INSERT INTO market_trading_calendar (
    market_type, exchange_code, cal_date, is_trading_day,
    data_source, source_quality, source_api
)
SELECT 
    CASE 
        WHEN exchange IN ('SSE', 'SZSE') THEN 'STOCK'
        ELSE 'FUTURES'
    END as market_type,
    exchange as exchange_code,
    STR_TO_DATE(cal_date, '%Y%m%d') as cal_date,
    CASE WHEN is_open = '1' THEN TRUE ELSE FALSE END as is_trading_day,
    'TUSHARE' as data_source,
    'HIGH' as source_quality,
    'trade_cal' as source_api
FROM tushare_trade_cal_raw;
```

---


---

**版本信息**
- 版本: v4.0 (统一业务架构版)
- 发布日期: 2025-01-26
- 架构原则: 业务导向、数据源透明化、查询优化
- 设计目标: 简洁高效、扩展性强、维护性好
