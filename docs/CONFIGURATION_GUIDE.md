# AQUA 配置驱动指南

## 概述

AQUA项目实现了100%配置驱动架构，所有环境参数、依赖配置、网络设置都通过`config/settings.toml`统一管理。

## 配置文件结构

### 应用基础配置
```toml
[app]
name = "AQUA"                    # 应用名称
version = "2.0"                  # 版本号  
default_environment = "dev"      # 默认环境
```

### 环境管理配置
```toml
[environment]
uv_path = ".venv/bin/uv"                              # UV路径
python_path = ".venv/bin/python"                      # Python路径
detection_sources = ["AQUA_ENV", "ENV", "ENVIRONMENT"] # 环境检测源
validation_required = true                            # 是否需要环境验证
```

### 基础依赖配置
```toml
[bootstrap]
dependencies = [              # 基础依赖列表
    "toml==0.10.2",
    "rich>=13.7.0", 
    "loguru==0.7.2"
]
max_retries = 3               # 安装重试次数
timeout_seconds = 60          # 安装超时时间
fallback_to_official = true   # 是否回退到官方源
```

### 网络配置
```toml
[network]
china_detection_timeout = 2                            # 中国网络检测超时(秒)
mirror_test_timeout = 3                                # 镜像源测试超时(秒)
connection_retries = 3                                 # 连接重试次数
china_detection_host = "pypi.tuna.tsinghua.edu.cn"    # 检测主机

# 镜像源配置（按优先级排序）
china_mirrors = [
    { url = "https://pypi.tuna.tsinghua.edu.cn/simple", host = "pypi.tuna.tsinghua.edu.cn", name = "清华大学" },
    { url = "https://mirrors.aliyun.com/pypi/simple/", host = "mirrors.aliyun.com", name = "阿里云" }
]
```

### 多环境配置

每个环境(dev/test/prod)都有独立的配置段：

```toml
[dev]
# 开发环境配置

[dev.database]
path = "/path/to/dev.duckdb"
auto_create = true
backup_dir = "data/backup"

[dev.csv]
data_dir = "/path/to/data"
batch_size = 1000
enable_parallel = false

[test]
# 测试环境配置 - 类似结构

[prod]  
# 生产环境配置 - 类似结构
```

## 配置验证

系统提供全面的配置验证功能：

### 验证级别
- **致命错误(critical)**: 必须修复，否则系统无法运行
- **警告(warning)**: 建议修复，不影响基本功能
- **信息(info)**: 优化建议

### 验证内容
- 配置完整性检查
- 数值范围验证  
- 格式正确性验证
- 环境配置一致性检查

## 配置最佳实践

### 1. 环境特定配置
- 开发环境：较小的批处理大小，详细日志
- 测试环境：中等配置，完整功能验证
- 生产环境：优化性能配置，关键信息日志

### 2. 网络配置优化
- 根据网络环境调整超时时间
- 配置适合的镜像源优先级
- 设置合理的重试次数

### 3. 安全配置
- 敏感信息通过环境变量注入
- 生产环境禁用自动创建功能
- 定期审查配置权限

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查TOML语法正确性
   - 确认必需字段完整
   - 验证数值范围合理

2. **环境检测失败**
   - 设置AQUA_ENV环境变量
   - 检查环境配置段存在
   - 验证数据库路径正确

3. **依赖安装失败**
   - 检查网络连接
   - 验证镜像源可用性
   - 确认UV工具正常

### 配置修复建议

系统会自动提供具体的修复建议，例如：
- "在[app]段中添加 name = \"适当的值\""
- "建议设置在1-10之间"
- "添加至少一个镜像源配置"

## 使用实践指南

### 1. 环境初始化脚本使用

#### 基本用法
```bash
# 完整环境初始化（推荐首次使用）
python scripts/env_init.py

# 仅检测环境，不做修复/安装（日常检查）
python scripts/env_init.py --check-only
```

#### 环境特定初始化
```bash
# 开发环境初始化
AQUA_ENV=dev python scripts/env_init.py

# 测试环境初始化  
AQUA_ENV=test python scripts/env_init.py

# 生产环境检测（建议仅检测）
AQUA_ENV=prod python scripts/env_init.py --check-only
```

### 2. 启动脚本使用

#### 前端启动
```bash
# Linux/macOS
./start_frontend.sh

# Windows  
start_frontend.bat
```

#### 后端启动
```bash
# Linux/macOS
./start_backend.sh

# Windows
start_backend.bat
```

### 3. 脚本依赖关系
```
启动脚本 → env_init.py → 配置管理器组件 → settings.toml
```

所有启动脚本都首先调用`env_init.py`进行环境校验：
- **start_frontend.sh/bat**: 校验环境 → 检查pnpm依赖 → 启动前端
- **start_backend.sh/bat**: 校验环境 → 启动FastAPI服务

### 4. 动态环境切换
```bash
# 临时切换环境
export AQUA_ENV=test
./start_backend.sh

# 永久设置环境（添加到.bashrc/.zshrc）
echo "export AQUA_ENV=dev" >> ~/.bashrc
```

### 5. 配置热加载
配置管理器支持配置缓存失效和重新加载：
```python
config_manager.invalidate_cache()
config_manager.reload_config()
```

### 6. 批量配置验证
```bash
# 验证所有环境配置
python -c "
from scripts.config_manager import AQUAConfigManager
from scripts.config_validator import AQUAConfigValidator

config_manager = AQUAConfigManager()
validator = AQUAConfigValidator(config_manager)
validator.validate_all_configurations()
"
```

## 配置管理器API

### 主要类
- `AQUAConfigManager`: 统一配置管理
- `ConfigDrivenDependencyManager`: 依赖管理
- `ConfigDrivenNetworkManager`: 网络管理
- `AQUAConfigValidator`: 配置验证

### 使用示例
```python
from scripts.config_manager import AQUAConfigManager

config_manager = AQUAConfigManager()
env_config = config_manager.get_environment_config('dev')
app_config = config_manager.get_app_config()
```

---

通过配置驱动架构，AQUA项目实现了：
- ✅ 100%配置化管理
- ✅ 多环境无缝切换  
- ✅ 智能配置验证
- ✅ 统一错误处理
- ✅ 向后兼容保证