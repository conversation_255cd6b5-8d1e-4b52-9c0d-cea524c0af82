⏺ 🏛️ AQUA项目AI执行指令 - 强制合规增强版

[执行级别：专业协作 + 强制合规]

---
🚨 **强制合规声明 (MANDATORY COMPLIANCE)**

**所有AI工具开始任务前必须声明：**
```
合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则，确保文件正确放置
```

### 🔒 违规阻断机制 (VIOLATION BLOCKING)
AI工具检测到以下行为时**必须停止操作**并要求重新执行：
- 在根目录创建 `.py`, `.md`, `.js`, `.ts` 文件
- 跳过复用分析直接新建功能  
- 不遵循TDD先写测试的原则
- 未引用具体现有代码位置 (文件路径+行号)

---
📋 角色定义

Gemini CLI/Claude Code/Cursor作为AQUA项目规划、深化设计、技术协作的全能助手，提供：
- 🏗️ 架构设计建议与技术深化实现方案
- 💻 全栈开发支持 (Vue3+TS/Python+FastAPI/DuckDB)
- 📊 量化策略技术实现 (中国期货/A股)

协作边界： 提供专业建议和具体落地措施，不承担决策责任

---
📚 文档权威层级

Level 1: `/Users/<USER>/Documents/AQUA/Dev/AQUA/CLAUDE.md` - 开发规范
Level 2: `/Users/<USER>/Documents/AQUA/Dev/AQUA/.cursor/rules/development_standards.mdc` - **强制开发标准**
Level 3: `/Users/<USER>/Documents/AQUA/Dev/AQUA/docs/AQUA_GUIDE.md` - 项目架构
Level 4: `/Users/<USER>/Documents/AQUA/Dev/AQUA/docs/FAQ.md` - 操作标准
Level 5: `/Users/<USER>/Documents/AQUA/Dev/AQUA/docs/database/DATA_DICTIONARY.md` - 数据标准

冲突处理： **强制标准优先** → 互补整合 → 场景适配 → 层级优先

---
⚙️ 智能执行策略 + 强制合规检查

任务复杂度自动评估

```python
def assess_task_with_compliance(description):
    # 强制合规检查
    if not compliance_declared():
        raise ComplianceError("必须先声明合规确认")
    
    # 复杂度评估
    if simple_modification(description):    return "RAPID"
    elif complex_architecture(description): return "ARCHITECT"
    else:                                   return "STANDARD"
```

三级执行模式 + 强制合规要求

🚀 **RAPID** (≤1h, <50行)
- ✅ **合规检查**: 声明确认 + 文件位置验证
- 🔍 **复用扫描**: 快速检查现有相似功能
- 📁 **目录验证**: 确保文件创建在正确位置
- 🧪 **最小TDD**: 核心功能测试用例
- 💻 **实现开发**: 基础实现 + 基础测试

🔧 **STANDARD** (1-4h, 50-500行)  
- ✅ **合规检查**: 完整合规声明和分析
- 🔍 **深度复用**: 分析现有代码架构，引用具体位置
- 📁 **严格目录**: tests/, docs/, src/, scripts/ 严格分类
- 🧪 **标准TDD**: 红-绿-重构完整循环
- 💻 **完整开发**: 标准实现 + 完整检验

🏗️ **ARCHITECT** (>4h, >500行)
- ✅ **合规审查**: 架构级合规性评估
- 🔍 **系统复用**: 全面分析现有系统架构
- 📁 **架构目录**: 多层级目录结构规划
- 🧪 **分层TDD**: 单元+集成+系统测试
- 💻 **架构开发**: 分阶段规划 + 全面质量保证

---
🧠 知识获取协议 + 合规验证

```python
def get_solution_with_compliance(query, urgency="normal"):
    # 强制合规检查
    validate_compliance_declaration()
    
    if urgency == "high":
        solution = quick_best_practice(query)
        validate_file_placement(solution)
        return solution

    if memory_reliable(query):
        solution = memory_solution(query)
        check_reuse_opportunities(solution)
        return solution

    if docs_sufficient(query):
        solution = docs_solution(query)
        validate_against_standards(solution)
        return solution

    return simple_clarification(query)
```

文档优先级： **development_standards.mdc** → CLAUDE.md → AQUA_GUIDE.md → FAQ.md → DATA_DICTIONARY.md

---
📋 执行工作流 + 强制合规门禁

**阶段0: 合规声明 [COMPLIANCE]**
```
🚨 强制合规检查清单:
- [ ] 已声明"合规确认：我将优先复用而非新建"
- [ ] 已确认理解目录结构规范 (tests/, docs/, scripts/, src/)
- [ ] 已承诺遵循TDD原则 (先写测试)
- [ ] 已承诺分析现有代码复用机会
```

**阶段1: 评估 + 复用分析 [ASSESS + REUSE]**
- 自动分析任务复杂度
- **强制复用分析**: 搜索现有相似功能
- **具体引用**: 必须引用现有代码文件路径+行号
- 选择执行模式 + 确定合规检查清单

**阶段2: 规划 + 目录验证 [PLAN + VALIDATE]**
- RAPID: 核心文档扫描 + 最小方案 + **文件位置确认**
- STANDARD: 完整分析 + 模块化拆分 + TDD策略 + **目录结构规划**
- ARCHITECT: 系统设计 + 分阶段规划 + 集成策略 + **架构合规审查**

**阶段3: 实施 + TDD强制 [EXECUTE + TDD]**
- **TDD强制检查**: 生产代码前必须先有测试用例
- RAPID: 🔴红→🟢绿 (最小TDD)
- STANDARD: 🔴红→🟢绿→🔵重构 (标准TDD)
- ARCHITECT: 🔴红→🟢绿→🔵重构 (分层TDD: 单元+集成+系统)

**阶段4: 验证 + 最终合规确认 [VERIFY + FINAL_COMPLIANCE]**
- 动态质量检查清单
- **文件位置最终验证**: 确保所有文件在正确目录
- **TDD完整性检查**: 确保测试覆盖率达标
- 代码规范检查 (Black/MyPy/Ruff)
- **最终合规声明**: 确认所有合规要求已满足

---
📁 **强制目录结构规范 (FILE PLACEMENT ENFORCEMENT)**

### 严格分类规则
```python
DIRECTORY_RULES = {
    # 测试文件 - 必须在tests/下
    'test_files': {
        'patterns': ['test_*.py', '*_test.py', '*.test.ts', '*.spec.ts'],
        'allowed_dirs': ['tests/unit/', 'tests/integration/', 'tests/e2e/', 'frontend/tests/'],
        'forbidden_dirs': ['/', 'src/', 'scripts/']
    },
    
    # 文档报告 - 必须在docs/下
    'documentation': {
        'patterns': ['*.md', '*_REPORT.md', '*_GUIDE.md', '*_ANALYSIS.md'],
        'allowed_dirs': ['docs/', 'docs/analysis_reports/', 'docs/tasks/'],
        'forbidden_dirs': ['/', 'src/', 'tests/']
    },
    
    # 工具脚本 - 必须在scripts/下
    'scripts': {
        'patterns': ['*_script.py', '*_cli.py', '*_tool.py'],
        'allowed_dirs': ['scripts/', 'scripts/claude_tools/'],
        'forbidden_dirs': ['/', 'src/', 'tests/']
    },
    
    # 业务代码 - 必须在src/下
    'source_code': {
        'patterns': ['*.py', '*.ts', '*.vue'],
        'allowed_dirs': ['src/', 'frontend/src/'],
        'forbidden_dirs': ['/'] # 根目录禁止业务代码
    }
}
```

### 自动验证函数
```python
def validate_file_placement(file_path: str, file_type: str) -> bool:
    """强制验证文件放置位置"""
    rules = DIRECTORY_RULES.get(file_type)
    if not rules:
        return True
        
    # 检查是否在禁止目录
    for forbidden_dir in rules['forbidden_dirs']:
        if file_path.startswith(forbidden_dir) and file_path != forbidden_dir:
            raise ComplianceError(f"违规: {file_path} 不能放在 {forbidden_dir}")
    
    # 检查是否在允许目录
    is_allowed = any(file_path.startswith(allowed_dir) 
                    for allowed_dir in rules['allowed_dirs'])
    
    if not is_allowed:
        raise ComplianceError(f"违规: {file_path} 必须放在 {rules['allowed_dirs']} 之一")
        
    return True
```

---
✅ 质量门禁 + 强制合规检查

**核心检查 (所有任务) - 强制执行**
- ✅ **合规声明确认**: 必须声明合规确认
- ✅ **文件位置验证**: 所有文件必须在正确目录
- ✅ **复用分析完成**: 必须分析现有代码并引用具体位置
- ✅ **TDD流程遵循**: 生产代码前必须先有测试
- ✅ **文档标准确认**: 遵循权威文档标准
- ✅ **代码规范检查**: Black/MyPy/Ruff全部通过

**场景检查 + 合规增强**
- 数据导入: FAQ.md + DATA_DICTIONARY.md遵循 + **tests/integration/test_*_import.py**
- 版本控制: gitee.md规范遵循 + **scripts/git_hooks/ 规范**
- API开发: RESTful标准 + 文档完整 + **tests/api/ 测试覆盖**
- 数据库操作: DATA_DICTIONARY.md严格遵循 + **tests/unit/test_*_db.py**

**质量分级 + 合规等级**
- MVP: 80%覆盖率 + 基础规范 + **基础合规**
- 生产: 90%覆盖率 + 严格规范 + **完整合规**
- 企业: 95%覆盖率 + 零告警 + **严格合规审查**

---
🎯 执行声明模板 + 强制合规确认

```
🚨 [COMPLIANCE] 合规确认：我将严格遵循AQUA开发标准，优先复用而非新建，遵循TDD原则，确保文件正确放置

🎯 [MODE: {执行模式}]

任务: {任务描述}
策略: {执行策略描述}
复杂度: {预估级别}

📁 文件放置计划:
├── 测试文件 → tests/{unit|integration|e2e}/
├── 文档报告 → docs/{analysis_reports|tasks}/
├── 工具脚本 → scripts/{claude_tools}/
└── 业务代码 → src/{api|data_import|utils}/

🔍 复用分析:
├── 现有相似功能: {具体文件路径}
├── 复用策略: {直接复用|扩展复用|重构复用}
└── 新建理由: {如需新建的详细论证}

✅ 开始执行...

ARCHITECT模式附加确认:
├── 文档已读取 ✅
├── 架构已设计 ✅  
├── 计划已制定 ✅
├── 标准已明确 ✅
└── **合规已验证 ✅**
```

---
🔄 服务承诺 + 合规保证

**专业性 + 合规性**
- 基于权威文档提供准确建议
- **严格遵循开发标准和目录结构**
- 根据复杂度选择最优策略
- **确保所有输出符合合规要求**

**效率性 + 质量性**
- 智能评估自动选择执行模式
- **强制合规检查不影响开发效率**
- 动态适配检查清单和流程
- **TDD原则确保代码质量**

**透明性 + 可追溯性**
- 说明分析过程和决策依据
- **详细记录合规检查过程**
- 明确协作边界和能力范围
- **所有文件创建都有明确理由和位置说明**

---
🚨 **违规处理协议 (VIOLATION HANDLING)**

### 检测机制
AI工具在以下情况必须触发违规检测：
1. 任何文件创建操作前
2. 生产代码编写前  
3. 功能实现完成后
4. 任务交付前

### 处理流程
```
违规检测 → 立即停止操作 → 输出详细违规报告 → 要求重新执行合规流程
```

### 自动修正
```bash
# 检测到文件放置错误时自动提示修正命令
echo "违规检测: test_*.py 文件在根目录"
echo "修正命令: mv test_*.py tests/unit/"
echo "修正命令: mv *_report.md docs/analysis_reports/"
```

---
📋 **AI工具自检清单 (AI SELF-CHECK LIST)**

执行任务前必须确认：
- [ ] 已声明"合规确认：我将优先复用而非新建"
- [ ] 已读取并理解 development_standards.mdc
- [ ] 已分析现有相关代码 (具体文件路径+行号)
- [ ] 已确认文件创建位置符合目录规范
- [ ] 已制定TDD测试计划 (先写测试)
- [ ] 已识别复用机会 (>80%相似度必须复用)

执行任务后必须确认：
- [ ] 所有文件都在正确目录位置
- [ ] 所有生产代码都有对应测试
- [ ] 所有合规要求都已满足
- [ ] 项目结构没有被破坏
- [ ] 开发标准得到严格遵循

---

> **版本**: v2.0 强制合规增强版  
> **生效日期**: 2025-07-26  
> **适用**: 所有AI工具 (Gemini CLI/Claude Code/Cursor)  
> **强制性**: 违规必须停止操作并重新执行合规流程