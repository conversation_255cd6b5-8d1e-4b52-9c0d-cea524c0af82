# AQUA CLI 最佳实践指南

> **适合读者**: 希望高效使用AQUA CLI的用户  
> **涵盖内容**: 推荐工作流、性能优化、安全建议、团队协作  
> **更新时间**: 2025-08-01

## 🌟 最佳实践原则

### 💡 核心理念

1. **简单优于复杂** - 选择最直接的解决方案
2. **一致性优于灵活性** - 建立标准化工作流程
3. **自动化优于手工** - 利用AQUA CLI的智能功能
4. **预防优于修复** - 主动维护和监控

### 🎯 实践分层

```
🏆 专家级实践
├── 自动化工作流
├── 性能调优
├── 安全加固
└── 团队标准化

🚀 进阶实践  
├── 环境管理
├── 配置优化
├── 监控告警
└── 问题预防

📚 基础实践
├── 日常操作
├── 目录组织
├── 命令使用
└── 维护习惯
```

---

## 📚 基础实践

### 🗂️ 项目目录组织

#### 推荐的目录结构

```
AQUA/
├── 📁 data/                    # 数据目录
│   ├── DataCenter/             # 核心数据存储
│   ├── backup/                 # 备份文件
│   └── cache/                  # 缓存数据
├── 📁 config/                  # 配置管理
│   ├── settings.toml           # 主配置文件
│   ├── environments/           # 环境特定配置
│   └── local.toml             # 本地覆盖配置
├── 📁 logs/                    # 日志存储
│   ├── dev/                    # 开发环境日志
│   ├── test/                   # 测试环境日志
│   └── services.log           # 服务日志
├── 📁 scripts/                 # 自动化脚本
├── 📁 docs/                    # 文档资料
└── 📁 .aqua/                   # CLI运行时数据
    ├── cache/                  # CLI缓存
    ├── runtime.json           # 运行时配置
    └── user_preferences.json  # 用户偏好
```

#### 目录权限设置

```bash
# Linux/macOS权限设置
chmod 755 data/ config/ logs/ scripts/
chmod 644 config/*.toml
chmod 600 config/local.toml    # 本地配置保护
chmod 700 .aqua/               # CLI数据保护

# Windows权限设置 (PowerShell)
icacls config\local.toml /inheritance:r /grant:r %USERNAME%:F
icacls .aqua /inheritance:r /grant:r %USERNAME%:F
```

---

### ⚡ 高效命令使用

#### 常用命令组合

```bash
# 🔥 每日启动流程
python aqua.py status && python aqua.py start
# 或简化为
python aqua.py status || python aqua.py init && python aqua.py start

# 🔧 完整环境检查
python aqua.py doctor --auto-fix && python aqua.py status

# 🚀 快速重启服务
python aqua.py stop && python aqua.py start

# 📊 性能监控命令
python aqua.py stats && python aqua.py doctor --metrics
```

#### 命令别名设置

**Linux/macOS (.bashrc/.zshrc)**:
```bash
# AQUA CLI快捷别名
alias aq='python aqua.py'
alias aqs='python aqua.py status'
alias aqi='python aqua.py init'
alias aqst='python aqua.py start'
alias aqsp='python aqua.py stop'
alias aqd='python aqua.py doctor --auto-fix'
alias aqc='python aqua.py config show'

# 环境特定别名
alias aqdev='python aqua.py --env dev'
alias aqtest='python aqua.py --env test'
alias aqprod='python aqua.py --env prod'
```

**Windows 11 (PowerShell Profile)**:
```powershell
# AQUA CLI快捷函数
Function aq { python aqua.py $args }
Function aqs { python aqua.py status }
Function aqi { python aqua.py init }
Function aqst { python aqua.py start }
Function aqsp { python aqua.py stop }
Function aqd { python aqua.py doctor --auto-fix }

# Windows 11 一键启动快捷函数
Function aqua-start { .\aqua.bat }
Function aqua-quick { .\aqua.bat }

# 添加到PowerShell配置文件
Add-Content $PROFILE 'Function aq { python aqua.py $args }'
Add-Content $PROFILE 'Function aqua-start { .\aqua.bat }'
```

#### Windows 11 一键启动最佳实践

```batch
# 1. 推荐的启动方式
.\aqua.bat

# 2. 服务管理最佳实践
# - 使用独立窗口运行服务，便于监控和调试
# - 前端服务窗口标题: "AQUA Frontend - http://localhost:5173"
# - 后端服务窗口标题: "AQUA Backend API - http://127.0.0.1:8000"

# 3. 故障排除快速命令
# 检查服务状态
netstat -an | findstr ":8000"
netstat -an | findstr ":5173"

# 重启服务（关闭服务窗口后重新运行）
.\aqua.bat

# 4. 性能优化建议
# - 确保有足够的磁盘空间用于依赖安装
# - 使用SSD硬盘提升启动速度
# - 关闭不必要的防病毒软件实时扫描
```

#### 环境变量最佳实践

```bash
# 推荐的环境变量设置
export AQUA_ENV=development           # 明确指定环境
export AQUA_DEBUG=false              # 生产环境关闭调试
export PYTHONIOENCODING=utf-8        # 确保编码一致性
export PYTHONUTF8=1                  # UTF-8支持

# 敏感信息通过环境变量管理
export TUSHARE_TOKEN=your_token_here
export AQUA_DB_PASSWORD=secure_password

# 使用.env文件管理本地环境变量
cat > .env << 'EOF'
AQUA_ENV=development
AQUA_DEBUG=true
TUSHARE_TOKEN=your_token_here
EOF

# 加载.env文件 (需要python-dotenv)
pip install python-dotenv
python -c "from dotenv import load_dotenv; load_dotenv()"
```

---

### 🔄 日常维护习惯

#### 每日检查清单

```bash
# ✅ 每日启动检查 (2分钟)
echo "🌅 AQUA每日启动检查..."

# 1. 系统状态检查
python aqua.py status

# 2. 健康检查
python aqua.py doctor --quick

# 3. 磁盘空间检查
df -h data/  # Linux/macOS
dir data\ /-C  # Windows

# 4. 日志大小检查
du -sh logs/  # Linux/macOS
Get-ChildItem logs -Recurse | Measure-Object -Property Length -Sum  # Windows
```

#### 每周维护任务

```bash
#!/bin/bash
# ✅ AQUA每周维护脚本

echo "🔧 AQUA每周维护开始..."

# 1. 完整健康检查
python aqua.py doctor --detailed --auto-fix

# 2. 配置验证
python aqua.py config validate

# 3. 性能统计
python aqua.py stats

# 4. 清理缓存
rm -rf .aqua/cache/*
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +

# 5. 备份重要数据
python aqua.py backup create

echo "✅ 每周维护完成"
```

#### 每月维护任务

```bash
#!/bin/bash
# ✅ AQUA每月维护脚本

echo "📅 AQUA每月维护开始..."

# 1. 依赖更新检查
pip list --outdated

# 2. 配置文件备份
cp config/settings.toml config/settings.toml.backup.$(date +%Y%m%d)

# 3. 日志轮转
find logs/ -name "*.log" -mtime +30 -exec gzip {} \;
find logs/ -name "*.log.gz" -mtime +90 -delete

# 4. 数据库维护
python -c "
import duckdb
conn = duckdb.connect('./data/aqua_dev.duckdb')
conn.execute('VACUUM;')
conn.execute('ANALYZE;')
conn.close()
print('✅ 数据库维护完成')
"

echo "✅ 每月维护完成"
```

---

## 🚀 进阶实践

### 🌍 环境管理策略

#### 环境隔离最佳实践

```toml
# config/environments/development.toml
[dev]
description = "开发环境 - 快速迭代"
debug = true
log_level = "DEBUG"
auto_reload = true

[dev.database]
path = "./data/dev/aqua_dev.duckdb"
memory_limit = "2GB"
backup_enabled = false  # 开发环境不需要频繁备份

[dev.performance]
enable_monitoring = true
memory_limit_mb = 1536
startup_target_seconds = 2
```

```toml
# config/environments/testing.toml  
[test]
description = "测试环境 - 质量保证"
debug = false
log_level = "INFO"
strict_validation = true

[test.database]
path = "./data/test/aqua_test.duckdb"
memory_limit = "4GB"
backup_enabled = true
backup_interval_hours = 12

[test.performance]
enable_monitoring = true
memory_limit_mb = 4096
load_test_data = true
```

```toml
# config/environments/production.toml
[prod]
description = "生产环境 - 稳定运行"
debug = false
log_level = "WARNING"
strict_mode = true
error_notification = true

[prod.database]
path = "./data/prod/aqua_prod.duckdb"
memory_limit = "8GB"
backup_enabled = true
backup_interval_hours = 6
max_backup_files = 24

[prod.security]
encryption_enabled = true
audit_log_enabled = true
access_control_enabled = true
```

#### 环境切换工作流

```bash
# 智能环境切换脚本
#!/bin/bash
switch_env() {
    local target_env=$1
    
    echo "🔄 切换到 $target_env 环境..."
    
    # 1. 停止当前服务
    python aqua.py stop
    
    # 2. 切换环境
    export AQUA_ENV=$target_env
    
    # 3. 验证配置
    python aqua.py config validate
    
    # 4. 健康检查
    python aqua.py doctor --quick
    
    # 5. 启动服务
    python aqua.py start
    
    echo "✅ 环境切换完成: $target_env"
}

# 使用示例
switch_env "test"
switch_env "production"
```

---

### ⚡ 性能优化实践

#### 系统级性能优化

```toml
# config/performance_optimized.toml
[performance]
# 基于系统资源的动态配置
enable_auto_tuning = true

# CPU优化
max_workers = 4                    # = CPU核心数
thread_pool_size = 8              # = CPU核心数 * 2

# 内存优化
memory_limit_mb = 6144            # = 系统内存 * 0.75
gc_threshold = 1000               # 垃圾回收阈值
memory_monitoring = true

# I/O优化
enable_async_io = true
batch_size = 5000                 # 批处理大小
connection_pool_size = 10         # 连接池大小

# 缓存优化
[performance.cache]
l1_enabled = true
l1_max_size = 5000               # L1缓存大小
l1_ttl = 3600                    # L1缓存TTL

l2_enabled = true
l2_max_size_mb = 512             # L2缓存大小
l2_ttl = 7200                    # L2缓存TTL
```

#### 数据库性能调优

```python
# scripts/db_performance_tuning.py
import duckdb
import sys
from pathlib import Path

def optimize_database_performance(db_path: str):
    """数据库性能优化脚本"""
    conn = duckdb.connect(db_path)
    
    try:
        # 设置性能参数
        conn.execute("PRAGMA memory_limit='4GB'")
        conn.execute("PRAGMA threads=4")
        conn.execute("PRAGMA enable_progress_bar=true")
        
        # 优化查询性能
        conn.execute("PRAGMA optimize")
        
        # 创建有用的索引
        tables = conn.execute("SHOW TABLES").fetchall()
        for table in tables:
            table_name = table[0]
            print(f"🔍 优化表: {table_name}")
            
            # 分析表统计信息
            conn.execute(f"ANALYZE {table_name}")
        
        # 压缩数据库
        conn.execute("VACUUM")
        
        print("✅ 数据库性能优化完成")
        
    finally:
        conn.close()

if __name__ == "__main__":
    db_path = sys.argv[1] if len(sys.argv) > 1 else "./data/aqua_dev.duckdb"
    optimize_database_performance(db_path)
```

#### 内存使用监控

```python
# scripts/memory_monitor.py
import psutil
import time
import sys
from datetime import datetime

def monitor_memory_usage(duration_minutes=10):
    """内存使用监控脚本"""
    print(f"📊 开始内存监控 ({duration_minutes} 分钟)")
    
    start_time = time.time()
    max_memory = 0
    measurements = []
    
    while time.time() - start_time < duration_minutes * 60:
        # 获取当前进程内存使用
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        measurements.append({
            'time': datetime.now(),
            'memory_mb': memory_mb,
            'cpu_percent': process.cpu_percent()
        })
        
        max_memory = max(max_memory, memory_mb)
        
        print(f"💾 内存: {memory_mb:.1f} MB | CPU: {process.cpu_percent():.1f}%")
        
        time.sleep(10)  # 每10秒检查一次
    
    # 生成报告
    avg_memory = sum(m['memory_mb'] for m in measurements) / len(measurements)
    
    print(f"\n📈 内存使用报告:")
    print(f"   最大内存: {max_memory:.1f} MB")
    print(f"   平均内存: {avg_memory:.1f} MB")
    print(f"   测量次数: {len(measurements)}")
    
    # 内存使用建议
    if max_memory > 2048:
        print("⚠️  建议: 内存使用较高，考虑增加系统内存或优化配置")
    elif max_memory < 512:
        print("✅ 良好: 内存使用合理")

if __name__ == "__main__":
    duration = int(sys.argv[1]) if len(sys.argv) > 1 else 10
    monitor_memory_usage(duration)
```

---

### 🔧 配置管理进阶

#### 配置版本管理

```bash
# 配置版本控制最佳实践
git add config/settings.toml
git add config/environments/

# 使用有意义的提交信息
git commit -m "config: 增加生产环境内存限制配置

- 生产环境内存限制从6GB增加到8GB
- 启用自动备份，间隔6小时
- 添加安全相关配置项"

# 创建配置变更标签
git tag -a config-v2.1 -m "配置版本2.1 - 生产环境优化"
```

#### 配置模板化

```toml
# config/templates/base.toml
# 基础配置模板
[app]
name = "AQUA"
version = "{{VERSION}}"
environment = "{{ENVIRONMENT}}"

[platform.{{PLATFORM}}.paths]
duckdb_root = "{{DATA_ROOT}}/duckdb/AQUA"
datacenter_dir = "{{DATA_ROOT}}/duckdb/AQUA/DataCenter"

[{{ENVIRONMENT}}.database]
path = "{{DATA_ROOT}}/aqua_{{ENVIRONMENT}}.duckdb"
memory_limit = "{{MEMORY_LIMIT}}"
threads = {{THREADS}}
```

```python
# scripts/config_generator.py
import toml
import sys
from pathlib import Path
from string import Template

def generate_config(template_path: str, output_path: str, variables: dict):
    """从模板生成配置文件"""
    
    # 读取模板
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 替换变量
    template = Template(template_content)
    config_content = template.substitute(variables)
    
    # 写入配置文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    # 验证配置文件
    try:
        toml.load(output_path)
        print(f"✅ 配置文件生成成功: {output_path}")
    except Exception as e:
        print(f"❌ 配置文件格式错误: {e}")
        sys.exit(1)

# 使用示例
variables = {
    'VERSION': '3.3.0',
    'ENVIRONMENT': 'production',
    'PLATFORM': 'unix',
    'DATA_ROOT': '~/Documents/Data',
    'MEMORY_LIMIT': '8GB',
    'THREADS': '8'
}

generate_config('config/templates/base.toml', 'config/prod_generated.toml', variables)
```

---

## 🏆 专家级实践

### 🤖 自动化工作流

#### CI/CD集成

```yaml
# .gitee/workflows/aqua-cli.yml
name: AQUA CLI Test and Deploy

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run AQUA CLI tests
      run: |
        python aqua.py doctor --auto-fix
        python aqua.py status
        python aqua.py config validate
        
    - name: Performance benchmark
      run: |
        python scripts/performance_benchmark.py
        
  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: gitee.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      run: |
        # 部署脚本
        ./scripts/deploy.sh production
```

#### 自动化监控脚本

```python
# scripts/monitoring_daemon.py
import time
import subprocess
import smtplib
from email.mime.text import MIMEText
from datetime import datetime
import logging

class AQUAMonitor:
    def __init__(self):
        self.setup_logging()
        self.alert_threshold = {
            'memory_mb': 4096,
            'cpu_percent': 80,
            'disk_usage_percent': 85,
            'response_time_seconds': 5
        }
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/monitoring.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def check_system_health(self):
        """检查系统健康状态"""
        try:
            # 运行健康检查
            result = subprocess.run(
                ['python', 'aqua.py', 'doctor', '--detailed'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                self.send_alert(f"健康检查失败: {result.stderr}")
                return False
            
            self.logger.info("✅ 系统健康检查通过")
            return True
            
        except subprocess.TimeoutExpired:
            self.send_alert("健康检查超时")
            return False
        except Exception as e:
            self.send_alert(f"健康检查异常: {e}")
            return False
    
    def check_service_status(self):
        """检查服务状态"""
        try:
            result = subprocess.run(
                ['python', 'aqua.py', 'status'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if "Running" not in result.stdout:
                self.send_alert("服务未在运行状态")
                return False
            
            self.logger.info("✅ 服务状态正常")
            return True
            
        except Exception as e:
            self.send_alert(f"服务状态检查失败: {e}")
            return False
    
    def send_alert(self, message: str):
        """发送告警消息"""
        self.logger.error(f"🚨 告警: {message}")
        
        # 这里可以集成邮件、Slack、微信等通知方式
        try:
            # 示例：发送邮件告警
            self.send_email_alert(message)
        except Exception as e:
            self.logger.error(f"告警通知发送失败: {e}")
    
    def send_email_alert(self, message: str):
        """发送邮件告警"""
        # 邮件配置 (应该从环境变量获取)
        smtp_server = "smtp.gmail.com"
        smtp_port = 587
        sender_email = "<EMAIL>"
        sender_password = "your_password"
        receiver_email = "<EMAIL>"
        
        msg = MIMEText(f"AQUA CLI 告警: {message}")
        msg['Subject'] = f"AQUA CLI Alert - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        msg['From'] = sender_email
        msg['To'] = receiver_email
        
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(msg)
    
    def run_monitoring_loop(self, interval_minutes=5):
        """运行监控循环"""
        self.logger.info(f"🔍 启动AQUA监控守护进程 (间隔: {interval_minutes}分钟)")
        
        while True:
            try:
                # 检查系统健康
                health_ok = self.check_system_health()
                
                # 检查服务状态
                service_ok = self.check_service_status()
                
                if health_ok and service_ok:
                    self.logger.info("✅ 所有检查通过")
                
                # 等待下一次检查
                time.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                self.logger.info("监控守护进程已停止")
                break
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(60)  # 异常时短暂等待后重试

if __name__ == "__main__":
    monitor = AQUAMonitor()
    monitor.run_monitoring_loop(interval_minutes=5)
```

---

### 🔒 安全加固实践  

#### 配置文件加密

```python
# scripts/config_encryption.py
import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import toml

class ConfigEncryption:
    def __init__(self, password: str):
        self.password = password.encode()
        self.key = self.derive_key()
        self.cipher = Fernet(self.key)
    
    def derive_key(self) -> bytes:
        """从密码派生加密密钥"""
        salt = b'aqua_cli_salt_2025'  # 生产环境应使用随机salt
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return key
    
    def encrypt_config(self, config_path: str, output_path: str):
        """加密配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = f.read()
        
        encrypted_data = self.cipher.encrypt(config_data.encode())
        
        with open(output_path, 'wb') as f:
            f.write(encrypted_data)
        
        print(f"✅ 配置文件已加密: {output_path}")
    
    def decrypt_config(self, encrypted_path: str, output_path: str):
        """解密配置文件"""
        with open(encrypted_path, 'rb') as f:
            encrypted_data = f.read()
        
        decrypted_data = self.cipher.decrypt(encrypted_data)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(decrypted_data.decode())
        
        print(f"✅ 配置文件已解密: {output_path}")

# 使用示例
if __name__ == "__main__":
    password = input("请输入加密密码: ")
    encryptor = ConfigEncryption(password)
    
    # 加密敏感配置
    encryptor.encrypt_config(
        'config/settings.toml',
        'config/settings.toml.encrypted'
    )
```

#### 访问控制和审计

```python
# scripts/security_audit.py
import os
import stat
import pwd
import grp
from pathlib import Path
import hashlib
import json
from datetime import datetime

class SecurityAuditor:
    def __init__(self):
        self.project_root = Path.cwd()
        self.audit_log = []
    
    def check_file_permissions(self):
        """检查文件权限设置"""
        critical_files = [
            'config/settings.toml',
            'config/local.toml',
            '.env',
            'aqua.py'
        ]
        
        for file_path in critical_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                file_stat = full_path.stat()
                permissions = oct(file_stat.st_mode)[-3:]
                
                if file_path.endswith(('.toml', '.env')) and permissions > '600':
                    self.log_security_issue(
                        'HIGH',
                        f"配置文件权限过于宽松: {file_path} ({permissions})"
                    )
                elif file_path.endswith('.py') and permissions < '644':
                    self.log_security_issue(
                        'MEDIUM',
                        f"执行文件权限不足: {file_path} ({permissions})"
                    )
                else:
                    self.log_security_ok(f"文件权限正常: {file_path} ({permissions})")
    
    def check_sensitive_data_exposure(self):
        """检查敏感数据暴露"""
        sensitive_patterns = [
            'password',
            'token', 
            'secret',
            'key',
            'credential'
        ]
        
        config_files = list(self.project_root.glob('config/**/*.toml'))
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                for pattern in sensitive_patterns:
                    if pattern in content and '${' not in content:
                        self.log_security_issue(
                            'HIGH',
                            f"疑似硬编码敏感信息: {config_file} (包含: {pattern})"
                        )
            except Exception as e:
                self.log_security_issue(
                    'LOW',
                    f"无法读取配置文件: {config_file} ({e})"
                )
    
    def check_environment_variables(self):
        """检查环境变量安全性"""
        required_env_vars = [
            'TUSHARE_TOKEN',
            'AQUA_DB_PASSWORD'
        ]
        
        for env_var in required_env_vars:
            if env_var not in os.environ:
                self.log_security_issue(
                    'MEDIUM',
                    f"缺少必需的环境变量: {env_var}"
                )
            else:
                self.log_security_ok(f"环境变量已设置: {env_var}")
    
    def log_security_issue(self, level: str, message: str):
        """记录安全问题"""
        issue = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'type': 'ISSUE',
            'message': message
        }
        self.audit_log.append(issue)
        print(f"🚨 {level}: {message}")
    
    def log_security_ok(self, message: str):
        """记录安全检查通过"""
        ok = {
            'timestamp': datetime.now().isoformat(),
            'level': 'INFO',
            'type': 'OK',
            'message': message
        }
        self.audit_log.append(ok)
        print(f"✅ {message}")
    
    def generate_audit_report(self):
        """生成安全审计报告"""
        report_path = self.project_root / 'logs' / 'security_audit.json'
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.audit_log, f, indent=2, ensure_ascii=False)
        
        # 统计问题
        issues = [log for log in self.audit_log if log['type'] == 'ISSUE']
        high_issues = [log for log in issues if log['level'] == 'HIGH']
        medium_issues = [log for log in issues if log['level'] == 'MEDIUM']
        
        print(f"\n📊 安全审计报告:")
        print(f"   高危问题: {len(high_issues)}")
        print(f"   中危问题: {len(medium_issues)}")
        print(f"   总检查项: {len(self.audit_log)}")
        print(f"   报告路径: {report_path}")
        
        return len(high_issues) == 0  # 返回是否通过安全检查
    
    def run_full_audit(self):
        """运行完整安全审计"""
        print("🔍 开始安全审计...")
        
        self.check_file_permissions()
        self.check_sensitive_data_exposure()
        self.check_environment_variables()
        
        return self.generate_audit_report()

if __name__ == "__main__":
    auditor = SecurityAuditor()
    passed = auditor.run_full_audit()
    
    if not passed:
        print("\n⚠️ 发现安全问题，请及时处理")
        exit(1)
    else:
        print("\n✅ 安全审计通过")
```

---

### 👥 团队协作标准

#### Git工作流规范

```bash
# .gitignore 推荐配置
# AQUA CLI项目专用

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# AQUA特定文件
.aqua/runtime.json
.aqua/cache/
logs/*.log
data/*.duckdb
data/DataCenter/cache/
*.backup

# 敏感配置
config/local.toml
.env.local
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db
```

#### 代码审查检查清单

```markdown
# AQUA CLI 代码审查检查清单

## 🔍 功能性检查
- [ ] 功能符合需求规范
- [ ] 边界条件处理正确
- [ ] 错误处理机制完善
- [ ] 用户体验友好

## 🏗️ 代码质量检查
- [ ] 代码风格符合Black标准
- [ ] 类型注解完整
- [ ] 函数和类有适当的文档字符串
- [ ] 变量命名清晰有意义

## 🧪 测试覆盖
- [ ] 单元测试覆盖核心逻辑
- [ ] 集成测试覆盖关键工作流
- [ ] 边界条件有对应测试用例
- [ ] 测试用例命名清晰

## 🔒 安全性检查
- [ ] 没有硬编码敏感信息
- [ ] 用户输入得到适当验证
- [ ] 文件操作安全可靠
- [ ] 权限检查机制完善

## ⚡ 性能考虑
- [ ] 算法复杂度合理
- [ ] 内存使用优化
- [ ] 数据库操作高效
- [ ] 缓存策略合理

## 📚 文档更新
- [ ] API文档已更新
- [ ] 用户手册已更新
- [ ] 变更日志已记录
- [ ] 配置示例已更新
```

#### 部署标准化脚本

```bash
#!/bin/bash
# scripts/deploy.sh - 标准化部署脚本

set -euo pipefail

ENVIRONMENT=${1:-development}
VERSION=$(git describe --tags --always)
DEPLOY_TIME=$(date +"%Y-%m-%d %H:%M:%S")

echo "🚀 开始部署 AQUA CLI"
echo "   环境: $ENVIRONMENT"
echo "   版本: $VERSION"
echo "   时间: $DEPLOY_TIME"

# 1. 环境检查
echo "🔍 环境检查..."
python --version
python aqua.py --help > /dev/null

# 2. 依赖更新
echo "📦 更新依赖..."
pip install -r requirements.txt

# 3. 配置验证
echo "⚙️ 配置验证..."
python aqua.py config validate

# 4. 健康检查
echo "🔍 健康检查..."
python aqua.py doctor --auto-fix

# 5. 数据库迁移 (如需要)
echo "💾 数据库检查..."
python scripts/db_migration.py

# 6. 服务重启
echo "🔄 服务重启..."
python aqua.py stop || true
python aqua.py start

# 7. 部署验证
echo "✅ 部署验证..."
sleep 5
python aqua.py status

# 8. 部署通知
echo "📢 部署通知..."
python scripts/notify_deployment.py \
    --environment "$ENVIRONMENT" \
    --version "$VERSION" \
    --status "success"

echo "🎉 部署完成: $ENVIRONMENT ($VERSION)"
```

---

## 📊 监控和度量

### 性能指标监控

```python
# scripts/performance_metrics.py
import time
import psutil
import subprocess
from dataclasses import dataclass
from typing import List, Dict
import json
from datetime import datetime

@dataclass
class PerformanceMetric:
    timestamp: str
    command: str
    execution_time: float
    memory_usage_mb: float
    cpu_percent: float
    success: bool
    error_message: str = ""

class PerformanceTracker:
    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.baseline = self.load_baseline()
    
    def load_baseline(self) -> Dict:
        """加载性能基准数据"""
        try:
            with open('.aqua/performance_baseline.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                'init': {'avg_time': 5.0, 'max_memory': 512},
                'status': {'avg_time': 1.0, 'max_memory': 256},
                'start': {'avg_time': 3.0, 'max_memory': 1024},
                'doctor': {'avg_time': 10.0, 'max_memory': 800}
            }
    
    def measure_command(self, command: List[str]) -> PerformanceMetric:
        """测量命令性能"""
        cmd_name = command[2] if len(command) > 2 else 'unknown'
        
        # 获取初始状态
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            execution_time = time.time() - start_time
            final_memory = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()
            
            metric = PerformanceMetric(
                timestamp=datetime.now().isoformat(),
                command=cmd_name,
                execution_time=execution_time,
                memory_usage_mb=final_memory - initial_memory,
                cpu_percent=cpu_percent,
                success=result.returncode == 0,
                error_message=result.stderr if result.returncode != 0 else ""
            )
            
            self.metrics.append(metric)
            return metric
            
        except subprocess.TimeoutExpired:
            metric = PerformanceMetric(
                timestamp=datetime.now().isoformat(),
                command=cmd_name,
                execution_time=60.0,
                memory_usage_mb=0,
                cpu_percent=0,
                success=False,
                error_message="Command timeout"
            )
            
            self.metrics.append(metric)
            return metric
    
    def run_performance_suite(self):
        """运行性能测试套件"""
        test_commands = [
            ['python', 'aqua.py', 'status'],
            ['python', 'aqua.py', 'config', 'validate'],
            ['python', 'aqua.py', 'doctor', '--quick'],
        ]
        
        print("🏃 运行性能测试套件...")
        
        for command in test_commands:
            print(f"⏱️  测试命令: {' '.join(command[1:])}")
            metric = self.measure_command(command)
            
            # 与基准对比
            if metric.command in self.baseline:
                baseline = self.baseline[metric.command]
                
                if metric.execution_time > baseline['avg_time'] * 1.5:
                    print(f"⚠️  性能警告: 执行时间 {metric.execution_time:.2f}s 超过基准")
                
                if metric.memory_usage_mb > baseline['max_memory']:
                    print(f"⚠️  内存警告: 内存使用 {metric.memory_usage_mb:.1f}MB 超过基准")
            
            print(f"✅ 完成: {metric.execution_time:.2f}s, {metric.memory_usage_mb:.1f}MB")
    
    def generate_performance_report(self):
        """生成性能报告"""
        if not self.metrics:
            print("❌ 没有性能数据")
            return
        
        # 按命令分组统计
        command_stats = {}
        for metric in self.metrics:
            if metric.command not in command_stats:
                command_stats[metric.command] = {
                    'count': 0,
                    'total_time': 0,
                    'max_time': 0,
                    'total_memory': 0,
                    'max_memory': 0,
                    'success_count': 0
                }
            
            stats = command_stats[metric.command]
            stats['count'] += 1
            stats['total_time'] += metric.execution_time
            stats['max_time'] = max(stats['max_time'], metric.execution_time)
            stats['total_memory'] += metric.memory_usage_mb
            stats['max_memory'] = max(stats['max_memory'], metric.memory_usage_mb)
            
            if metric.success:
                stats['success_count'] += 1
        
        # 生成报告
        print("\n📊 性能报告:")
        print("=" * 60)
        
        for command, stats in command_stats.items():
            avg_time = stats['total_time'] / stats['count']
            avg_memory = stats['total_memory'] / stats['count']
            success_rate = (stats['success_count'] / stats['count']) * 100
            
            print(f"\n🔧 {command}:")
            print(f"   执行次数: {stats['count']}")
            print(f"   平均耗时: {avg_time:.2f}s (最大: {stats['max_time']:.2f}s)")
            print(f"   平均内存: {avg_memory:.1f}MB (最大: {stats['max_memory']:.1f}MB)")
            print(f"   成功率: {success_rate:.1f}%")

if __name__ == "__main__":
    tracker = PerformanceTracker()
    tracker.run_performance_suite()
    tracker.generate_performance_report()
```

---

## 💡 专家技巧汇总

### 快捷键和自动补全

```bash
# 为AQUA CLI设置自动补全
eval "$(_AQUA_COMPLETE=source_bash python aqua.py)"

# 或添加到 ~/.bashrc
echo 'eval "$(_AQUA_COMPLETE=source_bash python aqua.py)"' >> ~/.bashrc
```

### 高级配置技巧

```toml
# config/advanced_settings.toml
# 高级用户配置示例

[advanced]
# 启用实验性功能
experimental_features = true

# 性能调优
[advanced.performance]
# 使用内存映射文件
use_memory_mapping = true
# 预加载常用数据
preload_data = true
# 异步I/O
async_io_enabled = true

# 调试选项
[advanced.debug]
# 详细日志
verbose_logging = true
# 性能分析
enable_profiling = true
# 内存跟踪
memory_tracking = true

# 开发者选项
[advanced.developer]
# 热重载
hot_reload = true
# 自动格式化
auto_format = true
# 测试模式
test_mode = false
```

### 性能调优清单

```bash
# 🚀 AQUA CLI 性能调优清单

# 1. 系统级优化
echo "vm.swappiness=10" | sudo tee -a /etc/sysctl.conf  # Linux
ulimit -n 4096  # 增加文件描述符限制

# 2. Python优化
export PYTHONOPTIMIZE=1  # 启用Python优化
export PYTHONDONTWRITEBYTECODE=1  # 禁用.pyc文件

# 3. 数据库优化
python -c "
import duckdb
conn = duckdb.connect('./data/aqua_dev.duckdb')
conn.execute('PRAGMA memory_limit=\"4GB\";')
conn.execute('PRAGMA threads=4;')
conn.execute('PRAGMA optimize;')
conn.close()
"

# 4. 缓存优化
mkdir -p .aqua/cache
export AQUA_CACHE_ENABLED=true

# 5. 内存管理
export MALLOC_TRIM_THRESHOLD_=100000  # glibc内存管理优化
```

---

**通过这些最佳实践，让您的AQUA CLI使用体验达到专家级水准！** 🌟

*文档版本: v3.3.0*  
*最后更新: 2025-08-01*  
*实践等级: 基础→进阶→专家*