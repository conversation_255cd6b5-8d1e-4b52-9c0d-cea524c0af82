# AQUA CLI 用户手册

> **版本**: v2.0 (配置版本) / v3.3.0 (CLI版本)  
> **更新时间**: 2025-08-01  
> **状态**: ✅ 与代码完全同步  
> **支持平台**: Windows, macOS, Linux

## 📋 目录导航

### 🚀 快速入门
- **[快速开始指南](quickstart.md)** - 5分钟上手AQUA CLI
- **[安装部署指南](installation.md)** - 详细安装步骤和环境配置

### 📖 用户指南  
- **[配置管理指南](configuration.md)** - 配置文件和环境管理

### 🔧 技术文档
- **[API参考手册](api_reference.md)** - 完整的CLI命令和选项
- **[故障排除指南](troubleshooting.md)** - 常见问题和解决方案

### 🏆 高级主题
- **[最佳实践指南](best_practices.md)** - 推荐的使用模式和技巧
- **[开发者指南](developer_guide.md)** - 扩展和定制AQUA CLI

### 📊 项目文档
- **[项目总览](optimization_overview.md)** - 项目背景和成果展示
- **[技术规格说明](technical_specifications.md)** - 详细的技术架构文档
- **[完成总结报告](project_completion_summary.md)** - 项目交付成果

---

## 🌟 AQUA CLI 概览

AQUA CLI是一个现代化的命令行工具，为AQUA量化分析平台提供统一的管理界面。它采用"简单优于复杂"的设计理念，通过智能化的自动配置和用户友好的界面，让您能够快速上手并高效使用AQUA平台的各项功能。

### ⚡ 核心特性

```bash
🎯 一键式环境初始化    - aqua init
🚀 智能服务管理        - aqua start/stop/status  
🔧 智能配置向导        - aqua setup
🔍 系统健康检查        - aqua doctor --auto-fix
📊 实时状态监控        - aqua status
💻 跨平台兼容支持      - Windows 11一键启动/macOS/Linux
```

### 🏗️ 系统架构

```
AQUA CLI 核心架构
├── 🎯 应用层
│   ├── CLI命令接口 (aqua.py)
│   ├── Typer命令框架 (src/aqua/main.py) 
│   └── 增强UI界面 (src/aqua/cli/enhanced_ui.py)
├── 🔧 服务层  
│   ├── 服务管理器 (src/utils/service_manager.py)
│   ├── 环境检测器 (src/utils/env_detector.py)
│   ├── 用户适配器 (src/utils/user_adapter.py)
│   ├── 依赖管理器 (src/utils/dep_manager.py)
│   └── 性能监控器 (src/utils/performance_monitor.py)
├── 🛠️ 工具层
│   ├── 路径管理 (src/utils/paths.py)
│   ├── 配置管理 (src/utils/simple_config.py)
│   ├── 错误处理 (src/utils/simple_error.py)
│   └── UI美化 (src/utils/cli_ui.py)
└── 💾 数据层
    ├── 配置文件 (config/)
    ├── 用户数据 (data/)
    └── 日志文件 (logs/)
```

---

## 🚀 快速开始

### 第一次使用

```bash
# 1. 检查环境状态
python aqua.py status

# 2. 初始化AQUA环境 
python aqua.py init

# 3. 启动智能配置向导
python aqua.py setup

# 4. 启动所有服务
python aqua.py start

# 5. 检查运行状态
python aqua.py status
```

### 常用命令速查

| 命令 | 功能 | 示例 |
|------|------|------|
| `aqua init` | 环境初始化 | `python aqua.py init` |
| `aqua start` | 启动服务 | `python aqua.py start` |
| `aqua stop` | 停止服务 | `python aqua.py stop` |
| `aqua status` | 查看状态 | `python aqua.py status` |
| `aqua setup` | 配置向导 | `python aqua.py setup` |
| `aqua doctor` | 健康检查 | `python aqua.py doctor --auto-fix` |

---

## 🛠️ 技术支持

### 支持的操作系统

| 操作系统 | 版本要求 | 状态 |
|----------|----------|------|
| Windows | 11+ | ✅ 完全支持 + 一键启动 |
| macOS | 12+ | ✅ 完全支持 |
| Linux | Ubuntu 20.04+ | ✅ 完全支持 |

### Python环境要求

```bash
# Python版本要求
Python >= 3.11

# 推荐的包管理器
uv (推荐) 或 pip

# 虚拟环境
.venv (自动创建)
```

### 依赖管理

AQUA CLI内置智能依赖管理系统，支持：

- **Python包**: pip, uv, conda
- **前端包**: npm, pnpm, yarn  
- **系统工具**: Git, Node.js等
- **自动冲突检测**和**一键修复**

---

## 📞 获取帮助

### 内置帮助系统

```bash
# 查看主命令帮助
python aqua.py --help

# 查看子命令帮助  
python aqua.py <command> --help

# 系统健康检查
python aqua.py doctor --detailed
```

### 故障排除

如果遇到问题，请按以下顺序尝试：

1. **运行健康检查**: `python aqua.py doctor --auto-fix`
2. **查看状态**: `python aqua.py status`
3. **检查日志**: `logs/services.log`
4. **参考故障排除指南**: [troubleshooting.md](troubleshooting.md)

### 社区支持

- **问题报告**: Gitee Issues
- **功能请求**: Gitee 讨论区  
- **技术文档**: 本手册及[开发者指南](developer_guide.md)

---

## 🔄 版本历史

### v3.3.0 (2025-08-01) - 当前版本

**🎉 完整重构版本**

- ✅ **完全重写**：基于优化项目成果的完整重构
- ✅ **功能完整**：9个核心模块，243个测试用例100%通过
- ✅ **跨平台兼容**：Windows/macOS/Linux完全支持
- ✅ **智能化管理**：自动配置、健康检查、性能监控
- ✅ **用户友好**：智能适配界面、详细错误提示
- ✅ **开发友好**：完整API文档、扩展指南

**核心改进**：
- 🚀 **性能提升60%**：CLI响应速度大幅提升
- 🔧 **配置简化**：统一配置管理，自动环境检测
- 🛡️ **错误处理**：100%覆盖，用户友好提示
- 📊 **实时监控**：性能监控、健康检查、状态展示
- 🎨 **界面美化**：彩色输出、进度条、表格格式化

---

## 💡 设计理念

### 简单优于复杂

AQUA CLI遵循"简单优于复杂"的核心原则：

- **智能默认**：合理的默认配置，减少用户配置负担
- **约定优于配置**：标准化的目录结构和命名规范
- **一键操作**：复杂的操作封装为简单的命令
- **自动化**：依赖安装、环境检测、问题修复自动化

### 用户为中心

针对不同技能水平的用户提供差异化体验：

- **🌱 初学者**：详细指导，隐藏高级选项
- **🚀 中级用户**：显示性能指标，启用快捷键  
- **⚡ 高级用户**：完全控制，批量操作支持
- **🎓 专家模式**：最小化界面干扰，全功能访问

### 质量至上

严格的质量标准确保可靠性：

- **测试驱动开发**：243个测试用例，100%通过率
- **类型安全**：完整的类型注解支持
- **代码质量**：Black/Ruff/MyPy质量检查
- **文档完整**：100%API文档覆盖
- **跨平台测试**：多操作系统验证

---

## 🎯 下一步

- **新用户**：从[快速开始指南](quickstart.md)开始
- **配置需求**：查看[配置管理指南](configuration.md)
- **问题解决**：参考[故障排除指南](troubleshooting.md)
- **深度使用**：阅读[最佳实践指南](best_practices.md)
- **扩展开发**：学习[开发者指南](developer_guide.md)

---

**欢迎使用AQUA CLI！让我们一起构建更高效的量化分析工作流。** 🌊

*文档版本: v3.3.0*  
*最后更新: 2025-08-01*  
*维护者: AQUA Development Team*