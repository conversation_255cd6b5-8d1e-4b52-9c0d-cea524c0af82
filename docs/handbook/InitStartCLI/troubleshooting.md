# AQUA CLI 故障排除指南

> **适合读者**: 遇到问题需要解决方案的用户  
> **覆盖范围**: 安装、配置、运行时常见问题及解决方案  
> **更新时间**: 2025-08-01

## 🚨 紧急故障快速修复

### 30秒快速诊断

```bash
# 1. 运行自动诊断和修复
python aqua.py doctor --auto-fix

# 2. 如果上述命令无法运行，尝试基础检查
python aqua.py status

# 3. 如果仍有问题，检查Python版本
python --version  # 应该显示 Python 3.11.x

# 4. 确认在正确的项目目录
ls -la aqua.py  # 应该存在此文件
```

### 🔧 快速修复工具箱

| 问题类型 | 快速命令 | 适用场景 |
|----------|----------|----------|
| 依赖问题 | `pip install -r requirements.txt` | 模块导入错误 |
| 权限问题 | `chmod +x aqua.py` | Linux/macOS权限错误 |
| 编码问题 | `chcp 65001` (Windows) | 中文显示乱码 |
| 环境问题 | `python aqua.py --env dev status` | 环境配置错误 |
| 服务问题 | `python aqua.py stop && python aqua.py start` | 服务异常 |

---

## 📋 问题分类导航

### 🚀 [安装相关问题](#安装相关问题)
- Python版本不兼容
- 虚拟环境创建失败
- 依赖安装失败
- 权限错误

### ⚙️ [配置相关问题](#配置相关问题)
- 配置文件缺失
- 环境变量未生效
- 路径配置错误
- 编码问题

### 🔄 [运行时问题](#运行时问题)
- 命令无法识别
- 模块导入错误
- 服务启动失败
- 性能问题

### 🖥️ [平台特定问题](#平台特定问题)
- Windows兼容性问题
- macOS权限问题
- Linux系统依赖

### 🔍 [调试技巧](#调试技巧)
- 日志分析
- 错误诊断
- 性能监控

---

## 🚀 安装相关问题

### 问题1: Python版本不兼容

#### 症状表现
```bash
$ python aqua.py --help
Python 3.9.x is not supported. Please upgrade to Python 3.11+
```

#### 根本原因
- 系统Python版本过低 (< 3.11)
- 使用了错误的Python解释器
- 虚拟环境中Python版本不正确

#### 解决方案

**方案A: 升级系统Python**
```bash
# macOS (使用Homebrew)
brew install python@3.11
brew link python@3.11

# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-pip python3.11-venv

# CentOS/RHEL
sudo dnf install python3.11 python3.11-pip

# Windows
# 访问 https://python.org/downloads/ 下载安装
```

**方案B: 使用pyenv管理Python版本**
```bash
# 安装pyenv
curl https://pyenv.run | bash

# 安装Python 3.11
pyenv install 3.11.3
pyenv global 3.11.3

# 验证版本
python --version  # 应该显示 Python 3.11.3
```

**方案C: 创建指定版本的虚拟环境**
```bash
# 使用特定Python版本创建虚拟环境
python3.11 -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate   # Windows

# 验证虚拟环境中的Python版本
python --version
```

#### 验证修复
```bash
python --version  # 应该显示 Python 3.11.x
python aqua.py --help  # 应该正常显示帮助信息
```

---

### 问题2: 虚拟环境创建失败

#### 症状表现
```bash
$ python -m venv .venv
The virtual environment was not created successfully
Error: ensurepip is not available
```

#### 根本原因
- `venv`模块未正确安装
- `ensurepip`模块缺失
- 磁盘空间不足
- 权限限制

#### 解决方案

**方案A: 安装venv模块**
```bash
# Ubuntu/Debian
sudo apt install python3.11-venv python3.11-pip

# CentOS/RHEL
sudo dnf install python3.11-pip python3.11-devel

# macOS (通常不需要额外安装)
# Windows (通常内置)
```

**方案B: 使用virtualenv**
```bash
# 安装virtualenv
pip install virtualenv

# 创建虚拟环境
python -m virtualenv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate   # Windows
```

**方案C: 使用conda**
```bash
# 创建conda环境
conda create -n aqua python=3.11
conda activate aqua

# 安装pip
conda install pip
```

**方案D: 使用uv (推荐)**
```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 创建虚拟环境
uv venv .venv

# 激活虚拟环境
source .venv/bin/activate
```

#### 验证修复
```bash
which python  # 应该指向.venv中的Python
pip --version  # pip应该可用
```

---

### 问题3: 依赖安装失败

#### 症状表现
```bash
$ pip install -r requirements.txt
ERROR: Could not install packages due to an EnvironmentError
[Errno 13] Permission denied: '/usr/local/lib/python3.11/site-packages/'
```

#### 根本原因
- 权限不足
- 网络连接问题
- 包索引服务器问题
- 磁盘空间不足
- 依赖冲突

#### 解决方案

**方案A: 权限问题修复**
```bash
# 确保在虚拟环境中
source .venv/bin/activate

# 升级pip
python -m pip install --upgrade pip

# 重新安装依赖
pip install -r requirements.txt
```

**方案B: 使用国内镜像源**
```bash
# 临时使用镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 永久配置镜像源 (Linux/macOS)
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple/
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF

# Windows
mkdir %APPDATA%\pip
echo [global] > %APPDATA%\pip\pip.ini
echo index-url = https://pypi.tuna.tsinghua.edu.cn/simple/ >> %APPDATA%\pip\pip.ini
```

**方案C: 逐个安装依赖**
```bash
# 关键依赖逐个安装
pip install typer
pip install rich  
pip install colorama
pip install toml

# 验证安装
python -c "import typer, rich, colorama"
```

**方案D: 使用uv加速安装**
```bash
# 使用uv安装依赖
uv pip install -r requirements.txt

# uv通常比pip快10-100倍
```

#### 网络问题诊断
```bash
# 测试网络连接
ping pypi.org
curl -I https://pypi.org/simple/

# 检查防火墙和代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

---

### 问题4: 权限错误

#### 症状表现
```bash
$ python aqua.py init
PermissionError: [Errno 13] Permission denied: 'data'
```

#### 根本原因
- 文件/目录权限不足
- 目录所有者不正确
- SELinux/AppArmor限制
- Windows UAC限制

#### 解决方案

**Linux/macOS方案**:
```bash
# 检查当前权限
ls -la aqua.py
ls -la .

# 修复文件权限
chmod +x aqua.py
chmod 755 .

# 修复目录所有权
sudo chown -R $USER:$USER .

# 创建必要目录
mkdir -p data config logs
chmod 755 data config logs
```

**Windows方案**:
```powershell
# 以管理员身份运行PowerShell

# 修复文件权限
icacls aqua.py /grant %USERNAME%:F

# 修复目录权限  
icacls . /grant %USERNAME%:F /T

# 或者右键点击文件夹 → 属性 → 安全 → 编辑 → 添加用户权限
```

**SELinux问题处理**:
```bash
# 检查SELinux状态
getenforce

# 临时禁用SELinux
sudo setenforce 0

# 或设置正确的SELinux上下文
sudo setsebool -P httpd_can_network_connect 1
```

---

## ⚙️ 配置相关问题

### 问题5: 配置文件缺失或损坏

#### 症状表现
```bash
$ python aqua.py status
ConfigurationError: Configuration file not found: config/settings.toml
```

#### 根本原因
- 配置文件被意外删除
- 配置文件路径错误
- 配置文件格式错误
- Git操作导致文件丢失

#### 解决方案

**方案A: 重新生成默认配置**
```bash
# 检查配置文件位置
python aqua.py config locate

# 从Git恢复配置文件
git checkout config/settings.toml

# 或手动创建配置目录
mkdir -p config
```

**方案B: 配置文件修复**
```bash
# 验证配置文件语法
python -c "
import toml
try:
    with open('config/settings.toml', 'r') as f:
        config = toml.load(f)
    print('✅ 配置文件语法正确')
except Exception as e:
    print(f'❌ 配置文件语法错误: {e}')
"

# 使用配置验证工具
python aqua.py config validate
```

**方案C: 使用备份配置**
```bash
# 查找备份文件
find . -name "settings.toml.bak" -o -name "*.toml"

# 从备份恢复
cp config/settings.toml.bak config/settings.toml
```

---

### 问题6: 环境变量未生效

#### 症状表现
```bash
$ echo $AQUA_ENV
# 没有输出或显示错误值

$ python aqua.py status
Environment variable TUSHARE_TOKEN not found
```

#### 根本原因
- 环境变量未正确设置
- Shell配置文件未重新加载
- 虚拟环境中环境变量丢失
- Windows环境变量设置问题

#### 解决方案

**Linux/macOS方案**:
```bash
# 检查当前环境变量
env | grep AQUA
echo $AQUA_ENV
echo $TUSHARE_TOKEN

# 临时设置环境变量
export AQUA_ENV=development
export TUSHARE_TOKEN=your_token_here

# 永久设置环境变量
echo 'export AQUA_ENV=development' >> ~/.bashrc
echo 'export TUSHARE_TOKEN=your_token_here' >> ~/.bashrc

# 重新加载配置
source ~/.bashrc

# 对于zsh用户
echo 'export AQUA_ENV=development' >> ~/.zshrc
source ~/.zshrc
```

**Windows方案**:
```powershell
# PowerShell中检查环境变量
Get-ChildItem Env: | Where-Object {$_.Name -like "AQUA*"}

# 临时设置
$env:AQUA_ENV = "development"
$env:TUSHARE_TOKEN = "your_token_here"

# 永久设置 (需要管理员权限)
[Environment]::SetEnvironmentVariable("AQUA_ENV", "development", "User")
[Environment]::SetEnvironmentVariable("TUSHARE_TOKEN", "your_token_here", "User")

# 验证设置
echo $env:AQUA_ENV
```

**虚拟环境方案**:
```bash
# 在虚拟环境中设置环境变量
source .venv/bin/activate

# 创建环境变量文件
cat > .venv/bin/activate_env << 'EOF'
export AQUA_ENV=development
export TUSHARE_TOKEN=your_token_here
EOF

# 修改激活脚本
echo 'source .venv/bin/activate_env' >> .venv/bin/activate
```

#### 环境变量验证
```bash
# 验证环境变量
python -c "
import os
print(f'AQUA_ENV: {os.getenv(\"AQUA_ENV\", \"Not Set\")}')
print(f'TUSHARE_TOKEN: {\"Set\" if os.getenv(\"TUSHARE_TOKEN\") else \"Not Set\"}')
"
```

---

### 问题7: 路径配置错误

#### 症状表现
```bash
$ python aqua.py init
PathError: Directory does not exist: /invalid/path/data
FileNotFoundError: No such file or directory: 'D:\Data\duckdb\AQUA'
```

#### 根本原因
- 配置文件中路径错误
- 跨平台路径分隔符问题
- 硬编码路径在不同环境中不存在
- 相对路径基准目录混乱

#### 解决方案

**方案A: 验证和修复路径配置**
```bash
# 检查当前路径配置
python aqua.py config show | grep path

# 验证路径有效性
python aqua.py config validate-paths

# 重置路径为默认值
python aqua.py config reset paths
```

**方案B: 自动创建缺失目录**
```bash
# 让系统自动创建必要目录
python aqua.py config create-dirs

# 手动创建基础目录结构
mkdir -p data config logs
mkdir -p data/DataCenter/backup
mkdir -p data/DataCenter/cache
mkdir -p data/DataCenter/logs
```

**方案C: 平台特定路径修复**
```bash
# Windows路径修复
# 编辑 config/settings.toml
[platform.windows.paths]
duckdb_root = "D:/Data/duckdb/AQUA"
datacenter_dir = "D:/Data/duckdb/AQUA/DataCenter"

# Unix路径修复  
[platform.unix.paths]
duckdb_root = "~/Documents/Data/duckdb/AQUA"
datacenter_dir = "~/Documents/Data/duckdb/AQUA/DataCenter"
```

**方案D: 使用相对路径**
```bash
# 配置使用项目根目录的相对路径
[dev.database]
path = "./data/aqua_dev.duckdb"

[dev.logging]
file_path = "./logs/dev/aqua_dev_{date}.log"
```

---

### 问题8: 编码问题

#### 症状表现
```bash
# Windows上中文显示乱码
鏋佹棤娲讳负鍙戣祫璐?

# 或者
UnicodeDecodeError: 'gbk' codec can't decode byte 0x80
```

#### 根本原因
- Windows默认使用GBK编码
- 终端编码设置不正确
- Python字符串编码问题
- 配置文件编码不统一

#### 解决方案

**Windows编码修复**:
```powershell
# 设置控制台代码页为UTF-8
chcp 65001

# 设置Python UTF-8环境变量
$env:PYTHONUTF8 = 1

# 永久设置环境变量
[Environment]::SetEnvironmentVariable("PYTHONUTF8", "1", "User")

# PowerShell配置文件设置
Add-Content $PROFILE '$env:PYTHONUTF8 = 1; chcp 65001'
```

**使用AQUA CLI内置修复**:
```bash
# Windows兼容性自动设置
python aqua.py windows --setup

# 检查编码设置
python aqua.py windows --check
```

**配置文件编码修复**:
```bash
# 检查文件编码
file -bi config/settings.toml

# 转换为UTF-8编码
iconv -f GBK -t UTF-8 config/settings.toml > config/settings_utf8.toml
mv config/settings_utf8.toml config/settings.toml
```

**Python代码级修复**:
```python
# 在脚本开头添加
import sys
import os
os.environ['PYTHONUTF8'] = '1'

# 或设置标准输出编码
import codecs
sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
```

---

## 🔄 运行时问题

### 问题9: 命令无法识别

#### 症状表现
```bash
$ aqua --help
command not found: aqua

$ python aqua.py --help
python: can't open file 'aqua.py': [Errno 2] No such file or directory
```

#### 根本原因
- 不在正确的项目目录
- aqua.py文件缺失
- Python不在PATH中
- 虚拟环境未激活

#### 解决方案

**方案A: 确认项目目录**
```bash
# 检查当前目录
pwd
ls -la

# 查找aqua.py文件
find . -name "aqua.py" -type f

# 切换到正确目录
cd /path/to/AQUA
```

**方案B: 验证文件完整性**
```bash
# 检查关键文件
ls -la aqua.py src/aqua/main.py config/settings.toml

# 从Git恢复文件
git status
git checkout aqua.py
```

**方案C: Python环境检查**
```bash
# 检查Python可用性
which python
python --version

# 检查虚拟环境
echo $VIRTUAL_ENV
source .venv/bin/activate
```

**方案D: 创建命令别名**
```bash
# Linux/macOS
echo 'alias aqua="python /path/to/AQUA/aqua.py"' >> ~/.bashrc
source ~/.bashrc

# Windows (PowerShell)
New-Alias -Name aqua -Value "python C:\path\to\AQUA\aqua.py"
```

---

### 问题10: 模块导入错误

#### 症状表现
```bash
$ python aqua.py status
ModuleNotFoundError: No module named 'typer'
ImportError: cannot import name 'ConfigLoader' from 'utils.config_loader'
```

#### 根本原因
- 依赖包未安装
- 虚拟环境未激活
- Python路径配置问题
- 包版本冲突

#### 解决方案

**方案A: 依赖包安装**
```bash
# 确保虚拟环境激活
source .venv/bin/activate

# 安装完整依赖
pip install -r requirements.txt

# 或逐个安装关键依赖
pip install typer rich colorama toml
```

**方案B: Python路径检查**
```bash
# 检查Python导入路径
python -c "import sys; print('\n'.join(sys.path))"

# 检查包安装位置
pip show typer

# 验证导入
python -c "import typer; print('✅ typer可用')"
```

**方案C: 强制重新安装**
```bash
# 清理pip缓存
pip cache purge

# 强制重新安装
pip install --force-reinstall -r requirements.txt

# 或使用uv
uv pip install --refresh -r requirements.txt
```

**方案D: 使用基础模式**
```bash
# 如果完整模块不可用，AQUA CLI会自动降级到基础模式
# 查看模块可用性
python -c "
try:
    from utils.config_loader import ConfigLoader
    print('✅ 完整模块可用')
except ImportError:
    print('⚠️ 使用基础模式')
"
```

---

### 问题11: 服务启动失败

#### 症状表现
```bash
$ python aqua.py start
--- ❌ 启动服务失败: ServiceManager initialization failed ---
❌ 无法初始化服务管理器
```

#### 根本原因
- Procfile.dev文件缺失
- 服务配置错误
- 端口冲突
- 权限不足
- 依赖服务未启动

#### 解决方案

**方案A: 检查Procfile.dev**
```bash
# 检查Procfile.dev是否存在
ls -la Procfile.dev

# 查看内容
cat Procfile.dev

# 如果不存在，创建基础版本
cat > Procfile.dev << 'EOF'
web: python -m http.server 8000
worker: python -c "print('Worker process started')"
EOF
```

**方案B: 端口检查**
```bash
# 检查端口占用
lsof -i :8000  # Linux/macOS
netstat -ano | findstr :8000  # Windows

# 杀死占用端口的进程
kill -9 <PID>  # Linux/macOS
taskkill /PID <PID> /F  # Windows
```

**方案C: 权限修复**
```bash
# 确保有执行权限
chmod +x Procfile.dev

# 检查防火墙设置
sudo ufw status  # Ubuntu
sudo firewall-cmd --list-all  # CentOS
```

**方案D: 依赖服务检查**
```bash
# 检查数据库连接
python -c "
import duckdb
try:
    conn = duckdb.connect('./data/aqua_dev.duckdb')
    print('✅ 数据库连接正常')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
"
```

---

### 问题12: 性能问题

#### 症状表现
```bash
$ python aqua.py status
# 响应时间超过10秒
# 内存使用量异常高
# CPU占用率持续100%
```

#### 根本原因
- 大数据集处理
- 内存泄漏
- 无限循环
- 依赖包版本冲突
- 系统资源不足

#### 解决方案

**方案A: 性能监控**
```bash
# 使用内置性能监控
python aqua.py doctor --detailed

# 查看系统资源
top  # Linux/macOS
tasklist  # Windows

# 内存使用分析
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'内存使用: {process.memory_info().rss / 1024 / 1024:.1f} MB')
print(f'CPU使用: {process.cpu_percent()}%')
"
```

**方案B: 配置优化**
```bash
# 编辑配置文件，调整性能参数
vim config/settings.toml

# 开发环境性能优化
[dev.performance]
memory_limit_mb = 1536
max_workers = 2
enable_monitoring = true
```

**方案C: 数据库优化**
```bash
# DuckDB性能优化
python -c "
import duckdb
conn = duckdb.connect('./data/aqua_dev.duckdb')
conn.execute('PRAGMA memory_limit=\"2GB\"')
conn.execute('PRAGMA threads=4')
conn.close()
"
```

**方案D: 环境清理**
```bash
# 清理缓存
rm -rf .aqua/cache/*
rm -rf __pycache__/
find . -name "*.pyc" -delete

# 重启Python进程
python aqua.py stop
python aqua.py start
```

---

## 🖥️ 平台特定问题

### Windows 11专项问题

#### 问题13: aqua.bat一键启动失败

**症状**:
```batch
# 运行 .\aqua.bat 后出现错误
'Reliable' 不是内部或外部命令，也不是可运行的程序
# 或者
[WARNING] Backend API port 8000 not yet available
[WARNING] Frontend port 5173 not yet available
```

**根本原因**:
- 批处理脚本编码问题
- Python依赖缺失（如pytz, six等）
- 虚拟环境未正确创建
- 端口被占用

**解决方案**:
```batch
# 方案A: 手动安装缺失依赖
.venv\Scripts\pip.exe install pytz six watchdog bs4 simplejson websocket-client

# 方案B: 重新创建虚拟环境
rmdir /s .venv
python -m venv .venv
.venv\Scripts\pip.exe install -r requirements.txt

# 方案C: 检查端口占用
netstat -ano | findstr ":8000"
netstat -ano | findstr ":5173"
# 如果有占用，杀死进程: taskkill /PID <PID> /F

# 方案D: 使用PowerShell替代方案
powershell -ExecutionPolicy Bypass -File "Start-AQUA.ps1"
```

#### 问题14: PowerShell执行策略限制

**症状**:
```powershell
PS> python aqua.py init
因为在此系统上禁止运行脚本，所以无法加载文件
```

**解决方案**:
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或临时绕过策略
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# 验证策略设置
Get-ExecutionPolicy -List
```

#### 问题15: Windows路径长度限制

**症状**:
```
FileNotFoundError: [Errno 2] The filename or extension is too long
```

**解决方案**:
```powershell
# 启用长路径支持 (需要管理员权限)
New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LongPathsEnabled" -Value 1 -PropertyType DWORD -Force

# 或使用短路径
python aqua.py config set paths.base_dir "C:\AQUA"
```

### macOS专项问题

#### 问题15: macOS Gatekeeper阻止

**症状**:
```
"python" cannot be opened because the developer cannot be verified
```

**解决方案**:
```bash
# 允许运行未知开发者的应用
sudo spctl --master-disable

# 或对特定文件
xattr -dr com.apple.quarantine aqua.py

# 使用Homebrew安装的Python
brew install python@3.11
which python3.11
```

#### 问题16: SIP (系统完整性保护) 限制

**症状**:
```
Operation not permitted
```

**解决方案**:
```bash
# 检查SIP状态
csrutil status

# 使用用户目录而非系统目录
mkdir -p ~/Applications/AQUA
cd ~/Applications/AQUA

# 使用虚拟环境
python3.11 -m venv .venv
source .venv/bin/activate
```

### Linux专项问题

#### 问题17: 系统依赖缺失

**症状**:
```
ModuleNotFoundError: No module named '_sqlite3'
gcc: command not found
```

**解决方案**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3.11-dev python3.11-venv build-essential libsqlite3-dev

# CentOS/RHEL
sudo dnf install -y python3.11-devel gcc sqlite-devel

# Arch Linux
sudo pacman -S python python-pip base-devel sqlite
```

#### 问题18: SELinux/AppArmor限制

**症状**:
```
audit: type=1400 audit(xxx): avc: denied { write } for pid=xxx
```

**解决方案**:
```bash
# 检查SELinux状态
getenforce
sestatus

# 临时设置为宽松模式
sudo setenforce 0

# 或设置正确的SELinux标签
sudo setsebool -P httpd_can_network_connect 1
sudo chcon -R -t httpd_exec_t /path/to/AQUA/
```

---

## 🔍 调试技巧

### 高级调试方法

#### 日志分析
```bash
# 查看详细日志
tail -f logs/services.log

# 按级别过滤日志
grep "ERROR" logs/services.log
grep "WARNING" logs/services.log

# 实时监控日志
watch -n 1 'tail -n 20 logs/services.log'
```

#### 错误追踪
```bash
# 启用详细错误输出
export PYTHONPATH=$(pwd)/src
export AQUA_DEBUG=true
python aqua.py status

# 使用pdb调试
python -m pdb aqua.py status

# 性能分析
python -m cProfile -o profile.stats aqua.py status
python -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(10)
"
```

#### 网络诊断
```bash
# 测试外部连接
curl -I https://pypi.org/simple/
curl -I https://tushare.pro

# DNS解析测试
nslookup pypi.org
dig pypi.org

# 代理设置检查
echo $HTTP_PROXY
echo $HTTPS_PROXY
echo $NO_PROXY
```

### 常用诊断命令

```bash
# 系统信息收集
python -c "
import sys, platform, os
print(f'Python: {sys.version}')
print(f'Platform: {platform.platform()}')
print(f'Architecture: {platform.architecture()}')
print(f'Current Dir: {os.getcwd()}')
print(f'PYTHONPATH: {sys.path[:3]}...')
"

# 依赖版本检查
pip list | grep -E "(typer|rich|colorama)"

# 磁盘空间检查
df -h .  # Linux/macOS
dir /-C  # Windows

# 内存使用检查
free -h  # Linux
vm_stat  # macOS
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory  # Windows
```

---

## 🆘 获取支持

### 自助服务

1. **运行自动诊断**: `python aqua.py doctor --auto-fix --detailed`
2. **查看系统状态**: `python aqua.py status`
3. **检查配置**: `python aqua.py config validate`
4. **搜索文档**: 使用本指南目录快速定位问题

### 问题报告

当自助解决无效时，请按以下格式报告问题：

```
## 问题描述
[简要描述遇到的问题]

## 复现步骤
1. 执行命令: python aqua.py xxx
2. 观察到: [具体现象]
3. 期望结果: [期望的正常行为]

## 环境信息
- 操作系统: [Windows 10/macOS 12/Ubuntu 20.04]
- Python版本: [python --version的输出]
- AQUA CLI版本: [如果能运行python aqua.py --help]
- 虚拟环境: [是否在虚拟环境中]

## 错误信息
```
[完整的错误输出]
```

## 已尝试的解决方案
- [列出已经尝试过的解决方法]
```

### 社区支持

- **Gitee Issues**: 提交Bug报告和功能请求
- **Gitee 讨论区**: 技术讨论和经验分享
- **文档贡献**: 提交Pull Request改进文档

---

**通过系统性的故障排除，让AQUA CLI稳定高效地为您服务！** 🔧

*文档版本: v3.3.0*  
*最后更新: 2025-08-01*  
*支持范围: 安装、配置、运行时问题*