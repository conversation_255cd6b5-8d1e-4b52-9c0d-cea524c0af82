# AQUA CLI API 参考手册

> **适合读者**: 需要完整命令参考的用户和开发者  
> **覆盖内容**: 所有CLI命令、选项、参数和用法示例  
> **更新时间**: 2025-08-01

## 📋 命令总览

AQUA CLI基于Typer框架构建，提供现代化的命令行界面和完整的帮助系统。

### 🌟 核心命令

| 命令 | 功能 | 状态 | 快速用法 |
|------|------|------|----------|
| `init` | 环境初始化 | ✅ 稳定 | `python aqua.py init` |
| `start` | 启动服务 | ✅ 稳定 | `python aqua.py start` |
| `stop` | 停止服务 | ✅ 稳定 | `python aqua.py stop` |
| `status` | 状态检查 | ✅ 稳定 | `python aqua.py status` |
| `setup` | 配置向导 | ✅ 稳定 | `python aqua.py setup` |

### 🔧 增强命令

| 命令 | 功能 | 状态 | 快速用法 |
|------|------|------|----------|
| `doctor` | 健康检查 | ✅ 稳定 | `python aqua.py doctor --auto-fix` |
| `windows` | Windows兼容 | ✅ 稳定 | `python aqua.py windows --setup` |
| `dev` | 开发工具 | ✅ 稳定 | `python aqua.py dev --setup` |
| `stats` | 使用统计 | ✅ 稳定 | `python aqua.py stats` |

---

## 🚀 主命令 (Global)

### 语法格式

```bash
python aqua.py [OPTIONS] COMMAND [ARGS]...
```

### 全局选项

#### `--env TEXT`
- **用途**: 指定运行环境
- **可选值**: `dev`, `test`, `prod` 
- **默认值**: 从配置文件读取 (`dev`)
- **环境变量**: `AQUA_ENV`
- **示例**:
  ```bash
  python aqua.py --env prod status
  python aqua.py --env test init
  ```

#### `--help`
- **用途**: 显示帮助信息并退出
- **示例**:
  ```bash
  python aqua.py --help
  python aqua.py status --help
  ```

### 执行上下文

每次执行AQUA CLI命令时，系统会自动：

1. **环境检测**: 检测操作系统平台和Python版本
2. **Windows兼容**: 在Windows上自动设置UTF-8编码
3. **状态初始化**: 创建全局状态对象和配置加载器
4. **欢迎信息**: 显示启动横幅和环境信息
5. **模块检查**: 检测CLI模块可用性并相应调整功能

---

## 📦 核心命令详解

### `init` - 环境初始化

#### 命令用途
初始化AQUA项目环境，包括安装依赖、创建目录结构、初始化数据库等。

#### 语法格式
```bash
python aqua.py [--env ENV] init
```

#### 执行流程

**完整模式** (CLI_MODULES_AVAILABLE=True):
1. 安装uv包管理器
2. 创建Python虚拟环境
3. 同步项目依赖
4. 初始化数据库
5. 创建必要目录

**基础模式** (CLI_MODULES_AVAILABLE=False):
1. 创建基本目录结构 (`data/`, `config/`, `logs/`)
2. 基础环境检查

#### 使用示例

```bash
# 基础初始化
python aqua.py init

# 指定环境初始化
python aqua.py --env test init

# 生产环境初始化  
python aqua.py --env prod init
```

#### 预期输出

**完整模式输出**:
```
--- 🚀 开始AQUA环境初始化 (环境: dev) ---
✅ uv包管理器已安装
✅ Python虚拟环境已创建
✅ 依赖同步完成
✅ 数据库初始化完成
--- ✅ AQUA环境初始化成功! ---
```

**基础模式输出**:
```
🚀 开始初始化AQUA项目环境...
✅ 创建目录: data/
✅ 创建目录: config/
✅ 创建目录: logs/
✅ 项目环境初始化完成！
💡 提示: 现在可以运行 'aqua status' 检查系统状态
```

#### 错误处理

| 错误类型 | 处理方式 | 退出码 |
|----------|----------|--------|
| 依赖同步失败 | 显示错误并退出 | 1 |
| 权限不足 | 显示权限错误 | 1 |
| 磁盘空间不足 | 显示存储错误 | 1 |

---

### `start` - 启动服务

#### 命令用途
启动在`Procfile.dev`中定义的所有服务。

#### 语法格式
```bash
python aqua.py [--env ENV] start
```

#### 前置条件
- 完整CLI模块必须可用
- `Procfile.dev`文件必须存在
- 服务未在运行中

#### 执行流程
1. 检查CLI模块可用性
2. 初始化服务管理器
3. 检查服务运行状态
4. 启动后台服务
5. 报告启动结果

#### 使用示例

```bash
# 启动所有服务
python aqua.py start

# 指定环境启动
python aqua.py --env prod start
```

#### 预期输出

**成功启动**:
```
--- 🚀 正在启动所有服务... ---
--- ✅ 服务已在后台启动 ---
使用 'aqua status' 查看状态，'aqua stop' 停止服务。
```

**服务已运行**:
```
--- 🚀 正在启动所有服务... ---
服务已经在运行中。
```

#### 错误处理

| 错误情况 | 错误信息 | 退出码 |
|----------|----------|--------|
| CLI模块不可用 | `❌ 服务管理功能需要完整的CLI模块` | 1 |
| 服务管理器初始化失败 | `❌ 无法初始化服务管理器` | 1 |
| 启动失败 | `--- ❌ 启动服务失败: {error} ---` | 1 |

---

### `stop` - 停止服务

#### 命令用途
停止所有由AQUA CLI启动的服务。

#### 语法格式
```bash
python aqua.py [--env ENV] stop
```

#### 前置条件
- 完整CLI模块必须可用
- 服务管理器可用
- 有服务正在运行

#### 执行流程
1. 检查CLI模块可用性
2. 初始化服务管理器
3. 检查服务运行状态
4. 停止所有服务
5. 确认停止结果

#### 使用示例

```bash
# 停止所有服务
python aqua.py stop

# 指定环境停止
python aqua.py --env test stop
```

#### 预期输出

**成功停止**:
```
--- 🛑 正在停止所有服务... ---
--- ✅ 所有服务已停止 ---
```

**服务未运行**:
```
--- 🛑 正在停止所有服务... ---
服务当前未运行。
```

#### 错误处理

| 错误情况 | 错误信息 | 退出码 |
|----------|----------|--------|
| CLI模块不可用 | `❌ 服务管理功能需要完整的CLI模块` | 1 |
| 服务管理器初始化失败 | `❌ 无法初始化服务管理器` | 1 |

---

### `status` - 状态查看

#### 命令用途
检查并显示服务的当前运行状态和系统信息。

#### 语法格式
```bash
python aqua.py [--env ENV] status
```

#### 输出内容

**完整模式**:
- 系统性能指标 (内存使用、CPU占用、运行时间)
- 服务运行状态
- 增强UI展示

**基础模式**:
- 关键文件存在性检查
- 基础项目结构验证

#### 使用示例

```bash
# 查看当前状态
python aqua.py status

# 不同环境状态
python aqua.py --env prod status
```

#### 预期输出

**完整模式输出**:
```
[增强UI界面显示系统指标]
AQUA 服务状态: Running
```

**基础模式输出**:
```
📊 检查AQUA系统状态...
✅ aqua.py 存在
✅ src/aqua/main.py 存在
✅ config/settings.toml 存在
✅ requirements.txt 存在
✅ 系统状态检查完成
```

#### 检查项目

**文件完整性检查**:
- `aqua.py` - CLI入口文件
- `main.py` - 旧版入口文件
- `src/aqua/main.py` - 新版CLI框架
- `config/settings.toml` - 主配置文件
- `requirements.txt` - 依赖清单

---

### `setup` - 配置向导

#### 命令用途
启动智能配置向导，帮助用户快速完成环境设置。

#### 语法格式
```bash
python aqua.py [--env ENV] setup
```

#### 功能特性

**完整模式**:
- 🧙‍♂️ 智能配置向导
- 🔍 平台环境检测
- 📊 系统资源扫描
- 🌐 网络连接测试
- 📦 依赖完整性检查
- ⚙️ 优化配置生成

**基础模式**:
- Python版本检查
- 项目结构验证
- 基础目录检查

#### 使用示例

```bash
# 启动配置向导
python aqua.py setup

# 指定环境配置
python aqua.py --env test setup
```

#### 预期输出

**完整模式输出**:
```
🧙‍♂️ 智能配置向导 - 5分钟完成环境设置
🔍 检测平台环境...
📊 扫描系统资源...
🌐 测试网络连接...
📦 检查依赖完整性...
⚙️ 生成优化配置...
✅ 配置完成！
```

**基础模式输出**:
```
🔧 启动AQUA项目设置向导...
Python版本: 3.11.3
✅ src/ 目录存在
✅ config/ 目录存在
✅ data/ 目录存在
✅ logs/ 目录存在
⚠️ frontend/ 目录不存在
✅ 项目设置检查完成！
💡 提示: 安装完整模块以获得更多功能
```

---

## 🔧 增强命令详解

### `doctor` - 健康检查

#### 命令用途
系统健康检查，自动诊断和修复常见问题。

#### 语法格式
```bash
python aqua.py [--env ENV] doctor [OPTIONS]
```

#### 选项参数

##### `--auto-fix / --no-auto-fix`
- **用途**: 启用或禁用自动修复
- **默认值**: `True`
- **示例**:
  ```bash
  python aqua.py doctor --auto-fix     # 启用自动修复
  python aqua.py doctor --no-auto-fix  # 仅检查不修复
  ```

##### `--detailed`
- **用途**: 显示详细的诊断信息
- **默认值**: `False`
- **示例**:
  ```bash
  python aqua.py doctor --detailed
  python aqua.py doctor --auto-fix --detailed
  ```

#### 检查项目

1. **Python版本兼容性** - 确保Python 3.11+
2. **虚拟环境配置** - 检查.venv目录和激活状态
3. **项目依赖完整性** - 验证requirements.txt安装状态
4. **数据库连接状态** - 测试DuckDB连接
5. **文件权限设置** - 检查关键文件读写权限
6. **磁盘空间充足性** - 验证可用存储空间
7. **网络连接测试** - 检查外部API可达性

#### 使用示例

```bash
# 完整健康检查并自动修复
python aqua.py doctor --auto-fix

# 详细诊断信息
python aqua.py doctor --detailed

# 仅检查不修复
python aqua.py doctor --no-auto-fix

# 指定环境检查
python aqua.py --env prod doctor --auto-fix
```

#### 预期输出

```
🔍 系统健康检查 - 自动诊断和修复
✅ Python版本兼容性 (3.11.3)
✅ 虚拟环境配置
✅ 项目依赖完整性
✅ 数据库连接状态
✅ 文件权限设置
✅ 磁盘空间充足性
📊 健康评分: 95/100 (优秀)
```

#### 错误处理

- **CLI模块不可用**: 提示安装完整依赖
- **权限不足**: 显示权限解决方案
- **依赖缺失**: 自动安装或提示手动安装

---

### `windows` - Windows兼容性

#### 命令用途
Windows平台深度兼容性管理和优化。

#### 语法格式
```bash
python aqua.py windows [OPTIONS]
```

#### 选项参数

##### `--setup`
- **用途**: 设置Windows兼容性配置
- **功能**: UTF-8编码、PowerShell配置、环境变量
- **示例**: `python aqua.py windows --setup`

##### `--check`
- **用途**: 检查兼容性状态
- **功能**: 验证编码设置、权限检查、路径验证
- **示例**: `python aqua.py windows --check`

##### `--report`
- **用途**: 生成兼容性报告
- **功能**: 详细的系统兼容性分析报告
- **示例**: `python aqua.py windows --report`

##### `--create-service`
- **用途**: 创建Windows服务
- **功能**: 将AQUA CLI注册为Windows服务
- **示例**: `python aqua.py windows --create-service`

#### 兼容性功能

1. **UTF-8编码配置** - 解决中文显示问题
2. **PowerShell优化** - 设置执行策略和编码
3. **路径处理** - Windows路径分隔符适配
4. **权限管理** - 文件权限自动设置
5. **服务集成** - Windows服务注册和管理

#### 使用示例

```bash
# 完整Windows设置
python aqua.py windows --setup

# 检查兼容性状态
python aqua.py windows --check

# 生成兼容性报告
python aqua.py windows --report

# 创建Windows服务
python aqua.py windows --create-service

# 组合使用
python aqua.py windows --setup --check
```

#### 仅Windows平台

此命令仅在Windows平台上提供完整功能，其他平台将显示不适用提示。

---

### `dev` - 开发工具链

#### 命令用途
开发工具链集成，提升开发效率和代码质量。

#### 语法格式
```bash
python aqua.py dev [OPTIONS]
```

#### 选项参数

##### `--setup`
- **用途**: 设置开发环境
- **功能**: 安装开发依赖、配置工具链、设置IDE集成
- **示例**: `python aqua.py dev --setup`

##### `--check`
- **用途**: 运行质量检查
- **功能**: 代码格式检查、类型检查、测试覆盖率
- **示例**: `python aqua.py dev --check`

##### `--metrics`
- **用途**: 显示质量指标
- **功能**: 代码质量评分、技术债务分析
- **示例**: `python aqua.py dev --metrics`

##### `--pre-commit`
- **用途**: 设置pre-commit钩子
- **功能**: 自动化代码质量检查
- **示例**: `python aqua.py dev --pre-commit`

#### 开发工具

1. **代码格式化** - Black, isort
2. **类型检查** - MyPy
3. **代码检查** - Ruff, Flake8
4. **测试框架** - pytest
5. **覆盖率分析** - coverage.py
6. **Pre-commit钩子** - 自动化质量检查

#### 使用示例

```bash
# 设置完整开发环境
python aqua.py dev --setup

# 运行质量检查
python aqua.py dev --check

# 显示代码质量指标
python aqua.py dev --metrics

# 安装pre-commit钩子
python aqua.py dev --pre-commit

# 组合操作
python aqua.py dev --setup --pre-commit
```

---

### `stats` - 使用统计

#### 命令用途
显示AQUA CLI的使用统计信息和历史数据。

#### 语法格式
```bash
python aqua.py stats
```

#### 统计内容

1. **命令使用频率** - 各命令执行次数统计
2. **性能指标** - 响应时间、成功率统计
3. **环境分布** - 不同环境使用占比
4. **错误统计** - 常见错误类型和频率
5. **用户行为分析** - 使用模式分析

#### 使用示例

```bash
# 显示使用统计
python aqua.py stats
```

#### 预期输出

```
📊 AQUA CLI 使用统计

本月命令执行统计:
├── init: 15次 (30%)
├── status: 12次 (24%)
├── start: 10次 (20%)
├── setup: 8次 (16%)
└── stop: 5次 (10%)

性能指标:
├── 平均响应时间: 1.2s
├── 成功率: 96.8%
└── 总执行次数: 50次

环境使用分布:
├── dev: 70%
├── test: 20%
└── prod: 10%
```

---

## 🔧 高级用法

### 环境变量支持

#### 支持的环境变量

| 变量名 | 用途 | 默认值 | 示例 |
|--------|------|--------|------|
| `AQUA_ENV` | 指定运行环境 | `dev` | `export AQUA_ENV=prod` |
| `PYTHONUTF8` | UTF-8编码支持 | `1` | 自动设置 |
| `AQUA_CONFIG_DIR` | 配置目录路径 | `config/` | 自定义配置路径 |
| `AQUA_DEBUG` | 调试模式 | `false` | `export AQUA_DEBUG=true` |

#### 环境变量优先级

1. 命令行参数 (最高优先级)
2. 环境变量
3. 配置文件
4. 默认值 (最低优先级)

### 配置文件集成

#### 主配置文件
- **路径**: `config/settings.toml`
- **格式**: TOML
- **用途**: 系统主配置

#### 环境特定配置
- **开发**: `config/environments/dev.toml`
- **测试**: `config/environments/test.toml`
- **生产**: `config/environments/prod.toml`

### 批量操作

```bash
# 连续命令执行
python aqua.py init && python aqua.py setup && python aqua.py start

# 条件执行
python aqua.py status || python aqua.py doctor --auto-fix

# 环境切换批量操作
for env in dev test prod; do
  python aqua.py --env $env status
done
```

### 脚本集成

#### Bash脚本示例
```bash
#!/bin/bash
# AQUA CLI自动化脚本

set -e

echo "启动AQUA环境初始化..."
python aqua.py init

echo "运行健康检查..."
python aqua.py doctor --auto-fix

echo "启动服务..."
python aqua.py start

echo "验证状态..."
python aqua.py status
```

#### PowerShell脚本示例
```powershell
# AQUA CLI Windows自动化脚本

$ErrorActionPreference = "Stop"

Write-Host "启动AQUA环境初始化..." -ForegroundColor Green
python aqua.py init

Write-Host "设置Windows兼容性..." -ForegroundColor Green
python aqua.py windows --setup

Write-Host "启动服务..." -ForegroundColor Green
python aqua.py start

Write-Host "验证状态..." -ForegroundColor Green
python aqua.py status
```

---

## 🚨 错误码参考

### 退出码含义

| 退出码 | 含义 | 常见情况 |
|--------|------|----------|
| 0 | 成功执行 | 命令正常完成 |
| 1 | 一般错误 | 命令执行失败 |
| 2 | 使用错误 | 命令参数错误 |

### 常见错误信息

#### 模块相关错误
```
❌ 服务管理功能需要完整的CLI模块
💡 请运行: uv pip install -r requirements.txt
```
**解决方案**: 安装完整依赖包

#### 权限相关错误
```
❌ 权限不足，无法创建目录
```
**解决方案**: 使用管理员权限运行或检查目录权限

#### 配置相关错误
```
❌ 配置文件不存在或格式错误
```
**解决方案**: 检查`config/settings.toml`文件

#### 服务相关错误
```
❌ 无法初始化服务管理器
```
**解决方案**: 检查`Procfile.dev`文件是否存在

---

## 📚 相关文档

### 用户文档
- [快速开始指南](quickstart.md) - 5分钟上手
- [配置管理指南](configuration.md) - 详细配置说明
- [故障排除指南](troubleshooting.md) - 常见问题解决

### 技术文档
- [开发者指南](developer_guide.md) - 扩展开发
- [最佳实践指南](best_practices.md) - 使用建议
- [技术规格说明](technical_specifications.md) - 架构设计

### 项目文档
- [项目总览](optimization_overview.md) - 项目背景
- [完成总结报告](project_completion_summary.md) - 交付成果

---

**完整的API参考让您充分发挥AQUA CLI的强大功能！** 🚀

*文档版本: v3.3.0*  
*最后更新: 2025-08-01*  
*API版本: v2.0*