# AQUA CLI 配置管理指南

> **适合读者**: 需要自定义配置的用户  
> **技术等级**: 中级  
> **更新时间**: 2025-08-01

## 📋 配置系统概览

AQUA CLI采用分层配置管理系统，支持多环境、多平台的灵活配置。配置系统基于"约定优于配置"的原则，提供智能默认值，同时支持细粒度的自定义配置。

### 🏗️ 配置架构

```
AQUA 配置层次结构
├── 🎯 环境变量 (最高优先级)
├── 🔧 命令行参数  
├── 📁 用户配置文件 (~/.aqua/config.toml)
├── 📄 项目配置文件 (config/settings.toml)  
└── ⚙️ 默认配置 (内置代码)
```

### 🔄 配置优先级

当多个配置源存在冲突时，系统按以下优先级应用配置：

1. **环境变量** - 最高优先级，用于运行时覆盖
2. **命令行参数** - 临时覆盖，单次运行有效  
3. **用户配置** - 个人偏好设置
4. **项目配置** - 项目默认设置
5. **内置默认** - 系统预设值

---

## 📁 配置文件位置

### 主配置文件

```bash
# 项目主配置文件
config/settings.toml

# 用户个人配置文件  
~/.aqua/config.toml        # Unix (macOS/Linux)
%USERPROFILE%\.aqua\config.toml  # Windows
```

### 环境特定配置

```bash
# 开发环境配置
config/environments/dev.toml

# 测试环境配置  
config/environments/test.toml

# 生产环境配置
config/environments/prod.toml
```

### 自动生成的配置

```bash
# 运行时配置缓存
.aqua/runtime.json

# 用户偏好配置
.aqua/user_preferences.json

# 性能配置文件
.aqua/performance_baseline.json
```

---

## ⚙️ 核心配置详解

### 应用基础配置

```toml
[app]
name = "AQUA"
version = "3.3.0"
description = "Advanced Quantitative Analysis Platform"
default_environment = "dev"
environments = ["dev", "test", "prod"]

# 核心模块
core_modules = [
    "data_warehouse",
    "strategy_drawer", 
    "backtest_workshop",
    "simulator",
    "ai_agent"
]
```

### 平台配置

```toml
[platform]
# 自动平台检测
auto_detect = true
normalize_paths = true
auto_create_dirs = true
path_validation = true

# 支持的平台
supported_platforms = ["windows", "darwin", "linux"]

# 跨平台优化
[platform.personal_dev]
env_vars = ["AQUA_CONFIG_DIR", "AQUA_DATA_DIR", "TUSHARE_TOKEN"]
default_encoding = "utf-8"
auto_path_separator = true
check_write_permissions = true
utf8_path_support = true
```

### 路径配置

#### Windows平台
```toml
[platform.windows.paths]
duckdb_root = "D:/Data/duckdb/AQUA"
datacenter_dir = "D:/Data/duckdb/AQUA/DataCenter"
backup_root = "D:/Data/duckdb/AQUA/DataCenter/backup"
cache_root = "D:/Data/duckdb/AQUA/DataCenter/cache"
logs_root = "D:/Data/duckdb/AQUA/DataCenter/logs"
```

#### Unix平台 (macOS/Linux)
```toml
[platform.unix.paths]
duckdb_root = "~/Documents/Data/duckdb/AQUA"
datacenter_dir = "~/Documents/Data/duckdb/AQUA/DataCenter"  
backup_root = "~/Documents/Data/duckdb/AQUA/DataCenter/backup"
cache_root = "~/Documents/Data/duckdb/AQUA/DataCenter/cache"
logs_root = "~/Documents/Data/duckdb/AQUA/DataCenter/logs"
```

---

## 🌍 环境配置

### 开发环境 (dev)

```toml
[dev]
description = "开发环境配置"
debug = true
log_level = "DEBUG"
enable_profiling = true
hot_reload = true

# 数据库配置
[dev.database]
path = "{datacenter_dir}/aqua_dev.duckdb"
auto_create = true
auto_backup = true
backup_interval_hours = 24
max_backup_files = 7
memory_limit = "2GB"
threads = 4

# 性能配置
[dev.performance]
enable_monitoring = true
memory_limit_mb = 1536
startup_target_seconds = 2
max_workers = 2
personal_dev_mode = true
memory_warning_threshold_mb = 1200
```

### 测试环境 (test)

```toml
[test]
description = "测试环境配置"
debug = false
log_level = "INFO"
load_test_data = true

# 数据库配置
[test.database]
path = "{datacenter_dir}/aqua_test.duckdb"
auto_backup = true
backup_interval_hours = 12
memory_limit = "4GB"
threads = 6

# 性能配置
[test.performance]
memory_limit_mb = 4096
max_workers = 4
chunk_size = 1000
```

### 生产环境 (prod)

```toml
[prod]
description = "生产环境配置"
debug = false
log_level = "WARNING"
strict_mode = true

# 数据库配置
[prod.database]
path = "{datacenter_dir}/aqua_prod.duckdb"
auto_create = false
auto_backup = true
backup_interval_hours = 6
max_backup_files = 24
memory_limit = "8GB"
threads = 8

# 性能配置
[prod.performance]
memory_limit_mb = 8192
max_workers = 8
chunk_size = 5000
```

---

## 🔧 高级配置选项

### 数据源配置

#### CSV数据源
```toml
[datasources.csv]
name = "CSV文件数据源"
enabled = true
file_patterns = ["*.csv", "*.CSV"]
encoding_fallback = ["utf-8", "utf-8-sig", "gbk", "gb2312"]
max_file_size_mb = 500
batch_size = 10000
parallel_processing = true
validation_enabled = true
```

#### Tushare API配置
```toml
[datasources.api.tushare]
name = "Tushare Pro"
enabled = true
base_url = "https://tushare.pro"
token = "${TUSHARE_TOKEN}"
rate_limit = 200
points_per_minute = 2000

# 积分预算控制
[datasources.api.tushare.budget]
total_points = 2100
daily_limit = 50
emergency_reserve = 200

# 预算模式
[datasources.api.tushare.budget.modes]
conservative = { daily_limit = 30, priority = "期货主力合约" }
normal = { daily_limit = 50, priority = "期货>股票>其他" }
aggressive = { daily_limit = 80, priority = "全量数据" }
```

### 缓存配置

```toml
[dev.cache]
enabled = true
type = "memory"  # memory, file, redis
l1_enabled = true
l1_max_size = 1000
l1_ttl = 1800
l2_enabled = true
l2_cache_dir = "{cache_root}/dev"
l2_max_size_mb = 100
l2_ttl = 3600
```

### 日志配置

```toml
[dev.logging]
level = "DEBUG"
console_output = true
file_output = true
file_path = "{logs_root}/dev/aqua_dev_{date}.log"
max_file_size = "10MB"
backup_count = 5
format = "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
```

---

## 🔐 环境变量配置

### 核心环境变量

| 变量名 | 用途 | 示例 |
|--------|------|------|
| `AQUA_ENV` | 指定运行环境 | `development`, `test`, `production` |
| `AQUA_DEBUG` | 启用调试模式 | `true`, `false` |
| `AQUA_LOG_LEVEL` | 设置日志级别 | `DEBUG`, `INFO`, `WARNING`, `ERROR` |
| `TUSHARE_TOKEN` | Tushare API令牌 | `your_token_here` |
| `AQUA_CONFIG_DIR` | 配置目录路径 | `/custom/config/path` |
| `AQUA_DATA_DIR` | 数据目录路径 | `/custom/data/path` |

### 设置环境变量

#### Windows (PowerShell)
```powershell
# 临时设置
$env:AQUA_ENV = "development"
$env:AQUA_DEBUG = "true"
$env:TUSHARE_TOKEN = "your_token_here"

# 永久设置
[Environment]::SetEnvironmentVariable("AQUA_ENV", "development", "User")
[Environment]::SetEnvironmentVariable("TUSHARE_TOKEN", "your_token_here", "User")
```

#### macOS/Linux (Bash)
```bash
# 临时设置
export AQUA_ENV=development
export AQUA_DEBUG=true
export TUSHARE_TOKEN=your_token_here

# 永久设置 (添加到 ~/.bashrc 或 ~/.zshrc)
echo 'export AQUA_ENV=development' >> ~/.bashrc
echo 'export TUSHARE_TOKEN=your_token_here' >> ~/.bashrc
source ~/.bashrc
```

---

## 🎛️ 配置管理命令

### 配置查看

```bash
# 查看当前配置
python aqua.py config show

# 查看特定环境配置
python aqua.py config show --env dev

# 查看配置项
python aqua.py config get database.path

# 验证配置有效性
python aqua.py config validate
```

### 配置修改

```bash
# 设置配置项
python aqua.py config set database.memory_limit "4GB"

# 设置环境特定配置
python aqua.py config set --env test database.threads 6

# 重置配置为默认值
python aqua.py config reset database

# 备份配置
python aqua.py config backup
```

### 配置导入导出

```bash
# 导出配置
python aqua.py config export --output config_backup.toml

# 导入配置
python aqua.py config import config_backup.toml

# 合并配置
python aqua.py config merge additional_config.toml
```

---

## 🔍 配置诊断

### 配置验证

```bash
# 运行配置健康检查
python aqua.py doctor --config

# 检查配置完整性
python aqua.py config check

# 验证路径有效性
python aqua.py config validate-paths

# 测试数据源连接
python aqua.py config test-connections
```

### 配置调试

```bash
# 显示配置加载过程
python aqua.py config debug

# 显示配置优先级
python aqua.py config priority

# 显示生效的配置
python aqua.py config effective

# 配置差异对比
python aqua.py config diff --env dev test
```

---

## 📝 自定义配置示例

### 创建用户配置文件

```bash
# 创建用户配置目录
mkdir -p ~/.aqua

# 创建用户配置文件
cat > ~/.aqua/config.toml << 'EOF'
# 用户个人配置文件

[personal]
name = "个人用户"
skill_level = "intermediate"
preferred_language = "zh-CN"

[personal.ui]
theme = "dark"
show_tips = false
compact_mode = true

[personal.shortcuts]
quick_start = "aqua start"
quick_status = "aqua status"
quick_stop = "aqua stop"

[personal.performance]
memory_limit_mb = 2048
max_workers = 3
EOF
```

### 项目特定配置

```bash
# 创建项目特定配置
cat > config/local.toml << 'EOF'
# 项目本地配置文件

[local]
description = "本地开发配置"

[local.database]
# 使用本地SQLite数据库进行开发
path = "./data/aqua_local.db"
auto_create = true

[local.datasources.csv]
# 本地CSV数据目录
data_dir = "./test_data/csv"
batch_size = 100

[local.logging]
# 详细的本地日志配置
level = "DEBUG"
console_output = true
file_output = false
EOF
```

### 环境特定覆盖

```bash
# 创建开发环境覆盖配置
cat > config/dev_override.toml << 'EOF'
# 开发环境覆盖配置

[dev.database]
# 开发环境使用内存数据库
path = ":memory:"
memory_limit = "1GB"

[dev.performance]
# 开发环境性能优化
enable_monitoring = true
profiling_enabled = true
memory_warning_threshold_mb = 800

[dev.cache]
# 开发环境禁用缓存
enabled = false
EOF
```

---

## 🛠️ 配置工具和实用程序

### 配置生成器

```bash
# 生成基础配置模板
python aqua.py config generate --template basic

# 生成完整配置模板
python aqua.py config generate --template full

# 生成特定环境配置
python aqua.py config generate --env production
```

### 配置迁移工具

```bash
# 从旧版本迁移配置
python aqua.py config migrate --from v2.0 --to v3.3

# 配置格式转换
python aqua.py config convert --from json --to toml

# 配置升级
python aqua.py config upgrade
```

### 配置安全工具

```bash
# 加密敏感配置
python aqua.py config encrypt --key your_encryption_key

# 解密配置
python aqua.py config decrypt --key your_encryption_key

# 配置安全扫描
python aqua.py config security-scan
```

---

## ⚠️ 常见配置问题

### 问题1: 配置文件找不到

**症状:**
```
ConfigurationError: Configuration file not found
```

**解决方案:**
```bash
# 检查配置文件位置
python aqua.py config locate

# 创建默认配置
python aqua.py config init

# 验证配置文件权限
ls -la config/settings.toml
```

### 问题2: 环境变量未生效

**症状:**
```
Environment variable TUSHARE_TOKEN not found
```

**解决方案:**
```bash
# 检查环境变量
python aqua.py config env-check

# 验证环境变量值
echo $TUSHARE_TOKEN  # Unix
echo $env:TUSHARE_TOKEN  # Windows

# 重新加载环境变量
source ~/.bashrc  # Unix
# 重启PowerShell  # Windows
```

### 问题3: 路径配置错误

**症状:**
```
PathError: Directory does not exist: /invalid/path
```

**解决方案:**
```bash
# 验证路径配置
python aqua.py config validate-paths

# 自动创建缺失目录
python aqua.py config create-dirs

# 重置路径为默认值
python aqua.py config reset paths
```

### 问题4: 配置语法错误

**症状:**
```
TOMLDecodeError: Invalid TOML syntax
```

**解决方案:**
```bash
# 验证配置语法
python aqua.py config lint

# 查找语法错误
python aqua.py config syntax-check

# 使用配置编辑器
python aqua.py config edit --validate
```

---

## 🎯 最佳实践

### 1. 配置管理原则

- **分层管理**: 使用不同层次的配置文件
- **环境隔离**: 不同环境使用独立配置
- **敏感信息**: 使用环境变量存储敏感数据
- **版本控制**: 配置文件纳入版本管理
- **文档化**: 添加配置注释和说明

### 2. 安全配置建议

- **不要提交**: 敏感信息不要提交到版本库
- **加密存储**: 对敏感配置进行加密
- **访问控制**: 限制配置文件访问权限
- **定期更新**: 定期更新API密钥等凭据
- **安全扫描**: 定期进行配置安全检查

### 3. 性能配置优化

- **资源限制**: 根据硬件合理设置资源限制
- **缓存配置**: 合理配置缓存策略
- **并发设置**: 根据CPU核心数设置工作线程
- **内存管理**: 设置合理的内存限制和警告阈值
- **监控启用**: 启用性能监控以便优化

---

## 📚 配置参考

### 完整配置模板

查看 `config/settings.toml` 文件获取完整的配置模板和说明。

### 配置API文档

所有配置选项的详细说明请参考：
- [API参考手册](api_reference.md)
- [技术规格文档](technical_specifications.md)

### 社区配置示例

- **Gitee**: 查看社区提供的配置示例
- **Wiki**: 配置最佳实践和常见模式
- **讨论区**: 配置问题和解决方案

---

**通过合理的配置管理，让AQUA CLI更好地适应您的工作环境和需求。** ⚙️

*文档版本: v3.3.0*  
*最后更新: 2025-08-01*  
*配置版本: v2.0*