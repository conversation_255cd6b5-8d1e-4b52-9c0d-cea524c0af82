# AQUA CLI 快速开始指南

> **适合读者**: 首次使用AQUA CLI的用户  
> **预计时间**: 5-10分钟  
> **前置要求**: Python 3.11+，终端基础操作

## 🎯 快速开始流程

### 🚀 Windows 11 一键启动（推荐）

如果您使用Windows 11，可以使用一键启动脚本，跳过复杂的环境配置：

```batch
# 1. 打开命令提示符或PowerShell
# 2. 进入AQUA项目目录
cd D:\AQUA\AQUA

# 3. 运行一键启动脚本
.\aqua.bat

# 脚本会自动完成:
# ✅ 检查和创建虚拟环境
# ✅ 安装所有必需的Python依赖
# ✅ 安装前端依赖
# ✅ 启动后端API服务 (http://127.0.0.1:8000)
# ✅ 启动前端开发服务 (http://localhost:5173)
# ✅ 自动打开浏览器访问前端界面
```

**启动成功标志:**
- 看到 `[SUCCESS] All services are running!`
- 浏览器自动打开 http://localhost:5173
- 任务栏出现"AQUA Backend API"和"AQUA Frontend"窗口

**如果一键启动成功，您可以跳过下面的手动步骤，直接开始使用AQUA！**

---

### 步骤1: 环境准备（手动方式）

```bash
# 检查Python版本（必须3.11+）
python --version
# 输出应该类似: Python 3.11.3

# 进入AQUA项目目录
cd /path/to/AQUA

# 验证项目结构
ls -la
# 应该看到: aqua.py, src/, config/, requirements.txt等
```

### 步骤2: 运行状态检查

```bash
# 检查当前系统状态
python aqua.py status

# 预期输出:
# 📊 检查AQUA系统状态...
# ✅ aqua.py 存在
# ✅ src/aqua/main.py 存在  
# ✅ config/settings.toml 存在
# ✅ requirements.txt 存在
# ✅ 系统状态检查完成
```

### 步骤3: 环境初始化

```bash
# 初始化AQUA环境
python aqua.py init

# 系统会自动执行:
# 🚀 开始初始化AQUA项目环境...
# ✅ 创建目录: data/
# ✅ 创建目录: config/
# ✅ 创建目录: logs/
# ✅ 项目环境初始化完成！
# 💡 提示: 现在可以运行 'aqua status' 检查系统状态
```

### 步骤4: 运行配置向导

```bash
# 启动智能配置向导  
python aqua.py setup

# 在基础模式下会显示:
# 🔧 启动AQUA项目设置向导...
# Python版本: 3.11.3
# ✅ src/ 目录存在
# ✅ config/ 目录存在
# ✅ data/ 目录存在
# ✅ logs/ 目录存在
# ⚠️  frontend/ 目录不存在
# ✅ 项目设置检查完成！
```

### 步骤5: 启动服务（可选）

```bash
# 如果需要启动后台服务
python aqua.py start

# 注意: 需要完整的CLI模块支持
# 如果出现模块缺失错误，请先安装依赖:
# pip install -r requirements.txt
```

### 步骤6: 验证安装

```bash
# 最终状态检查
python aqua.py status

# 查看帮助信息
python aqua.py --help
```

---

## 🔧 常见初始化问题

### 问题1: Python版本不兼容

```bash
# 错误信息:
# Python 3.9.x is not supported

# 解决方案:
# 升级到Python 3.11+
pyenv install 3.11.3
pyenv local 3.11.3
```

### 问题2: 缺少项目文件

```bash
# 错误信息:
# ❌ aqua.py 不存在

# 解决方案:
# 确保在正确的项目根目录
pwd
ls -la aqua.py
```

### 问题3: 模块导入错误

```bash
# 错误信息:
# 错误: 无法导入AQUA CLI应用

# 解决方案:
# 1. 检查虚拟环境
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate   # Windows

# 2. 安装依赖
pip install -r requirements.txt
```

---

## ⚡ 高级初始化（完整功能）

如果需要使用完整的AQUA CLI功能，包括智能配置向导、健康检查等高级功能：

### 1. 安装完整依赖

```bash
# 安装完整依赖包
pip install -r requirements.txt

# 或使用uv（推荐）
uv pip install -r requirements.txt
```

### 2. 运行完整配置向导

```bash
# 启动增强配置向导
python aqua.py setup

# 预期输出:
# 🧙‍♂️ 智能配置向导 - 5分钟完成环境设置
# 🔍 检测平台环境...
# 📊 扫描系统资源...
# 🌐 测试网络连接...
# 📦 检查依赖完整性...
# ⚙️  生成优化配置...
# ✅ 配置完成！
```

### 3. 运行系统健康检查

```bash
# 全面健康检查
python aqua.py doctor --auto-fix

# 预期输出:
# 🔍 系统健康检查 - 自动诊断和修复
# ✅ Python版本兼容性 (3.11.3)
# ✅ 虚拟环境配置
# ✅ 项目依赖完整性  
# ✅ 数据库连接状态
# ✅ 文件权限设置
# ✅ 磁盘空间充足性
# 📊 健康评分: 95/100 (优秀)
```

### 4. 启动完整服务管理

```bash
# 启动所有服务
python aqua.py start
# 预期输出:
# 🚀 正在启动所有服务...
# ✅ 服务已在后台启动

# 查看服务状态
python aqua.py status
# 预期输出:
# 📊 显示增强的状态信息
# AQUA 服务状态: Running
```

---

## 🎉 成功标志

完成以上步骤后，您应该看到：

### ✅ 基础功能可用

```bash
$ python aqua.py --help
Usage: aqua [OPTIONS] COMMAND [ARGS]...

  AQUA 项目统一管理工具 - 现代化、配置驱动的CLI。

Options:
  --env TEXT  指定运行环境 [dev, test, prod]
  --help      Show this message and exit.

Commands:
  init    初始化项目环境
  start   启动在 Procfile.dev 中定义的所有服务
  stop    停止所有由AQUA CLI启动的服务
  status  检查并显示服务的当前运行状态
  setup   智能配置向导
  doctor  系统健康检查
```

### ✅ 项目目录结构

```
AQUA/
├── aqua.py              # CLI入口
├── src/
│   ├── aqua/main.py     # 主命令框架
│   └── utils/           # 核心工具模块
├── config/              # 配置文件
├── data/                # 数据目录
├── logs/                # 日志目录
└── requirements.txt     # 依赖清单
```

### ✅ 核心命令可用

```bash
# 这些命令应该都能正常运行
python aqua.py status    ✅
python aqua.py init      ✅  
python aqua.py setup     ✅
python aqua.py --help    ✅
```

---

## 🚀 下一步

### 对于新用户

1. **了解基础功能**: 阅读[用户指南](user_guide.md)
2. **学习配置管理**: 查看[配置指南](configuration.md)
3. **掌握故障排除**: 参考[故障排除指南](troubleshooting.md)

### 对于高级用户

1. **安装完整功能**: 运行`pip install -r requirements.txt`
2. **探索高级命令**: 尝试`aqua doctor`、Windows 11一键启动等
3. **学习扩展开发**: 阅读[开发者指南](developer_guide.md)

### 对于开发者

1. **了解架构设计**: 查看[技术规格](technical_specifications.md)
2. **学习最佳实践**: 阅读[最佳实践指南](best_practices.md)
3. **参与项目开发**: 参考[开发者指南](developer_guide.md)

---

## 📞 需要帮助？

- **问题排查**: 运行`python aqua.py doctor --auto-fix`
- **查看日志**: 检查`logs/services.log`文件
- **获取帮助**: 查看[故障排除指南](troubleshooting.md)
- **功能请求**: 提交Gitee Issue

---

**恭喜！您已成功完成AQUA CLI的快速配置。** 🎉

*指南版本: v3.3.0*  
*最后更新: 2025-08-01*