# AQUA CLI 开发指南

> **目标读者**: 开发人员、贡献者、系统集成商  
> **技能要求**: Python 3.11+, 软件工程基础  
> **文档版本**: v3.2.0

## 🏗️ 开发环境设置

### 环境要求
```bash
# Python版本
python --version  # 需要 3.11+

# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate   # Windows (或直接使用 .\aqua.bat 一键启动)

# 安装开发依赖
pip install -r requirements-dev.txt
```

### 项目结构
```
AQUA/
├── src/utils/              # 核心工具模块
│   ├── paths.py           # 路径管理
│   ├── simple_config.py   # 配置管理
│   ├── simple_error.py    # 错误处理
│   ├── cli_ui.py          # 界面美化
│   ├── service_manager.py # 服务管理
│   ├── env_detector.py    # 环境检测
│   ├── user_adapter.py    # 用户适配
│   └── dep_manager.py     # 依赖管理
├── tests/unit/             # 单元测试
└── docs/handbook/          # 文档
```

## 🧪 测试驱动开发

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定模块测试
pytest tests/unit/test_paths.py

# 生成覆盖率报告
pytest --cov=src --cov-report=html

# 运行性能测试
pytest --benchmark-only
```

### 编写测试
```python
# 测试示例
def test_paths_creation():
    """测试路径管理器创建"""
    paths = Paths()
    assert paths.ROOT.exists()
    assert paths.HOME.exists()

def test_config_loading():
    """测试配置加载"""
    config = get_config_manager()
    assert config.get_environment() in ["development", "production", "testing"]
```

## 🔧 核心模块开发

### 1. 添加新的工具模块

```python
# src/utils/new_module.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA新功能模块

描述模块功能
版本: 1.0.0
创建时间: 2025-08-01
"""

from .paths import Paths
from .simple_config import get_config_manager
from .simple_error import safe_call, handle_error, ErrorLevel
from .cli_ui import get_ui


class NewFeature:
    """新功能类"""
    
    def __init__(self):
        self.config = get_config_manager()
        self.ui = get_ui()
        self.paths = Paths()
    
    @safe_call
    def main_method(self):
        """主要方法"""
        pass


def main():
    """测试函数"""
    feature = NewFeature()
    feature.main_method()


if __name__ == "__main__":
    main()
```

### 2. 编写对应测试

```python
# tests/unit/test_new_module.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新模块单元测试
"""

import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import sys

sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))
from utils.new_module import NewFeature


class TestNewFeature(unittest.TestCase):
    """新功能测试"""
    
    def setUp(self):
        """测试前准备"""
        pass
    
    def test_initialization(self):
        """测试初始化"""
        feature = NewFeature()
        self.assertIsNotNone(feature.config)
        self.assertIsNotNone(feature.ui)
        self.assertIsNotNone(feature.paths)


if __name__ == '__main__':
    unittest.main(verbosity=2)
```

## 🎨 UI组件开发

### 添加新的UI组件

```python
# 在cli_ui.py中添加新组件
def print_custom_widget(self, data: Dict[str, Any]):
    """自定义UI组件"""
    # 组件实现
    pass
```

### UI主题扩展

```python
# 添加新主题
class CustomTheme:
    """自定义主题"""
    
    def __init__(self):
        self.colors = {
            "primary": "\033[94m",
            "success": "\033[92m", 
            "warning": "\033[93m",
            "error": "\033[91m",
            "reset": "\033[0m"
        }
```

## 📊 服务开发

### 创建新服务

```python
# src/services/new_service.py
import asyncio
from typing import Dict, Any


class NewService:
    """新服务类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.running = False
    
    async def start(self):
        """启动服务"""
        self.running = True
        # 服务启动逻辑
    
    async def stop(self):
        """停止服务"""
        self.running = False
        # 服务停止逻辑
    
    async def health_check(self) -> bool:
        """健康检查"""
        return self.running
```

### 注册服务

```python
# 在service_manager.py中注册
SERVICE_CONFIGS = {
    "new_service": ServiceConfig(
        name="new_service",
        command=["python", "-m", "services.new_service"],
        working_directory=Path.cwd(),
        environment={},
        health_check_url="http://localhost:9000/health"
    )
}
```

## 🔌 扩展开发

### 插件系统（规划中）

```python
# 插件接口
from abc import ABC, abstractmethod


class PluginInterface(ABC):
    """插件接口"""
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> None:
        """初始化插件"""
        pass
    
    @abstractmethod
    def execute(self, context: ExecutionContext) -> PluginResult:
        """执行插件"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理资源"""
        pass


# 插件实现示例
class ExamplePlugin(PluginInterface):
    """示例插件"""
    
    def initialize(self, config: Dict[str, Any]) -> None:
        self.config = config
    
    def execute(self, context: ExecutionContext) -> PluginResult:
        # 插件逻辑
        return PluginResult(success=True, data={})
    
    def cleanup(self) -> None:
        # 清理逻辑
        pass
```

## 📦 代码质量

### 代码格式化
```bash
# 使用black格式化代码
black src/ tests/

# 使用ruff检查代码
ruff check src/ tests/

# 使用mypy类型检查
mypy src/
```

### 提交前检查
```bash
# 安装pre-commit
pip install pre-commit
pre-commit install

# 手动运行检查
pre-commit run --all-files
```

### 配置文件
```toml
# pyproject.toml
[tool.black]
line-length = 100
target-version = ['py311']

[tool.mypy]
python_version = "3.11"
strict = true
ignore_missing_imports = true

[tool.ruff]
select = ["E", "F", "W", "C", "N"]
line-length = 100
target-version = "py311"
```

## 🚀 性能优化

### 性能监控
```python
import time
import functools


def performance_monitor(func):
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        print(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper


@performance_monitor
def expensive_operation():
    """耗时操作"""
    time.sleep(1)
```

### 缓存策略
```python
from functools import lru_cache


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(exist_ok=True)
    
    @lru_cache(maxsize=128)
    def get_cached_result(self, key: str):
        """获取缓存结果"""
        cache_file = self.cache_dir / f"{key}.json"
        if cache_file.exists():
            with open(cache_file) as f:
                return json.load(f)
        return None
    
    def set_cache(self, key: str, data: Any):
        """设置缓存"""
        cache_file = self.cache_dir / f"{key}.json"
        with open(cache_file, 'w') as f:
            json.dump(data, f)
```

## 🐛 调试技巧

### 日志调试
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def debug_function():
    """调试函数"""
    logger.debug("开始执行")
    logger.info("执行信息")
    logger.warning("警告信息")
    logger.error("错误信息")
```

### 断点调试
```python
import pdb

def complex_function():
    """复杂函数"""
    data = {"key": "value"}
    
    # 设置断点
    pdb.set_trace()
    
    # 处理数据
    result = process_data(data)
    return result
```

## 🤝 贡献指南

### 提交流程
1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 编写代码和测试
4. 运行质量检查: `pytest && black . && ruff check .`
5. 提交更改: `git commit -m "feat: add new feature"`
6. 推送分支: `git push origin feature/new-feature`
7. 创建Pull Request

### 提交消息规范
```
feat: 新功能
fix: 错误修复
docs: 文档更新
style: 代码格式化
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 代码审查清单
- [ ] 代码遵循项目规范
- [ ] 包含充分的测试
- [ ] 文档已更新
- [ ] 性能影响已评估
- [ ] 安全考虑已检查

## 📚 API文档

### 自动生成文档
```bash
# 安装文档工具
pip install sphinx sphinx-autodoc-typehints

# 生成文档
sphinx-build -b html docs/ docs/_build/
```

### 文档字符串规范
```python
def example_function(param1: str, param2: int = 0) -> bool:
    """示例函数说明.
    
    Args:
        param1: 字符串参数说明
        param2: 整数参数说明，默认为0
    
    Returns:
        布尔值结果说明
    
    Raises:
        ValueError: 参数无效时抛出
        
    Example:
        >>> example_function("test", 1)
        True
    """
    if not param1:
        raise ValueError("param1不能为空")
    
    return len(param1) > param2
```

## 🔄 持续集成

### Gitee Actions配置
```yaml
# .gitee/workflows/ci.yml
name: CI/CD
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: |
        pytest --cov=src --cov-report=xml
    
    - name: Code quality
      run: |
        black --check .
        ruff check .
        mypy src/
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

---

## 🎯 最佳实践

### 1. 代码组织
- 每个模块职责单一
- 使用清晰的命名
- 避免循环导入
- 合理使用类型注解

### 2. 错误处理
- 使用`safe_call`装饰器
- 提供用户友好的错误消息
- 记录详细的错误日志
- 实现优雅的错误恢复

### 3. 性能考虑
- 避免不必要的计算
- 使用缓存优化重复操作
- 合理使用异步编程
- 监控资源使用情况

### 4. 安全要求
- 验证所有外部输入
- 避免在日志中泄露敏感信息
- 使用最小权限原则
- 定期更新依赖包

---

希望这份开发指南能帮助你更好地参与AQUA项目的开发！如有疑问，请随时联系开发团队。

*文档版本: v3.2.0*  
*最后更新: 2025-08-01*  
*编写者: Claude Code AI Assistant*