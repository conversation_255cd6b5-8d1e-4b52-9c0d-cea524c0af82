# AQUA CLI 安装部署指南

> **适合读者**: 需要详细安装步骤的用户  
> **覆盖平台**: Windows 10+, macOS 12+, Ubuntu 20.04+  
> **更新时间**: 2025-08-01

## 📋 系统要求

### 基础要求

| 组件 | 要求 | 推荐 |
|------|------|------|
| Python | 3.11+ | 3.11.3+ |
| 内存 | 2GB+ | 4GB+ |
| 磁盘空间 | 1GB+ | 2GB+ |
| 网络 | 互联网连接 | 稳定宽带 |

### 操作系统支持

| 操作系统 | 版本 | 状态 | 备注 |
|----------|------|------|------|
| Windows | 11+ | ✅ 完全支持 | PowerShell 5.1+, 一键启动 |
| macOS | 12+ | ✅ 完全支持 | Intel/Apple Silicon |
| Ubuntu | 20.04+ | ✅ 完全支持 | LTS版本 |
| CentOS | 8+ | ✅ 基础支持 | 需手动配置 |
| De<PERSON> | 11+ | ✅ 基础支持 | 需手动配置 |

---

## 🔧 安装步骤

### 方式一: 标准安装（推荐）

#### Step 1: Python环境准备

**Windows:**
```powershell
# 1. 下载并安装Python 3.11+
# 访问: https://python.org/downloads/

# 2. 验证安装
python --version
pip --version

# 3. 配置UTF-8支持
$env:PYTHONUTF8=1
[Environment]::SetEnvironmentVariable("PYTHONUTF8", "1", "User")
```

**macOS:**
```bash
# 使用Homebrew安装Python
brew install python@3.11

# 验证安装
python3.11 --version
pip3.11 --version

# 创建符号链接（可选）
ln -sf /opt/homebrew/bin/python3.11 /usr/local/bin/python
ln -sf /opt/homebrew/bin/pip3.11 /usr/local/bin/pip
```

**Ubuntu/Debian:**
```bash
# 更新包管理器
sudo apt update

# 安装Python 3.11
sudo apt install python3.11 python3.11-pip python3.11-venv

# 验证安装
python3.11 --version
pip3.11 --version

# 创建符号链接（可选）
sudo ln -sf /usr/bin/python3.11 /usr/local/bin/python
sudo ln -sf /usr/bin/pip3.11 /usr/local/bin/pip
```

#### Step 2: 获取AQUA项目

```bash
# 方式1: Git克隆（推荐）
git clone <AQUA-REPO-URL>
cd AQUA

# 方式2: 下载ZIP包
# 下载并解压到指定目录
```

#### Step 3: 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate

# macOS/Linux:
source .venv/bin/activate

# 验证虚拟环境
which python  # 应该显示.venv路径
```

#### Step 4: 安装依赖

```bash
# 升级pip
python -m pip install --upgrade pip

# 安装基础依赖
pip install -r requirements.txt

# 验证安装
python aqua.py --help
```

### 方式二: 使用uv安装（高性能）

```bash
# 1. 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 使用uv创建环境
uv venv .venv

# 3. 激活环境
source .venv/bin/activate  # macOS/Linux
# .venv\Scripts\activate   # Windows

# 4. 安装依赖
uv pip install -r requirements.txt

# 5. 验证安装
python aqua.py --help
```

---

## 🌍 平台特定配置

### Windows 11 配置

#### 一键启动配置（推荐）
```batch
# 1. 使用AQUA一键启动脚本
.\aqua.bat

# 功能特性:
# - 自动检查和创建虚拟环境
# - 自动安装缺失的Python依赖
# - 自动安装前端依赖
# - 启动后端API服务 (端口8000)
# - 启动前端开发服务 (端口5173)
# - 自动打开浏览器访问前端界面
# - 实时服务状态检查
```

#### PowerShell配置
```powershell
# 1. 设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 2. 配置UTF-8编码
chcp 65001
$env:PYTHONUTF8=1

# 3. 添加到PowerShell配置文件
Add-Content $PROFILE '$env:PYTHONUTF8=1'

# 4. 使用PowerShell专用启动脚本（可选）
powershell -ExecutionPolicy Bypass -File "Start-AQUA.ps1"
```

#### 环境变量设置
```powershell
# 设置AQUA环境变量
[Environment]::SetEnvironmentVariable("AQUA_ENV", "development", "User")
[Environment]::SetEnvironmentVariable("PYTHONIOENCODING", "utf-8", "User")
```

#### 依赖自动管理
```batch
# aqua.bat 自动安装以下关键依赖:
# - uvicorn[standard] (ASGI服务器)
# - fastapi (Web框架)
# - h11 (HTTP/1.1协议)
# - pydantic-core (数据验证)
# - pytz (时区处理)
# - six (Python 2/3兼容)
# - watchdog (文件监控)
# - bs4 (HTML解析)
# - simplejson (JSON处理)
# - websocket-client (WebSocket客户端)
```

### macOS 配置

#### 权限设置
```bash
# 确保执行权限
chmod +x aqua.py

# 设置环境变量
echo 'export AQUA_ENV=development' >> ~/.zshrc
source ~/.zshrc
```

#### 系统集成
```bash
# 创建命令别名（可选）
echo 'alias aqua="python /path/to/AQUA/aqua.py"' >> ~/.zshrc
source ~/.zshrc
```

### Linux 配置

#### 系统依赖
```bash
# Ubuntu/Debian
sudo apt install -y git curl build-essential

# CentOS/RHEL
sudo yum install -y git curl gcc python3-devel

# 或者对于较新版本
sudo dnf install -y git curl gcc python3-devel
```

#### 服务配置
```bash
# 创建systemd服务（可选）
sudo cp config/aqua.service /etc/systemd/system/
sudo systemctl enable aqua
sudo systemctl start aqua
```

---

## 🔍 安装验证

### 基础功能验证

```bash
# 1. 检查Python版本
python --version
# 预期: Python 3.11.x

# 2. 检查虚拟环境
which python
# 预期: /path/to/AQUA/.venv/bin/python

# 3. 验证AQUA CLI
python aqua.py --help
# 预期: 显示命令帮助信息

# 4. 运行状态检查
python aqua.py status
# 预期: 显示系统状态

# 5. 测试初始化
python aqua.py init
# 预期: 成功创建必要目录
```

### 高级功能验证

```bash
# 1. 健康检查
python aqua.py doctor --detailed
# 预期: 显示详细的系统健康报告

# 2. 配置向导
python aqua.py setup
# 预期: 启动智能配置向导

# 3. 性能测试
python aqua.py stats
# 预期: 显示使用统计信息

# 4. 平台兼容性（Windows）
python aqua.py windows --check
# 预期: 显示Windows兼容性状态
```

---

## 🚨 常见安装问题

### 问题1: Python版本不兼容

**症状:**
```
Python 3.9.x is not supported. Please upgrade to Python 3.11+
```

**解决方案:**
```bash
# 检查已安装的Python版本
python --version
python3 --version

# 安装Python 3.11
# Windows: 从python.org下载安装
# macOS: brew install python@3.11
# Ubuntu: sudo apt install python3.11

# 更新符号链接
sudo ln -sf /usr/bin/python3.11 /usr/local/bin/python
```

### 问题2: 虚拟环境创建失败

**症状:**
```
The virtual environment was not created successfully
```

**解决方案:**
```bash
# 1. 安装venv模块
pip install virtualenv

# 2. 手动创建虚拟环境
python -m virtualenv .venv

# 3. 或使用conda
conda create -n aqua python=3.11
conda activate aqua
```

### 问题3: 依赖安装失败

**症状:**
```
ERROR: Could not install packages due to an EnvironmentError
```

**解决方案:**
```bash
# 1. 升级pip
python -m pip install --upgrade pip

# 2. 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 3. 逐个安装依赖
pip install typer rich colorama
```

### 问题4: 模块导入错误

**症状:**
```
ModuleNotFoundError: No module named 'xxx'
```

**解决方案:**
```bash
# 1. 检查虚拟环境激活
which python
echo $VIRTUAL_ENV

# 2. 重新安装依赖
pip install -r requirements.txt

# 3. 检查Python路径
python -c "import sys; print(sys.path)"
```

### 问题5: 权限错误

**症状:**
```
PermissionError: [Errno 13] Permission denied
```

**解决方案:**
```bash
# Linux/macOS
chmod +x aqua.py
sudo chown -R $USER:$USER .

# Windows (以管理员身份运行PowerShell)
icacls aqua.py /grant %USERNAME%:F
```

---

## 🎯 安装后配置

### 基础配置

```bash
# 1. 运行初始化
python aqua.py init

# 2. 配置环境
python aqua.py setup

# 3. 健康检查
python aqua.py doctor --auto-fix
```

### 高级配置

```bash
# 1. 配置开发环境
python aqua.py dev --setup

# 2. 设置pre-commit钩子
python aqua.py dev --pre-commit

# 3. Windows深度配置
python aqua.py windows --setup  # 仅Windows
```

### 性能优化

```bash
# 1. 基准测试
python -c "
from src.utils.performance_monitor import PerformanceMonitor
monitor = PerformanceMonitor()
results = monitor.benchmark_system()
print(f'性能评分: {results[\"overall_performance_score\"]:.1f}/100')
"

# 2. 缓存配置
mkdir -p .aqua/cache
echo 'cache_enabled=true' >> .aqua/config
```

---

## 📦 部署选项

### 开发环境部署

```bash
# 1. 克隆项目
git clone <repo> aqua-dev
cd aqua-dev

# 2. 开发配置
python -m venv .venv
source .venv/bin/activate
pip install -r requirements-dev.txt

# 3. 开发工具设置
python aqua.py dev --setup
```

### 生产环境部署

```bash
# 1. 最小化安装
pip install -r requirements.txt --no-dev

# 2. 生产配置
export AQUA_ENV=production
python aqua.py init

# 3. 服务配置
python aqua.py start
```

### Docker部署（可选）

```dockerfile
# Dockerfile示例
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "aqua.py", "start"]
```

```bash
# 构建和运行
docker build -t aqua-cli .
docker run -it aqua-cli
```

---

## ✅ 安装完成检查清单

### 必须项目

- [ ] Python 3.11+ 安装成功
- [ ] 虚拟环境创建并激活
- [ ] 基础依赖安装完成
- [ ] `python aqua.py --help` 正常显示
- [ ] `python aqua.py status` 正常运行
- [ ] `python aqua.py init` 成功执行

### 推荐项目

- [ ] 完整依赖安装（包含高级功能）
- [ ] 健康检查通过 (`python aqua.py doctor`)
- [ ] 配置向导完成 (`python aqua.py setup`)
- [ ] 平台特定配置完成
- [ ] 性能基准测试完成

### 可选项目

- [ ] 开发工具链配置
- [ ] Pre-commit钩子安装
- [ ] Windows服务注册（Windows）
- [ ] 系统集成配置
- [ ] Docker镜像构建

---

## 🆘 获取支持

### 自助排查

1. **运行诊断**: `python aqua.py doctor --detailed`
2. **查看日志**: `logs/services.log`
3. **检查配置**: `config/settings.toml`

### 社区支持

- **问题报告**: Gitee Issues
- **讨论交流**: Gitee 讨论区
- **文档反馈**: 提交Pull Request

### 企业支持

- **技术咨询**: 联系开发团队
- **定制开发**: 商业支持选项
- **培训服务**: 团队培训方案

---

**安装完成！开始使用AQUA CLI构建高效的量化分析工作流。** 🎉

*文档版本: v3.3.0*  
*最后更新: 2025-08-01*  
*适用版本: AQUA CLI v3.3.0+*