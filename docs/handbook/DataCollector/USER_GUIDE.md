# AQUA数据采集完整使用指南 v2.0

**集成DataProcessor数据处理器的全新版本**

## 📋 目录
- [安装配置](#安装配置)
- [DataProcessor数据处理器](#dataprocessor数据处理器)
- [基础使用](#基础使用)
- [增量采集功能](#增量采集功能)
- [高级功能](#高级功能)
- [配置文件](#配置文件)
- [数据查看](#数据查看)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

---

## 安装配置

### 系统要求
- **Python**: 3.11+
- **操作系统**: macOS 10.15+ 或 Windows 10+
- **内存**: 建议8GB+ (DataProcessor需要更多内存用于数据处理)
- **磁盘**: 至少1GB可用空间

### 安装步骤

#### 方式一：Git克隆（推荐）
```bash
git clone https://github.com/your-org/AQUA.git
cd AQUA
pip install -r requirements.txt
```

#### 方式二：直接下载
1. 下载项目压缩包并解压
2. 进入项目目录
3. 运行 `pip install -r requirements.txt`

### 多数据源配置

AQUA数据采集工具采用"Multi-Source Real Data Only"设计理念，支持三种真实数据源，100%使用真实数据，不提供任何模拟数据：

#### TUSHARE数据源配置（Web API）
```bash
# 临时设置
export TUSHARE_TOKEN="your_token_here"

# 永久设置（macOS/Linux）
echo 'export TUSHARE_TOKEN="your_token_here"' >> ~/.bashrc
source ~/.bashrc

# 永久设置（Windows）
setx TUSHARE_TOKEN "your_token_here"
```

**获取TUSHARE Token**：
1. 访问 [TUSHARE官网](https://tushare.pro/) 注册账户
2. 在个人中心获取Token
3. 按上述方法配置到环境变量

#### CSV数据源配置（本地文件）
```bash
# 设置CSV数据路径（可选，默认为data/csv/）
export CSV_DATA_PATH="/path/to/your/csv/data/"
```

**CSV数据源特性**：
- 支持FROMC2C格式期货数据
- 支持股票历史数据导入
- 本地文件，无网络依赖
- **支持DataProcessor自动清洗和去重**

#### MySQL数据源配置（数据库）
```bash
# 配置MySQL连接参数
export MYSQL_HOST="localhost"
export MYSQL_PORT="3306"  # 可选，默认3306
export MYSQL_USER="your_username"
export MYSQL_PASSWORD="your_password"
export MYSQL_DATABASE="your_database"
```

**MySQL数据源特性**：
- 支持现有MySQL数据库迁移
- 数据库到DuckDB的无缝转换
- 支持多种金融数据表结构
- **集成DataProcessor数据质量控制**

### 验证安装
```bash
python -m src.cli.main --help

# 看到帮助信息说明安装成功
```

---

## DataProcessor数据处理器

**🆕 AQUA v2.0 重大更新：集成DataProcessor数据处理器**

DataProcessor是AQUA系统的数据守门员，为所有数据源提供统一的数据清洗、去重和质量控制功能。

### 核心特性

#### ✨ 自动数据清洗
- **字段验证**: 自动检查数据类型、范围、格式
- **跨字段验证**: 验证OHLC价格一致性、交易时间逻辑
- **智能修复**: 自动修复常见数据问题（如时区转换）
- **错误分类**: 将错误数据分类为可修复、需隔离、应丢弃

#### 🔄 智能去重
- **内部去重**: 去除单次采集中的重复记录
- **数据库去重**: 避免重复入库已存在的数据
- **数据源优先级**: TUSHARE > FROMC2C > MySQL > AKSHARE
- **主键管理**: 基于业务逻辑的复合主键去重

#### 📊 实时质量评分
- **质量指标**: 实时计算数据清洁度评分 (0-1)
- **处理统计**: 详细的清洗、去重、错误统计
- **性能监控**: 处理速度、处理时间统计
- **错误详情**: 分类错误报告和修复建议

#### ⚡ 高性能处理
- **智能引擎**: 根据数据量自动选择Polars或DuckDB引擎
- **处理能力**: 超过200万条/秒的处理能力
- **内存优化**: 大数据集分批处理，避免内存溢出
- **并发支持**: 支持多任务并发数据处理

### 配置管理

DataProcessor配置集成在 `config/settings.toml` 中：

```toml
# DataProcessor基础配置
[data_processor]
enabled = true                          # 启用DataProcessor
fail_safe_mode = true                   # 失败安全模式
show_quality_stats = true               # 显示质量统计
data_source_priority = ["TUSHARE", "FROMC2C", "MYSQL", "AKSHARE"]
small_data_limit = 100000               # 小数据集阈值
large_data_limit = 1000000              # 大数据集阈值
min_quality_score = 0.80                # 最低质量要求

# 环境特定配置
[dev.data_processor]
show_quality_stats = true               # 开发环境显示统计
log_level = "DEBUG"

[prod.data_processor] 
show_quality_stats = false              # 生产环境静默模式
log_level = "WARNING"
```

### 环境变量控制（向后兼容）

```bash
# 主开关
export AQUA_ENABLE_DATA_PROCESSOR=true

# 失败安全模式
export AQUA_DATA_PROCESSOR_FAIL_SAFE=true

# 质量统计显示
export AQUA_SHOW_QUALITY_STATS=true
```

---

## 基础使用

### 命令格式
```bash
python -m src.cli.main collect <标的代码> --source <数据源> [选项]
```

### 🆕 带DataProcessor的数据采集

#### 数据源能力检查（推荐先检查）
```bash
# 检查TUSHARE数据源能力（包含DataProcessor状态）
python -m src.cli.main collect --check-capabilities --source tushare

# 输出示例：
# ✅ TUSHARE数据源状态: 正常
# ✅ DataProcessor状态: 已启用
# 📊 预期数据质量提升: 15-25%
# ⚡ 处理性能: 200万+条/秒
```

#### 预览模式（推荐先试试）
```bash
# TUSHARE数据源预览（含质量预估）
python -m src.cli.main collect 000001.SZ --source tushare --preview

# 输出示例：
# 📊 数据预览 - DataProcessor集成版
# 标的代码: 000001.SZ
# 数据源: TUSHARE (100%真实数据)
# 预计原始数据: 250行
# 预计清洁数据: 245行 (98%质量评分)
# 预计去重后: 240行
# 预计处理时间: <0.1秒
```

#### 实际采集（自动数据处理）
```bash
# TUSHARE数据源采集（自动清洗去重）
python -m src.cli.main collect 000001.SZ --source tushare

# 成功输出：
# 🔄 启动DataProcessor数据处理...
# 📈 原始数据: 250条
# 🧹 数据清洗: 去除5条脏数据
# 🔄 数据去重: 去除5条重复数据  
# 📊 数据质量评分: 0.96
# ⚡ 处理速度: 2,268,540条/秒
# ✅ 清洁数据: 240条 -> 保存到数据库
# 💾 保存位置: data/aqua_real_data.duckdb
# ⏱️  总用时: <1秒
```

#### 多数据源采集对比
```bash
# CSV数据源（期货数据）
python -m src.cli.main collect futures_data --source csv
# 🔄 DataProcessor优化: FROMC2C格式自动标准化
# 📊 质量提升: 原始85% -> 清洗后95%

# MySQL数据源（历史数据迁移）
python -m src.cli.main collect stock_table --source mysql  
# 🔄 DataProcessor优化: 数据库字段自动映射
# 🧹 去重优化: 避免重复迁移已存在数据
```

### 多只股票采集（批量处理优化）
```bash
# 批量采集（DataProcessor自动批处理优化）
python -m src.cli.main collect 000001.SZ,600036.SH,000002.SZ

# 输出示例：
# 🔄 批量处理模式: 3只股票
# 📊 预计处理: 750条原始数据
# ⚡ DataProcessor并行处理中...
# ✅ 000001.SZ: 240条清洁数据 (质量评分: 0.96)
# ✅ 600036.SH: 238条清洁数据 (质量评分: 0.95)
# ✅ 000002.SZ: 245条清洁数据 (质量评分: 0.98)
# 📈 总计: 723条清洁数据入库
# ⏱️  总用时: 1.2秒
```

---

## 增量采集功能

### 🚀 增量采集概述

增量采集是AQUA数据采集CLI的核心功能之一，它能够智能地只采集新增的数据，避免重复采集已有的历史数据，显著提升采集效率并节省API调用成本。

#### 核心特性
- **严格无重叠**: 从最后采集日期的下一个交易日开始采集
- **智能交易日历**: 基于真实交易日历，自动跳过节假日和周末
- **自动回退**: 增量采集失败时自动回退到全量采集
- **跨数据源支持**: 支持TUSHARE、CSV、MySQL三种数据源
- **配置驱动**: 通过settings.toml灵活配置默认行为

### 📊 基础使用

#### 启用增量采集
```bash
# 基础增量采集
python -m src.cli.main collect 000001.SZ --incremental

# 增量采集多个标的
python -m src.cli.main collect 000001.SZ 600000.SH --incremental

# 期货增量采集
python -m src.cli.main collect RB2501 CU2501 --type futures --incremental

# 分钟数据增量采集
python -m src.cli.main collect 000001.SZ --freq 1min --incremental
```

#### 首次使用增量采集
当首次使用增量采集时（没有历史记录），系统会自动使用settings.toml中配置的默认时间范围：

```bash
# 首次增量采集 - 自动使用默认配置
python -m src.cli.main collect 000001.SZ --incremental

# 输出示例：
# 📊 增量采集：未找到历史记录，使用默认配置
# 📈 增量采集范围: 2025-07-05 到 2025-08-04 (30天)
```

#### 后续增量采集
有历史记录后，系统会自动计算增量范围：

```bash
# 后续增量采集 - 自动计算增量范围
python -m src.cli.main collect 000001.SZ --incremental

# 输出示例：
# 📈 增量采集范围: 2025-08-05 到 2025-08-04 (新数据)
# 或
# 📊 增量采集：无新数据需要采集
```

### ⚙️ 增量采集配置

#### 📍 配置文件位置
增量采集的所有配置都在 `config/settings.toml` 文件中，这是系统的核心配置文件。

#### 🎯 默认时间范围配置
```toml
# 数据采集默认时间范围配置
[data_collection.default_days]
stocks = 30      # 股票数据默认采集30天
futures = 60     # 期货数据默认采集60天
minutes = 7      # 分钟数据默认采集7天
```

**配置说明**:
- `stocks`: 股票数据的默认采集天数
- `futures`: 期货数据的默认采集天数
- `minutes`: 分钟级数据的默认采集天数（适用于1min、5min等）

#### 🔧 增量采集行为配置
```toml
# 增量采集配置
[data_collection.incremental]
enabled = true                    # 启用增量采集
overlap_protection = true         # 重叠保护（严格无重叠）
fallback_to_full = true          # 失败时回退到全量采集
cache_trading_calendar = true     # 缓存交易日历
max_gap_days = 30                # 最大间隔天数，超过则建议全量采集

# 交易日历配置
[data_collection.trading_calendar]
auto_initialize = true            # 自动初始化交易日历
cache_ttl_hours = 24             # 缓存有效期（小时）
update_frequency = "yearly"       # 更新频率：yearly, monthly, weekly
fallback_to_simple = true        # 回退到简化逻辑（排除周末）

# 性能配置
[data_collection.performance]
batch_size = 1000                # 批处理大小
max_concurrent = 2               # 最大并发数
timeout_seconds = 30             # 超时时间
retry_attempts = 3               # 重试次数
```

#### 🌍 环境特定配置
```toml
# 开发环境 - 使用较小的默认范围（节省资源和时间）
[dev.data_collection.default_days]
stocks = 7       # 开发环境股票数据默认7天
futures = 14     # 开发环境期货数据默认14天
minutes = 1      # 开发环境分钟数据默认1天

[dev.data_collection.incremental]
enabled = true
fallback_to_full = true
max_gap_days = 7

# 测试环境 - 使用最小范围（快速测试）
[test.data_collection.default_days]
stocks = 3       # 测试环境股票数据默认3天
futures = 5      # 测试环境期货数据默认5天
minutes = 1      # 测试环境分钟数据默认1天

[test.data_collection.incremental]
enabled = true
fallback_to_full = true
max_gap_days = 3

# 生产环境 - 使用标准范围（完整数据）
[prod.data_collection.default_days]
stocks = 30      # 生产环境股票数据默认30天
futures = 60     # 生产环境期货数据默认60天
minutes = 7      # 生产环境分钟数据默认7天

[prod.data_collection.incremental]
enabled = true
overlap_protection = true
fallback_to_full = true
max_gap_days = 30
```

#### 📊 配置优先级说明
增量采集的范围计算遵循以下优先级（从高到低）：

1. **历史记录优先**: 如果有历史采集记录，自动从最后采集日期的下一个交易日开始
2. **环境配置**: 根据当前环境（dev/test/prod）使用对应的 `[环境.data_collection.default_days]` 配置
3. **全局配置**: 使用 `[data_collection.default_days]` 全局默认配置
4. **硬编码默认值**: 配置读取失败时的后备值（stocks=30天，futures=60天，minutes=7天）

#### 🛠️ 如何修改配置

**方法1: 直接编辑配置文件（推荐）**
```bash
# 编辑配置文件
vim config/settings.toml
# 或使用其他编辑器
code config/settings.toml
```

**方法2: 查看当前配置**
```bash
# 查看当前使用的配置
python -c "
from src.utils.config_loader import ConfigLoader
config = ConfigLoader().get_config('test')  # 或 'dev', 'prod'
print('当前默认天数配置:')
print(config.get('data_collection', {}).get('default_days', {}))
"
```

**方法3: 测试配置效果**
```bash
# 测试配置是否生效（预览模式）
python -m src.cli.main collect 000001.SZ --incremental --preview

# 输出会显示实际使用的时间范围：
# 📊 增量采集：未找到历史记录，使用默认配置
# 📈 增量采集范围: 2025-08-01 到 2025-08-04 (3天)
```

### 🎯 实际使用场景和配置示例

#### 场景1：个人量化研究（推荐配置）
```toml
# config/settings.toml
[data_collection.default_days]
stocks = 15      # 个人研究通常15天足够
futures = 30     # 期货波动大，需要更多历史数据
minutes = 3      # 分钟数据3天，平衡效率和完整性
```

```bash
# 日常使用
python -m src.cli.main collect 000001.SZ 600000.SH --incremental
python -m src.cli.main collect RB2501 CU2501 --type futures --incremental
```

#### 场景2：高频交易策略开发
```toml
# config/settings.toml - 针对高频策略优化
[data_collection.default_days]
stocks = 7       # 高频策略关注近期数据
futures = 14     # 期货高频需要适中历史
minutes = 1      # 分钟数据只需1天，减少存储

[data_collection.performance]
batch_size = 500         # 小批次，快速处理
max_concurrent = 1       # 单线程，避免API限制
timeout_seconds = 15     # 短超时，快速失败
```

```bash
# 高频数据采集
python -m src.cli.main collect 000001.SZ --freq 1min --incremental
python -m src.cli.main collect 000001.SZ --freq 5min --incremental
```

#### 场景3：机构级数据采集
```toml
# config/settings.toml - 机构级配置
[prod.data_collection.default_days]
stocks = 60      # 机构需要更长历史数据
futures = 120    # 期货需要更多历史用于风控
minutes = 14     # 分钟数据保留2周

[prod.data_collection.performance]
batch_size = 2000        # 大批次，提高效率
max_concurrent = 4       # 多线程，充分利用资源
timeout_seconds = 60     # 长超时，处理大数据集
```

```bash
# 批量机构级采集
python -m src.cli.main collect 000001.SZ 000002.SZ 600000.SH 600036.SH --incremental
python -m src.cli.main collect RB2501 CU2501 AL2501 ZN2501 --type futures --incremental
```

#### 场景4：开发测试环境
```toml
# config/settings.toml - 开发测试优化
[dev.data_collection.default_days]
stocks = 3       # 开发测试只需最少数据
futures = 5      # 快速验证功能
minutes = 1      # 最小化分钟数据

[dev.data_collection.incremental]
enabled = true
fallback_to_full = true
max_gap_days = 3         # 短间隔，快速测试
```

```bash
# 开发测试使用
python -m src.cli.main collect 000001.SZ --incremental --preview  # 先预览
python -m src.cli.main collect 000001.SZ --incremental            # 实际采集
```

#### 场景5：成本敏感型使用（节省TUSHARE积分）
```toml
# config/settings.toml - 成本优化配置
[data_collection.default_days]
stocks = 10      # 减少默认天数
futures = 20     # 控制期货数据量
minutes = 2      # 最小化分钟数据

[data_collection.incremental]
enabled = true
overlap_protection = true    # 严格避免重复
fallback_to_full = false    # 禁用回退，避免意外全量采集
max_gap_days = 10           # 较小间隔，更频繁增量采集
```

```bash
# 成本优化使用
python -m src.cli.main collect 000001.SZ --incremental  # 优先使用增量
# 避免使用全量采集参数如 --last-days, --start-date 等
```

#### 场景6：数据完整性优先
```toml
# config/settings.toml - 完整性优先配置
[data_collection.default_days]
stocks = 90      # 更长历史，确保完整性
futures = 180    # 期货需要更多历史数据
minutes = 30     # 分钟数据保留一个月

[data_collection.incremental]
enabled = true
overlap_protection = true
fallback_to_full = true     # 启用回退，确保数据完整
max_gap_days = 60          # 较大间隔容忍度
```

```bash
# 完整性优先使用
python -m src.cli.main collect 000001.SZ --incremental
# 如果增量失败，会自动回退到全量采集
```

### 🔍 配置验证和状态查看

#### 验证当前配置
```bash
# 1. 查看当前环境的配置
python -c "
from src.utils.config_loader import ConfigLoader
import json
config = ConfigLoader().get_config('test')  # 或 'dev', 'prod'
data_config = config.get('data_collection', {})
print('=== 当前数据采集配置 ===')
print(f'默认天数配置: {data_config.get(\"default_days\", {})}')
print(f'增量采集配置: {data_config.get(\"incremental\", {})}')
print(f'性能配置: {data_config.get(\"performance\", {})}')
"

# 2. 测试配置是否生效
python -c "
from src.data_import.incremental_collection_helper import IncrementalCollectionHelper
helper = IncrementalCollectionHelper()
# 测试股票配置
stock_range = helper._get_default_range_from_settings('stocks', 'daily')
print(f'股票默认范围: {stock_range}')
# 测试期货配置
futures_range = helper._get_default_range_from_settings('futures', 'daily')
print(f'期货默认范围: {futures_range}')
# 测试分钟数据配置
minute_range = helper._get_default_range_from_settings('stocks', '1min')
print(f'分钟数据默认范围: {minute_range}')
"

# 3. 预览实际采集范围
python -m src.cli.main collect 000001.SZ --incremental --preview
python -m src.cli.main collect RB2501 --type futures --incremental --preview
```

#### 配置修改后的验证流程
```bash
# 1. 修改配置文件后，重新加载验证
python -c "
from src.utils.config_loader import ConfigLoader
# 强制重新加载配置
loader = ConfigLoader()
loader.reload_config()
config = loader.get_config('test')
print('配置重新加载完成')
print('新的默认天数:', config.get('data_collection', {}).get('default_days', {}))
"

# 2. 测试新配置的效果
python -m src.cli.main collect 000001.SZ --incremental --preview

# 3. 如果满意，进行实际采集
python -m src.cli.main collect 000001.SZ --incremental
```

#### 查看采集历史和状态
```bash
# 查看最近的采集历史
python -m src.cli.main history --limit 10

# 查看特定标的的采集历史
python -m src.cli.main history --symbol 000001.SZ

# 查看增量采集统计
python -c "
from src.data_import.incremental_collection_helper import IncrementalCollectionHelper
helper = IncrementalCollectionHelper()
# 检查是否建议使用增量采集
should_use = helper.should_use_incremental(['000001.SZ'], 'stocks')
print(f'是否建议使用增量采集: {should_use}')
"
```

#### 增量采集日志解读
增量采集过程中会产生详细的日志信息，帮助理解采集过程：

```
# 成功的增量采集日志示例
2025-08-04 22:30:00 - INFO - 计算增量采集范围: symbols=['000001.SZ'], data_type=stocks
2025-08-04 22:30:00 - INFO - 查询到标的 000001.SZ 最后导入日期: 2025-08-03
2025-08-04 22:30:00 - INFO - 使用最早的最后采集日期作为基准: 2025-08-03
2025-08-04 22:30:00 - INFO - 下一个交易日: 2025-08-04
2025-08-04 22:30:00 - INFO - 计算得到增量范围: {'start_date': '2025-08-04', 'end_date': '2025-08-04'}

# 首次使用增量采集的日志示例
2025-08-04 22:30:00 - INFO - 未找到历史采集记录，使用默认配置
2025-08-04 22:30:00 - INFO - 使用默认配置: stocks daily -> 3天 (2025-08-01 to 2025-08-04)

# 无新数据的日志示例
2025-08-04 22:30:00 - INFO - 查询到标的 000001.SZ 最后导入日期: 2025-08-04
2025-08-04 22:30:00 - INFO - 无下一个交易日，无新数据需要采集
```

### 🛠️ 配置故障排除

#### 问题1: 配置不生效
**症状**: 修改了settings.toml但增量采集仍使用旧的时间范围

**解决方案**:
```bash
# 1. 检查配置文件语法
python -c "
import toml
try:
    config = toml.load('config/settings.toml')
    print('✅ 配置文件语法正确')
except Exception as e:
    print(f'❌ 配置文件语法错误: {e}')
"

# 2. 检查配置是否被正确读取
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
loader.reload_config()  # 强制重新加载
config = loader.get_config('test')
print('当前配置:', config.get('data_collection', {}).get('default_days', {}))
"

# 3. 检查环境变量是否覆盖了配置
env | grep AQUA
```

#### 问题2: 时间范围计算错误
**症状**: 增量采集的时间范围不符合预期

**解决方案**:
```bash
# 1. 检查交易日历是否正常
python -c "
from src.data_import.trading_calendar_manager import TradingCalendarManager
manager = TradingCalendarManager()
# 测试交易日判断
is_trading = manager.is_trading_day('2025-08-04', 'STOCK', 'SSE')
print(f'2025-08-04是否为交易日: {is_trading}')
# 测试下一交易日计算
next_date = manager.get_next_trading_date('2025-08-03', 'STOCK', 'SSE')
print(f'2025-08-03的下一交易日: {next_date}')
"

# 2. 检查历史记录查询
python -c "
from src.data_import.import_history_manager import ImportHistoryManager
manager = ImportHistoryManager()
last_date = manager.get_last_import_date('000001.SZ', 'stocks', 'TUSHARE')
print(f'000001.SZ最后采集日期: {last_date}')
"

# 3. 手动测试增量范围计算
python -c "
from src.data_import.incremental_collection_helper import IncrementalCollectionHelper
helper = IncrementalCollectionHelper()
range_result = helper.calculate_incremental_range(['000001.SZ'], 'stocks', 'daily', 'TUSHARE')
print(f'计算的增量范围: {range_result}')
"
```

#### 问题3: 不同环境配置混乱
**症状**: 开发环境使用了生产环境的配置，或配置不一致

**解决方案**:
```bash
# 1. 明确当前使用的环境
python -c "
import os
from src.utils.config_loader import ConfigLoader
# 检查环境变量
env = os.getenv('AQUA_ENV', 'test')
print(f'当前环境: {env}')
# 检查配置加载器使用的环境
loader = ConfigLoader()
config = loader.get_config('test')  # 明确指定环境
print('使用test环境配置')
"

# 2. 比较不同环境的配置
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
for env in ['dev', 'test', 'prod']:
    try:
        config = loader.get_config(env)
        default_days = config.get('data_collection', {}).get('default_days', {})
        print(f'{env}环境配置: {default_days}')
    except Exception as e:
        print(f'{env}环境配置读取失败: {e}')
"

# 3. 设置环境变量（如果需要）
export AQUA_ENV=dev    # 使用开发环境
export AQUA_ENV=prod   # 使用生产环境
```

#### 问题4: 配置值类型错误
**症状**: 配置文件中的数值被当作字符串处理

**解决方案**:
```toml
# ❌ 错误：使用引号包围数值
[data_collection.default_days]
stocks = "30"     # 错误：字符串类型
futures = "60"    # 错误：字符串类型

# ✅ 正确：直接使用数值
[data_collection.default_days]
stocks = 30       # 正确：整数类型
futures = 60      # 正确：整数类型
```

#### 问题5: 配置文件路径问题
**症状**: 找不到配置文件或配置文件路径错误

**解决方案**:
```bash
# 1. 检查配置文件是否存在
ls -la config/settings.toml

# 2. 检查当前工作目录
pwd
# 确保在AQUA项目根目录下运行命令

# 3. 检查配置文件权限
ls -la config/settings.toml
# 确保有读取权限

# 4. 如果配置文件不存在，从模板创建
cp config/settings.toml.example config/settings.toml  # 如果有模板
```

### ⚠️ 使用注意事项

#### 何时使用增量采集
- ✅ **推荐使用**：日常数据更新、定期数据同步
- ✅ **推荐使用**：高频数据采集（分钟级数据）
- ✅ **推荐使用**：大量标的的批量更新
- ✅ **推荐使用**：成本敏感场景（节省TUSHARE积分）

#### 何时不使用增量采集
- ❌ **不推荐**：首次大规模历史数据采集
- ❌ **不推荐**：需要重新采集特定时间段的数据
- ❌ **不推荐**：数据质量有问题需要重新采集
- ❌ **不推荐**：需要精确控制采集时间范围的场景

#### 增量采集与时间参数的关系
```bash
# ❌ 错误：同时使用增量采集和时间范围参数
python -m src.cli.main collect 000001.SZ --incremental --start-date 2025-07-01 --end-date 2025-08-01

# ✅ 正确：使用增量采集（自动计算时间范围）
python -m src.cli.main collect 000001.SZ --incremental

# ✅ 正确：使用指定时间范围（全量采集）
python -m src.cli.main collect 000001.SZ --start-date 2025-07-01 --end-date 2025-08-01

# ✅ 正确：使用相对时间范围（全量采集）
python -m src.cli.main collect 000001.SZ --last-days 30
```

### 📋 快速配置参考表

#### 常用配置组合

| 使用场景 | stocks | futures | minutes | 说明 |
|---------|--------|---------|---------|------|
| 个人研究 | 15 | 30 | 3 | 平衡效率和完整性 |
| 高频交易 | 7 | 14 | 1 | 关注近期数据 |
| 机构级 | 60 | 120 | 14 | 完整历史数据 |
| 开发测试 | 3 | 5 | 1 | 最小化数据量 |
| 成本优化 | 10 | 20 | 2 | 节省API积分 |
| 完整性优先 | 90 | 180 | 30 | 最大历史覆盖 |

#### 环境配置对照表

| 环境 | 用途 | stocks | futures | minutes | 特点 |
|------|------|--------|---------|---------|------|
| dev | 开发 | 7 | 14 | 1 | 快速开发，节省资源 |
| test | 测试 | 3 | 5 | 1 | 最小数据，快速验证 |
| prod | 生产 | 30 | 60 | 7 | 标准配置，平衡性能 |

#### 数据类型默认值

| 数据类型 | 推荐天数 | 原因 |
|---------|---------|------|
| 股票日线 | 30天 | 覆盖一个月交易数据，适合大多数分析 |
| 期货日线 | 60天 | 期货波动大，需要更多历史数据 |
| 分钟数据 | 7天 | 分钟数据量大，7天平衡存储和分析需求 |
| 高频数据 | 1-3天 | 高频数据主要用于短期分析 |

### 🚀 性能优势

#### TUSHARE积分节省
```bash
# 全量采集（消耗更多积分）
python -m src.cli.main collect 000001.SZ --last-days 30  # 消耗30天的积分

# 增量采集（只消耗新数据的积分）
python -m src.cli.main collect 000001.SZ --incremental   # 可能只消耗1-2天的积分
```

#### 采集速度提升
- **全量采集**: 需要处理大量历史数据
- **增量采集**: 只处理新增数据，速度提升50-90%

#### 网络流量节省
- **减少API调用**: 只请求必要的新数据
- **降低网络负载**: 特别适合网络环境不稳定的情况

#### 实际性能对比
```bash
# 性能测试示例
# 全量采集30天数据
time python -m src.cli.main collect 000001.SZ --last-days 30
# 预期: 15-30秒，消耗30个积分

# 增量采集（假设只有1天新数据）
time python -m src.cli.main collect 000001.SZ --incremental
# 预期: 2-5秒，消耗1个积分
```

---

## 高级功能

### DataProcessor高级配置

#### 质量控制模式
```bash
# 严格模式（失败时停止采集）
export AQUA_DATA_PROCESSOR_FAIL_SAFE=false
python -m src.cli.main collect 000001.SZ

# 宽松模式（失败时使用原始数据）
export AQUA_DATA_PROCESSOR_FAIL_SAFE=true
python -m src.cli.main collect 000001.SZ
```

#### 性能调优模式
```bash
# 查看详细性能统计
export AQUA_SHOW_QUALITY_STATS=true
python -m src.cli.main collect 000001.SZ

# 输出包含：
# 📊 详细质量统计:
#   - 处理引擎: Polars (自动选择)
#   - 字段验证: 通过 15/15 字段
#   - 跨字段验证: 通过 OHLC一致性检查
#   - 去重统计: 内部去重5条，数据库去重0条
#   - 错误详情: 发现并修复2条时区问题
```

#### 大数据集处理
```bash
# 大数据集自动优化（>100万条记录）
python -m src.cli.main collect large_dataset --source csv

# DataProcessor自动切换到DuckDB引擎处理大数据集
# 📊 自动引擎切换: Polars -> DuckDB (数据量>100万条)
# ⚡ 大数据优化: 分批处理，内存友好
# 🔄 处理进度: [████████████████████████████████] 100%
```

### 使用预设模板（DataProcessor优化）
```bash
# 查看可用模板
python -m src.cli.main collect --list-templates

# 使用银行股模板（自动批量优化）
python -m src.cli.main collect --template bank_stocks_daily
# 🔄 DataProcessor批量优化: 10只银行股统一处理
# 📊 批量质量控制: 统一验证规则和去重策略

# 使用期货主力合约模板
python -m src.cli.main collect --template futures_main_contracts
# 🔄 期货数据特殊优化: 合约代码标准化、持仓量验证
```

### 数据源管理（DataProcessor集成）
```bash
# 查看数据源状态（包含DataProcessor状态）
python -m src.cli.main --check-capabilities

# 输出示例：
# 📊 数据源能力报告 (DataProcessor集成版):
# ✅ TUSHARE: 可用 (DataProcessor: 95%质量提升)
# ✅ CSV: 可用 (DataProcessor: 90%格式标准化)  
# ✅ MySQL: 可用 (DataProcessor: 85%去重优化)
# ⚡ 处理性能: 226万条/秒
# 🛡️ 失败安全: 已启用
```

---

## 配置文件

### DataProcessor配置文件示例

创建 `dataprocessor_config.yaml`:
```yaml
# DataProcessor高级配置
data_processor:
  # 基础开关
  enabled: true
  fail_safe_mode: true
  show_quality_stats: true
  
  # 性能配置
  small_data_limit: 50000      # 小数据集阈值（调低以更多使用DuckDB）
  large_data_limit: 500000     # 大数据集阈值
  max_concurrent_tasks: 4      # 并发任务数
  
  # 质量控制
  min_quality_score: 0.85      # 最低质量要求
  enable_data_repair: true     # 启用自动修复
  enable_cross_validation: true # 启用跨字段验证
  
  # 数据源优先级
  data_source_priority:
    - "TUSHARE"     # 最高优先级
    - "FROMC2C"     # 期货数据优先
    - "MYSQL"       # 数据库数据
    - "AKSHARE"     # 备用数据源

# 基础采集配置
symbols:
  - "000001.SZ"
  - "600036.SH"
  - "600000.SH"

source: "tushare"
start_date: "2024-01-01"
end_date: "2024-06-30"
```

### 使用配置文件
```bash
python -m src.cli.main collect --config dataprocessor_config.yaml

# 输出包含DataProcessor配置生效确认：
# 🔄 DataProcessor配置加载:
#   - 质量要求: 0.85
#   - 并发任务: 4
#   - 自动修复: 启用
#   - 跨字段验证: 启用
```

---

## 数据查看

### 使用Python查看处理后的数据
```python
import duckdb

# 连接数据库
conn = duckdb.connect('data/aqua_dev.duckdb')

# 查看DataProcessor处理统计
result = conn.execute("""
    SELECT 
        ts_code,
        COUNT(*) as total_records,
        MIN(trade_date) as start_date,
        MAX(trade_date) as end_date,
        AVG(close) as avg_close
    FROM stock_daily 
    WHERE ts_code = '000001.SZ'
    GROUP BY ts_code
""").fetchall()

print("DataProcessor处理后的清洁数据:")
for row in result:
    print(f"代码:{row[0]}, 记录数:{row[1]}, 时间范围:{row[2]}-{row[3]}, 平均价格:{row[4]:.2f}")

# 数据质量验证
quality_check = conn.execute("""
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN high >= low THEN 1 END) as valid_ohlc,
        COUNT(CASE WHEN volume > 0 THEN 1 END) as valid_volume
    FROM stock_daily 
    WHERE ts_code = '000001.SZ'
""").fetchall()

print("数据质量检查 (DataProcessor已自动清洗):")
print(f"总记录: {quality_check[0][0]}")
print(f"OHLC一致性: {quality_check[0][1]}/{quality_check[0][0]} (100%)")
print(f"有效成交量: {quality_check[0][2]}/{quality_check[0][0]} (100%)")
```

### 查看DataProcessor处理日志
```python
import logging

# 查看DataProcessor处理日志
with open('logs/datacollector/collect_service.log', 'r') as f:
    lines = f.readlines()
    
    # 筛选DataProcessor相关日志
    dp_logs = [line for line in lines if 'DataProcessor' in line]
    
    print("最近的DataProcessor处理记录:")
    for log in dp_logs[-10:]:  # 显示最近10条
        print(log.strip())

# 典型日志示例：
# INFO  - DataProcessor处理完成: daily_basic, 数据源: TUSHARE
# INFO  - 质量评分: 0.960
# INFO  - 处理统计: 清洁245条, 脏数据5条, 重复0条
# INFO  - 处理时间: 0.003秒
# INFO  - 处理速度: 83333条/秒
```

---

## 最佳实践

### 👍 DataProcessor推荐做法

1. **启用质量统计进行监控**
   ```bash
   # 开发和测试时启用详细统计
   export AQUA_SHOW_QUALITY_STATS=true
   python -m src.cli.main collect 000001.SZ
   
   # 生产环境静默模式
   export AQUA_SHOW_QUALITY_STATS=false
   ```

2. **合理配置质量阈值**
   ```toml
   # config/settings.toml
   [data_processor]
   min_quality_score = 0.80    # 一般场景
   # min_quality_score = 0.95  # 高质量要求场景
   ```

3. **利用数据源优先级**
   ```bash
   # TUSHARE数据质量最高，优先使用
   python -m src.cli.main collect 000001.SZ --source tushare
   
   # 期货数据使用CSV(FROMC2C)
   python -m src.cli.main collect IF2508 --source csv
   ```

4. **大数据集分批处理**
   ```bash
   # 大数据集让DataProcessor自动优化
   python -m src.cli.main collect large_symbols.txt --batch-size 1000
   
   # DataProcessor会：
   # - 自动切换到DuckDB引擎
   # - 分批处理避免内存溢出  
   # - 并行处理提高效率
   ```

### ⚡ 性能优化策略

1. **充分利用DataProcessor缓存**
   ```bash
   # 相同数据多次处理时，DataProcessor会利用缓存加速
   python -m src.cli.main collect 000001.SZ  # 首次处理
   python -m src.cli.main collect 000001.SZ  # 第二次更快
   ```

2. **调整处理引擎阈值**
   ```toml
   # 对于内存充足的机器，可以提高阈值
   [data_processor]
   small_data_limit = 200000    # 更多使用高性能Polars
   large_data_limit = 2000000   # 更大数据才切换DuckDB
   ```

3. **并发处理优化**
   ```toml
   [data_processor]
   max_concurrent_tasks = 4     # 根据CPU核心数调整
   ```

### 🛡️ 数据质量保证

1. **监控数据质量评分**
   ```bash
   # 定期检查质量评分趋势
   python -c "
   import duckdb
   conn = duckdb.connect('data/aqua_dev.duckdb')
   # 查看最近的数据质量统计
   # (需要在数据库中记录质量统计表)
   "
   ```

2. **处理质量异常**
   ```bash
   # 当质量评分低于阈值时，检查原始数据源
   # DataProcessor会记录详细的错误分类信息
   
   # 查看错误详情日志
   grep "ERROR" logs/datacollector/collect_service.log | tail -20
   ```

3. **验证数据完整性**
   ```python
   # 定期验证DataProcessor处理后的数据完整性
   import duckdb
   
   conn = duckdb.connect('data/aqua_dev.duckdb')
   
   # 检查是否有异常的价格数据
   anomalies = conn.execute("""
       SELECT ts_code, trade_date, high, low, close
       FROM stock_daily 
       WHERE high < low OR close <= 0
   """).fetchall()
   
   if anomalies:
       print("发现异常数据（DataProcessor可能需要规则调整）:")
       for row in anomalies:
           print(row)
   else:
       print("✅ 数据完整性检查通过，DataProcessor工作正常")
   ```

---

## 故障排除

### DataProcessor常见问题

#### 问题1: DataProcessor初始化失败
```bash
# 错误信息: DataProcessor初始化失败，将跳过数据处理
# 解决方案:
# 1. 检查配置文件
python -c "
from src.utils.config_loader import ConfigLoader
config = ConfigLoader().get_data_processor_config('dev')
print('DataProcessor配置:', config)
"

# 2. 检查依赖包
pip install polars duckdb

# 3. 临时禁用DataProcessor
export AQUA_ENABLE_DATA_PROCESSOR=false
```

#### 问题2: 质量评分持续偏低
```bash
# 检查数据源质量
python -m src.cli.main collect 000001.SZ --source tushare --preview

# 调整质量阈值
# 在config/settings.toml中:
# [data_processor]
# min_quality_score = 0.70  # 降低阈值

# 查看详细错误分类
export AQUA_SHOW_QUALITY_STATS=true
python -m src.cli.main collect 000001.SZ
```

#### 问题3: 处理速度慢
```bash
# 检查是否正确选择处理引擎
export AQUA_SHOW_QUALITY_STATS=true
python -m src.cli.main collect large_dataset

# 手动调整引擎选择阈值
# 在config/settings.toml中:
# [data_processor]  
# small_data_limit = 50000   # 降低阈值，更早使用DuckDB

# 调整并发数
# max_concurrent_tasks = 2   # 降低并发减少资源竞争
```

#### 问题4: 内存不足
```bash
# 对于大数据集，启用分批处理
python -m src.cli.main collect large_symbols --batch-size 500

# 调整处理引擎，优先使用DuckDB
# 在config/settings.toml中:
# [data_processor]
# small_data_limit = 10000   # 更小的阈值
```

### 回退策略

#### 临时禁用DataProcessor
```bash
# 方法1: 环境变量
export AQUA_ENABLE_DATA_PROCESSOR=false
python -m src.cli.main collect 000001.SZ

# 方法2: 配置文件
# 在config/settings.toml中设置:
# [data_processor]
# enabled = false
```

#### 启用失败安全模式
```bash
# DataProcessor失败时自动使用原始数据
export AQUA_DATA_PROCESSOR_FAIL_SAFE=true
python -m src.cli.main collect 000001.SZ

# 成功时会看到:
# ✅ 清洁数据: 240条 -> 保存到数据库
# 失败时会看到:
# ⚠️ 数据处理失败，使用原始数据: 250条（回退模式）
```

---

## 🆘 获取帮助

- **遇到问题**: 查看 [FAQ.md](FAQ.md)
- **DataProcessor文档**: `docs/tasks/DEV_Tasks_DataProcessorDesignTODOList.md`
- **部署指南**: `docs/tasks/DataProcessor_Deployment_Strategy.md`
- **配置参考**: `config/settings.toml` - DataProcessor配置段
- **功能建议**: 提交GitHub Issue

---

## 📊 版本历史

### v2.0 (当前版本) - DataProcessor集成版
- ✅ 集成DataProcessor数据处理器
- ✅ 统一配置管理系统
- ✅ 226万条/秒处理性能
- ✅ 多数据源质量控制
- ✅ 失败安全模式
- ✅ 实时质量评分
- ✅ T1-T2-T3渐进部署验证

### v1.0 - 基础版本
- 支持多数据源采集
- 基础数据验证
- DuckDB存储

### 增量采集常见问题

#### 问题1: 增量采集显示"无新数据需要采集"
**现象**:
```bash
python -m src.cli.main collect 000001.SZ --incremental
# 输出: 📊 增量采集：无新数据需要采集
```

**原因**:
- 最后采集日期已经是最新的交易日
- 当前时间不是交易时间

**解决方案**:
```bash
# 1. 检查最后采集日期
python -m src.cli.main history --symbol 000001.SZ --limit 1

# 2. 如果需要重新采集，使用全量采集
python -m src.cli.main collect 000001.SZ --last-days 1

# 3. 或者指定具体时间范围
python -m src.cli.main collect 000001.SZ --start-date 2025-08-01 --end-date 2025-08-04
```

#### 问题2: 增量采集自动回退到全量采集
**现象**:
```bash
⚠️  增量采集失败，回退到全量采集: 查询最后导入日期失败
```

**原因**:
- 数据库连接问题
- 历史记录表结构不匹配
- 权限问题

**解决方案**:
```bash
# 1. 检查数据库连接
python -m src.cli.main check-db

# 2. 重新初始化数据库
python -m src.cli.main init-db

# 3. 使用全量采集作为临时解决方案
python -m src.cli.main collect 000001.SZ --last-days 7
```

#### 问题3: 交易日历数据缺失
**现象**:
```bash
交易日查询失败，使用简化逻辑: Table "market_trading_calendar" does not exist
```

**原因**:
- 交易日历表未初始化
- 数据库版本不匹配

**解决方案**:
```bash
# 1. 初始化交易日历数据（需要TUSHARE_TOKEN）
export TUSHARE_TOKEN="your_token_here"
python -c "
from src.data_import.trading_calendar_manager import TradingCalendarManager
manager = TradingCalendarManager()
result = manager.initialize_trading_calendar()
print(f'交易日历初始化结果: {result}')
"

# 2. 验证交易日历数据
python -c "
from src.data_import.trading_calendar_manager import TradingCalendarManager
manager = TradingCalendarManager()
is_trading = manager.is_trading_day('2025-08-04', 'STOCK', 'SSE')
print(f'2025-08-04是否为交易日: {is_trading}')
"
```

#### 问题4: 增量采集范围计算错误
**现象**:
增量采集的时间范围不符合预期

**解决方案**:
```bash
# 1. 检查历史记录
python -m src.cli.main history --symbol 000001.SZ --limit 5

# 2. 手动验证交易日历
python -c "
from src.data_import.trading_calendar_manager import TradingCalendarManager
manager = TradingCalendarManager()
next_date = manager.get_next_trading_date('2025-08-03', 'STOCK', 'SZSE')
print(f'下一个交易日: {next_date}')
"

# 3. 清空缓存重新计算
python -c "
from src.data_import.trading_calendar_manager import TradingCalendarManager
manager = TradingCalendarManager()
manager.clear_cache()
print('交易日历缓存已清空')
"
```

#### 问题5: 增量采集性能问题
**现象**:
增量采集速度比预期慢

**解决方案**:
```bash
# 1. 检查缓存状态
python -c "
from src.data_import.trading_calendar_manager import TradingCalendarManager
manager = TradingCalendarManager()
stats = manager.get_cache_stats()
print(f'缓存统计: {stats}')
"

# 2. 优化数据库索引
python -c "
from src.database.unified_storage_manager import UnifiedStorageManager
manager = UnifiedStorageManager()
# 创建增量采集优化索引
manager.execute_query('CREATE INDEX IF NOT EXISTS idx_import_history_incremental ON data_import_history(symbol, data_type, source, import_date DESC)')
print('索引创建完成')
"

# 3. 调整配置参数
# 在settings.toml中调整:
# [data_collection.performance]
# batch_size = 1000
# max_concurrent = 2
# timeout_seconds = 30
```

### 🔧 调试和诊断

#### 启用详细日志
```bash
# 设置日志级别为DEBUG
export AQUA_LOG_LEVEL=DEBUG
python -m src.cli.main collect 000001.SZ --incremental
```

#### 增量采集诊断命令
```bash
# 诊断增量采集状态
python -c "
from src.data_import.incremental_collection_helper import IncrementalCollectionHelper
helper = IncrementalCollectionHelper()

# 检查是否建议使用增量采集
should_use = helper.should_use_incremental(['000001.SZ'], 'stocks')
print(f'是否建议使用增量采集: {should_use}')

# 计算增量范围
range_result = helper.calculate_incremental_range(['000001.SZ'], 'stocks')
print(f'增量范围: {range_result}')
"
```

---

*更新日期: 2025-08-04 | 版本: v2.1.0 | DataProcessor集成版 + 增量采集功能*