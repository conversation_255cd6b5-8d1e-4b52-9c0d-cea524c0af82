# Claude Code Tools Guide

> **Comprehensive documentation for Claude Code automation tools and templates**
> **Version**: v1.0
> **Compatible with**: CLAUDE.md v5.0 (Streamlined Edition)

-----

## Table of Contents

- [Overview](#overview)
- [Automation Tools](#automation-tools)
- [Template Libraries](#template-libraries)
- [Usage Examples](#usage-examples)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

-----

## Overview

The Claude Code tool ecosystem provides comprehensive automation and standardization for AQUA project development. This guide covers all available tools, templates, and their usage patterns.

### Tool Categories

1. **Analysis Tools** - Code reuse analysis and complexity assessment
2. **Generation Tools** - Template and code generation utilities
3. **Quality Tools** - Metrics calculation and quality assessment
4. **Template Libraries** - Standardized code and test templates

-----

## Automation Tools

### 1. Reuse Analyzer (`scripts/claude_tools/reuse_analyzer.py`)

**Purpose**: Automated code similarity analysis and reuse strategy recommendation

**Features**:
- Scans existing codebase for similar functionality
- Calculates similarity scores using keyword analysis
- Recommends optimal reuse strategy (Direct/Extended/Refactored/Partial/New)
- Estimates time savings for each strategy

**Usage**:

```bash
# Basic analysis
python scripts/claude_tools/reuse_analyzer.py "implement portfolio risk calculation"

# JSON output for programmatic use
python scripts/claude_tools/reuse_analyzer.py "new data processor" --format json

# Specify project root
python scripts/claude_tools/reuse_analyzer.py "API endpoint" --project-root /path/to/project
```

**Output Example**:
```
🔍 Reuse Analysis Results
━━━━━━━━━━━━━━━━━━━━━━
Strategy: EXTEND_REUSE
Justification: Good similarity found. Existing implementation can be extended to meet new requirements.
Time Savings: 70% time saved

📁 Found Matches:
  • data_processor.py:DataProcessor (similarity: 0.75)
  • risk_calculator.py:RiskCalculator (similarity: 0.68)

💡 Recommendation:
  Extend existing functionality to meet new requirements
```

**Configuration**:
The analyzer categorizes functions and classes into:
- `data_processing` - Transform, convert, process operations
- `api_endpoints` - API routes and endpoints
- `database_operations` - Save, load, store, fetch operations
- `calculations` - Calculate, compute, analyze operations
- `utilities` - General utility functions

### 2. TDD Generator (`scripts/claude_tools/tdd_generator.py`)

**Purpose**: Generate comprehensive test and implementation templates following TDD principles

**Features**:
- Generates complete test suites with multiple scenarios
- Creates implementation templates with AQUA standards
- Supports different module types (core vs general)
- Includes performance, integration, and error handling tests

**Usage**:

```bash
# Generate templates for core module
python scripts/claude_tools/tdd_generator.py --feature "portfolio_risk_calculator" --type core

# Generate with specific test scenarios
python scripts/claude_tools/tdd_generator.py --feature "data_processor" --scenarios happy_path edge_cases

# Specify output directory
python scripts/claude_tools/tdd_generator.py --feature "api_client" --output-dir src/new_module
```

**Generated Files**:
- `test_{feature_name}.py` - Complete test suite
- `{feature_name}.py` - Implementation template

**Test Scenarios Included**:
- **Happy Path** - Valid input scenarios with expected outputs
- **Edge Cases** - Empty input, large datasets, boundary conditions
- **Error Handling** - Invalid input, configuration errors, exceptions
- **Performance** - Execution time and memory usage tests
- **Integration** - Mocked dependencies and system integration

**Template Features**:
- Type annotations for all parameters and returns
- Comprehensive error handling with custom exceptions
- Configuration-driven behavior with validation
- Logging integration and performance monitoring
- Resource management and cleanup patterns

### 3. Mode Selector (`scripts/claude_tools/mode_selector.py`)

**Purpose**: Intelligent recommendation of optimal execution mode based on task complexity

**Features**:
- Analyzes task description for complexity indicators
- Estimates code lines and development time
- Checks for architectural impact
- Recommends RAPID/STANDARD/ARCHITECT mode with reasoning

**Usage**:

```bash
# Analyze task complexity
python scripts/claude_tools/mode_selector.py "Fix typo in configuration file"

# JSON output for automation
python scripts/claude_tools/mode_selector.py "Refactor data processing architecture" --format json
```

**Output Example**:
```
🎯 Task Analysis Results
━━━━━━━━━━━━━━━━━━━━━━
Task: Implement new portfolio risk calculation algorithm

📊 Analysis:
  • Complexity Score: 0.65
  • Estimated Lines: 250
  • Estimated Hours: 2.1
  • Architectural Impact: false

🚀 Recommended Mode: STANDARD

💡 Reasoning:
  STANDARD mode recommended because:
  • Moderate complexity score: 0.65
  • Moderate code size: 250 lines
  • Standard development time: 2.1 hours
  • Standard TDD process appropriate
  This task fits the standard development workflow.

📋 Mode-Specific Guidance:
  • Complete reuse analysis → Standard TDD cycle → Comprehensive testing
  • Quality gates: Full suite (Black + MyPy + Ruff + Coverage)
```

**Decision Criteria**:

| Mode | Criteria | Process |
|------|----------|---------|
| **RAPID** | Complexity ≤0.3, Lines ≤50, Time ≤1h, No arch impact | Quick scan → Implement → Basic test |
| **STANDARD** | Moderate complexity, 50-500 lines, 1-4 hours | Full reuse → TDD → Quality gates |
| **ARCHITECT** | High complexity ≥0.7, >500 lines, >4h, or arch impact | Design → Plan → Layered TDD → Full audit |

### 4. Quality Calculator (`scripts/claude_tools/quality_calculator.py`)

**Purpose**: Comprehensive project quality assessment with improvement recommendations

**Features**:
- Calculates 5 key quality metrics with trend analysis
- Generates improvement suggestions based on current performance
- Provides overall project health score
- Supports custom project data input

**Usage**:

```bash
# Generate full quality report
python scripts/claude_tools/quality_calculator.py --generate-report

# Use custom data file
python scripts/claude_tools/quality_calculator.py --data-file project_metrics.json --generate-report

# JSON output for dashboards
python scripts/claude_tools/quality_calculator.py --format json
```

**Quality Metrics**:

1. **Code Reuse Rate** (Target: >60%)
   - Calculation: `(reused_lines + pattern_bonus) / total_lines`
   - Weight: 25%

2. **Test Coverage** (Target: Core >95%, Standard >85%)
   - Calculation: Weighted average of core and standard modules
   - Weight: 30%

3. **TDD Compliance** (Target: >90%)
   - Calculation: `test_first_features / total_features + quality_factors`
   - Weight: 20%

4. **Code Quality Score** (Target: >9.0/10)
   - Calculation: Composite of complexity, maintainability, duplication, documentation
   - Weight: 15%

5. **Defect Density** (Target: <1 bug/KLOC)
   - Calculation: `bugs_found / (total_lines / 1000)`
   - Weight: 10%

**Sample Project Data**:
```json
{
  "total_lines_of_code": 50000,
  "reused_lines_of_code": 30000,
  "common_patterns_count": 15,
  "core_modules": {
    "data_warehouse": 0.96,
    "backtest_workshop": 0.94
  },
  "standard_modules": {
    "utils": 0.88,
    "config": 0.85
  },
  "total_features": 50,
  "test_first_features": 45,
  "avg_complexity": 6.5,
  "maintainability": 82,
  "code_duplication": 0.08,
  "doc_coverage": 0.75,
  "bugs_found_30_days": 3
}
```

-----

## Template Libraries

### Test Templates (`templates/claude/test_templates/`)

#### Unit Test Template (`unit_test_template.py`)

**Features**:
- Complete pytest-based test suite structure
- Fixtures for test data and instance setup
- Parametrized tests for multiple scenarios
- Performance and memory usage tests
- Mock-based integration tests
- Thread safety and concurrent access tests

**Usage Patterns**:
```python
# Replace placeholders with actual values
{ClassName} → ActualClassName
{class_name} → actual_class_name  
{method_name} → actual_method_name
{module_name} → actual_module_name
```

**Test Categories Included**:
- **Happy Path Tests** - Valid inputs and expected behaviors
- **Edge Cases** - Empty inputs, large datasets, boundary values
- **Error Handling** - Invalid inputs, configuration errors
- **Performance Tests** - Execution time and memory benchmarks
- **Integration Tests** - Mocked dependencies and system interactions
- **Concurrency Tests** - Thread safety and concurrent access

### Implementation Templates (`templates/claude/implementation_templates/`)

#### Class Template (`class_template.py`)

**Features**:
- Full AQUA standards compliance
- Configuration-driven architecture with dataclass config
- Comprehensive error handling and validation
- Performance monitoring and metrics collection
- Resource management with context manager support
- Caching and batch processing capabilities

**Key Components**:

1. **Configuration Class**:
```python
@dataclass
class {ClassName}Config:
    enable_validation: bool = True
    enable_performance_monitoring: bool = True
    timeout_seconds: int = 30
    # ... additional config parameters
```

2. **Main Implementation Class**:
```python
class {ClassName}:
    def __init__(self, config: Optional[{ClassName}Config] = None)
    def process(self, input_data: Any) -> Any
    def process_batch(self, input_batch: List[Any]) -> List[Any]
    # ... additional methods
```

3. **Factory Function**:
```python
def create_{class_name}(config_dict=None, config_file=None) -> {ClassName}
```

**Built-in Features**:
- Input/output validation
- Caching with automatic eviction
- Performance metrics collection
- Resource cleanup and context management
- Batch processing for large datasets
- Comprehensive logging integration

### Error Handling Patterns (`templates/claude/error_patterns/`)

#### Error Handling Decorators (`error_handling_decorators.py`)

**Available Decorators**:

1. **@handle_errors** - Comprehensive error handling with context
2. **@retry_with_backoff** - Exponential backoff retry logic  
3. **@circuit_breaker** - Circuit breaker pattern for external services
4. **@timeout** - Function execution timeout enforcement
5. **@performance_monitor** - Performance monitoring and alerting
6. **@validate_input** - Input validation with custom validators
7. **@log_execution** - Execution logging with timing

**Custom Exception Classes**:
- `AQUABaseException` - Base exception with context
- `ValidationError` - Data validation failures
- `ProcessingError` - Processing failures
- `ConfigurationError` - Configuration issues
- `ExternalServiceError` - External service communication
- `PerformanceError` - Performance threshold violations
- `TimeoutError` - Operation timeout

**Usage Examples**:
```python
@handle_errors(reraise=True)
@retry_with_backoff(max_retries=3)
@performance_monitor(max_execution_time=5.0)
def process_data(data):
    return expensive_operation(data)
```

-----

## Usage Examples

### Example 1: Complete Feature Development Workflow

```bash
# Step 1: Analyze reuse opportunities
python scripts/claude_tools/reuse_analyzer.py "implement user authentication system"

# Step 2: Determine execution mode
python scripts/claude_tools/mode_selector.py "implement user authentication system"

# Step 3: Generate TDD templates (if STANDARD/ARCHITECT mode)
python scripts/claude_tools/tdd_generator.py --feature "user_authenticator" --type core

# Step 4: Develop using TDD cycle (Red-Green-Refactor)
# ... implement tests and code ...

# Step 5: Check quality metrics
python scripts/claude_tools/quality_calculator.py --generate-report
```

### Example 2: Automated Project Analysis

```python
#!/usr/bin/env python3
"""Automated project analysis script"""

import subprocess
import json

# Analyze multiple features
features = [
    "data validation system",
    "report generation engine", 
    "cache management layer"
]

for feature in features:
    print(f"\n🔍 Analyzing: {feature}")
    
    # Get reuse analysis
    reuse_result = subprocess.run([
        "python", "scripts/claude_tools/reuse_analyzer.py", 
        feature, "--format", "json"
    ], capture_output=True, text=True)
    
    # Get mode recommendation  
    mode_result = subprocess.run([
        "python", "scripts/claude_tools/mode_selector.py",
        feature, "--format", "json" 
    ], capture_output=True, text=True)
    
    reuse_data = json.loads(reuse_result.stdout)
    mode_data = json.loads(mode_result.stdout)
    
    print(f"  Strategy: {reuse_data['reuse_strategy']}")
    print(f"  Mode: {mode_data['recommended_mode']}")
    print(f"  Time Savings: {reuse_data['time_savings']}")
```

### Example 3: Custom Quality Metrics Integration

```python
#!/usr/bin/env python3
"""Custom quality metrics collection"""

import json
from pathlib import Path

# Collect project metrics
project_data = {
    "total_lines_of_code": get_total_lines(),
    "reused_lines_of_code": calculate_reused_lines(), 
    "core_modules": get_core_module_coverage(),
    "standard_modules": get_standard_module_coverage(),
    # ... collect other metrics
}

# Save to file
with open("project_metrics.json", "w") as f:
    json.dump(project_data, f, indent=2)

# Generate report
subprocess.run([
    "python", "scripts/claude_tools/quality_calculator.py",
    "--data-file", "project_metrics.json",
    "--generate-report"
])
```

-----

## Best Practices

### 1. Tool Integration Workflow

**Recommended Sequence**:
1. **Reuse Analysis** - Always start with reuse assessment
2. **Mode Selection** - Choose appropriate execution mode
3. **Template Generation** - Generate standardized templates
4. **TDD Development** - Follow Red-Green-Refactor cycles
5. **Quality Assessment** - Validate against quality metrics

### 2. Template Customization

**Template Placeholders**:
- Use descriptive placeholder names with clear patterns
- Follow consistent naming conventions (PascalCase, snake_case)
- Include comprehensive docstrings and type annotations
- Maintain AQUA coding standards throughout

**Customization Process**:
```bash
# 1. Generate base template
python scripts/claude_tools/tdd_generator.py --feature "my_processor" --type core

# 2. Replace placeholders systematically
sed -i 's/{ClassName}/MyProcessor/g' *.py
sed -i 's/{class_name}/my_processor/g' *.py
sed -i 's/{method_name}/process_data/g' *.py

# 3. Customize business logic
# Edit generated files to add specific functionality
```

### 3. Quality Metrics Monitoring

**Continuous Monitoring**:
```bash
# Add to CI/CD pipeline
python scripts/claude_tools/quality_calculator.py --generate-report > quality_report.md

# Set up scheduled quality checks
crontab -e
# Add: 0 6 * * * cd /path/to/project && python scripts/claude_tools/quality_calculator.py --generate-report
```

**Quality Gates Integration**:
```python
# quality_gate.py
import json
import subprocess
import sys

# Get quality metrics
result = subprocess.run([
    "python", "scripts/claude_tools/quality_calculator.py", "--format", "json"
], capture_output=True, text=True)

metrics = json.loads(result.stdout)

# Check quality gates
failed_gates = []
for metric in metrics:
    if metric["status"] == "critical":
        failed_gates.append(metric["metric_name"])

if failed_gates:
    print(f"❌ Quality gates failed: {', '.join(failed_gates)}")
    sys.exit(1)
else:
    print("✅ All quality gates passed")
```

### 4. Error Handling Strategy

**Decorator Stacking**:
```python
# Recommended order for multiple decorators
@handle_errors(reraise=True)           # Outermost - comprehensive handling
@retry_with_backoff(max_retries=3)     # Retry logic
@circuit_breaker(failure_threshold=5)  # Circuit breaker
@performance_monitor(max_time=10.0)    # Performance monitoring
@validate_input(validator, "message")  # Input validation
@log_execution(level=logging.INFO)     # Innermost - execution logging
def my_function(data):
    return process(data)
```

**Exception Hierarchy**:
```python
# Use specific exception types for better error handling
try:
    result = risky_operation()
except ValidationError as e:
    # Handle validation issues
    log_validation_error(e)
    return default_value
except ProcessingError as e:
    # Handle processing failures
    notify_administrators(e)
    raise
except ExternalServiceError as e:
    # Handle external service issues
    schedule_retry(e)
    return cached_value
```

-----

## Troubleshooting

### Common Issues

#### 1. Import Errors

**Problem**: `ModuleNotFoundError` when running tools

**Solutions**:
```bash
# Ensure you're in the project root
cd /path/to/AQUA/project

# Add project root to PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Or use absolute imports in tools
python -m scripts.claude_tools.reuse_analyzer "task description"
```

#### 2. Template Placeholder Issues

**Problem**: Placeholders not properly replaced in generated templates

**Solutions**:
```bash
# Use consistent find-and-replace
find . -name "*.py" -exec sed -i 's/{ClassName}/ActualName/g' {} \;

# Verify replacements
grep -r "{ClassName}" . --include="*.py"
```

#### 3. Quality Calculator Data Issues

**Problem**: Missing or invalid project data causing calculation errors

**Solutions**:
```python
# Validate data structure before calculation
required_fields = [
    "total_lines_of_code", "core_modules", "standard_modules"
]

for field in required_fields:
    if field not in project_data:
        project_data[field] = get_default_value(field)
```

#### 4. Performance Issues with Large Codebases

**Problem**: Reuse analyzer slow on large projects

**Solutions**:
```python
# Optimize pattern loading in reuse_analyzer.py
def _load_existing_patterns(self, max_files: int = 1000):
    # Limit number of files scanned
    py_files = list(src_path.rglob("*.py"))[:max_files]
    
# Use caching for repeated analyses
@lru_cache(maxsize=128)
def calculate_similarity(self, requirement: str, code_content: str):
    # Cache similarity calculations
```

### Debug Mode

Enable debug logging for all tools:
```bash
# Set environment variable
export CLAUDE_TOOLS_DEBUG=1

# Or use Python logging
python -c "import logging; logging.basicConfig(level=logging.DEBUG)" scripts/claude_tools/reuse_analyzer.py "task"
```

### Tool Dependencies

Required Python packages:
```bash
pip install psutil  # For memory monitoring in templates
pip install pytest  # For test execution
pip install toml    # For configuration file parsing
```

-----

## Tool Development

### Adding New Tools

1. **Create tool file** in `scripts/claude_tools/`
2. **Follow naming convention**: `{tool_purpose}.py`
3. **Include CLI interface** with argparse
4. **Add comprehensive docstrings** and type annotations
5. **Implement error handling** using AQUA patterns
6. **Update this guide** with tool documentation

### Template Development

1. **Create template file** in appropriate `templates/claude/` subdirectory
2. **Use consistent placeholders** with clear naming
3. **Include comprehensive examples** and documentation
4. **Follow AQUA coding standards** throughout
5. **Test template generation** with multiple scenarios

### Contributing Guidelines

- All tools must include CLI interface and programmatic API
- Comprehensive error handling using AQUA exception patterns
- Full type annotations and docstrings
- Example usage in docstrings and this guide
- Unit tests for core functionality

-----

**Document Version**: v1.0  
**Last Updated**: 2025-07-25  
**Compatible With**: CLAUDE.md v5.0 (Streamlined Edition)  
**Maintainer**: AQUA Development Team