# AQUA 数据采集CLI - Windows 11 端到端验证指南

> **目标受众**: 个人量化开发者  
> **测试环境**: Windows 11  
> **验证范围**: 完整的数据采集链路 (配置→采集→存储→验证)  
> **文档版本**: v2.0 (完全重构版)

---

## 📋 **测试任务概述**

基于对AQUA配置管理系统的深度分析，本指南将验证以下核心功能：

### 🎯 **验证目标**
1. **配置系统正确性** - 验证config/.env + settings.toml在Windows下正确工作
2. **跨平台路径映射** - 验证路径占位符正确展开到Windows路径
3. **三种数据源完整链路** - Tushare API、MySQL数据库、CSV文件(含FromC2C)
4. **--config功能完整性** - YAML配置文件驱动和参数优先级
5. **数据完整性保证** - 源数据与目标DuckDB数据一致性

### 🏗️ **四层测试架构**
```
第一层：配置系统验证 (基础设施)
    ├── 环境文件正确读取
    ├── 路径占位符展开验证
    └── 环境切换机制测试

第二层：数据源连接测试 (连通性)
    ├── Tushare API认证和调用
    ├── MySQL数据库连接验证
    └── CSV文件路径访问测试

第三层：数据采集集成测试 (功能性)
    ├── Tushare → DuckDB完整链路
    ├── MySQL → DuckDB迁移测试
    ├── CSV → DuckDB导入测试
    └── 配置文件驱动功能测试

第四层：数据完整性验证 (质量保证)
    ├── 数据对比和一致性检查
    ├── Windows平台性能基准
    └── 异常场景恢复测试
```

---

## 第一章：测试环境准备

### 1.1 系统要求
- **操作系统**: Windows 11 (推荐21H2或更高版本)
- **Python版本**: 3.11.x (必需)
- **内存要求**: 最低4GB，推荐8GB
- **磁盘空间**: 至少5GB可用空间

### 1.2 Python与Git环境安装

#### 安装Python 3.11
```powershell
# 下载并安装Python 3.11.x
# 官方下载页面: https://www.python.org/downloads/windows/
# ⚠️ 重要：安装时必须勾选 "Add Python 3.11 to PATH"
```

#### 安装Git
```powershell
# 下载并安装Git for Windows
# 官方下载页面: https://git-scm.com/download/win
```

#### 验证安装
```powershell
# 验证Python安装
python --version
# 预期输出: Python 3.11.x

# 验证Git安装
git --version
# 预期输出: git version 2.x.x
```

### 1.3 获取AQUA项目代码

```powershell
# 克隆项目到用户目录
cd ~
git clone https://github.com/your-org/AQUA.git
cd AQUA

# 验证项目结构
dir config
# 预期看到: .env.example, settings.toml
```

### 1.4 Python虚拟环境搭建

```powershell
# 安装UV包管理器
pip install uv

# 创建虚拟环境
uv venv

# 激活虚拟环境 (PowerShell)
.\.venv\Scripts\Activate.ps1
# 如果使用CMD: .\.venv\Scripts\activate.bat

# 验证虚拟环境激活
# 命令行前缀应显示: (.venv)

# 安装项目依赖
uv pip install -r requirements.txt
```

**🚨 常见问题解决：**
- **执行策略错误**: 以管理员身份运行PowerShell，执行：`Set-ExecutionPolicy RemoteSigned`
- **UV安装失败**: 使用官方pip：`pip install uv`
- **依赖安装超时**: 使用国内镜像：`uv pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple`

---

## 第二章：第一层测试 - 配置系统验证

### 2.1 创建环境配置文件

**步骤1: 复制并配置.env文件**
```powershell
# 复制环境配置模板
copy config\.env.example config\.env

# 编辑.env文件 (使用记事本或VS Code)
notepad config\.env
```

**步骤2: 配置必要的环境变量**
在`.env`文件中填入以下内容：
```bash
# AQUA 环境变量配置
# TUSHARE API配置 (必需)
TUSHARE_TOKEN=你的真实token

# 数据库配置 (MySQL测试需要)
AQUA_MYSQL_PASSWORD=你的mysql密码

# 环境配置
AQUA_ENV=dev
AQUA_DEBUG=true
```

### 2.2 配置系统基础验证

**测试1: 验证配置文件加载**
```powershell
# 测试配置加载器
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
print('配置文件加载成功!')
print('支持的环境:', loader.get_all_environments())
"
```

**预期输出：**
```
配置文件加载成功!
支持的环境: ['dev', 'test', 'prod']
```

**测试2: 验证环境变量注入**
```powershell
# 验证Tushare Token注入
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
config = loader.get_config('dev')
tushare_config = config.get('datasources', {}).get('api', {}).get('tushare', {})
print('Tushare Token配置:', '已配置' if tushare_config.get('token') else '未配置')
"
```

**预期输出：**
```
Tushare Token配置: 已配置
```

### 2.3 跨平台路径展开验证

**测试3: 验证Windows路径映射**
```powershell
# 创建路径验证脚本
python -c "
from src.utils.config_loader import ConfigLoader
import json

loader = ConfigLoader()
platform_info = loader.get_platform_info()
print('平台信息:')
print(json.dumps(platform_info, indent=2, ensure_ascii=False))

# 测试关键路径展开
test_paths = [
    '{datacenter_dir}/aqua_dev.duckdb',
    '{csv_root}/dev',
    '{fromc2c_data}',
    '{logs_root}/dev'
]

print('\n路径展开测试:')
for path_config in test_paths:
    try:
        expanded = loader.expand_cross_platform_path(path_config, auto_mkdir=False)
        print(f'{path_config} -> {expanded}')
    except Exception as e:
        print(f'{path_config} -> 错误: {e}')
"
```

**预期输出：**
```
平台信息:
{
  "system": "Windows",
  "is_windows": true,
  ...
}

路径展开测试:
{datacenter_dir}/aqua_dev.duckdb -> D:\Data\duckdb\AQUA\DataCenter\aqua_dev.duckdb
{csv_root}/dev -> D:\Data\duckdb\AQUA\DataCenter\datasources\csv\dev
{fromc2c_data} -> D:\Data\RAW\FromC2C
{logs_root}/dev -> D:\Data\duckdb\AQUA\DataCenter\logs\dev
```

### 2.4 环境切换机制验证

**测试4: 验证多环境配置**
```powershell
# 测试环境切换
python -c "
from src.utils.config_loader import ConfigLoader
import os

loader = ConfigLoader()

# 测试默认dev环境
dev_db = loader.get_database_config('dev')
print('DEV环境数据库路径:', dev_db['path'])

# 测试test环境
test_db = loader.get_database_config('test')
print('TEST环境数据库路径:', test_db['path'])

# 通过环境变量切换
os.environ['AQUA_ENV'] = 'test'
print('环境变量设置为test后，当前环境检测成功')
"
```

**✅ 第一层测试完成标志：**
- 配置文件正确加载 ✓
- 环境变量正确注入 ✓  
- Windows路径正确映射 ✓
- 环境切换正常工作 ✓

---

## 第三章：第二层测试 - 数据源连接验证

### 3.1 Tushare API连接测试

**测试5: Tushare连接能力检查**
```powershell
# 检查Tushare数据源状态
python -m src.cli.main collect --check-capabilities --source tushare
```

**预期输出：**
```
数据源连接状态检查
┌──────────┬────────────┬─────────────────┬──────────────┐
│ 数据源   │ 状态       │ 连接状态        │ 详细信息     │
├──────────┼────────────┼─────────────────┼──────────────┤
│ tushare  │ available  │ connected       │ Token验证通过│
└──────────┴────────────┴─────────────────┴──────────────┘
```

**测试6: Tushare数据预览测试**
```powershell
# 预览平安银行股票数据
python -m src.cli.main collect 000001.SZ --source tushare --preview --start-date 2024-07-01 --end-date 2024-07-02
```

**预期输出应包含：**
- 数据预览面板显示
- 标的代码: 000001.SZ
- 数据源: tushare  
- 预估行数: > 0

### 3.2 MySQL数据库连接测试

**测试7: MySQL连接验证**
```powershell
# 检查MySQL数据源状态
python -m src.cli.main collect --check-capabilities --source mysql
```

**预期输出：**
```
数据源连接状态检查
┌──────────┬────────────┬─────────────────┬──────────────────────┐
│ 数据源   │ 状态       │ 连接状态        │ 详细信息             │
├──────────┼────────────┼─────────────────┼──────────────────────┤
│ mysql    │ available  │ connected       │ 连接到192.168.2.100 │
└──────────┴────────────┴─────────────────┴──────────────────────┘
```

**🚨 MySQL连接问题排查：**
如果连接失败，检查以下项目：
```powershell
# 检查网络连通性
ping 192.168.2.100

# 检查端口连通性 (需要telnet客户端)
telnet 192.168.2.100 3306

# 验证配置文件中的MySQL设置
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
mysql_config = loader.get_mysql_config('dev')
print('MySQL配置:', mysql_config)
"
```

### 3.3 CSV文件路径访问测试

**测试8: CSV数据源基础验证**
```powershell
# 检查CSV数据源状态
python -m src.cli.main collect --check-capabilities --source csv
```

**测试9: 创建测试用CSV数据**
```powershell
# 创建CSV测试目录结构
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()

# 获取CSV配置路径
csv_config = loader.get_csv_config('dev')
print('CSV配置路径:', csv_config.get('data_dir'))

# 创建测试目录和文件
import os
from pathlib import Path

csv_test_dir = loader.expand_cross_platform_path('{csv_root}/dev')
fromc2c_test_dir = loader.expand_cross_platform_path('{fromc2c_data}')

print(f'创建CSV测试目录: {csv_test_dir}')
csv_test_dir.mkdir(parents=True, exist_ok=True)

print(f'FromC2C测试目录: {fromc2c_test_dir}')
fromc2c_test_dir.mkdir(parents=True, exist_ok=True)

# 创建测试CSV文件
test_csv_content = '''timestamp,symbol,open,high,low,close,volume
2024-07-01 09:30:00,TEST001,100.0,102.0,99.5,101.5,10000
2024-07-01 09:31:00,TEST001,101.5,103.0,101.0,102.0,8000
2024-07-01 09:32:00,TEST001,102.0,102.5,100.5,101.0,12000
'''

test_file = csv_test_dir / 'test_data.csv'
with open(test_file, 'w', encoding='utf-8') as f:
    f.write(test_csv_content)

print(f'创建测试文件: {test_file}')
print('CSV测试环境准备完成!')
"
```

**测试10: CSV文件预览测试**
```powershell
# 预览测试CSV文件
python -m src.cli.main collect test_data --source csv --preview
```

**✅ 第二层测试完成标志：**
- Tushare API连接成功 ✓
- MySQL数据库连接成功 ✓
- CSV文件路径访问正常 ✓
- 测试数据准备完成 ✓

---

## 第四章：第三层测试 - 数据采集集成测试

### 4.1 Tushare → DuckDB完整链路测试 

**测试11: Tushare数据采集到DuckDB**
```powershell
# 采集平安银行小样本数据
python -m src.cli.main collect 000001.SZ --source tushare --start-date 2024-07-01 --end-date 2024-07-03
```

**预期行为：**
- 显示数据采集进度条
- 完成后显示采集统计信息
- 数据存储到dev环境的DuckDB文件

**验证数据存储：**
```powershell
# 验证数据是否正确存储到DuckDB
python -c "
import duckdb
from src.utils.config_loader import ConfigLoader

loader = ConfigLoader()
db_config = loader.get_database_config('dev')
db_path = loader.expand_cross_platform_path(db_config['path'])

print(f'连接数据库: {db_path}')
conn = duckdb.connect(str(db_path))

# 检查表是否创建
tables = conn.execute('SHOW TABLES').fetchall()
print('数据库中的表:', tables)

# 检查股票数据
if tables:
    try:
        result = conn.execute(\"\"\"
            SELECT COUNT(*) as record_count, 
                   MIN(trade_date) as start_date,
                   MAX(trade_date) as end_date
            FROM stock_daily 
            WHERE ts_code = '000001.SZ'
        \"\"\").fetchone()
        print(f'采集数据统计: {result[0]}条记录, 时间范围: {result[1]} 到 {result[2]}')
    except Exception as e:
        print(f'数据查询异常: {e}')

conn.close()
"
```

### 4.2 MySQL → DuckDB数据迁移测试

**测试12: MySQL数据迁移**
```powershell
# 从MySQL导入表数据到DuckDB
python -m src.cli.main collect your_table_name --source mysql --preview
```

**如果成功，继续完整导入：**
```powershell
python -m src.cli.main collect your_table_name --source mysql
```

**验证迁移结果：**
```powershell
# 验证MySQL迁移数据
python -c "
import duckdb
from src.utils.config_loader import ConfigLoader

loader = ConfigLoader()
db_config = loader.get_database_config('dev')
db_path = loader.expand_cross_platform_path(db_config['path'])

conn = duckdb.connect(str(db_path))

# 检查迁移的表
try:
    result = conn.execute('SELECT COUNT(*) FROM your_table_name').fetchone()
    print(f'MySQL迁移数据: {result[0]}条记录')
except Exception as e:
    print(f'MySQL表不存在或迁移失败: {e}')

conn.close()
"
```

### 4.3 CSV → DuckDB导入测试

**测试13: CSV文件导入**
```powershell
# 导入之前创建的测试CSV文件
python -m src.cli.main collect test_data --source csv
```

**测试14: FromC2C历史数据导入 (如果有数据)**
```powershell
# 导入FromC2C格式的历史数据
python -m src.cli.main collect some_contract_15min --source csv --preview
```

### 4.4 配置文件驱动功能测试

**测试15: 创建YAML配置文件**
```powershell
# 创建Windows测试配置文件
@"
symbols:
  - "000001.SZ"
  - "600036.SH"
source: "tushare"
start_date: "2024-07-01"
end_date: "2024-07-03"
preview: false
save_format: "duckdb"
"@ | Out-File -FilePath "win_test_config.yaml" -Encoding UTF8
```

**测试16: 配置文件驱动采集**
```powershell
# 使用配置文件执行采集
python -m src.cli.main collect --config win_test_config.yaml
```

**测试17: 参数优先级验证**
```powershell
# 配置文件 + 命令行参数 (命令行应覆盖配置文件)
python -m src.cli.main collect --config win_test_config.yaml --source csv --preview
```

**预期行为：**
- 应该使用CSV数据源 (覆盖配置文件中的tushare)
- 应该只预览不保存 (覆盖配置文件中的preview: false)
- symbols等其他参数从配置文件读取

**✅ 第三层测试完成标志：**
- Tushare数据成功采集并存储 ✓
- MySQL数据成功迁移 ✓
- CSV数据成功导入 ✓
- 配置文件功能正常工作 ✓
- 参数优先级逻辑正确 ✓

---

## 第五章：第四层测试 - 数据完整性验证

### 5.1 数据一致性验证脚本

**测试18: 创建数据验证脚本**
```powershell
# 创建数据完整性验证脚本
@"
#!/usr/bin/env python3
import duckdb
from src.utils.config_loader import ConfigLoader
import sys

def verify_data_integrity():
    loader = ConfigLoader()
    db_config = loader.get_database_config('dev')
    db_path = loader.expand_cross_platform_path(db_config['path'])
    
    conn = duckdb.connect(str(db_path))
    
    print('=== AQUA数据完整性验证报告 ===')
    print(f'数据库路径: {db_path}')
    
    # 检查表结构
    tables = conn.execute('SHOW TABLES').fetchall()
    print(f'数据库表数量: {len(tables)}')
    
    total_records = 0
    for table in tables:
        table_name = table[0]
        try:
            count = conn.execute(f'SELECT COUNT(*) FROM {table_name}').fetchone()[0]
            print(f'表 {table_name}: {count:,} 条记录')
            total_records += count
        except Exception as e:
            print(f'表 {table_name}: 查询失败 - {e}')
    
    print(f'总记录数: {total_records:,}')
    
    # 检查数据质量
    print('\n=== 数据质量检查 ===')
    if 'stock_daily' in [t[0] for t in tables]:
        # 检查股票数据质量
        quality_checks = [
            ('重复记录检查', \"\"\"
                SELECT ts_code, trade_date, COUNT(*) as dup_count 
                FROM stock_daily 
                GROUP BY ts_code, trade_date 
                HAVING COUNT(*) > 1
            \"\"\"),
            ('空值检查', \"\"\"
                SELECT COUNT(*) as null_count 
                FROM stock_daily 
                WHERE close IS NULL OR open IS NULL
            \"\"\"),
            ('价格合理性检查', \"\"\"
                SELECT COUNT(*) as invalid_price_count 
                FROM stock_daily 
                WHERE close <= 0 OR open <= 0 OR high < low
            \"\"\")
        ]
        
        for check_name, query in quality_checks:
            try:
                result = conn.execute(query).fetchall()
                print(f'{check_name}: {result}')
            except Exception as e:
                print(f'{check_name}: 检查失败 - {e}')
    
    conn.close()
    print('\n验证完成!')

if __name__ == '__main__':
    verify_data_integrity()
"@ | Out-File -FilePath "data_integrity_check.py" -Encoding UTF8
```

**执行数据验证：**
```powershell
python data_integrity_check.py
```

### 5.2 性能基准测试

**测试19: Windows平台性能测试**
```powershell
# 创建性能基准测试
python -c "
import time
import psutil
import os
from src.utils.config_loader import ConfigLoader

print('=== Windows平台性能基准测试 ===')

# 系统信息
print(f'CPU核心数: {psutil.cpu_count()}')
print(f'内存总量: {psutil.virtual_memory().total / (1024**3):.1f}GB')
print(f'可用内存: {psutil.virtual_memory().available / (1024**3):.1f}GB')

# 配置加载性能
start_time = time.time()
loader = ConfigLoader()
config = loader.get_config('dev')
config_load_time = time.time() - start_time
print(f'配置加载时间: {config_load_time:.3f}秒')

# 路径展开性能
start_time = time.time()
test_paths = ['{datacenter_dir}/test.db', '{csv_root}/data', '{fromc2c_data}']
for path in test_paths:
    expanded = loader.expand_cross_platform_path(path, auto_mkdir=False)
path_expand_time = time.time() - start_time
print(f'路径展开时间({len(test_paths)}个): {path_expand_time:.3f}秒')

print('性能基准测试完成!')
"
```

### 5.3 异常场景恢复测试

**测试20: 错误处理验证**
```powershell
# 测试各种异常情况的处理
echo "=== 异常场景测试 ==="

# 测试无效Tushare Token
echo "测试1: 无效Token处理"
python -c "
import os
# 临时设置无效token
old_token = os.environ.get('TUSHARE_TOKEN', '')
os.environ['TUSHARE_TOKEN'] = 'invalid_token_test'

try:
    from src.cli.main import main
    # 这应该优雅地失败而不是崩溃
    import sys
    sys.argv = ['main', 'collect', '--check-capabilities', '--source', 'tushare']
    main()
except SystemExit:
    print('Token验证失败，程序正确退出')
except Exception as e:
    print(f'异常处理: {e}')
finally:
    os.environ['TUSHARE_TOKEN'] = old_token
"

# 测试网络连接异常
echo "测试2: 网络异常处理"
python -m src.cli.main collect 000001.SZ --source tushare --preview --start-date 2024-07-01 --end-date 2024-07-01

# 测试磁盘空间不足场景 (模拟)
echo "测试3: 配置验证"
python -c "
from src.utils.config_loader import ConfigLoader
loader = ConfigLoader()
validation = loader.validate_config('dev')
print('配置验证结果:', validation)
"
```

**✅ 第四层测试完成标志：**
- 数据完整性验证通过 ✓
- 性能基准测试完成 ✓
- 异常处理验证通过 ✓
- 错误恢复机制正常 ✓

---

## 第六章：测试结果汇总与问题排查

### 6.1 完整测试清单

**配置系统验证 (第一层)**
- [ ] 配置文件加载成功
- [ ] 环境变量正确注入
- [ ] Windows路径正确映射
- [ ] 环境切换机制正常

**数据源连接验证 (第二层)**
- [ ] Tushare API连接成功
- [ ] MySQL数据库连接成功
- [ ] CSV文件路径访问正常
- [ ] 测试数据准备完成

**数据采集集成测试 (第三层)**
- [ ] Tushare → DuckDB链路正常
- [ ] MySQL → DuckDB迁移成功
- [ ] CSV → DuckDB导入成功
- [ ] 配置文件功能正常
- [ ] 参数优先级正确

**数据完整性验证 (第四层)**
- [ ] 数据一致性检查通过
- [ ] 性能基准达标
- [ ] 异常处理正常
- [ ] 错误恢复正常

### 6.2 常见问题排查指南

**问题1: Python模块导入错误**
```powershell
# 解决方案: 重新安装依赖
.\.venv\Scripts\Activate.ps1
uv pip install -r requirements.txt --force-reinstall
```

**问题2: 路径访问权限错误**
```powershell
# 检查路径权限
icacls "D:\Data\duckdb\AQUA"
# 如果需要，修改权限
icacls "D:\Data\duckdb\AQUA" /grant %USERNAME%:F /T
```

**问题3: DuckDB连接失败**
```powershell
# 检查DuckDB文件状态
python -c "
from pathlib import Path
from src.utils.config_loader import ConfigLoader

loader = ConfigLoader()
db_path = loader.expand_cross_platform_path('{datacenter_dir}/aqua_dev.duckdb')
print(f'数据库路径: {db_path}')
print(f'文件存在: {db_path.exists()}')
print(f'文件大小: {db_path.stat().st_size if db_path.exists() else \"N/A\"} bytes')
print(f'父目录权限: {db_path.parent.exists()}')
"
```

**问题4: 中文路径编码问题**
```powershell
# 设置Python编码环境变量
$env:PYTHONIOENCODING="utf-8"
# 永久设置
setx PYTHONIOENCODING "utf-8"
```

### 6.3 测试完成验收标准

**✅ 完全成功标准:**
- 所有20个测试项目通过
- 三种数据源均能正常采集数据
- 数据完整性验证无异常
- 性能指标符合预期

**⚠️ 部分成功标准:**
- 核心功能(Tushare/CSV)正常工作
- 数据能够正确存储到DuckDB
- 配置文件功能正常
- 可接受的性能表现

**❌ 失败标准:**
- 配置系统无法正常工作
- 主要数据源连接失败
- 数据采集无法完成
- 严重的系统兼容性问题

### 6.4 测试报告模板

```
=== AQUA Windows 11 端到端测试报告 ===

测试日期: [填写日期]
测试环境: Windows 11 [版本号]
Python版本: [填写版本]
项目版本: [Git提交号]

第一层测试结果: [通过/失败] ([通过数]/4)
第二层测试结果: [通过/失败] ([通过数]/4) 
第三层测试结果: [通过/失败] ([通过数]/5)
第四层测试结果: [通过/失败] ([通过数]/3)

总体评估: [完全成功/部分成功/失败]

关键发现:
- [记录重要发现]
- [记录性能数据]
- [记录遇到的问题]

建议改进:
- [记录改进建议]

测试人员: [姓名]
```

---

## 第七章：持续验证与维护

### 7.1 定期验证脚本

创建自动化验证脚本，支持定期运行：

```powershell
# 创建快速健康检查脚本
@"
#!/usr/bin/env python3
# Windows 11 AQUA 健康检查脚本
import sys
from src.utils.config_loader import ConfigLoader

def quick_health_check():
    try:
        print('🔍 执行AQUA快速健康检查...')
        
        # 1. 配置系统检查
        loader = ConfigLoader()
        config = loader.get_config('dev')
        print('✅ 配置系统正常')
        
        # 2. 路径系统检查  
        db_path = loader.expand_cross_platform_path('{datacenter_dir}/aqua_dev.duckdb')
        print(f'✅ 数据库路径可访问: {db_path}')
        
        # 3. 数据源基础检查
        from src.cli.main import main
        print('✅ CLI模块导入成功')
        
        print('🎉 快速健康检查通过!')
        return True
        
    except Exception as e:
        print(f'❌ 健康检查失败: {e}')
        return False

if __name__ == '__main__':
    success = quick_health_check()
    sys.exit(0 if success else 1)
"@ | Out-File -FilePath "aqua_health_check.py" -Encoding UTF8
```

### 7.2 版本升级验证

每次AQUA版本更新后，建议执行：

```powershell
# 版本升级后验证流程
echo "=== 版本升级验证 ==="

# 1. 快速健康检查
python aqua_health_check.py

# 2. 配置兼容性验证
python -c "
loader = ConfigLoader()
validation = loader.validate_config('dev')
print('配置验证:', validation['valid'])
if not validation['valid']:
    print('错误:', validation['errors'])
"

# 3. 核心功能抽样测试
python -m src.cli.main collect --check-capabilities --source tushare
python -m src.cli.main collect 000001.SZ --source tushare --preview --start-date 2024-07-01 --end-date 2024-07-01
```

---

## 🎯 **总结与后续行动**

通过完成本指南的四层测试，您将获得：

1. **配置系统完全掌握** - 深入理解AQUA的config/.env + settings.toml架构
2. **跨平台兼容验证** - 确保Windows 11环境下的路径和功能正确性
3. **完整数据链路验证** - 验证从配置到数据存储的端到端流程
4. **生产就绪评估** - 获得AQUA系统在Windows下的生产可用性评估

### 🚀 **下一步建议**

**如果测试完全通过:**
- 可以放心在Windows 11环境下使用AQUA进行量化分析
- 建议定期执行快速健康检查
- 考虑设置自动化的数据备份策略

**如果测试部分通过:**
- 优先解决配置系统和核心数据源问题
- 针对失败的测试项目进行专项修复
- 在修复后重新执行完整测试流程

**如果测试失败较多:**
- 检查Windows系统环境和权限设置
- 验证Python和依赖库安装的完整性
- 考虑在虚拟机中建立标准测试环境

---

**文档维护信息:**
- **创建日期**: 2024-08-01
- **最后更新**: 2024-08-01  
- **适用版本**: AQUA v2.0+
- **测试覆盖**: Windows 11 21H2+
- **维护责任**: AQUA开发团队

**反馈渠道:**
如发现本指南中的问题或需要补充，请通过项目Issue系统提交反馈。