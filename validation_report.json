{"platform": "macos", "timestamp": "macOS-12.7.6-x86_64-i386-64bit", "summary": {"total_tests": 43, "passed": 41, "failed": 2, "pass_rate": "95.3%"}, "results": [{"test": "Python版本", "passed": true, "message": "3.11.3"}, {"test": "Package: fastapi", "passed": true, "message": "已安装"}, {"test": "Package: uvic<PERSON>", "passed": true, "message": "已安装"}, {"test": "Package: polars", "passed": true, "message": "已安装"}, {"test": "Package: duckdb", "passed": true, "message": "已安装"}, {"test": "Package: pydantic", "passed": true, "message": "已安装"}, {"test": "Package: python-multipart", "passed": true, "message": "已安装"}, {"test": "Node.js版<PERSON>", "passed": true, "message": "v24.4.0"}, {"test": "pnpm版本", "passed": true, "message": "10.12.4"}, {"test": "结构: main.py", "passed": true, "message": "FastAPI主程序"}, {"test": "结构: pyproject.toml", "passed": true, "message": "Python项目配置"}, {"test": "结构: CLAUDE.md", "passed": true, "message": "项目文档"}, {"test": "结构: frontend", "passed": true, "message": "前端项目根目录"}, {"test": "结构: frontend/package.json", "passed": true, "message": "前端包配置"}, {"test": "结构: frontend/src", "passed": true, "message": "前端源码目录"}, {"test": "结构: frontend/src/App.vue", "passed": true, "message": "前端主应用"}, {"test": "结构: src", "passed": true, "message": "后端源码目录"}, {"test": "结构: src/api", "passed": true, "message": "API路由目录"}, {"test": "结构: src/database", "passed": true, "message": "数据库相关"}, {"test": "结构: src/data_import", "passed": true, "message": "数据导入模块"}, {"test": "结构: config", "passed": true, "message": "配置目录"}, {"test": "结构: config/settings.toml", "passed": true, "message": "应用配置"}, {"test": "结构: scripts", "passed": true, "message": "脚本目录"}, {"test": "跨平台助手", "passed": true, "message": "CrossPlatformHelper.ts存在"}, {"test": "个人配置管理", "passed": true, "message": "PersonalConfig.ts存在"}, {"test": "启动脚本", "passed": true, "message": "dev_personal.sh可执行"}, {"test": "Python启动器", "passed": true, "message": "start_aqua.py存在"}, {"test": "数据目录路径", "passed": true, "message": "预期: /Users/<USER>/Documents/AQUA_Data"}, {"test": "路径兼容性", "passed": true, "message": "路径为纯ASCII字符，兼容性良好"}, {"test": "前端端口 8080", "passed": true, "message": "可用"}, {"test": "后端端口 8000", "passed": false, "message": "被占用"}, {"test": "TS配置: tsconfig.json", "passed": true, "message": "存在"}, {"test": "路径别名配置", "passed": true, "message": "@/* 别名已配置"}, {"test": "TS配置: tsconfig.app.json", "passed": true, "message": "存在"}, {"test": "路径别名配置", "passed": false, "message": "@/* 别名未配置"}, {"test": "Vite配置", "passed": true, "message": "vite.config.js存在"}, {"test": "Vite别名配置", "passed": true, "message": "路径别名已配置"}, {"test": "组件: 文件导入组件", "passed": true, "message": "存在"}, {"test": "跨平台集成: 文件导入组件", "passed": true, "message": "已集成CrossPlatformHelper"}, {"test": "组件: 数据中心页面", "passed": true, "message": "存在"}, {"test": "跨平台集成: 数据中心页面", "passed": true, "message": "已集成CrossPlatformHelper"}, {"test": "组件: 数据中心API", "passed": true, "message": "存在"}, {"test": "组件: FromC2C验证器", "passed": true, "message": "存在"}], "recommendations": ["关闭占用8000/8080端口的服务或使用自定义端口"]}