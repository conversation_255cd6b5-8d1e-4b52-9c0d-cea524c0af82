[project]
name = "aqua"
version = "4.0.0"
description = "AQUA 量化分析平台 - 个人开发者版"
readme = "README.md"
authors = [
    {name = "AQUA Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
requires-python = ">=3.11"

# 核心依赖 (整合所有requirements)
dependencies = [
    # 核心框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.20.0", 
    "typer>=0.9.0",
    
    # 数据处理
    "polars>=0.20.0",
    "duckdb>=0.9.0", 
    "pandas>=2.0.0",
    
    # 跨平台支持
    "rich>=13.0.0",
    "pathlib-abc>=0.1.0",
    
    # 网络和API
    "httpx>=0.25.0",
    "aiofiles>=23.0.0",
    
    # Windows特定依赖 (条件依赖)
    "pywin32>=306; sys_platform == 'win32'",
    "colorama>=0.4.6; sys_platform == 'win32'",
    
    # 金融数据
    "tushare>=1.2.0",
    "pymysql>=1.1.0",
    
    # 基础工具
    "python-dotenv>=1.0.0",
    "cryptography>=41.0.0",
    "toml>=0.10.2",
    "pydantic>=2.0.0",
    "python-multipart>=0.0.6",
    "jinja2>=3.1.0",
    "pytz>=2023.3"
]

[project.optional-dependencies]
# 开发依赖
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "mypy>=1.5.0", 
    "ruff>=0.1.0",
    "pre-commit>=3.4.0"
]

# 测试依赖
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "httpx>=0.25.0"
]

# 分析工具
analysis = [
    "jupyter>=1.0.0",
    "ipython>=8.0.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.15.0",
    "notebook>=7.0.0"
]

[project.scripts]
aqua = "aqua.main:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "jupyter>=1.0.0",
    "ipython>=8.0.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.15.0",
    "notebook>=7.0.0",
]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | node_modules
)/
'''

[tool.mypy]
python_version = "3.11"
strict = true

[tool.ruff]
target-version = "py311"
line-length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
addopts = "-v --cov=src --cov-report=html"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/.*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]