# AQUA CLI 跨平台使用指南

## 🚀 快速开始

### macOS/Linux 用户

1. **使用Python脚本** (推荐):
   ```bash
   python aqua.py status
   python aqua.py init
   python aqua.py mirror --test
   ```

2. **使用Shell脚本**:
   ```bash
   ./aqua.sh status
   ./aqua.sh init
   ./aqua.sh mirror --test
   ```

### Windows 用户

1. **使用Python脚本** (推荐):
   ```cmd
   python aqua.py status
   python aqua.py init
   python aqua.py mirror --test
   ```

2. **使用批处理脚本**:
   ```cmd
   aqua.bat status
   aqua.bat init
   aqua.bat mirror --test
   ```

## 🔧 环境准备

### 依赖安装

**macOS/Linux:**
```bash
# 激活虚拟环境 (如果需要)
source .venv/bin/activate

# 安装依赖
uv pip install -r requirements.txt
```

**Windows:**
```cmd
# 激活虚拟环境 (如果需要)
.venv\Scripts\activate

# 安装Windows优化依赖
uv pip install -r requirements-windows.txt
```

## 🌟 特性对比

| 功能 | macOS | Windows | 说明 |
|------|-------|---------|------|
| ✅ CLI基础功能 | 完全支持 | 完全支持 | 所有命令均可用 |
| ✅ 智能镜像源 | 完全支持 | 完全支持 | 自动选择最佳镜像源 |
| ✅ 虚拟环境检测 | 自动检测 | 自动检测 | 自动使用.venv环境 |
| ✅ UTF-8编码 | 原生支持 | 自动设置 | 中文界面完全支持 |
| ✅ 路径管理 | Unix风格 | Windows风格 | 自动适配路径分隔符 |

## 🛠️ 故障排除

### 常见问题

1. **ImportError: No module named 'typing_extensions'**
   
   **解决方案:**
   ```bash
   # macOS/Linux
   .venv/bin/pip install typing_extensions shellingham pygments
   
   # Windows
   .venv\Scripts\pip install typing_extensions shellingham pygments
   ```

2. **编码错误 (Windows)**
   
   **解决方案:**
   ```cmd
   # 设置UTF-8编码
   chcp 65001
   set PYTHONUTF8=1
   ```

3. **权限错误 (macOS/Linux)**
   
   **解决方案:**
   ```bash
   # 使shell脚本可执行
   chmod +x aqua.sh
   ```

## 🔄 镜像源配置

系统会根据网络环境自动选择最佳镜像源：

### 中国网络环境优化
- **阿里云镜像**: 全国覆盖，企业级支持
- **腾讯云镜像**: 华南地区优化，CDN加速
- **清华大学镜像**: 华北地区，高同步频率
- **华为云镜像**: 华南地区，云原生支持

### 测试镜像源性能
```bash
# 测试所有镜像源
python aqua.py mirror --test

# 显示最佳镜像源
python aqua.py mirror --best
```

## 📝 使用示例

### 1. 系统初始化
```bash
# 初始化项目环境
python aqua.py init

# 检查系统状态
python aqua.py status

# 运行健康检查
python aqua.py doctor --auto-fix
```

### 2. 镜像源管理
```bash
# 查看镜像源状态
python aqua.py mirror --status

# 测试镜像源性能
python aqua.py mirror --test

# 选择最佳镜像源
python aqua.py mirror --best --strategy speed
```

### 3. 开发工具
```bash
# 启动开发服务
python aqua.py start

# 停止服务
python aqua.py stop

# 查看使用统计
python aqua.py stats
```

## 📋 支持的命令

| 命令 | 功能 | macOS | Windows |
|------|------|-------|---------|
| `init` | 初始化项目环境 | ✅ | ✅ |
| `status` | 显示系统状态 | ✅ | ✅ |
| `start` | 启动服务 | ✅ | ✅ |
| `stop` | 停止服务 | ✅ | ✅ |
| `doctor` | 健康检查 | ✅ | ✅ |
| `setup` | 配置向导 | ✅ | ✅ |
| `mirror` | 镜像源管理 | ✅ | ✅ |
| `stats` | 使用统计 | ✅ | ✅ |
| `dev` | 开发工具 | ✅ | ✅ |
| `windows` | Windows兼容 | ⚠️ | ✅ |

## 🎯 最佳实践

### 1. 开发环境设置
```bash
# 创建并激活虚拟环境
python -m venv .venv
source .venv/bin/activate  # macOS/Linux
# 或
.venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt  # macOS/Linux
pip install -r requirements-windows.txt  # Windows
```

### 2. 定期维护
```bash
# 更新依赖
python aqua.py doctor --auto-fix

# 检查镜像源状态
python aqua.py mirror --test

# 查看系统状态
python aqua.py status
```

### 3. 性能优化
```bash
# 选择最快的镜像源
python aqua.py mirror --best --strategy speed

# 启用缓存
python aqua.py setup  # 选择启用缓存选项
```

## 🆘 获取帮助

- **查看命令帮助**: `python aqua.py --help`
- **查看子命令帮助**: `python aqua.py <command> --help`
- **启用调试模式**: `AQUA_DEBUG=1 python aqua.py <command>`
- **问题报告**: 提交到项目的Issues页面

---

**版本**: v3.3.0  
**更新日期**: 2025-08-01  
**支持平台**: macOS, Windows 11, Linux