# AQUA 核心依赖 - 跨平台兼容版本
# 适用于 Windows/macOS/Linux

# === 核心Python依赖 ===
typing_extensions>=4.0.0
shellingham>=1.3.0

# === CLI 和用户界面 ===
typer>=0.9.0,<1.0.0
rich>=13.0.0
click>=8.0.0
pygments>=2.0.0

# === 数据处理和存储 ===
duckdb>=1.3.0,<2.0.0
polars>=1.30.0
pandas>=2.2.0
numpy>=1.24.0

# === Web框架和API ===
fastapi>=0.100.0
uvicorn>=0.20.0
starlette>=0.37.0
pydantic>=2.5.0

# === 数据源和网络 ===
requests>=2.31.0
aiohttp>=3.9.0
tushare>=1.4.0
yfinance>=0.2.30
akshare>=1.14.0

# === 数据库 ===
pymysql>=1.1.0
python-multipart>=0.0.6

# === 配置和工具 ===
python-dotenv>=1.0.0
toml>=0.10.0
pyyaml>=6.0.0

# === 系统监控 ===
psutil>=5.9.0

# === 测试 ===
pytest>=7.4.0

# === 数据分析和可视化 ===
matplotlib>=3.7.0
plotly>=5.15.0
quantstats>=0.0.60
seaborn>=0.12.0

# === 工具库 ===
python-dateutil>=2.8.0
apscheduler>=3.10.0
ratelimit>=2.2.0

# === 安全和认证 ===
python-jose>=3.3.0

# === 异步和网络 (跨平台) ===
# 注意: uvloop 仅在 Unix 系统支持, Windows下会自动跳过
# httptools 在 Windows 下也可能有问题，但通常可以安装

# === 可选: Unix系统专用 (Windows下会跳过) ===
# uvloop>=0.17.0; sys_platform != "win32"
# httptools>=0.5.0; sys_platform != "win32"

# === Web和爬虫相关 ===
beautifulsoup4>=4.12.0
lxml>=4.9.0
html5lib>=1.1

# === 数据文件处理 ===
openpyxl>=3.1.0
xlrd>=2.0.0

# === Streamlit (可选，用于Web界面) ===
streamlit>=1.28.0

# === Windows支持 ===
pywin32>=306; sys_platform == "win32"

# === 开发工具（可选） ===
mypy>=1.0.0
black>=23.0.0
ruff>=0.0.270
isort>=5.12.0

# === 其他工具 ===
tqdm>=4.65.0