# AQUA - 个人量化投机武器库

> **文档地位**: 本文档是AQUA项目的**唯一入口和架构仪表盘**。它为开发者提供了对整个系统最高级别、最全面的概览，包括其核心理念、快速上手指南、完整的项目结构解析以及核心组件之间的多维依赖关系。

---

## ✨ 核心理念 (Core Philosophy)

*   **配置驱动**: `config/settings.toml` 是驱动所有行为的唯一事实来源，杜绝硬编码。
*   **规则先行**: `docs/` 目录下的权威文档是项目的“宪法”，代码实现严格遵循文档定义。
*   **自动化**: 所有重复性任务，特别是环境设置、数据处理和测试，都必须通过脚本实现自动化。
*   **ELT 架构**: 采用现代数据栈的 `Extract-Load-Transform` 模式，保证数据处理的高性能和高保真性。

---

## 🚀 快速开始 (Quick Start)

### 核心入口 (仅2个)

#### CLI模式
```bash
# OS X / Linux
python aqua.py [命令]

# Windows
aqua.bat [命令]
# 或
python aqua.py [命令]
```

#### API服务模式
```bash
python main.py
```

### 快速启动
1. CLI模式: `python aqua.py --help`
2. API服务: `python main.py` 然后访问 http://localhost:8000/docs

### 环境配置

1.  **环境初始化**: 首次运行或环境变更后，执行此命令。它将自动处理虚拟环境、依赖安装、数据库创建等所有事务。
    ```bash
    python scripts/env_init.py
    ```

2.  **激活环境**:
    ```bash
    # macOS / Linux
    source .venv/bin/activate
    
    # Windows
    .venv\Scripts\activate
    ```

3.  **运行数据导入**: 以`FROMC2C`数据为例，执行完整的检查、建表、导入和验证流程。
    ```bash
    python scripts/fromC2C_import_cli.py --env dev --full
    ```

4.  **启动后端服务**:
    ```bash
    ./start_backend.sh
    ```

5.  **启动前端服务**:
    ```bash
    ./start_frontend.sh
    ```

---

## 🗺️ 项目架构深度解析 (Architecture Deep Dive)

### 1. 多维关系图 (Multi-Dimensional Dependency Graph)

此图展示了项目的核心信息流，阐明了“文档”是如何驱动“代码”实现的，以及业务逻辑是如何在不同代码模块间流转的。

```mermaid
graph TD
    subgraph "权威定义层 (Authoritative Definition)"
        direction LR
        D_CLAUDE["CLAUDE.md<br/>(最高执行指令)"]
        D_GUIDE["AQUA_GUIDE.md<br/>(项目总纲)"]
        D_DICT["DATA_DICTIONARY.md<br/>(数据结构法典)"]
    end

    subgraph "规划与配置层 (Planning & Configuration)"
        direction LR
        D_ROADMAP["AQUA_ROADMAP.md<br/>(战略路线图)"]
        D_TASKS["Dev_Tasks_EPIC*.md<br/>(战术任务清单)"]
        F_CONFIG["config/settings.toml<br/>(行为配置文件)"]
    end

    subgraph "代码实现层 (Code Implementation)"
        direction TB
        C_INIT_CHECK["src/database/duckdb_init_check.py<br/>(数据库初始化/验证器)"]
        C_CONN_MAN["src/database/connection_manager.py<br/>(数据库连接器)"]
        C_IMPORTER["src/data_import/*_importer.py<br/>(数据导入逻辑)"]
        S_ENV_INIT["scripts/env_init.py<br/>(环境守卫)"]
        S_IMPORT_CLI["scripts/*_import_cli.py<br/>(数据导入入口)"]
    end
    
    subgraph "执行与协作 (Execution & Collaboration)"
         DEV["Developer / Gemini"]
    end

    %% --- 依赖关系 ---
    DEV -- "遵循 (Follows)" --> D_CLAUDE
    DEV -- "遵循 (Follows)" --> D_GUIDE
    DEV -- "执行 (Executes)" --> D_TASKS
    
    D_GUIDE -- "指导 (Guides)" --> D_ROADMAP
    D_ROADMAP -- "分解为 (Decomposes to)" --> D_TASKS

    F_CONFIG -- "配置 (Configures)" --> C_CONN_MAN
    F_CONFIG -- "配置 (Configures)" --> C_IMPORTER
    F_CONFIG -- "配置 (Configures)" --> S_ENV_INIT
    F_CONFIG -- "配置 (Configures)" --> C_INIT_CHECK

    D_DICT -- "定义Schema<br/>(Defines Schema for)" --> C_INIT_CHECK
    
    C_INIT_CHECK -- "使用 (Uses)" --> C_CONN_MAN
    C_IMPORTER -- "使用 (Uses)" --> C_CONN_MAN
    
    S_ENV_INIT -- "调用 (Calls)" --> C_INIT_CHECK
    S_IMPORT_CLI -- "调用 (Calls)" --> C_IMPORTER
    
    classDef definition fill:#e6f2ff,stroke:#0066cc,stroke-width:2px;
    classDef planning fill:#e6ffed,stroke:#009933,stroke-width:2px;
    classDef code fill:#fff5e6,stroke:#ff9900,stroke-width:2px;
    classDef actor fill:#f0e6ff,stroke:#9933ff,stroke-width:2px;

    class D_CLAUDE,D_GUIDE,D_DICT definition;
    class D_ROADMAP,D_TASKS,F_CONFIG planning;
    class C_INIT_CHECK,C_CONN_MAN,C_IMPORTER,S_ENV_INIT,S_IMPORT_CLI code;
    class DEV actor;
```

### 2. 完整结构树与组件职责 (Full Structure Tree & Component Responsibilities)

这是AQUA项目的完整文件结构“照片”，以及每个关键组件的核心职责说明。

```
.
├── .claude/                # Claude/Cursor AI的本地配置文件
├── .cursor/                # Cursor IDE的配置文件目录
│   └── rules/              # Cursor的强制代码规则，定义了各领域的开发标准
├── .eslintrc.js            # ESLint配置文件 (JavaScript)
├── .gitignore              # Git忽略文件配置
├── .lintstagedrc.json      # lint-staged配置文件，用于在Git暂存时运行脚本
├── .nvmrc                  # Node.js版本锁定文件
├── .pre-commit-config.yaml # pre-commit钩子配置文件，强制代码提交前的质量检查
├── .prettierrc             # Prettier代码格式化配置文件
├── .python-version         # pyenv的Python版本锁定文件
├── .vscode/                # VSCode编辑器特定配置
├── CLAUDE.md               # 与Claude AI协作的最高执行指令
├── chating.md              # 与AI协作的通用沟通记录
├── config/                 # [配置中心] 项目所有配置的存放目录
│   ├── .env.example        # 环境变量模板文件
│   └── settings.toml       # [项目唯一事实来源] 定义所有环境的数据库、路径、参数等
├── data/                   # [数据存储] 本地数据仓库目录
│   ├── backup/             # 数据库及重要数据的备份目录
│   ├── duckdb/             # DuckDB数据库文件存放目录
│   ├── processed/          # 已处理/清洗后的数据
│   └── raw/                # 原始数据
├── docs/                   # [项目大脑与宪法] 所有权威文档的存放目录
│   ├── AQUA_GUIDE.md       # [唯一权威指南] 项目所有规范、流程和标准的最高定义
│   ├── AQUA_ROADMAP.md     # [战略规划] 项目发展的史诗级（Epic）路线图
│   ├── database/
│   │   └── DATA_DICTIONARY.md # [数据法典] 数据库所有表结构、字段和类型的唯一权威定义
│   ├── tasks/
│   │   └── Dev_Tasks_EPIC*.md # [战术执行清单] 各个Epic的原子化TDD任务列表
│   └── ai_prompts/
│       └── gemini.md       # [AI记忆库] 记录与Gemini协作的关键共识
├── frontend/               # [前端用户界面] Vue 3 + TypeScript项目源码
│   ├── public/             # 静态资源，会被直接复制到构建输出目录
│   ├── src/                # 前端核心源码
│   │   ├── api/            # 后端API接口的TypeScript封装
│   │   ├── assets/         # 前端组件使用的静态资源（图片、样式等）
│   │   ├── components/     # 可复用的通用UI组件
│   │   ├── modules/        # 按业务功能划分的页面模块（如数据中心、回测）
│   │   ├── router/         # Vue Router的路由配置
│   │   └── stores/         # Pinia状态管理
│   ├── tests/              # 前端测试目录
│   │   ├── mock/           # 前端API的Mock数据
│   │   └── unit/           # 单元测试
│   └── vite.config.ts      # Vite构建工具的配置文件
├── logs/                   # [日志中心] 存放各类日志文件
│   ├── audit/              # 关键操作（如数据库变更）的审计日志
│   └── dev_log.md          # 开发过程中的结构化变更日志
├── main.py                 # 后端FastAPI应用的主入口（规划中）
├── scripts/                # [自动化中心] 存放所有自动化脚本
│   ├── env_init.py         # [环境守卫] 项目基石，负责环境检测、依赖安装、配置加载和DB初始化
│   ├── fromC2C_import_cli.py # [数据导入脚本] FROMC2C数据源的命令行导入工具
│   ├── reset_database.py   # 数据库重置工具，提供安全、可重复的数据库清空和重建功能
│   └── git_hooks/          # Git钩子脚本，如pre-commit
├── src/                    # [后端核心逻辑] Python后端源码
│   ├── api/                # (规划中) FastAPI的路由和API端点实现
│   ├── data_import/        # 数据导入模块
│   │   └── fromC2C_csv_main_contract_importer.py # 实现了FROMC2C数据源的完整导入逻辑
│   ├── database/           # 数据库交互模块
│   │   ├── connection_manager.py # 负责管理与DuckDB的连接
│   │   └── duckdb_init_check.py  # [数据库立法者] 读取数据字典，验证和创建表结构
│   ├── modules/            # (规划中) 核心业务逻辑模块（回测引擎、策略管理等）
│   └── utils/              # 通用工具函数（日志、配置加载、异常处理等）
└── tests/                  # [质量保证中心] 后端测试代码
    ├── fixtures/           # Pytest的Fixtures，提供测试数据和通用设置
    ├── integration/        # 集成测试，测试多个组件协同工作的场景
    └── unit/               # 单元测试，测试单个函数或类的逻辑
```

---

## 🛠️ 技术栈 (Tech Stack)

*   **后端**: Python 3.11+, FastAPI, DuckDB, Polars
*   **前端**: Vue 3, TypeScript, Vite, Pinia, Naive UI
*   **依赖管理**: `uv` (Python), `pnpm` (Node.js)
*   **代码质量**: `pre-commit`, `flake8`, `black`, `eslint`, `prettier`
*   **测试**: `pytest`

---
*最后更新: 2025-07-20*
