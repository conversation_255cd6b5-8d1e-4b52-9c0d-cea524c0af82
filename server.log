/Users/<USER>/Documents/AQUA/Dev/AQUA/main.py:71: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
/Users/<USER>/Documents/AQUA/Dev/AQUA/main.py:135: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("shutdown")
INFO:     Started server process [77030]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
[2025-07-26T18:26:58.164733+08:00][INFO][main] 以调试模式启动Uvicorn服务...
[2025-07-26T18:26:58.211009+08:00][INFO][startup] AQUA API服务启动中...
[2025-07-26T18:26:58.211009+08:00][INFO][startup] 使用环境: dev
[2025-07-26T18:26:58.211009+08:00][INFO][startup] 开始数据库自动初始化...
[2025-07-26T18:26:58.211009+08:00][INFO][startup] 数据库初始化成功: 数据库初始化完成，创建了 0 个表
[2025-07-26T18:26:58.211009+08:00][INFO][startup] 数据库健康状态: ok
[2025-07-26T18:26:58.211009+08:00][INFO][startup] 日志级别: DEBUG
[2025-07-26T18:26:58.211009+08:00][INFO][startup] 最大工作线程: 2
[2025-07-26T18:26:58.211009+08:00][INFO][startup] AQUA API服务启动完成
[2025-07-26T18:26:58.211009+08:00][INFO][startup] WebSocket管理器初始化完成
INFO:     127.0.0.1:64272 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:64273 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:64274 - "GET /api/data/config/mysql/dev HTTP/1.1" 200 OK
INFO:     127.0.0.1:64275 - "GET /api/data/config/mysql/test HTTP/1.1" 200 OK
INFO:     127.0.0.1:64276 - "GET /api/data/config/mysql/prod HTTP/1.1" 200 OK
INFO:     127.0.0.1:64277 - "POST /api/data/import/pre-check HTTP/1.1" 200 OK
INFO:     127.0.0.1:64278 - "POST /api/data/import/pre-check HTTP/1.1" 200 OK
INFO:     127.0.0.1:64279 - "POST /api/data/import/pre-check HTTP/1.1" 200 OK
