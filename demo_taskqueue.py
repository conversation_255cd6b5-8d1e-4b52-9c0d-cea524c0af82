#!/usr/bin/env python3
"""
TaskQueueEngine 集成演示脚本

展示TaskQueueEngine在AQUA项目中的完整功能
"""

import sys
import time
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.task_queue_engine import TaskQueueEngine, TaskPriority, TaskStatus


def demo_task_processors():
    """演示任务处理器"""
    
    def epic_processor(task_type, payload):
        print(f"🎯 处理Epic: {payload['title']}")
        print(f"   描述: {payload.get('description', 'N/A')}")
        print(f"   预估工时: {payload.get('estimated_hours', 'N/A')}小时")
        return {
            "status": "success", 
            "epic_id": f"epic-{int(time.time())}",
            "message": "Epic创建成功"
        }
    
    def feature_processor(task_type, payload):
        print(f"🚀 处理Feature: {payload['title']}")
        print(f"   所属Epic: {payload.get('epic_id', 'N/A')}")
        print(f"   描述: {payload.get('description', 'N/A')}")
        return {
            "status": "success", 
            "feature_id": f"feature-{int(time.time())}",
            "message": "Feature创建成功"
        }
    
    def task_processor(task_type, payload):
        print(f"✅ 处理Task: {payload['title']}")
        print(f"   所属Feature: {payload.get('feature_id', 'N/A')}")
        print(f"   指派给: {payload.get('assignee', 'N/A')}")
        return {
            "status": "success", 
            "task_id": f"task-{int(time.time())}",
            "message": "Task创建成功"
        }
    
    return epic_processor, feature_processor, task_processor


def main():
    """主演示函数"""
    print("🎉 AQUA TaskQueueEngine 集成演示")
    print("=" * 50)
    
    # 1. 初始化TaskQueueEngine
    print("\n📋 1. 初始化TaskQueueEngine")
    config = {
        "max_queue_size": 100,
        "worker_count": 2,
        "max_retry_count": 3,
        "task_timeout": 30,
        "enable_monitoring": True
    }
    
    engine = TaskQueueEngine(config)
    print(f"✅ TaskQueueEngine初始化成功")
    print(f"   最大队列大小: {engine.max_queue_size}")
    print(f"   工作线程数: {engine.worker_count}")
    
    # 2. 注册任务处理器
    print("\n🔧 2. 注册任务处理器")
    epic_proc, feature_proc, task_proc = demo_task_processors()
    
    engine.register_processor("epic_creation", epic_proc)
    engine.register_processor("feature_creation", feature_proc)
    engine.register_processor("task_creation", task_proc)
    print("✅ 任务处理器注册完成")
    
    # 3. 提交任务
    print("\n📝 3. 提交项目任务")
    
    # 提交Epic任务
    epic_data = {
        "title": "AQUA用户认证系统",
        "description": "实现完整的用户认证和权限管理系统",
        "estimated_hours": 40
    }
    epic_id = engine.submit_epic_task(epic_data, TaskPriority.HIGH)
    print(f"🎯 Epic任务已提交: {epic_id}")
    
    # 提交Feature任务
    feature_data = {
        "title": "用户登录功能",
        "description": "实现用户登录、注销和会话管理",
        "epic_id": epic_id,
        "estimated_hours": 12
    }
    feature_id = engine.submit_feature_task(feature_data)
    print(f"🚀 Feature任务已提交: {feature_id}")
    
    # 提交Task任务
    task_data = {
        "title": "设计登录API接口",
        "description": "设计RESTful登录API的接口规范",
        "feature_id": feature_id,
        "assignee": "开发工程师",
        "estimated_hours": 4
    }
    task_id = engine.submit_task_task(task_data)
    print(f"✅ Task任务已提交: {task_id}")
    
    # 4. 显示统计信息
    print("\n📊 4. 任务队列统计")
    stats = engine.get_project_statistics()
    print(f"   总任务数: {stats['total_tasks']}")
    print(f"   Epic数量: {stats['epic_count']}")
    print(f"   Feature数量: {stats['feature_count']}")
    print(f"   Task数量: {stats['task_count']}")
    print(f"   待处理: {stats['pending_count']}")
    print(f"   已完成: {stats['completed_count']}")
    
    # 5. 启动工作线程处理任务
    print("\n⚡ 5. 启动任务处理")
    engine.start_workers()
    print("🔄 工作线程已启动，正在处理任务...")
    
    # 等待任务完成
    time.sleep(2)
    
    # 6. 显示最终统计
    print("\n📈 6. 处理完成后统计")
    final_stats = engine.get_project_statistics()
    print(f"   总任务数: {final_stats['total_tasks']}")
    print(f"   已完成: {final_stats['completed_count']}")
    print(f"   完成率: {final_stats['completion_rate']:.2%}")
    print(f"   失败率: {final_stats['failure_rate']:.2%}")
    
    # 7. 验证任务状态
    print("\n🔍 7. 验证任务执行结果")
    epic_task = engine.get_task(epic_id)
    feature_task = engine.get_task(feature_id)
    task_task = engine.get_task(task_id)
    
    print(f"   Epic状态: {epic_task.status.value}")
    print(f"   Feature状态: {feature_task.status.value}")
    print(f"   Task状态: {task_task.status.value}")
    
    if epic_task.result:
        print(f"   Epic结果: {epic_task.result}")
    
    # 8. 清理
    print("\n🧹 8. 清理资源")
    engine.shutdown()
    print("✅ TaskQueueEngine已关闭")
    
    print("\n🎊 演示完成！TaskQueueEngine已成功集成到AQUA项目中")
    print("\n💡 使用方法:")
    print("   python aqua.py taskqueue status          # 查看队列状态")
    print("   python aqua.py taskqueue submit-epic     # 提交Epic任务")
    print("   python aqua.py taskqueue submit-feature  # 提交Feature任务")
    print("   python aqua.py taskqueue submit-task     # 提交Task任务")


if __name__ == "__main__":
    main()
