# AQUA项目路径大小写转换问题分析报告

## 问题描述
配置文件中正确配置的Windows路径"D:/Data/duckdb/AQUA/DataCenter"被错误转换为小写的"d:\data\duckdb\aqua\datacenter\"。

## 根本原因分析

### 1. 配置节名不匹配问题
**问题核心：** ConfigLoader中的配置节名与settings.toml中的实际配置节名不匹配。

- **ConfigLoader期望：** `cross_platform` 配置节
- **settings.toml实际：** `platform` 配置节

### 2. 具体代码问题

#### ConfigLoader.get_cross_platform_config()方法
```python
def get_cross_platform_config(self) -> Dict:
    return self._config_cache.get("cross_platform", {})  # ❌ 错误：查找不存在的节
```

#### _resolve_platform_placeholders()方法
```python
if platform_info["is_windows"]:
    platform_paths = cross_platform_config.get("windows_paths", {})  # ❌ 期望错误的结构
else:
    platform_paths = cross_platform_config.get("unix_paths", {})
```

### 3. 问题链条
1. `get_cross_platform_config()` 返回空字典 `{}`（因为找不到`cross_platform`节）
2. `platform_paths` 变成空字典 `{}`
3. 占位符映射使用硬编码默认值：`"~/Documents/Data/duckdb/AQUA"`
4. `{datacenter_dir}` 占位符无法被正确替换
5. 最终路径使用Unix风格的默认路径并可能被系统转换为小写

## 调试验证结果

运行调试脚本的结果证实了分析：

```
1. 检查跨平台配置:
   cross_platform配置: {}  # ❌ 空字典，无法读取配置

2. 检查platform配置:
   datacenter_dir: D:/Data/duckdb/AQUA/DataCenter  # ✅ 正确配置存在

3. 测试占位符解析:
   占位符解析后: {datacenter_dir}/aqua_test.duckdb  # ❌ 占位符未被替换

4. 测试数据库配置:  
   数据库路径: {datacenter_dir}/aqua_test.duckdb  # ❌ 占位符仍然存在
```

## 解决方案

### 方案1：修改ConfigLoader（推荐）
修改`src/utils/config_loader.py`中的配置读取逻辑：

```python
def get_cross_platform_config(self) -> Dict:
    """获取跨平台配置"""
    if self._config_cache is None:
        self._config_cache = self._load_config()
    return self._config_cache.get("platform", {})  # 修改：使用platform节

def _resolve_platform_placeholders(self, path_config: str) -> str:
    """解析平台特定占位符"""
    cross_platform_config = self.get_cross_platform_config()
    platform_info = self.get_platform_info()

    # 修改：直接从platform配置中读取paths
    if platform_info["is_windows"]:
        platform_paths = cross_platform_config.get("windows", {}).get("paths", {})
    else:
        platform_paths = cross_platform_config.get("unix", {}).get("paths", {})
    
    # 其余逻辑保持不变...
```

### 方案2：修改配置文件结构（不推荐）
将settings.toml中的`[platform]`节重命名为`[cross_platform]`，但这会影响其他依赖platform配置的代码。

## 影响范围
- 所有使用占位符的路径配置（如数据库路径、日志路径、缓存路径等）
- 跨平台部署的一致性
- DuckDB数据库连接初始化

## 修复优先级
🔴 **高优先级** - 这是一个影响核心功能的配置解析错误，需要立即修复。