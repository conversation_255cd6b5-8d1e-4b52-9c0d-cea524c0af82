@echo off
setlocal

::
:: 统一部署脚本 for Windows
::
:: 用法:
::   scripts\deploy.bat [local|test|prod]
::
:: 描述:
::   本脚本用于自动化部署AQUA项目到不同环境。
::   它首先会强制执行环境校验，然后根据传入的参数
::   执行对应环境的部署流程。
::
::   - local: 启动本地开发服务器，支持热重载。
::   - test:  (待实现) 构建测试环境产物并部署。
::   - prod:  (待实现) 构建生产环境产物并部署。
::
:: 作者: CURSOR/GEMINI
:: 创建日期: 2024-07-27
::

:: --- 配置 ---
:: 将当前目录切换到脚本所在目录的上级目录（项目根目录）
pushd %~dp0..
set "BASE_DIR=%CD%"

:: --- 伪函数定义 ---
:: 使用 goto :eof 模拟函数返回

:log_info
echo %DATE% %TIME% [INFO] [deploy.bat] %~1
goto :eof

:log_error
echo %DATE% %TIME% [ERROR] [deploy.bat] %~1 >&2
goto :eof

:: --- 主逻辑 ---

:: 1. 解析环境参数
set "ENV=%1"
if not defined ENV (
    call :log_error "未提供环境参数。用法: deploy.bat [local^|test^|prod]"
    goto :end
)

:: 2. 强制环境校验
call :log_info "开始执行环境校验 (scripts/env_init.py)..."
python scripts/env_init.py
if %errorlevel% neq 0 (
    call :log_error "环境校验失败，中止部署。"
    goto :end
)
call :log_info "环境校验通过。"

:: 3. 根据环境执行不同部署逻辑
call :log_info "准备为 '%ENV%' 环境执行部署..."

if /i "%ENV%"=="local" (
    call :log_info "正在启动本地开发环境..."
    call :log_info "将在新窗口中分别启动前端和后端服务。"
    start "AQUA Frontend" cmd /c "call start_frontend.bat"
    start "AQUA Backend" cmd /c "call start_backend.bat"
    call :log_info "前端和后端服务已在新的终端窗口启动。"
) else if /i "%ENV%"=="test" (
    call :log_error "部署到 'test' 环境的逻辑尚未实现。"
) else if /i "%ENV%"=="prod" (
    call :log_error "部署到 'prod' 环境的逻辑尚未实现。"
) else (
    call :log_error "无效的环境参数 '%ENV%'。请使用 'local', 'test', or 'prod'."
)

call :log_info "部署脚本执行完毕。"

:end
popd
endlocal 