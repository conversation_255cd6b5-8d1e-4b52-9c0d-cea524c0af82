#!/usr/bin/env python3
"""
自动安装依赖脚本
自动检查和安装Python、Node.js和系统依赖
"""

import os
import sys
import subprocess
import platform
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.dependency_checker import (
    PythonDependency<PERSON>hecker,
    NodeJSDependency<PERSON>hecker,
    SystemDependencyChecker,
    DependencyStatus,
    DependencyType
)


class AutoInstaller:
    """自动安装器"""
    
    def __init__(self, project_root: Path, dry_run: bool = False):
        self.project_root = project_root
        self.dry_run = dry_run
        self.logger = logging.getLogger(__name__)
        self.system = platform.system()
        
        # 创建检查器
        self.python_checker = PythonDependencyChecker(project_root)
        self.nodejs_checker = NodeJSDependencyChecker(project_root)
        self.system_checker = SystemDependencyChecker(project_root)
        
        # 安装结果
        self.install_results = {
            "python": [],
            "nodejs": [],
            "system": []
        }
    
    def check_prerequisites(self) -> bool:
        """检查安装前提条件"""
        print("🔍 检查安装前提条件...")
        
        # 检查Python版本
        python_info = self.python_checker.check_python_version()
        if python_info.status != DependencyStatus.INSTALLED:
            print(f"❌ Python版本不满足要求: {python_info.error_message}")
            return False
        
        print(f"✅ Python版本: {python_info.installed_version}")
        
        # 检查pip
        pip_info = self.python_checker.check_pip()
        if pip_info.status != DependencyStatus.INSTALLED:
            print(f"❌ pip不可用: {pip_info.error_message}")
            return False
        
        print(f"✅ pip版本: {pip_info.installed_version}")
        
        return True
    
    def install_python_dependencies(self) -> bool:
        """安装Python依赖"""
        print("\n🐍 安装Python依赖...")
        
        missing_deps = self.python_checker.get_missing_dependencies()
        if not missing_deps:
            print("✅ 所有Python依赖都已安装")
            return True
        
        print(f"发现 {len(missing_deps)} 个缺失的Python依赖")
        
        success = True
        for dep in missing_deps:
            if dep.name in ['python', 'pip']:
                continue  # 跳过基础工具
            
            if dep.install_command:
                result = self._execute_command(dep.install_command, f"安装 {dep.name}")
                self.install_results["python"].append({
                    "name": dep.name,
                    "command": dep.install_command,
                    "success": result
                })
                
                if not result:
                    success = False
        
        return success
    
    def install_nodejs_dependencies(self) -> bool:
        """安装Node.js依赖"""
        print("\n📦 安装Node.js依赖...")
        
        # 首先检查Node.js和npm是否可用
        nodejs_info = self.nodejs_checker.check_nodejs_version()
        npm_info = self.nodejs_checker.check_npm()
        
        if nodejs_info.status != DependencyStatus.INSTALLED:
            print(f"⚠️ Node.js未安装: {nodejs_info.error_message}")
            print("请先安装Node.js: https://nodejs.org")
            return False
        
        if npm_info.status != DependencyStatus.INSTALLED:
            print(f"⚠️ npm不可用: {npm_info.error_message}")
            return False
        
        print(f"✅ Node.js版本: {nodejs_info.installed_version}")
        print(f"✅ npm版本: {npm_info.installed_version}")
        
        missing_deps = self.nodejs_checker.get_missing_nodejs_dependencies()
        if not missing_deps:
            print("✅ 所有Node.js依赖都已安装")
            return True
        
        # 过滤掉基础工具
        package_deps = [dep for dep in missing_deps if dep.name not in ['nodejs', 'npm']]
        
        if not package_deps:
            print("✅ 所有Node.js包依赖都已安装")
            return True
        
        print(f"发现 {len(package_deps)} 个缺失的Node.js包")
        
        # 按目录分组安装
        install_dirs = {}
        for dep in package_deps:
            # 确定安装目录
            if (self.project_root / "frontend" / "package.json").exists():
                install_dir = self.project_root / "frontend"
            else:
                install_dir = self.project_root
            
            if install_dir not in install_dirs:
                install_dirs[install_dir] = []
            
            install_dirs[install_dir].append(dep)
        
        success = True
        for install_dir, deps in install_dirs.items():
            print(f"在目录 {install_dir.relative_to(self.project_root)} 中安装包...")
            
            # 批量安装
            package_names = []
            for dep in deps:
                if dep.install_command and "npm install" in dep.install_command:
                    package_name = dep.install_command.replace("npm install ", "")
                    package_names.append(package_name)
            
            if package_names:
                batch_command = f"npm install {' '.join(package_names)}"
                result = self._execute_command(batch_command, f"批量安装Node.js包", cwd=install_dir)
                
                for dep in deps:
                    self.install_results["nodejs"].append({
                        "name": dep.name,
                        "command": batch_command,
                        "success": result
                    })
                
                if not result:
                    success = False
        
        return success
    
    def install_system_dependencies(self) -> bool:
        """安装系统依赖"""
        print("\n🖥️ 安装系统依赖...")
        
        missing_deps = self.system_checker.get_missing_system_dependencies()
        if not missing_deps:
            print("✅ 所有系统依赖都已安装")
            return True
        
        # 过滤掉可选依赖和复杂安装的依赖
        installable_deps = []
        manual_deps = []
        
        for dep in missing_deps:
            if dep.install_command and not any(keyword in dep.install_command.lower() 
                                             for keyword in ['官网', '下载', 'https://', 'http://']):
                installable_deps.append(dep)
            else:
                manual_deps.append(dep)
        
        print(f"发现 {len(installable_deps)} 个可自动安装的系统依赖")
        if manual_deps:
            print(f"发现 {len(manual_deps)} 个需要手动安装的系统依赖")
        
        success = True
        
        # 自动安装
        for dep in installable_deps:
            result = self._execute_command(dep.install_command, f"安装 {dep.name}")
            self.install_results["system"].append({
                "name": dep.name,
                "command": dep.install_command,
                "success": result
            })
            
            if not result:
                success = False
        
        # 显示手动安装指导
        if manual_deps:
            print(f"\n📋 需要手动安装的依赖:")
            for dep in manual_deps:
                print(f"  - {dep.name}: {dep.install_command}")
        
        return success
    
    def _execute_command(self, command: str, description: str, cwd: Optional[Path] = None) -> bool:
        """执行安装命令"""
        print(f"  🔧 {description}...")
        
        if self.dry_run:
            print(f"    [DRY RUN] {command}")
            return True
        
        try:
            # 处理需要sudo的命令
            if command.startswith("sudo ") and self.system != "Windows":
                print(f"    需要管理员权限执行: {command}")
                print(f"    请在终端中手动执行: {command}")
                return True
            
            # 执行命令
            result = subprocess.run(
                command.split(),
                cwd=cwd or self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print(f"    ✅ {description}成功")
                return True
            else:
                print(f"    ❌ {description}失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"    ⏰ {description}超时")
            return False
        except Exception as e:
            print(f"    💥 {description}异常: {e}")
            return False
    
    def generate_install_report(self) -> str:
        """生成安装报告"""
        report = f"""
# 依赖自动安装报告

## 安装概览
- **执行时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **平台**: {self.system}
- **模式**: {'试运行' if self.dry_run else '实际安装'}

## 安装结果

### Python依赖
"""
        
        if self.install_results["python"]:
            for result in self.install_results["python"]:
                status = "✅ 成功" if result["success"] else "❌ 失败"
                report += f"- {result['name']}: {status}\n"
                report += f"  命令: `{result['command']}`\n"
        else:
            report += "- 无需安装Python依赖\n"
        
        report += "\n### Node.js依赖\n"
        if self.install_results["nodejs"]:
            for result in self.install_results["nodejs"]:
                status = "✅ 成功" if result["success"] else "❌ 失败"
                report += f"- {result['name']}: {status}\n"
                report += f"  命令: `{result['command']}`\n"
        else:
            report += "- 无需安装Node.js依赖\n"
        
        report += "\n### 系统依赖\n"
        if self.install_results["system"]:
            for result in self.install_results["system"]:
                status = "✅ 成功" if result["success"] else "❌ 失败"
                report += f"- {result['name']}: {status}\n"
                report += f"  命令: `{result['command']}`\n"
        else:
            report += "- 无需安装系统依赖\n"
        
        return report
    
    def run_auto_install(self) -> bool:
        """运行自动安装"""
        print("🚀 开始自动安装依赖")
        print("=" * 50)
        
        # 检查前提条件
        if not self.check_prerequisites():
            print("\n❌ 前提条件检查失败，无法继续安装")
            return False
        
        success = True
        
        # 安装Python依赖
        if not self.install_python_dependencies():
            success = False
        
        # 安装Node.js依赖
        if not self.install_nodejs_dependencies():
            success = False
        
        # 安装系统依赖
        if not self.install_system_dependencies():
            success = False
        
        # 生成报告
        report = self.generate_install_report()
        report_file = self.project_root / "docs" / "opti_pre_prod" / "dependency_install_report.md"
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 安装报告已保存: {report_file}")
        
        if success:
            print("\n✅ 依赖自动安装完成!")
        else:
            print("\n⚠️ 依赖安装完成，但有部分失败，请查看报告")
        
        return success


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="自动安装AQUA项目依赖")
    parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际执行安装")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建自动安装器
    installer = AutoInstaller(project_root, dry_run=args.dry_run)
    
    # 运行安装
    success = installer.run_auto_install()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
