#!/usr/bin/env python3
"""
一键跨平台同步脚本
自动检测平台并执行相应的同步操作
"""

import sys
import platform
import subprocess
from pathlib import Path

def detect_platform():
    """检测当前平台"""
    system = platform.system()
    if system == "Windows":
        return "windows"
    elif system == "Darwin":
        return "macos"
    elif system == "Linux":
        return "linux"
    else:
        return "unknown"

def run_sync_script(script_name):
    """运行指定的同步脚本"""
    script_path = Path(__file__).parent / script_name
    
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    try:
        result = subprocess.run([sys.executable, str(script_path)], check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 脚本执行失败: {e}")
        return False

def main():
    print("🚀 AQUA 一键跨平台同步")
    print("=" * 50)
    
    # 检测平台
    current_platform = detect_platform()
    print(f"🔍 检测到平台: {current_platform}")
    
    if current_platform == "windows":
        print("🪟 Windows环境 - 执行全量拉取同步")
        print("⚠️  这将用远程代码覆盖本地所有更改")
        
        confirm = input("确认继续? (yes/no): ").lower().strip()
        if confirm not in ['yes', 'y']:
            print("❌ 操作已取消")
            return False
        
        success = run_sync_script("sync_from_gitee.py")
        
    elif current_platform == "macos":
        print("🍎 macOS环境 - 执行全量推送同步")
        print("⚠️  这将强制推送当前代码到远程仓库")
        
        confirm = input("确认继续? (yes/no): ").lower().strip()
        if confirm not in ['yes', 'y']:
            print("❌ 操作已取消")
            return False
        
        success = run_sync_script("sync_to_gitee.py")
        
    else:
        print(f"❌ 不支持的平台: {current_platform}")
        print("💡 请手动选择同步方式:")
        print("   - OS X/Linux推送: python scripts/sync_to_gitee.py")
        print("   - Windows拉取: python scripts/sync_from_gitee.py")
        return False
    
    if success:
        print("\n🎉 同步完成!")
        
        if current_platform == "macos":
            print("💡 现在可以在Windows环境下运行:")
            print("   python scripts/one_click_sync.py")
        elif current_platform == "windows":
            print("💡 代码已更新到最新版本")
        
        return True
    else:
        print("\n❌ 同步失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)