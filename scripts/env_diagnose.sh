#!/bin/bash
# AQUA环境快速诊断脚本
# 基于gitee.md实战经验创建

echo "🔍 === AQUA环境诊断报告 ==="
echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 基础环境检查
echo "📋 基础环境："
echo "Python: $(python --version 2>/dev/null || echo '❌ 未安装')"
echo "Node: $(node --version 2>/dev/null || echo '❌ 未安装')"
echo "UV: $(uv --version 2>/dev/null || echo '❌ 未安装')"
echo "pnpm: $(pnpm --version 2>/dev/null || echo '❌ 未安装')"
echo ""

# Git状态检查
echo "📋 Git状态："
echo "当前分支: $(git branch --show-current 2>/dev/null || echo '❌ 不在Git仓库')"
echo "远程仓库: $(git remote -v 2>/dev/null | head -1 | awk '{print $2}' || echo '❌ 无远程仓库')"
echo "最近提交: $(git log --oneline -1 2>/dev/null || echo '❌ 无提交记录')"
echo "工作区状态: $(git status --porcelain 2>/dev/null | wc -l | xargs)个文件有变更"
echo ""

# 项目文件检查
echo "📋 项目状态："
echo "后端依赖: $([ -f requirements.txt ] && echo '✅ requirements.txt存在' || echo '❌ requirements.txt缺失')"
echo "前端依赖: $([ -f frontend/package.json ] && echo '✅ package.json存在' || echo '❌ package.json缺失')"
echo "配置文件: $([ -f config/settings.toml ] && echo '✅ settings.toml存在' || echo '❌ settings.toml缺失')"
echo "数据库文件: $([ -f data/aqua.duckdb ] && echo '✅ aqua.duckdb存在' || echo '❌ aqua.duckdb缺失')"
echo ""

# 虚拟环境检查
echo "📋 虚拟环境："
if [ -d ".venv" ]; then
    echo "✅ .venv目录存在"
    if [ -f ".venv/bin/python" ] || [ -f ".venv/Scripts/python.exe" ]; then
        echo "✅ Python解释器存在"
        VENV_PYTHON=$(.venv/bin/python --version 2>/dev/null || .venv/Scripts/python.exe --version 2>/dev/null)
        echo "虚拟环境Python: $VENV_PYTHON"
    else
        echo "❌ Python解释器缺失"
    fi
else
    echo "❌ .venv目录不存在"
fi
echo ""

# 网络连接检查
echo "📋 网络连接："
if ping -c 1 gitee.com >/dev/null 2>&1; then
    echo "✅ Gitee连接正常"
else
    echo "❌ Gitee连接失败"
fi

if ssh -T ************* 2>&1 | grep -q "Hi"; then
    echo "✅ SSH密钥配置正常"
else
    echo "❌ SSH密钥配置有问题"
fi
echo ""

# 快速修复建议
echo "🔧 快速修复建议："
[ ! -d ".venv" ] && echo "- 运行: python scripts/env_init.py"
[ ! -f "requirements.txt" ] && echo "- 运行: uv pip compile requirements.in --output-file requirements.txt"
[ ! -f "frontend/node_modules" ] && echo "- 运行: cd frontend && pnpm install"
[ "$(git status --porcelain 2>/dev/null | wc -l)" -gt 0 ] && echo "- 有未提交变更，考虑运行: git add . && git commit"

echo ""
echo "🎉 诊断完成！"