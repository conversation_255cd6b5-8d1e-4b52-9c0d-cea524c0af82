#!/usr/bin/env python3
"""
Windows从Gitee全量拉取脚本
安全地从Gitee仓库全量拉取代码覆盖本地
"""

import subprocess
import sys
import shutil
from datetime import datetime
from pathlib import Path

class GiteeFullPull:
    def __init__(self):
        self.project_root = Path(__file__).resolve().parent.parent
        self.current_branch = None
        self.backup_branch = None
        
    def run_command(self, command, description=""):
        """执行Git命令并返回结果"""
        print(f"▶️ {description}")
        print(f"🔧 执行: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command, 
                cwd=self.project_root,
                capture_output=True, 
                text=True, 
                check=True
            )
            if result.stdout.strip():
                print(f"✅ {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ 错误: {e}")
            if e.stderr:
                print(f"stderr: {e.stderr}")
            return None
    
    def get_current_branch(self):
        """获取当前分支名"""
        result = self.run_command(['git', 'branch', '--show-current'], "获取当前分支")
        if result:
            self.current_branch = result.stdout.strip()
            return self.current_branch
        return None
    
    def check_git_status(self):
        """检查Git工作区状态"""
        print("\n📋 检查Git工作区状态...")
        
        # 检查是否有未提交的更改
        result = self.run_command(['git', 'status', '--porcelain'], "检查工作区状态")
        if result and result.stdout.strip():
            print("⚠️ 发现未提交的更改:")
            print(result.stdout)
            
            # 显示具体的更改文件
            lines = result.stdout.strip().split('\n')
            modified_files = []
            for line in lines:
                if line.strip():
                    status = line[:2]
                    filename = line[3:]
                    modified_files.append(f"  {status} {filename}")
            
            if modified_files:
                print("修改的文件:")
                for file_info in modified_files[:10]:  # 只显示前10个
                    print(file_info)
                if len(modified_files) > 10:
                    print(f"  ... 还有 {len(modified_files) - 10} 个文件")
            
            return False
        
        print("✅ 工作区干净，可以安全同步")
        return True
    
    def create_local_backup(self):
        """创建本地备份分支"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_branch = f"local_backup_before_pull_{timestamp}"
        
        print(f"\n💾 创建本地备份分支: {self.backup_branch}")
        result = self.run_command([
            'git', 'branch', self.backup_branch
        ], f"创建本地备份分支 {self.backup_branch}")
        
        if result:
            print(f"✅ 本地备份分支已创建: {self.backup_branch}")
            return True
        return False
    
    def fetch_all_remote_changes(self):
        """拉取所有远程更改"""
        print(f"\n📥 拉取所有远程更改...")
        result = self.run_command([
            'git', 'fetch', '--all', '--prune'
        ], "拉取远程更改")
        
        if result:
            print("✅ 远程更改已拉取")
            return True
        return False
    
    def reset_to_remote_branch(self):
        """重置到远程分支"""
        print(f"\n🔄 重置到远程分支 origin/{self.current_branch}...")
        result = self.run_command([
            'git', 'reset', '--hard', f'origin/{self.current_branch}'
        ], f"重置到 origin/{self.current_branch}")
        
        if result:
            print(f"✅ 已重置到远程分支 origin/{self.current_branch}")
            return True
        return False
    
    def clean_untracked_files(self):
        """清理未跟踪的文件"""
        print(f"\n🧹 清理未跟踪的文件和目录...")
        result = self.run_command([
            'git', 'clean', '-fd'
        ], "清理未跟踪文件")
        
        if result:
            print("✅ 未跟踪文件已清理")
            return True
        return False
    
    def verify_sync_result(self):
        """验证同步结果"""
        print(f"\n🔍 验证同步结果...")
        
        # 检查当前HEAD和远程分支是否一致
        result = self.run_command([
            'git', 'rev-parse', 'HEAD'
        ], "获取本地HEAD")
        
        if not result:
            return False
        
        local_head = result.stdout.strip()
        
        result = self.run_command([
            'git', 'rev-parse', f'origin/{self.current_branch}'
        ], "获取远程HEAD")
        
        if not result:
            return False
        
        remote_head = result.stdout.strip()
        
        if local_head == remote_head:
            print("✅ 同步验证成功，本地和远程完全一致")
            print(f"🔖 HEAD: {local_head[:8]}")
            return True
        else:
            print("❌ 同步验证失败，本地和远程不一致")
            print(f"本地HEAD: {local_head[:8]}")
            print(f"远程HEAD: {remote_head[:8]}")
            return False
    
    def force_handle_local_changes(self):
        """强制处理本地更改（暂存未提交的更改）"""
        print(f"\n⚠️ 处理本地更改...")
        
        # 选择处理方式
        print("发现本地未提交的更改，请选择处理方式:")
        print("1. 暂存更改到stash (推荐)")
        print("2. 强制丢弃所有更改 (⚠️ 不可恢复)")
        print("3. 取消同步")
        
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == '1':
            # 暂存更改
            result = self.run_command(['git', 'stash', 'push', '-m', f'Auto stash before sync {datetime.now()}'], "暂存本地更改")
            if result:
                print("✅ 本地更改已暂存到stash")
                print("💡 同步完成后可以执行 'git stash pop' 恢复更改")
                return True
            else:
                print("❌ 暂存失败")
                return False
                
        elif choice == '2':
            # 强制丢弃
            confirm = input("⚠️ 确认要丢弃所有本地更改吗? (yes/no): ").lower().strip()
            if confirm in ['yes', 'y']:
                result1 = self.run_command(['git', 'reset', '--hard', 'HEAD'], "丢弃工作区更改")
                result2 = self.run_command(['git', 'clean', '-fd'], "清理未跟踪文件")
                if result1 and result2:
                    print("✅ 本地更改已丢弃")
                    return True
            print("❌ 操作失败或已取消")
            return False
        else:
            print("❌ 同步已取消")
            return False
    
    def show_recovery_instructions(self):
        """显示恢复指令"""
        print(f"\n📝 恢复指令 (如果需要):")
        print("=" * 60)
        print("如果需要恢复到同步前的状态，可以执行:")
        print(f"git checkout {self.backup_branch}")
        print(f"git branch -D {self.current_branch}")
        print(f"git checkout -b {self.current_branch}")
        print()
        print("如果使用了stash，可以恢复暂存的更改:")
        print("git stash list  # 查看stash列表")
        print("git stash pop   # 恢复最新的stash")
        print("=" * 60)
    
    def sync_from_gitee(self):
        """执行完整的同步流程"""
        print("🚀 AQUA Gitee -> Windows 全量同步")
        print("=" * 70)
        print("⚠️  此操作将用远程仓库内容覆盖本地")
        print("💾 自动创建本地备份分支以防意外")
        print()
        
        # 1. 获取当前分支
        if not self.get_current_branch():
            print("❌ 无法获取当前分支")
            return False
        
        print(f"📍 当前分支: {self.current_branch}")
        
        # 2. 检查工作区状态
        has_local_changes = not self.check_git_status()
        
        if has_local_changes:
            # 获取用户确认和处理方式
            print("\n⚠️ 检测到本地未提交的更改")
            confirm = input("确认要继续同步吗? 将会处理这些更改 (yes/no): ").lower().strip()
            if confirm not in ['yes', 'y']:
                print("❌ 操作已取消")
                return False
            
            # 处理本地更改
            if not self.force_handle_local_changes():
                return False
        else:
            # 工作区干净，获取用户最终确认
            confirm = input("确认要执行全量拉取覆盖吗? (yes/no): ").lower().strip()
            if confirm not in ['yes', 'y']:
                print("❌ 操作已取消")
                return False
        
        # 3. 创建本地备份分支
        if not self.create_local_backup():
            print("❌ 创建本地备份分支失败")
            return False
        
        # 4. 拉取远程更改
        if not self.fetch_all_remote_changes():
            print("❌ 拉取远程更改失败")
            return False
        
        # 5. 重置到远程分支
        if not self.reset_to_remote_branch():
            print("❌ 重置到远程分支失败")
            return False
        
        # 6. 清理未跟踪文件
        if not self.clean_untracked_files():
            print("⚠️ 清理未跟踪文件失败，但继续执行")
        
        # 7. 验证同步结果
        if not self.verify_sync_result():
            print("❌ 同步验证失败")
            return False
        
        # 8. 显示成功信息
        print("\n🎉 全量同步成功完成!")
        print(f"✅ 远程代码已全量覆盖本地 {self.current_branch} 分支")
        print(f"💾 本地备份分支: {self.backup_branch}")
        
        self.show_recovery_instructions()
        
        return True


def main():
    puller = GiteeFullPull()
    success = puller.sync_from_gitee()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()