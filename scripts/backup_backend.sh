#!/bin/bash
# 后端代码备份脚本
# 作者: AI (CURSOR/GEMINI)
# 创建时间: 2025-07-02
# 版本: 1.0.0
# 变更记录:
#   - 2025-07-02: 创建初始版本

set -e
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
SETTINGS_PATH="$PROJECT_ROOT/config/settings.toml"
LOG_PATH="$PROJECT_ROOT/logs/backup.log"
BACKUP_DIR="$PROJECT_ROOT/data/backup"

# 读取环境参数
env=${1:-local}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 读取后端备份目标（可扩展）
BACKEND_NAME="backend_${env}_${TIMESTAMP}.zip"
BACKEND_TARGET="$BACKUP_DIR/$BACKEND_NAME"

log_info() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"INFO\", \"module\": \"backup_backend\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}
log_error() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"ERROR\", \"module\": \"backup_backend\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}

mkdir -p "$BACKUP_DIR"

# 备份核心逻辑
{
  zip -r "$BACKEND_TARGET" "$PROJECT_ROOT/src" "$PROJECT_ROOT/config" "$PROJECT_ROOT/requirements.txt" > /dev/null 2>&1
  log_info "后端代码备份成功: $BACKEND_TARGET"
} || {
  log_error "后端代码备份失败: $BACKEND_TARGET"
  exit 1
} 