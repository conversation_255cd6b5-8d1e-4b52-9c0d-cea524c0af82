#!/bin/bash
# 数据库备份脚本
# 作者: AI (CURSOR/GEMINI)
# 创建时间: 2025-07-02
# 版本: 1.0.0
# 变更记录:
#   - 2025-07-02: 创建初始版本

set -e
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
SETTINGS_PATH="$PROJECT_ROOT/config/settings.toml"
LOG_PATH="$PROJECT_ROOT/logs/backup.log"
BACKUP_DIR="$PROJECT_ROOT/data/backup"

# 读取环境参数
env=${1:-local}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 读取数据库路径
DB_PATH=$(grep -A2 '\[database\]' "$SETTINGS_PATH" | grep path | awk -F'=' '{print $2}' | tr -d ' ')
DB_PATH=${DB_PATH:-data/aqua.duckdb}
DB_PATH="$PROJECT_ROOT/$DB_PATH"

DB_BAK_NAME="backup_${env}_${TIMESTAMP}.duckdb"
DB_BAK_TARGET="$BACKUP_DIR/$DB_BAK_NAME"

log_info() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"INFO\", \"module\": \"backup_database\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}
log_error() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"ERROR\", \"module\": \"backup_database\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}

mkdir -p "$BACKUP_DIR"

# 备份核心逻辑
if [ ! -f "$DB_PATH" ]; then
  log_error "数据库文件不存在: $DB_PATH"
  exit 1
fi

{
  cp "$DB_PATH" "$DB_BAK_TARGET"
  log_info "数据库备份成功: $DB_BAK_TARGET"
} || {
  log_error "数据库备份失败: $DB_BAK_TARGET"
  exit 1
} 