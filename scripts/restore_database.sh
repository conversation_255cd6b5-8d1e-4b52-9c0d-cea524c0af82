#!/bin/bash
# 数据库恢复脚本
# 作者: AI (CURSOR/GEMINI)
# 创建时间: 2025-07-02
# 版本: 1.0.0
# 变更记录:
#   - 2025-07-02: 创建初始版本

set -e
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
SETTINGS_PATH="$PROJECT_ROOT/config/settings.toml"
LOG_PATH="$PROJECT_ROOT/logs/restore.log"

# 读取环境参数
env=${1:-local}
BACKUP_FILE=$2

# 读取数据库路径
DB_PATH=$(grep -A2 '\[database\]' "$SETTINGS_PATH" | grep path | awk -F'=' '{print $2}' | tr -d ' ')
DB_PATH=${DB_PATH:-data/aqua.duckdb}
DB_PATH="$PROJECT_ROOT/$DB_PATH"

log_info() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"INFO\", \"module\": \"restore_database\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}
log_error() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"ERROR\", \"module\": \"restore_database\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}

if [ -z "$BACKUP_FILE" ] || [ ! -f "$BACKUP_FILE" ]; then
  log_error "未指定有效的数据库备份文件: $BACKUP_FILE"
  exit 1
fi

# 恢复核心逻辑
{
  cp "$BACKUP_FILE" "$DB_PATH"
  log_info "数据库恢复成功: $BACKUP_FILE -> $DB_PATH"
} || {
  log_error "数据库恢复失败: $BACKUP_FILE -> $DB_PATH"
  exit 1
} 