#!/usr/bin/env python3
"""
权限需求分析脚本
分析AQUA项目在不同平台上的权限需求差异
"""

import os
import sys
import stat
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
import json
import pwd
import grp

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.paths import Paths


class PermissionRequirementAnalyzer:
    """权限需求分析器"""
    
    def __init__(self):
        self.project_root = Paths.ROOT
        self.current_platform = platform.system()
        self.current_user = self._get_current_user()
        
        # 权限分析结果
        self.analysis_results = {
            'platform_info': {},
            'directory_permissions': {},
            'file_permissions': {},
            'executable_permissions': {},
            'network_permissions': {},
            'system_permissions': {},
            'permission_issues': [],
            'recommendations': []
        }
        
        # 需要分析的关键目录
        self.key_directories = [
            Paths.ROOT,
            Paths.SRC,
            Paths.CONFIG,
            Paths.DATA,
            Paths.LOGS,
            Paths.CACHE,
            Paths.BACKUP,
            Paths.TESTS,
            Paths.DOCS,
            Paths.SCRIPTS,
        ]
        
        # 需要分析的关键文件
        self.key_files = [
            "src/aqua/main.py",
            "src/cli/main.py",
            "scripts/env_init.py",
            "config/settings.toml",
            ".env",
            "requirements.txt",
            "package.json"
        ]
        
        # 可执行文件模式
        self.executable_patterns = [
            "*.py",
            "*.sh",
            "*.bat",
            "*.cmd"
        ]
    
    def _get_current_user(self) -> Dict:
        """获取当前用户信息"""
        user_info = {
            'username': os.getlogin() if hasattr(os, 'getlogin') else 'unknown',
            'uid': os.getuid() if hasattr(os, 'getuid') else None,
            'gid': os.getgid() if hasattr(os, 'getgid') else None,
            'groups': [],
            'is_admin': False
        }
        
        try:
            if self.current_platform != "Windows":
                # Unix-like系统
                import pwd
                import grp
                
                user_info['username'] = pwd.getpwuid(os.getuid()).pw_name
                user_info['groups'] = [grp.getgrgid(gid).gr_name for gid in os.getgroups()]
                
                # 检查是否为管理员
                user_info['is_admin'] = (
                    os.getuid() == 0 or  # root用户
                    'sudo' in user_info['groups'] or
                    'wheel' in user_info['groups'] or
                    'admin' in user_info['groups']
                )
            else:
                # Windows系统
                import ctypes
                user_info['is_admin'] = ctypes.windll.shell32.IsUserAnAdmin() != 0
                
        except Exception as e:
            print(f"⚠️ 获取用户信息失败: {e}")
        
        return user_info
    
    def analyze_platform_info(self):
        """分析平台信息"""
        print("🔍 分析平台权限特性...")
        
        platform_info = {
            'system': self.current_platform,
            'version': platform.version(),
            'architecture': platform.architecture(),
            'user_info': self.current_user,
            'permission_model': self._get_permission_model(),
            'security_features': self._get_security_features()
        }
        
        self.analysis_results['platform_info'] = platform_info
        return platform_info
    
    def _get_permission_model(self) -> Dict:
        """获取权限模型信息"""
        if self.current_platform == "Windows":
            return {
                'type': 'ACL',  # Access Control List
                'supports_unix_permissions': False,
                'supports_file_attributes': True,
                'supports_inheritance': True,
                'default_umask': None
            }
        else:
            # Unix-like系统
            umask_value = None
            try:
                # 获取当前umask值
                current_umask = os.umask(0o022)
                os.umask(current_umask)  # 恢复原值
                umask_value = oct(current_umask)
            except:
                pass
            
            return {
                'type': 'POSIX',
                'supports_unix_permissions': True,
                'supports_file_attributes': True,
                'supports_inheritance': False,
                'default_umask': umask_value
            }
    
    def _get_security_features(self) -> List[str]:
        """获取安全特性"""
        features = []
        
        if self.current_platform == "Windows":
            features.extend([
                'UAC',  # User Account Control
                'NTFS_Permissions',
                'File_Attributes',
                'Execution_Policy'
            ])
        elif self.current_platform == "Darwin":
            features.extend([
                'SIP',  # System Integrity Protection
                'Gatekeeper',
                'Code_Signing',
                'Sandboxing',
                'POSIX_Permissions'
            ])
        else:  # Linux
            features.extend([
                'POSIX_Permissions',
                'SELinux',
                'AppArmor',
                'Capabilities',
                'Namespaces'
            ])
        
        return features
    
    def analyze_directory_permissions(self):
        """分析目录权限"""
        print("📁 分析目录权限...")
        
        directory_analysis = {}
        
        for directory in self.key_directories:
            if not directory.exists():
                continue
            
            dir_info = {
                'path': str(directory),
                'exists': directory.exists(),
                'is_directory': directory.is_dir(),
                'permissions': self._get_path_permissions(directory),
                'owner': self._get_path_owner(directory),
                'issues': [],
                'recommendations': []
            }
            
            # 检查权限问题
            self._check_directory_permission_issues(directory, dir_info)
            
            directory_analysis[str(directory.relative_to(self.project_root))] = dir_info
        
        self.analysis_results['directory_permissions'] = directory_analysis
        return directory_analysis
    
    def analyze_file_permissions(self):
        """分析文件权限"""
        print("📄 分析文件权限...")
        
        file_analysis = {}
        
        for file_path in self.key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
            
            file_info = {
                'path': file_path,
                'exists': full_path.exists(),
                'is_file': full_path.is_file(),
                'permissions': self._get_path_permissions(full_path),
                'owner': self._get_path_owner(full_path),
                'is_executable': self._is_executable(full_path),
                'issues': [],
                'recommendations': []
            }
            
            # 检查文件权限问题
            self._check_file_permission_issues(full_path, file_info)
            
            file_analysis[file_path] = file_info
        
        self.analysis_results['file_permissions'] = file_analysis
        return file_analysis
    
    def analyze_executable_permissions(self):
        """分析可执行文件权限"""
        print("⚙️ 分析可执行文件权限...")
        
        executable_analysis = {}
        
        # 查找所有可执行文件
        for pattern in self.executable_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file() and not self._should_skip_file(file_path):
                    rel_path = str(file_path.relative_to(self.project_root))
                    
                    exec_info = {
                        'path': rel_path,
                        'permissions': self._get_path_permissions(file_path),
                        'is_executable': self._is_executable(file_path),
                        'should_be_executable': self._should_be_executable(file_path),
                        'issues': [],
                        'recommendations': []
                    }
                    
                    # 检查可执行权限问题
                    self._check_executable_permission_issues(file_path, exec_info)
                    
                    executable_analysis[rel_path] = exec_info
        
        self.analysis_results['executable_permissions'] = executable_analysis
        return executable_analysis
    
    def analyze_network_permissions(self):
        """分析网络权限需求"""
        print("🌐 分析网络权限需求...")
        
        network_analysis = {
            'required_ports': [],
            'outbound_connections': [],
            'firewall_requirements': [],
            'issues': [],
            'recommendations': []
        }
        
        # 分析端口需求
        config_files = [
            self.project_root / "config" / "settings.toml",
            self.project_root / "frontend" / "vite.config.js",
            self.project_root / "package.json"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                try:
                    content = config_file.read_text(encoding='utf-8')
                    ports = self._extract_ports_from_config(content)
                    network_analysis['required_ports'].extend(ports)
                except Exception as e:
                    print(f"⚠️ 读取配置文件失败: {config_file} - {e}")
        
        # 去重
        network_analysis['required_ports'] = list(set(network_analysis['required_ports']))
        
        # 分析外部连接需求
        network_analysis['outbound_connections'] = [
            {'host': 'api.tushare.pro', 'port': 443, 'protocol': 'HTTPS', 'purpose': 'Tushare数据API'},
            {'host': 'pypi.org', 'port': 443, 'protocol': 'HTTPS', 'purpose': 'Python包下载'},
            {'host': 'registry.npmjs.org', 'port': 443, 'protocol': 'HTTPS', 'purpose': 'NPM包下载'},
        ]
        
        self.analysis_results['network_permissions'] = network_analysis
        return network_analysis
    
    def analyze_system_permissions(self):
        """分析系统权限需求"""
        print("🔧 分析系统权限需求...")
        
        system_analysis = {
            'file_system_access': [],
            'process_permissions': [],
            'registry_access': [],  # Windows
            'service_permissions': [],
            'issues': [],
            'recommendations': []
        }
        
        # 文件系统访问需求
        system_analysis['file_system_access'] = [
            {'path': str(Paths.DATA), 'access': 'read/write', 'purpose': '数据存储'},
            {'path': str(Paths.LOGS), 'access': 'read/write', 'purpose': '日志记录'},
            {'path': str(Paths.CACHE), 'access': 'read/write', 'purpose': '缓存存储'},
            {'path': str(Paths.BACKUP), 'access': 'read/write', 'purpose': '备份存储'},
        ]
        
        # 进程权限需求
        system_analysis['process_permissions'] = [
            {'permission': 'create_process', 'purpose': '启动子进程'},
            {'permission': 'network_bind', 'purpose': '绑定网络端口'},
            {'permission': 'file_lock', 'purpose': '文件锁定'},
        ]
        
        if self.current_platform == "Windows":
            system_analysis['registry_access'] = [
                {'key': 'HKEY_CURRENT_USER\\Environment', 'access': 'read', 'purpose': '环境变量读取'},
            ]
        
        self.analysis_results['system_permissions'] = system_analysis
        return system_analysis
    
    def _get_path_permissions(self, path: Path) -> Dict:
        """获取路径权限信息"""
        try:
            stat_info = path.stat()
            mode = stat_info.st_mode
            
            permissions = {
                'octal': oct(stat.S_IMODE(mode)),
                'readable': os.access(path, os.R_OK),
                'writable': os.access(path, os.W_OK),
                'executable': os.access(path, os.X_OK),
            }
            
            if self.current_platform != "Windows":
                permissions.update({
                    'owner_read': bool(mode & stat.S_IRUSR),
                    'owner_write': bool(mode & stat.S_IWUSR),
                    'owner_execute': bool(mode & stat.S_IXUSR),
                    'group_read': bool(mode & stat.S_IRGRP),
                    'group_write': bool(mode & stat.S_IWGRP),
                    'group_execute': bool(mode & stat.S_IXGRP),
                    'other_read': bool(mode & stat.S_IROTH),
                    'other_write': bool(mode & stat.S_IWOTH),
                    'other_execute': bool(mode & stat.S_IXOTH),
                })
            
            return permissions
        except Exception as e:
            return {'error': str(e)}
    
    def _get_path_owner(self, path: Path) -> Dict:
        """获取路径所有者信息"""
        try:
            stat_info = path.stat()
            owner_info = {
                'uid': stat_info.st_uid,
                'gid': stat_info.st_gid,
            }
            
            if self.current_platform != "Windows":
                try:
                    import pwd
                    import grp
                    owner_info['username'] = pwd.getpwuid(stat_info.st_uid).pw_name
                    owner_info['groupname'] = grp.getgrgid(stat_info.st_gid).gr_name
                except:
                    pass
            
            return owner_info
        except Exception as e:
            return {'error': str(e)}
    
    def _is_executable(self, path: Path) -> bool:
        """检查文件是否可执行"""
        return os.access(path, os.X_OK)
    
    def _should_be_executable(self, path: Path) -> bool:
        """判断文件是否应该是可执行的"""
        if path.suffix in ['.py', '.sh']:
            return True
        if path.suffix in ['.bat', '.cmd'] and self.current_platform == "Windows":
            return True
        if path.name in ['manage.py', 'run.py']:
            return True
        return False
    
    def _should_skip_file(self, path: Path) -> bool:
        """判断是否应该跳过文件"""
        skip_dirs = {'.git', '__pycache__', 'node_modules', '.venv', 'dist', 'build'}
        return any(part in skip_dirs for part in path.parts)
    
    def _extract_ports_from_config(self, content: str) -> List[int]:
        """从配置内容中提取端口号"""
        import re
        ports = []
        
        # 匹配端口号模式
        port_patterns = [
            r'port\s*[=:]\s*(\d+)',
            r'PORT\s*[=:]\s*(\d+)',
            r'localhost:(\d+)',
            r'127\.0\.0\.1:(\d+)',
        ]
        
        for pattern in port_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            ports.extend([int(port) for port in matches])
        
        return ports
    
    def _check_directory_permission_issues(self, directory: Path, dir_info: Dict):
        """检查目录权限问题"""
        # 检查是否可读写
        if not dir_info['permissions'].get('readable', False):
            dir_info['issues'].append("目录不可读")
            dir_info['recommendations'].append("确保目录具有读权限")
        
        if not dir_info['permissions'].get('writable', False):
            dir_info['issues'].append("目录不可写")
            dir_info['recommendations'].append("确保目录具有写权限")
    
    def _check_file_permission_issues(self, file_path: Path, file_info: Dict):
        """检查文件权限问题"""
        # 检查配置文件权限
        if file_path.name in ['.env', 'settings.toml']:
            if file_info['permissions'].get('other_read', False):
                file_info['issues'].append("敏感配置文件对其他用户可读")
                file_info['recommendations'].append("限制文件权限为600 (仅所有者可读写)")
    
    def _check_executable_permission_issues(self, file_path: Path, exec_info: Dict):
        """检查可执行文件权限问题"""
        should_be_exec = exec_info['should_be_executable']
        is_exec = exec_info['is_executable']
        
        if should_be_exec and not is_exec:
            exec_info['issues'].append("应该可执行但不可执行")
            exec_info['recommendations'].append("添加执行权限")
        elif not should_be_exec and is_exec:
            exec_info['issues'].append("不应该可执行但可执行")
            exec_info['recommendations'].append("移除执行权限")
    
    def generate_analysis_report(self) -> str:
        """生成权限分析报告"""
        report = []
        report.append("# AQUA项目权限需求分析报告")
        report.append(f"生成时间: {self._get_timestamp()}")
        report.append(f"分析平台: {self.current_platform}")
        report.append("")
        
        # 平台信息
        platform_info = self.analysis_results['platform_info']
        report.append("## 🖥️ 平台信息")
        report.append(f"- 操作系统: {platform_info['system']} {platform_info['version']}")
        report.append(f"- 架构: {platform_info['architecture'][0]}")
        report.append(f"- 权限模型: {platform_info['permission_model']['type']}")
        report.append(f"- 当前用户: {platform_info['user_info']['username']}")
        report.append(f"- 管理员权限: {'是' if platform_info['user_info']['is_admin'] else '否'}")
        report.append("")
        
        # 目录权限分析
        report.append("## 📁 目录权限分析")
        dir_perms = self.analysis_results['directory_permissions']
        for dir_path, info in dir_perms.items():
            if info['issues']:
                report.append(f"### ⚠️ {dir_path}")
                for issue in info['issues']:
                    report.append(f"- 问题: {issue}")
                for rec in info['recommendations']:
                    report.append(f"- 建议: {rec}")
                report.append("")
        
        # 网络权限需求
        network_perms = self.analysis_results['network_permissions']
        if network_perms['required_ports']:
            report.append("## 🌐 网络权限需求")
            report.append("### 需要的端口")
            for port in network_perms['required_ports']:
                report.append(f"- 端口 {port}")
            report.append("")
        
        return "\n".join(report)
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def run_full_analysis(self) -> Dict:
        """运行完整的权限分析"""
        print("🔍 开始权限需求分析...")
        
        self.analyze_platform_info()
        self.analyze_directory_permissions()
        self.analyze_file_permissions()
        self.analyze_executable_permissions()
        self.analyze_network_permissions()
        self.analyze_system_permissions()
        
        print("✅ 权限需求分析完成")
        return self.analysis_results


def main():
    """主函数"""
    print("🔐 AQUA项目权限需求分析")
    print("=" * 50)
    
    analyzer = PermissionRequirementAnalyzer()
    results = analyzer.run_full_analysis()
    
    # 生成报告
    report = analyzer.generate_analysis_report()
    
    # 保存报告
    report_path = Paths.DOCS / "opti_pre_prod" / "permission_requirements_analysis.md"
    report_path.parent.mkdir(parents=True, exist_ok=True)
    report_path.write_text(report, encoding='utf-8')
    
    print(f"📄 权限分析报告已保存到: {report_path}")
    
    # 保存详细数据
    json_path = Paths.DOCS / "opti_pre_prod" / "permission_requirements_data.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"📊 详细数据已保存到: {json_path}")
    
    # 显示摘要
    platform_info = results['platform_info']
    print(f"\n📊 分析摘要:")
    print(f"   平台: {platform_info['system']}")
    print(f"   权限模型: {platform_info['permission_model']['type']}")
    print(f"   分析目录: {len(results['directory_permissions'])}")
    print(f"   分析文件: {len(results['file_permissions'])}")
    print(f"   可执行文件: {len(results['executable_permissions'])}")


if __name__ == "__main__":
    main()
