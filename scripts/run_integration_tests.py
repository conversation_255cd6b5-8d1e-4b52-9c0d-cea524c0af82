#!/usr/bin/env python3
"""
AQUA CLI 集成测试运行脚本
支持macOS和Windows环境的完整集成测试
"""
import os
import sys
import platform
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse

# 添加src路径到sys.path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
    from rich.text import Text
    console = Console()
except ImportError:
    console = None
    print("Warning: Rich library not available, using basic output")

class IntegrationTestRunner:
    """集成测试运行器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        self.platform = platform.system()
        
    def run_tests(self, test_type: str = "all", verbose: bool = False, 
                  stop_on_first_failure: bool = False) -> Dict[str, Any]:
        """运行集成测试"""
        self.start_time = datetime.now()
        
        if console:
            console.print(Panel.fit(
                f"🧪 [bold cyan]AQUA CLI 集成测试[/bold cyan]\n\n"
                f"平台: {self.platform}\n"
                f"项目根目录: {self.project_root}\n"
                f"测试类型: {test_type}\n"
                f"详细输出: {verbose}\n"
                f"遇错即停: {stop_on_first_failure}",
                title="集成测试启动",
                border_style="cyan"
            ))
        else:
            print(f"🧪 AQUA CLI 集成测试")
            print(f"平台: {self.platform}")
            print(f"测试类型: {test_type}")
        
        # 根据测试类型选择测试套件
        test_suites = self._get_test_suites(test_type)
        
        # 执行测试
        for suite_name, suite_config in test_suites.items():
            result = self._run_test_suite(
                suite_name, 
                suite_config, 
                verbose, 
                stop_on_first_failure
            )
            self.test_results[suite_name] = result
            
            if stop_on_first_failure and not result.get('success', False):
                break
        
        self.end_time = datetime.now()
        
        # 生成测试报告
        report = self._generate_test_report()
        
        # 显示结果
        self._display_test_results()
        
        return report
    
    def _get_test_suites(self, test_type: str) -> Dict[str, Dict[str, Any]]:
        """获取测试套件配置"""
        base_suites = {
            "cli_commands": {
                "description": "CLI命令集成测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestCLICommandsIntegration",
                "timeout": 120,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "setup_wizard": {
                "description": "智能配置向导测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestSetupWizardIntegration",
                "timeout": 180,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "health_checker": {
                "description": "健康检查系统测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestHealthCheckerIntegration",
                "timeout": 240,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "enhanced_ui": {
                "description": "增强用户界面测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestEnhancedUIIntegration",
                "timeout": 120,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "windows_compat": {
                "description": "Windows兼容性测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestWindowsCompatIntegration",
                "timeout": 150,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "dev_tools": {
                "description": "开发工具链测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestDevToolsIntegration",
                "timeout": 300,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "service_integration": {
                "description": "服务管理集成测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestServiceIntegration",
                "timeout": 180,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "end_to_end": {
                "description": "端到端工作流测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestEndToEndWorkflows",
                "timeout": 360,
                "platform": ["Darwin", "Linux", "Windows"]
            },
            "performance": {
                "description": "性能集成测试",
                "test_file": "test_aqua_cli_integration.py",
                "test_class": "TestPerformanceIntegration",
                "timeout": 240,
                "platform": ["Darwin", "Linux", "Windows"]
            }
        }
        
        # Windows特定测试套件
        windows_suites = {
            "windows_utf8": {
                "description": "Windows UTF-8兼容性测试",
                "test_file": "test_windows_environment_plan.py",
                "test_class": "TestWindowsUTF8Compatibility",
                "timeout": 180,
                "platform": ["Windows"]
            },
            "windows_powershell": {
                "description": "Windows PowerShell兼容性测试",
                "test_file": "test_windows_environment_plan.py",
                "test_class": "TestWindowsPowerShellCompatibility",
                "timeout": 240,
                "platform": ["Windows"]
            },
            "windows_long_path": {
                "description": "Windows长路径支持测试",
                "test_file": "test_windows_environment_plan.py",
                "test_class": "TestWindowsLongPathSupport",
                "timeout": 180,
                "platform": ["Windows"]
            },
            "windows_permission": {
                "description": "Windows权限管理测试",
                "test_file": "test_windows_environment_plan.py",
                "test_class": "TestWindowsPermissionManagement",
                "timeout": 300,
                "platform": ["Windows"]
            },
            "windows_service": {
                "description": "Windows服务集成测试",
                "test_file": "test_windows_environment_plan.py",
                "test_class": "TestWindowsServiceIntegration",
                "timeout": 360,
                "platform": ["Windows"]
            }
        }
        
        # 根据测试类型和平台过滤测试套件
        if test_type == "all":
            all_suites = {**base_suites}
            if self.platform == "Windows":
                all_suites.update(windows_suites)
        elif test_type == "basic":
            all_suites = {k: v for k, v in base_suites.items() 
                         if k in ["cli_commands", "setup_wizard", "health_checker"]}
        elif test_type == "advanced":
            all_suites = {k: v for k, v in base_suites.items() 
                         if k in ["enhanced_ui", "dev_tools", "end_to_end", "performance"]}
        elif test_type == "windows":
            if self.platform == "Windows":
                all_suites = windows_suites
            else:
                all_suites = {k: v for k, v in base_suites.items() 
                             if "windows" in k.lower()}
        else:
            # 特定测试套件
            all_suites = {k: v for k, v in base_suites.items() if k == test_type}
            if self.platform == "Windows" and test_type in windows_suites:
                all_suites[test_type] = windows_suites[test_type]
        
        # 过滤平台兼容性
        filtered_suites = {}
        for name, config in all_suites.items():
            if self.platform in config.get("platform", []):
                filtered_suites[name] = config
        
        return filtered_suites
    
    def _run_test_suite(self, suite_name: str, suite_config: Dict[str, Any], 
                       verbose: bool, stop_on_first_failure: bool) -> Dict[str, Any]:
        """运行单个测试套件"""
        if console:
            console.print(f"\n🔍 [yellow]运行测试套件: {suite_name}[/yellow]")
            console.print(f"   {suite_config['description']}")
        else:
            print(f"\n🔍 运行测试套件: {suite_name}")
        
        test_file = self.project_root / "tests" / "integration" / suite_config["test_file"]
        test_class = suite_config["test_class"]
        timeout = suite_config.get("timeout", 300)
        
        # 构建pytest命令
        cmd = [
            sys.executable, "-m", "pytest",
            str(test_file) + "::" + test_class,
            "-v" if verbose else "-q",
            "--tb=short"
        ]
        
        if stop_on_first_failure:
            cmd.append("-x")
        
        start_time = time.time()
        
        try:
            # 执行测试
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_root
            )
            
            execution_time = time.time() - start_time
            
            # 解析结果
            test_result = {
                "success": result.returncode == 0,
                "exit_code": result.returncode,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "timeout": timeout,
                "command": " ".join(cmd)
            }
            
            # JSON报告（简化）
            test_result["json_report"] = {
                "returncode": result.returncode,
                "stdout_lines": len(result.stdout.split('\n')),
                "stderr_lines": len(result.stderr.split('\n'))
            }
            
            # 显示结果
            if test_result["success"]:
                if console:
                    console.print(f"   ✅ [green]通过[/green] ({execution_time:.2f}s)")
                else:
                    print(f"   ✅ 通过 ({execution_time:.2f}s)")
            else:
                if console:
                    console.print(f"   ❌ [red]失败[/red] ({execution_time:.2f}s)")
                    if verbose:
                        console.print(f"   错误输出: {result.stderr[:200]}...")
                else:
                    print(f"   ❌ 失败 ({execution_time:.2f}s)")
                    if verbose:
                        print(f"   错误输出: {result.stderr[:200]}...")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            execution_time = time.time() - start_time
            if console:
                console.print(f"   ⏰ [yellow]超时[/yellow] ({execution_time:.2f}s)")
            else:
                print(f"   ⏰ 超时 ({execution_time:.2f}s)")
            
            return {
                "success": False,
                "exit_code": -1,
                "execution_time": execution_time,
                "error": "timeout",
                "timeout": timeout,
                "command": " ".join(cmd)
            }
        
        except Exception as e:
            execution_time = time.time() - start_time
            if console:
                console.print(f"   💥 [red]异常[/red]: {str(e)}")
            else:
                print(f"   💥 异常: {str(e)}")
            
            return {
                "success": False,
                "exit_code": -2,
                "execution_time": execution_time,
                "error": str(e),
                "command": " ".join(cmd)
            }
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get("success", False))
        failed_tests = total_tests - passed_tests
        
        total_time = (self.end_time - self.start_time).total_seconds()
        
        report = {
            "test_summary": {
                "platform": self.platform,
                "start_time": self.start_time.isoformat(),
                "end_time": self.end_time.isoformat(),
                "total_duration": total_time,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "test_results": self.test_results,
            "environment_info": {
                "python_version": sys.version,
                "platform": platform.platform(),
                "architecture": platform.architecture(),
                "processor": platform.processor(),
                "project_root": str(self.project_root)
            }
        }
        
        return report
    
    def _display_test_results(self):
        """显示测试结果"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get("success", False))
        failed_tests = total_tests - passed_tests
        
        if console:
            # 创建结果表格
            table = Table(title="集成测试结果")
            table.add_column("测试套件", style="cyan")
            table.add_column("状态", justify="center")
            table.add_column("执行时间", justify="right", style="yellow")
            table.add_column("说明", style="dim")
            
            for suite_name, result in self.test_results.items():
                status = "✅ 通过" if result.get("success", False) else "❌ 失败"
                exec_time = f"{result.get('execution_time', 0):.2f}s"
                note = ""
                
                if not result.get("success", False):
                    if result.get("error") == "timeout":
                        note = "超时"
                    elif "exit_code" in result:
                        note = f"退出码: {result['exit_code']}"
                    else:
                        note = "执行异常"
                
                table.add_row(suite_name, status, exec_time, note)
            
            console.print(table)
            
            # 显示总结
            total_time = (self.end_time - self.start_time).total_seconds()
            success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
            
            summary_color = "green" if failed_tests == 0 else "red" if failed_tests > passed_tests else "yellow"
            
            console.print(Panel.fit(
                f"📊 [bold]测试总结[/bold]\n\n"
                f"总测试数: {total_tests}\n"
                f"通过: [green]{passed_tests}[/green]\n"
                f"失败: [red]{failed_tests}[/red]\n"
                f"成功率: [bold]{success_rate:.1f}%[/bold]\n"
                f"总耗时: {total_time:.2f}秒",
                title="集成测试完成",
                border_style=summary_color
            ))
        else:
            print(f"\n📊 测试总结")
            print(f"总测试数: {total_tests}")
            print(f"通过: {passed_tests}")
            print(f"失败: {failed_tests}")
            print(f"成功率: {(passed_tests / total_tests * 100) if total_tests > 0 else 0:.1f}%")
            print(f"总耗时: {(self.end_time - self.start_time).total_seconds():.2f}秒")
    
    def save_report(self, report_path: Optional[Path] = None):
        """保存测试报告"""
        if report_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = self.project_root / "reports" / f"integration_test_report_{timestamp}.json"
        
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        report = self._generate_test_report()
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        if console:
            console.print(f"📄 测试报告已保存到: [cyan]{report_path}[/cyan]")
        else:
            print(f"📄 测试报告已保存到: {report_path}")
        
        return report_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AQUA CLI 集成测试运行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
测试类型选项:
  all      - 运行所有集成测试
  basic    - 运行基础功能测试 (CLI命令, 配置向导, 健康检查)
  advanced - 运行高级功能测试 (增强UI, 开发工具, 端到端, 性能)
  windows  - 运行Windows特定测试
  
特定测试套件:
  cli_commands    - CLI命令集成测试
  setup_wizard    - 智能配置向导测试
  health_checker  - 健康检查系统测试
  enhanced_ui     - 增强用户界面测试
  dev_tools       - 开发工具链测试
  performance     - 性能集成测试
  
示例:
  python scripts/run_integration_tests.py --type all --verbose
  python scripts/run_integration_tests.py --type basic --stop-on-failure
  python scripts/run_integration_tests.py --type windows --report /tmp/report.json
        """
    )
    
    parser.add_argument(
        "--type", "-t",
        choices=["all", "basic", "advanced", "windows", "cli_commands", 
                 "setup_wizard", "health_checker", "enhanced_ui", "dev_tools", "performance"],
        default="all",
        help="要运行的测试类型 (默认: all)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出模式"
    )
    
    parser.add_argument(
        "--stop-on-failure", "-x",
        action="store_true",
        help="遇到第一个失败就停止"
    )
    
    parser.add_argument(
        "--report", "-r",
        type=Path,
        help="测试报告保存路径"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="仅显示将要运行的测试，不实际执行"
    )
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = IntegrationTestRunner()
    
    if args.dry_run:
        # 干运行模式：只显示测试计划
        test_suites = runner._get_test_suites(args.type)
        
        if console:
            console.print(Panel.fit(
                f"🔍 [bold cyan]集成测试计划预览[/bold cyan]\n\n"
                f"平台: {runner.platform}\n"
                f"测试类型: {args.type}\n"
                f"测试套件数量: {len(test_suites)}",
                title="干运行模式",
                border_style="blue"
            ))
            
            table = Table(title="测试套件列表")
            table.add_column("套件名称", style="cyan")
            table.add_column("描述", style="white")
            table.add_column("超时时间", justify="right", style="yellow")
            table.add_column("支持平台", style="dim")
            
            for name, config in test_suites.items():
                table.add_row(
                    name,
                    config["description"],
                    f"{config.get('timeout', 300)}s",
                    ", ".join(config.get("platform", []))
                )
            
            console.print(table)
        else:
            print(f"🔍 集成测试计划预览")
            print(f"平台: {runner.platform}")
            print(f"测试类型: {args.type}")
            print(f"测试套件数量: {len(test_suites)}")
            
            for name, config in test_suites.items():
                print(f"  - {name}: {config['description']}")
        
        return
    
    # 运行测试
    try:
        report = runner.run_tests(
            test_type=args.type,
            verbose=args.verbose,
            stop_on_first_failure=args.stop_on_failure
        )
        
        # 保存报告
        if args.report or any(not result.get("success", False) for result in runner.test_results.values()):
            runner.save_report(args.report)
        
        # 设置退出码
        failed_tests = sum(1 for result in runner.test_results.values() 
                          if not result.get("success", False))
        
        if failed_tests > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        if console:
            console.print("\n⏸️ [yellow]测试被用户中断[/yellow]")
        else:
            print("\n⏸️ 测试被用户中断")
        sys.exit(130)
    
    except Exception as e:
        if console:
            console.print(f"\n💥 [red]测试运行器异常: {str(e)}[/red]")
        else:
            print(f"\n💥 测试运行器异常: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()