#!/bin/bash
# AQUA 个人开发者版预提交钩子
# 目标：保持基础代码质量，减少不必要的约束

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}[AQUA个人开发] 运行预提交检查...${NC}"

# 1. Python代码格式化（可选）
if command -v black >/dev/null 2>&1; then
  echo -e "${YELLOW}[格式化] 运行Black格式化Python代码...${NC}"
  black src/ tests/ --quiet 2>/dev/null || true
fi

# 2. 前端格式化（仅当修改前端文件时）
if git diff --cached --name-only | grep -q '^frontend/'; then
  if command -v pnpm >/dev/null 2>&1 && [ -f frontend/package.json ]; then
    echo -e "${YELLOW}[格式化] 运行Prettier格式化前端代码...${NC}"
    cd frontend && pnpm exec prettier --write src/**/*.{ts,vue} 2>/dev/null && cd .. || true
  fi
fi

# 3. 敏感信息检测（仅检测真正的敏感信息，不包括环境变量模板）
SENSITIVE_PATTERNS="AKIA[0-9A-Z]{16}|['\"]sk-[a-zA-Z0-9]{48}['\"]|password\s*=\s*['\"][a-zA-Z0-9]{8,}['\"]|secret\s*=\s*['\"][a-zA-Z0-9]{8,}['\"]"

if git diff --cached --name-only | xargs grep -l -E "$SENSITIVE_PATTERNS" 2>/dev/null; then
  echo -e "${RED}[安全] 检测到可能的真实敏感信息，请检查：${NC}"
  git diff --cached --name-only | xargs grep -n -E --color=always "$SENSITIVE_PATTERNS" 2>/dev/null
  echo -e "${YELLOW}注意：环境变量模板如 \${TUSHARE_TOKEN} 不会被检测${NC}"
  
  read -p "确认这些不是真实敏感信息？(y/N): " confirm
  if [[ $confirm != [yY] ]]; then
    echo -e "${RED}[安全] 提交已取消${NC}"
    exit 1
  fi
fi

# 4. 基础Python语法检查（如果有pyflakes）
if command -v pyflakes >/dev/null 2>&1; then
  echo -e "${YELLOW}[语法] 检查Python语法错误...${NC}"
  if ! pyflakes src/ tests/ 2>/dev/null; then
    echo -e "${YELLOW}[语法] 发现语法警告，建议修复但不阻止提交${NC}"
  fi
fi

# 5. 大文件检查（避免误提交大文件 >10MB）
large_files=$(git diff --cached --name-only | xargs ls -la 2>/dev/null | awk '$5 > 10485760 {print $9 " (" $5/1048576 "MB)"}')
if [ -n "$large_files" ]; then
  echo -e "${YELLOW}[文件大小] 检测到大文件：${NC}"
  echo "$large_files"
  read -p "确认要提交这些大文件？(y/N): " confirm
  if [[ $confirm != [yY] ]]; then
    echo -e "${RED}[文件大小] 提交已取消${NC}"
    exit 1
  fi
fi

# 6. 可选：Commit message建议（不强制，不阻断）
COMMIT_MSG_FILE=".git/COMMIT_EDITMSG"
if [ -f "$COMMIT_MSG_FILE" ]; then
  COMMIT_MSG=$(cat "$COMMIT_MSG_FILE")
  if ! echo "$COMMIT_MSG" | grep -Eq "^(feat|fix|docs|style|refactor|test|chore):"; then
    echo -e "${YELLOW}[建议] 推荐使用规范的commit message格式：${NC}"
    echo "  feat: 新功能  |  fix: 修复问题  |  docs: 文档更新"
    echo "  style: 代码格式  |  refactor: 重构  |  test: 测试相关"
    echo -e "${YELLOW}  (这只是建议，不会阻止提交)${NC}"
  fi
fi

echo -e "${GREEN}[AQUA个人开发] 预提交检查完成！✅${NC}"
exit 0