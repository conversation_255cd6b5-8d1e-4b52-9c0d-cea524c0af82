#!/usr/bin/env python3
"""
跨平台同步流程演示
展示完整的OS X -> Windows同步流程
"""

import platform
from pathlib import Path

def demo_sync_flow():
    """演示完整的跨平台同步流程"""
    print("🚀 AQUA 跨平台全量同步方案演示")
    print("=" * 70)
    
    # 检测当前平台
    current_platform = platform.system()
    print(f"🔍 当前平台: {current_platform}")
    
    # 检查脚本文件是否存在
    script_dir = Path(__file__).parent
    scripts = {
        "sync_to_gitee.py": "OS X端全量推送脚本",
        "sync_from_gitee.py": "Windows端全量拉取脚本", 
        "sync_via_branch.py": "分支模式同步脚本",
        "one_click_sync.py": "一键同步脚本"
    }
    
    print(f"\n📋 可用的同步脚本:")
    print("-" * 50)
    for script, description in scripts.items():
        script_path = script_dir / script
        status = "✅" if script_path.exists() else "❌"
        print(f"{status} {script:<20} - {description}")
    
    print(f"\n🔄 推荐的同步流程:")
    print("=" * 70)
    
    print("【方案1: 强制同步 - 快速但需谨慎】")
    print("1️⃣ OS X端操作:")
    print("   python scripts/sync_to_gitee.py")
    print("   ↓ 自动创建备份分支")
    print("   ↓ 强制推送当前分支到远程")
    
    print("\n2️⃣ Windows端操作:")
    print("   python scripts/sync_from_gitee.py")
    print("   ↓ 处理本地更改（stash或丢弃）")  
    print("   ↓ 强制重置到远程分支")
    print("   ↓ 验证同步结果")
    
    print(f"\n【方案2: 分支同步 - 安全推荐】")
    print("1️⃣ OS X端操作:")
    print("   python scripts/sync_via_branch.py --push")
    print("   ↓ 创建 osx-to-windows-sync 分支")
    print("   ↓ 推送同步分支到远程")
    
    print("\n2️⃣ Windows端操作:")
    print("   python scripts/sync_via_branch.py --pull")
    print("   ↓ 拉取同步分支")
    print("   ↓ 预览差异")
    print("   ↓ 确认后合并到主分支")
    
    print(f"\n【方案3: 一键同步 - 最简单】")
    print("🍎 OS X端: python scripts/one_click_sync.py")
    print("🪟 Windows端: python scripts/one_click_sync.py")
    print("   ↓ 自动检测平台并执行相应操作")
    
    print(f"\n🎯 使用建议:")
    print("=" * 70)
    print("• 首次同步: 使用方案2（分支模式）更安全")
    print("• 日常同步: 使用方案3（一键同步）更便捷")  
    print("• 紧急同步: 使用方案1（强制同步）更快速")
    print("• 重要节点: 使用方案2并仔细检查差异")
    
    print(f"\n💡 安全提示:")
    print("=" * 70)
    print("✅ 所有脚本都会自动创建备份分支")
    print("✅ 操作前会进行用户确认") 
    print("✅ 提供完整的恢复指令")
    print("✅ 支持本地更改的处理（stash/丢弃）")
    
    print(f"\n📚 详细文档:")
    print("=" * 70)
    print("完整使用指南: docs/CROSS_PLATFORM_SYNC_GUIDE.md")
    
    # 根据当前平台给出具体建议
    if current_platform == "Darwin":
        print(f"\n🍎 当前建议（macOS环境）:")
        print("运行: python scripts/sync_to_gitee.py")
        print("然后在Windows环境下运行: python scripts/sync_from_gitee.py")
    elif current_platform == "Windows":
        print(f"\n🪟 当前建议（Windows环境）:")
        print("运行: python scripts/sync_from_gitee.py")
        print("这将拉取最新的远程代码覆盖本地")
    else:
        print(f"\n🐧 当前建议（{current_platform}环境）:")
        print("请参考macOS环境的操作流程")

if __name__ == "__main__":
    demo_sync_flow()