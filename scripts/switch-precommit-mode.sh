#!/bin/bash
# AQUA 预提交钩子模式切换脚本

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

show_help() {
  echo -e "${BLUE}AQUA 预提交钩子模式切换工具${NC}"
  echo ""
  echo "用法: ./scripts/switch-precommit-mode.sh [模式]"
  echo ""
  echo "可用模式:"
  echo -e "  ${GREEN}personal${NC}   - 个人开发者模式 (宽松，适合个人项目)"
  echo -e "  ${YELLOW}standard${NC}   - 标准模式 (当前默认模式)"
  echo -e "  ${RED}enterprise${NC} - 企业模式 (严格，适合团队协作)"
  echo -e "  ${BLUE}status${NC}     - 显示当前模式"
  echo ""
  echo "模式对比:"
  echo "┌──────────────┬──────────┬──────────┬──────────┐"
  echo "│ 功能         │ Personal │ Standard │Enterprise│"
  echo "├──────────────┼──────────┼──────────┼──────────┤"
  echo "│ 代码格式化   │    ✅    │    ✅    │    ✅    │"
  echo "│ 敏感信息检测 │  宽松    │   严格   │  非常严格│"
  echo "│ 日志同步要求 │    ❌    │    ✅    │    ✅    │"
  echo "│ Commit规范   │  建议    │   警告   │   强制   │"
  echo "│ 前端检查     │  条件性  │   总是   │   总是   │"
  echo "│ 文件大小检查 │    ✅    │    ❌    │    ✅    │"
  echo "└──────────────┴──────────┴──────────┴──────────┘"
}

get_current_mode() {
  if [ -L .git/hooks/pre-commit ]; then
    local target=$(readlink .git/hooks/pre-commit)
    case "$target" in
      *"pre-commit-personal")
        echo "personal"
        ;;
      *"pre-commit-enterprise") 
        echo "enterprise"
        ;;
      *)
        echo "standard"
        ;;
    esac
  elif [ -f .git/hooks/pre-commit ]; then
    if grep -q "AQUA个人开发" .git/hooks/pre-commit 2>/dev/null; then
      echo "personal"
    elif grep -q "企业级" .git/hooks/pre-commit 2>/dev/null; then
      echo "enterprise"
    else
      echo "standard"
    fi
  else
    echo "none"
  fi
}

switch_mode() {
  local mode=$1
  
  case "$mode" in
    "personal")
      echo -e "${GREEN}切换到个人开发者模式...${NC}"
      cp scripts/git_hooks/pre-commit-personal .git/hooks/pre-commit
      cp .pre-commit-config-personal.yaml .pre-commit-config.yaml
      chmod +x .git/hooks/pre-commit
      echo -e "${GREEN}✅ 已切换到个人开发者模式${NC}"
      echo -e "${YELLOW}特点: 宽松的敏感信息检测，不强制日志同步，条件性前端检查${NC}"
      ;;
      
    "standard")
      echo -e "${YELLOW}切换到标准模式...${NC}"
      cp scripts/git_hooks/pre-commit .git/hooks/pre-commit
      git checkout HEAD -- .pre-commit-config.yaml 2>/dev/null || echo "使用默认配置"
      chmod +x .git/hooks/pre-commit
      echo -e "${YELLOW}✅ 已切换到标准模式${NC}"
      echo -e "${YELLOW}特点: 当前的默认配置${NC}"
      ;;
      
    "enterprise")
      echo -e "${RED}企业模式尚未实现${NC}"
      echo -e "${YELLOW}如需企业级严格模式，请联系项目维护者${NC}"
      ;;
      
    "status")
      local current=$(get_current_mode)
      echo -e "${BLUE}当前预提交钩子模式: ${NC}"
      case "$current" in
        "personal")
          echo -e "  ${GREEN}✅ Personal (个人开发者模式)${NC}"
          ;;
        "standard") 
          echo -e "  ${YELLOW}✅ Standard (标准模式)${NC}"
          ;;
        "enterprise")
          echo -e "  ${RED}✅ Enterprise (企业模式)${NC}"
          ;;
        "none")
          echo -e "  ${RED}❌ 未配置预提交钩子${NC}"
          ;;
      esac
      ;;
      
    *)
      echo -e "${RED}❌ 未知模式: $mode${NC}"
      show_help
      exit 1
      ;;
  esac
}

# 主逻辑
if [ $# -eq 0 ]; then
  show_help
  exit 0
fi

case "$1" in
  "-h"|"--help"|"help")
    show_help
    ;;
  *)
    switch_mode "$1"
    ;;
esac