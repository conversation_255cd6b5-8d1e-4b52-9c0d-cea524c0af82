#!/usr/bin/env python3
"""
环境变量命名标准化脚本
根据AQUA项目规范标准化所有环境变量的命名
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Set
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.paths import Paths


class EnvironmentVariableStandardizer:
    """环境变量标准化器"""
    
    def __init__(self):
        self.project_root = Paths.ROOT
        
        # 标准化规则
        self.standardization_rules = {
            # 现有变量的标准化映射
            "TUSHARE_TOKEN": "AQUA_TUSHARE_TOKEN",
            "CSV_DATA_PATH": "AQUA_CSV_DATA_PATH",
            "BACKUP_DIR": "AQUA_BACKUP_DIR",
            "PROJECT_ROOT": "AQUA_PROJECT_ROOT",
            "LOG_PATH": "AQUA_LOG_PATH",
            "SETTINGS_PATH": "AQUA_SETTINGS_PATH",
            "FRONTEND_HOST": "AQUA_FRONTEND_HOST",
            "FRONTEND_PORT": "AQUA_FRONTEND_PORT",
            
            # 系统变量保持不变（不需要前缀）
            "HOME": "HOME",  # 系统变量
            "USERPROFILE": "USERPROFILE",  # Windows系统变量
            "PATH": "PATH",  # 系统变量
            "NODE_ENV": "NODE_ENV",  # Node.js标准变量
            "CI": "CI",  # CI/CD标准变量
        }
        
        # 需要保持原样的变量（系统变量或第三方标准变量）
        self.system_variables = {
            "HOME", "USERPROFILE", "PATH", "NODE_ENV", "CI", 
            "UPSTASH_VECTOR_REST_URL"  # 第三方服务变量
        }
        
        # 文件扩展名过滤
        self.code_extensions = {'.py', '.js', '.ts', '.vue', '.toml', '.yaml', '.yml', '.json', '.sh', '.bat', '.md'}
        
        # 排除的目录
        self.excluded_dirs = {'.git', '__pycache__', 'node_modules', '.venv', 'dist', 'build'}
        
        # 环境变量使用模式
        self.env_var_patterns = [
            (r'os\.environ\.get\([\'"]([^\'\"]+)[\'"]', 'python_get'),
            (r'os\.environ\[[\'"]([^\'\"]+)[\'"]\]', 'python_direct'),
            (r'os\.getenv\([\'"]([^\'\"]+)[\'"]', 'python_getenv'),
            (r'\$\{([A-Z_][A-Z0-9_]*)\}', 'shell_brace'),
            (r'process\.env\.([A-Z_][A-Z0-9_]*)', 'js_process_env'),
            (r'import\.meta\.env\.([A-Z_][A-Z0-9_]*)', 'vite_env'),
            (r'^([A-Z_][A-Z0-9_]*)=', 'env_file'),  # .env文件格式
        ]
        
        # 标准化结果
        self.standardization_plan: List[Dict] = []
        self.affected_files: Set[Path] = set()
    
    def analyze_current_usage(self) -> Dict:
        """分析当前环境变量使用情况"""
        print("🔍 分析当前环境变量使用情况...")
        
        usage_analysis = {
            'variables_found': {},
            'files_scanned': 0,
            'standardization_needed': []
        }
        
        for file_path in self._get_code_files():
            usage_analysis['files_scanned'] += 1
            
            try:
                content = file_path.read_text(encoding='utf-8', errors='ignore')
                
                for pattern, pattern_type in self.env_var_patterns:
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    for match in matches:
                        var_name = match.group(1)
                        
                        if var_name not in usage_analysis['variables_found']:
                            usage_analysis['variables_found'][var_name] = []
                        
                        usage_analysis['variables_found'][var_name].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': content[:match.start()].count('\n') + 1,
                            'pattern_type': pattern_type,
                            'context': self._get_context(content, match.start(), match.end())
                        })
                        
            except Exception as e:
                print(f"⚠️ 扫描文件失败: {file_path} - {e}")
        
        # 分析需要标准化的变量
        for var_name in usage_analysis['variables_found']:
            if self._needs_standardization(var_name):
                standard_name = self._get_standard_name(var_name)
                usage_analysis['standardization_needed'].append({
                    'original': var_name,
                    'standard': standard_name,
                    'reason': self._get_standardization_reason(var_name)
                })
        
        return usage_analysis
    
    def _needs_standardization(self, var_name: str) -> bool:
        """检查变量是否需要标准化"""
        # 系统变量不需要标准化
        if var_name in self.system_variables:
            return False
        
        # 已经符合AQUA或VITE前缀的变量不需要标准化
        if var_name.startswith(('AQUA_', 'VITE_')):
            return False
        
        # 单字符变量可能是临时变量，需要检查
        if len(var_name) <= 2:
            return True
        
        # 其他变量需要标准化
        return True
    
    def _get_standard_name(self, var_name: str) -> str:
        """获取变量的标准名称"""
        # 检查预定义的标准化规则
        if var_name in self.standardization_rules:
            return self.standardization_rules[var_name]
        
        # 系统变量保持不变
        if var_name in self.system_variables:
            return var_name
        
        # 前端相关变量使用VITE_前缀
        if any(keyword in var_name.lower() for keyword in ['frontend', 'ui', 'app', 'client']):
            return f"VITE_{var_name}"
        
        # 其他变量使用AQUA_前缀
        return f"AQUA_{var_name}"
    
    def _get_standardization_reason(self, var_name: str) -> str:
        """获取标准化原因"""
        if var_name in self.standardization_rules:
            return "预定义标准化规则"
        
        if len(var_name) <= 2:
            return "变量名过短，不符合命名规范"
        
        if not var_name.startswith(('AQUA_', 'VITE_')):
            return "缺少项目前缀"
        
        return "其他命名规范问题"
    
    def create_standardization_plan(self) -> Dict:
        """创建标准化计划"""
        print("📋 创建环境变量标准化计划...")
        
        analysis = self.analyze_current_usage()
        
        plan = {
            'summary': {
                'total_variables': len(analysis['variables_found']),
                'variables_need_standardization': len(analysis['standardization_needed']),
                'files_affected': 0
            },
            'standardization_actions': [],
            'migration_guide': [],
            'validation_steps': []
        }
        
        # 为每个需要标准化的变量创建行动计划
        for item in analysis['standardization_needed']:
            original = item['original']
            standard = item['standard']
            
            if original == standard:
                continue  # 不需要更改
            
            action = {
                'original_name': original,
                'standard_name': standard,
                'reason': item['reason'],
                'affected_files': [],
                'replacement_patterns': []
            }
            
            # 收集受影响的文件
            if original in analysis['variables_found']:
                for usage in analysis['variables_found'][original]:
                    action['affected_files'].append(usage['file'])
                    
                    # 根据使用模式创建替换规则
                    pattern_type = usage['pattern_type']
                    if pattern_type == 'python_get':
                        action['replacement_patterns'].append({
                            'pattern': f'os.environ.get([\'"]){original}([\'"])',
                            'replacement': f'os.environ.get(\\1{standard}\\2)',
                            'type': 'regex'
                        })
                    elif pattern_type == 'python_direct':
                        action['replacement_patterns'].append({
                            'pattern': f'os.environ[[\'"]]{original}[[\'"]]',
                            'replacement': f'os.environ["{standard}"]',
                            'type': 'regex'
                        })
                    elif pattern_type == 'shell_brace':
                        action['replacement_patterns'].append({
                            'pattern': f'${{{original}}}',
                            'replacement': f'${{{standard}}}',
                            'type': 'literal'
                        })
                    elif pattern_type == 'js_process_env':
                        action['replacement_patterns'].append({
                            'pattern': f'process.env.{original}',
                            'replacement': f'process.env.{standard}',
                            'type': 'literal'
                        })
                    elif pattern_type == 'vite_env':
                        action['replacement_patterns'].append({
                            'pattern': f'import.meta.env.{original}',
                            'replacement': f'import.meta.env.{standard}',
                            'type': 'literal'
                        })
                    elif pattern_type == 'env_file':
                        action['replacement_patterns'].append({
                            'pattern': f'{original}=',
                            'replacement': f'{standard}=',
                            'type': 'literal'
                        })
            
            # 去重受影响的文件
            action['affected_files'] = list(set(action['affected_files']))
            plan['summary']['files_affected'] += len(action['affected_files'])
            
            plan['standardization_actions'].append(action)
        
        # 创建迁移指南
        plan['migration_guide'] = self._create_migration_guide(plan['standardization_actions'])
        
        # 创建验证步骤
        plan['validation_steps'] = self._create_validation_steps()
        
        return plan
    
    def _create_migration_guide(self, actions: List[Dict]) -> List[Dict]:
        """创建迁移指南"""
        guide = []
        
        for action in actions:
            guide.append({
                'step': f"将 {action['original_name']} 重命名为 {action['standard_name']}",
                'description': f"原因: {action['reason']}",
                'affected_files': len(action['affected_files']),
                'manual_steps': [
                    f"1. 更新环境变量定义文件(.env, .env.*)中的 {action['original_name']}",
                    f"2. 更新部署配置中的环境变量设置",
                    f"3. 通知团队成员更新本地环境配置"
                ]
            })
        
        return guide
    
    def _create_validation_steps(self) -> List[str]:
        """创建验证步骤"""
        return [
            "1. 运行所有单元测试确保功能正常",
            "2. 检查环境变量管理器的验证功能",
            "3. 验证前端构建过程中的环境变量",
            "4. 测试不同环境(dev/test/prod)的配置",
            "5. 确认CI/CD流程中的环境变量设置"
        ]
    
    def generate_standardization_report(self, plan: Dict) -> str:
        """生成标准化报告"""
        report = []
        report.append("# AQUA项目环境变量标准化计划")
        report.append(f"生成时间: {self._get_timestamp()}")
        report.append("")
        
        # 摘要
        summary = plan['summary']
        report.append("## 📊 标准化摘要")
        report.append(f"- 发现的环境变量总数: {summary['total_variables']}")
        report.append(f"- 需要标准化的变量: {summary['variables_need_standardization']}")
        report.append(f"- 受影响的文件数: {summary['files_affected']}")
        report.append("")
        
        # 标准化行动
        if plan['standardization_actions']:
            report.append("## 🔧 标准化行动")
            for i, action in enumerate(plan['standardization_actions'], 1):
                report.append(f"### {i}. {action['original_name']} → {action['standard_name']}")
                report.append(f"**原因**: {action['reason']}")
                report.append(f"**受影响文件**: {len(action['affected_files'])} 个")
                
                if action['affected_files']:
                    report.append("**文件列表**:")
                    for file in action['affected_files'][:5]:  # 只显示前5个
                        report.append(f"- {file}")
                    if len(action['affected_files']) > 5:
                        report.append(f"- ... 还有 {len(action['affected_files']) - 5} 个文件")
                report.append("")
        
        # 迁移指南
        if plan['migration_guide']:
            report.append("## 📋 迁移指南")
            for i, guide in enumerate(plan['migration_guide'], 1):
                report.append(f"### 步骤 {i}: {guide['step']}")
                report.append(f"**说明**: {guide['description']}")
                report.append(f"**影响文件**: {guide['affected_files']} 个")
                report.append("**手动操作**:")
                for step in guide['manual_steps']:
                    report.append(f"   {step}")
                report.append("")
        
        # 验证步骤
        report.append("## ✅ 验证步骤")
        for step in plan['validation_steps']:
            report.append(f"- {step}")
        report.append("")
        
        return "\n".join(report)
    
    def _get_code_files(self):
        """获取所有需要扫描的代码文件"""
        for root, dirs, files in os.walk(self.project_root):
            # 排除特定目录
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix in self.code_extensions:
                    yield file_path
    
    def _get_context(self, content: str, start: int, end: int, context_size: int = 30) -> str:
        """获取匹配位置的上下文"""
        context_start = max(0, start - context_size)
        context_end = min(len(content), end + context_size)
        return content[context_start:context_end].strip()
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    print("🔧 AQUA项目环境变量标准化")
    print("=" * 50)
    
    standardizer = EnvironmentVariableStandardizer()
    
    # 创建标准化计划
    plan = standardizer.create_standardization_plan()
    
    # 生成报告
    report = standardizer.generate_standardization_report(plan)
    
    # 保存报告
    report_path = Paths.DOCS / "opti_pre_prod" / "environment_variables_standardization_plan.md"
    report_path.parent.mkdir(parents=True, exist_ok=True)
    report_path.write_text(report, encoding='utf-8')
    
    print(f"📄 标准化计划已保存到: {report_path}")
    
    # 显示摘要
    summary = plan['summary']
    print("\n📊 标准化摘要:")
    print(f"   总变量数: {summary['total_variables']}")
    print(f"   需标准化: {summary['variables_need_standardization']}")
    print(f"   影响文件: {summary['files_affected']}")
    
    # 保存详细数据
    json_path = Paths.DOCS / "opti_pre_prod" / "environment_variables_standardization_data.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(plan, f, indent=2, ensure_ascii=False)
    
    print(f"📊 详细数据已保存到: {json_path}")
    print("✅ 环境变量标准化计划生成完成")


if __name__ == "__main__":
    main()
