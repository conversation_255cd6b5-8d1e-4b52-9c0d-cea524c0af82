#!/usr/bin/env python3
"""
通过专用分支进行跨平台同步
更安全的OS X到Windows全量同步方案
"""

import subprocess
import sys
import argparse
from datetime import datetime
from pathlib import Path

class BranchBasedSync:
    def __init__(self):
        self.project_root = Path(__file__).resolve().parent.parent
        self.current_branch = None
        self.sync_branch = "osx-to-windows-sync"
        
    def run_command(self, command, description=""):
        """执行Git命令并返回结果"""
        print(f"▶️ {description}")
        print(f"🔧 执行: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command, 
                cwd=self.project_root,
                capture_output=True, 
                text=True, 
                check=True
            )
            if result.stdout.strip():
                print(f"✅ {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ 错误: {e}")
            if e.stderr:
                print(f"stderr: {e.stderr}")
            return None
    
    def get_current_branch(self):
        """获取当前分支名"""
        result = self.run_command(['git', 'branch', '--show-current'], "获取当前分支")
        if result:
            self.current_branch = result.stdout.strip()
            return self.current_branch
        return None
    
    def create_or_update_sync_branch(self):
        """创建或更新同步分支"""
        print(f"\n🔄 创建/更新同步分支: {self.sync_branch}")
        
        # 删除本地同步分支（如果存在）
        self.run_command(['git', 'branch', '-D', self.sync_branch], f"删除本地 {self.sync_branch} 分支")
        
        # 基于当前分支创建新的同步分支
        result = self.run_command([
            'git', 'checkout', '-b', self.sync_branch
        ], f"创建同步分支 {self.sync_branch}")
        
        if result:
            print(f"✅ 同步分支 {self.sync_branch} 已创建")
            return True
        return False
    
    def push_sync_branch(self):
        """推送同步分支到远程"""
        print(f"\n📤 推送同步分支到远程...")
        result = self.run_command([
            'git', 'push', '--force', 'origin', self.sync_branch
        ], f"强制推送 {self.sync_branch}")
        
        if result:
            print(f"✅ 同步分支已推送: origin/{self.sync_branch}")
            return True
        return False
    
    def return_to_original_branch(self):
        """返回到原分支"""
        print(f"\n🔙 返回到原分支: {self.current_branch}")
        result = self.run_command([
            'git', 'checkout', self.current_branch
        ], f"切换回 {self.current_branch}")
        
        if result:
            print(f"✅ 已返回到 {self.current_branch}")
            return True
        return False
    
    def push_from_osx(self):
        """OS X端推送流程"""
        print("🍎 OS X端同步分支推送")
        print("=" * 50)
        
        # 1. 获取当前分支
        if not self.get_current_branch():
            print("❌ 无法获取当前分支")
            return False
        
        print(f"📍 当前分支: {self.current_branch}")
        
        # 2. 检查工作区状态
        result = self.run_command(['git', 'status', '--porcelain'], "检查工作区状态")
        if result and result.stdout.strip():
            print("⚠️ 发现未提交的更改，请先提交")
            return False
        
        # 3. 创建同步分支
        if not self.create_or_update_sync_branch():
            print("❌ 创建同步分支失败")
            return False
        
        # 4. 推送同步分支
        if not self.push_sync_branch():
            print("❌ 推送同步分支失败")
            return False
        
        # 5. 返回原分支
        if not self.return_to_original_branch():
            print("❌ 返回原分支失败")
            return False
        
        # 6. 显示Windows端操作指令
        print("\n🪟 Windows端操作指令:")
        print("=" * 50)
        print("在Windows环境下执行以下命令:")
        print(f"python scripts/sync_via_branch.py --pull")
        print("\n或手动执行:")
        print("git fetch origin")
        print(f"git checkout {self.sync_branch}")
        print(f"git merge origin/{self.sync_branch}")
        print("# 检查差异后...")
        print(f"git checkout {self.current_branch}")
        print(f"git merge {self.sync_branch}")
        
        print("\n✅ OS X端同步分支推送完成!")
        return True
    
    def pull_from_windows(self):
        """Windows端拉取流程"""
        print("🪟 Windows端同步分支拉取")
        print("=" * 50)
        
        # 1. 获取当前分支
        if not self.get_current_branch():
            print("❌ 无法获取当前分支")
            return False
        
        print(f"📍 当前分支: {self.current_branch}")
        
        # 2. 拉取远程更改
        result = self.run_command(['git', 'fetch', 'origin'], "拉取远程更改")
        if not result:
            print("❌ 拉取远程更改失败")
            return False
        
        # 3. 切换到同步分支
        result = self.run_command(['git', 'checkout', self.sync_branch], f"切换到 {self.sync_branch}")
        if not result:
            # 如果本地没有同步分支，从远程创建
            result = self.run_command([
                'git', 'checkout', '-b', self.sync_branch, f'origin/{self.sync_branch}'
            ], f"从远程创建 {self.sync_branch}")
            if not result:
                print("❌ 无法切换到同步分支")
                return False
        
        # 4. 更新同步分支
        result = self.run_command([
            'git', 'pull', 'origin', self.sync_branch
        ], f"更新 {self.sync_branch}")
        if not result:
            print("❌ 更新同步分支失败")
            return False
        
        # 5. 显示差异预览
        print(f"\n📋 即将合并的差异预览:")
        self.run_command([
            'git', 'diff', '--stat', self.current_branch, self.sync_branch
        ], "显示差异统计")
        
        # 6. 获取用户确认
        confirm = input(f"\n确认要将 {self.sync_branch} 合并到 {self.current_branch} 吗? (yes/no): ").lower().strip()
        if confirm not in ['yes', 'y']:
            print("❌ 操作已取消")
            return False
        
        # 7. 切换回原分支并合并
        result = self.run_command([
            'git', 'checkout', self.current_branch
        ], f"切换回 {self.current_branch}")
        if not result:
            print("❌ 切换回原分支失败")
            return False
        
        # 8. 执行合并
        result = self.run_command([
            'git', 'merge', self.sync_branch
        ], f"合并 {self.sync_branch}")
        if not result:
            print("❌ 合并失败，可能存在冲突")
            print("请手动解决冲突后重试")
            return False
        
        print("\n✅ Windows端同步完成!")
        print(f"💡 可以删除同步分支: git branch -d {self.sync_branch}")
        
        return True


def main():
    parser = argparse.ArgumentParser(description="跨平台分支同步工具")
    parser.add_argument('--push', action='store_true', help='OS X端推送模式')
    parser.add_argument('--pull', action='store_true', help='Windows端拉取模式')
    
    args = parser.parse_args()
    
    syncer = BranchBasedSync()
    
    if args.push:
        success = syncer.push_from_osx()
    elif args.pull:
        success = syncer.pull_from_windows()
    else:
        # 自动检测平台
        import platform
        if platform.system() == "Windows":
            print("🪟 检测到Windows环境，执行拉取模式")
            success = syncer.pull_from_windows()
        else:
            print("🍎 检测到非Windows环境，执行推送模式")
            success = syncer.push_from_osx()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()