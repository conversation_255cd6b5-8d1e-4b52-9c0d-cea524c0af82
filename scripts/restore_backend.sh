#!/bin/bash
# 后端代码恢复脚本
# 作者: AI (CURSOR/GEMINI)
# 创建时间: 2025-07-02
# 版本: 1.0.0
# 变更记录:
#   - 2025-07-02: 创建初始版本

set -e
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
SETTINGS_PATH="$PROJECT_ROOT/config/settings.toml"
LOG_PATH="$PROJECT_ROOT/logs/restore.log"

# 读取环境参数
env=${1:-local}
BACKUP_FILE=$2

log_info() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"INFO\", \"module\": \"restore_backend\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}
log_error() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"ERROR\", \"module\": \"restore_backend\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}

if [ -z "$BACKUP_FILE" ] || [ ! -f "$BACKUP_FILE" ]; then
  log_error "未指定有效的后端备份文件: $BACKUP_FILE"
  exit 1
fi

# 恢复核心逻辑
{
  unzip -o "$BACKUP_FILE" -d "$PROJECT_ROOT" > /dev/null 2>&1
  log_info "后端代码恢复成功: $BACKUP_FILE -> $PROJECT_ROOT"
} || {
  log_error "后端代码恢复失败: $BACKUP_FILE"
  exit 1
} 