#!/usr/bin/env python3
"""
启动脚本测试验证工具
验证服务启动脚本的正确性和跨平台兼容性
"""

import os
import sys
import time
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.unified_service_launcher import (
    UnifiedServiceLauncher, 
    ServiceConfig, 
    ServiceType,
    ServiceStatus
)
from src.utils.platform_service_adapters import create_platform_adapter


class ServiceStartupValidator:
    """服务启动脚本验证器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
        self.launcher = UnifiedServiceLauncher(project_root)
        self.platform_adapter = create_platform_adapter(project_root)
        
        # 测试结果
        self.test_results = []
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_startup_scripts(self) -> Dict[str, Any]:
        """验证启动脚本"""
        print("🔧 启动脚本测试验证")
        print("=" * 50)
        
        validation_result = {
            "success": True,
            "platform": platform.system(),
            "tests_passed": 0,
            "tests_failed": 0,
            "errors": [],
            "warnings": [],
            "details": []
        }
        
        # 1. 验证基本启动脚本
        self._validate_basic_startup_scripts(validation_result)
        
        # 2. 验证跨平台兼容性
        self._validate_cross_platform_compatibility(validation_result)
        
        # 3. 验证服务配置
        self._validate_service_configurations(validation_result)
        
        # 4. 验证启动方法
        self._validate_startup_methods(validation_result)
        
        # 5. 验证错误处理
        self._validate_error_handling(validation_result)
        
        # 6. 验证性能指标
        self._validate_performance_metrics(validation_result)
        
        # 汇总结果
        validation_result["success"] = validation_result["tests_failed"] == 0
        
        return validation_result
    
    def _validate_basic_startup_scripts(self, result: Dict[str, Any]):
        """验证基本启动脚本"""
        print("\n📋 验证基本启动脚本...")
        
        # 检查主要启动脚本是否存在
        startup_scripts = [
            "Start-AQUA.ps1",  # Windows PowerShell
            "start_aqua.sh",   # Unix shell
            "Procfile",        # Honcho
        ]
        
        for script in startup_scripts:
            script_path = self.project_root / script
            test_name = f"检查启动脚本: {script}"
            
            if script_path.exists():
                result["tests_passed"] += 1
                result["details"].append({
                    "test": test_name,
                    "status": "PASS",
                    "message": f"启动脚本存在: {script_path}"
                })
                print(f"  ✅ {test_name}")
            else:
                result["tests_failed"] += 1
                result["errors"].append(f"启动脚本不存在: {script}")
                result["details"].append({
                    "test": test_name,
                    "status": "FAIL",
                    "message": f"启动脚本不存在: {script_path}"
                })
                print(f"  ❌ {test_name}")
    
    def _validate_cross_platform_compatibility(self, result: Dict[str, Any]):
        """验证跨平台兼容性"""
        print("\n🌐 验证跨平台兼容性...")
        
        # 测试平台适配器创建
        test_name = "平台适配器创建"
        try:
            adapter = create_platform_adapter(self.project_root)
            result["tests_passed"] += 1
            result["details"].append({
                "test": test_name,
                "status": "PASS",
                "message": f"成功创建{type(adapter).__name__}"
            })
            print(f"  ✅ {test_name}: {type(adapter).__name__}")
        except Exception as e:
            result["tests_failed"] += 1
            result["errors"].append(f"平台适配器创建失败: {e}")
            result["details"].append({
                "test": test_name,
                "status": "FAIL",
                "message": f"创建失败: {e}"
            })
            print(f"  ❌ {test_name}: {e}")
        
        # 测试路径处理
        test_name = "跨平台路径处理"
        try:
            test_paths = [
                "logs/test.log",
                "data/test.db",
                "config/test.json"
            ]
            
            for path_str in test_paths:
                normalized = Path(path_str)
                if normalized.is_absolute() or ".." in str(normalized):
                    raise ValueError(f"路径处理异常: {path_str}")
            
            result["tests_passed"] += 1
            result["details"].append({
                "test": test_name,
                "status": "PASS",
                "message": "路径处理正常"
            })
            print(f"  ✅ {test_name}")
        except Exception as e:
            result["tests_failed"] += 1
            result["errors"].append(f"路径处理失败: {e}")
            result["details"].append({
                "test": test_name,
                "status": "FAIL",
                "message": f"处理失败: {e}"
            })
            print(f"  ❌ {test_name}: {e}")
    
    def _validate_service_configurations(self, result: Dict[str, Any]):
        """验证服务配置"""
        print("\n⚙️ 验证服务配置...")
        
        # 创建测试服务配置
        test_configs = [
            ServiceConfig(
                name="test_backend",
                service_type=ServiceType.BACKEND,
                command=["echo", "backend test"],
                port=8080
            ),
            ServiceConfig(
                name="test_frontend",
                service_type=ServiceType.FRONTEND,
                command=["echo", "frontend test"],
                port=3000
            )
        ]
        
        for config in test_configs:
            test_name = f"验证服务配置: {config.name}"
            try:
                # 添加服务到启动器
                self.launcher.add_service(config)
                
                # 验证配置
                validation = self.launcher.validate_service_configuration(config.name)
                
                if validation["valid"]:
                    result["tests_passed"] += 1
                    result["details"].append({
                        "test": test_name,
                        "status": "PASS",
                        "message": "配置验证通过"
                    })
                    print(f"  ✅ {test_name}")
                else:
                    result["tests_failed"] += 1
                    result["errors"].extend(validation["errors"])
                    result["warnings"].extend(validation["warnings"])
                    result["details"].append({
                        "test": test_name,
                        "status": "FAIL",
                        "message": f"配置验证失败: {validation['errors']}"
                    })
                    print(f"  ❌ {test_name}: {validation['errors']}")
                    
            except Exception as e:
                result["tests_failed"] += 1
                result["errors"].append(f"服务配置验证异常: {e}")
                result["details"].append({
                    "test": test_name,
                    "status": "ERROR",
                    "message": f"验证异常: {e}"
                })
                print(f"  ❌ {test_name}: {e}")
    
    def _validate_startup_methods(self, result: Dict[str, Any]):
        """验证启动方法"""
        print("\n🚀 验证启动方法...")
        
        # 测试不同启动方法的可用性
        startup_methods = [
            ("direct", "直接启动"),
            ("honcho", "Honcho启动"),
            ("systemd", "Systemd启动"),
            ("launchd", "Launchd启动"),
            ("windows_service", "Windows服务"),
            ("powershell", "PowerShell启动")
        ]
        
        for method, description in startup_methods:
            test_name = f"检查启动方法: {description}"
            try:
                # 检查方法是否可用
                available = self.launcher._is_startup_method_available(method)
                
                if available:
                    result["tests_passed"] += 1
                    result["details"].append({
                        "test": test_name,
                        "status": "PASS",
                        "message": f"{description}可用"
                    })
                    print(f"  ✅ {test_name}")
                else:
                    result["warnings"].append(f"{description}不可用")
                    result["details"].append({
                        "test": test_name,
                        "status": "SKIP",
                        "message": f"{description}在当前平台不可用"
                    })
                    print(f"  ⚠️ {test_name}: 当前平台不支持")
                    
            except Exception as e:
                result["tests_failed"] += 1
                result["errors"].append(f"启动方法检查失败: {e}")
                result["details"].append({
                    "test": test_name,
                    "status": "ERROR",
                    "message": f"检查异常: {e}"
                })
                print(f"  ❌ {test_name}: {e}")
    
    def _validate_error_handling(self, result: Dict[str, Any]):
        """验证错误处理"""
        print("\n🛡️ 验证错误处理...")
        
        # 测试无效配置的错误处理
        test_name = "无效配置错误处理"
        try:
            invalid_config = ServiceConfig(
                name="",  # 无效名称
                service_type=ServiceType.BACKEND,
                command=[],  # 无效命令
                port=99999  # 无效端口
            )
            
            self.launcher.add_service(invalid_config)
            validation = self.launcher.validate_service_configuration("")
            
            if not validation["valid"] and validation["errors"]:
                result["tests_passed"] += 1
                result["details"].append({
                    "test": test_name,
                    "status": "PASS",
                    "message": "正确识别无效配置"
                })
                print(f"  ✅ {test_name}")
            else:
                result["tests_failed"] += 1
                result["errors"].append("未能识别无效配置")
                result["details"].append({
                    "test": test_name,
                    "status": "FAIL",
                    "message": "未能识别无效配置"
                })
                print(f"  ❌ {test_name}")
                
        except Exception as e:
            result["tests_passed"] += 1  # 异常也是正确的错误处理
            result["details"].append({
                "test": test_name,
                "status": "PASS",
                "message": f"正确抛出异常: {e}"
            })
            print(f"  ✅ {test_name}: 正确抛出异常")
    
    def _validate_performance_metrics(self, result: Dict[str, Any]):
        """验证性能指标"""
        print("\n📊 验证性能指标...")
        
        test_name = "性能指标收集"
        try:
            # 创建测试服务
            test_config = ServiceConfig(
                name="metrics_test",
                service_type=ServiceType.BACKEND,
                command=["sleep", "1"],
                port=8081
            )
            
            self.launcher.add_service(test_config)
            
            # 获取性能指标
            metrics = self.launcher.get_service_metrics("metrics_test")
            
            required_fields = ["name", "status", "uptime", "memory_usage", "cpu_usage", "port", "pid"]
            missing_fields = [field for field in required_fields if field not in metrics]
            
            if not missing_fields:
                result["tests_passed"] += 1
                result["details"].append({
                    "test": test_name,
                    "status": "PASS",
                    "message": "性能指标收集完整"
                })
                print(f"  ✅ {test_name}")
            else:
                result["tests_failed"] += 1
                result["errors"].append(f"性能指标缺失字段: {missing_fields}")
                result["details"].append({
                    "test": test_name,
                    "status": "FAIL",
                    "message": f"缺失字段: {missing_fields}"
                })
                print(f"  ❌ {test_name}: 缺失字段 {missing_fields}")
                
        except Exception as e:
            result["tests_failed"] += 1
            result["errors"].append(f"性能指标收集失败: {e}")
            result["details"].append({
                "test": test_name,
                "status": "ERROR",
                "message": f"收集异常: {e}"
            })
            print(f"  ❌ {test_name}: {e}")
    
    def generate_validation_report(self, result: Dict[str, Any]) -> str:
        """生成验证报告"""
        report = f"""
# 启动脚本测试验证报告

## 验证概览
- **平台**: {result['platform']}
- **验证时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **测试通过**: {result['tests_passed']}
- **测试失败**: {result['tests_failed']}
- **总体结果**: {'✅ 通过' if result['success'] else '❌ 失败'}

## 详细结果
"""
        
        for detail in result['details']:
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '💥',
                'SKIP': '⚠️'
            }.get(detail['status'], '❓')
            
            report += f"- {status_icon} **{detail['test']}**: {detail['message']}\n"
        
        if result['errors']:
            report += "\n## 错误列表\n"
            for error in result['errors']:
                report += f"- ❌ {error}\n"
        
        if result['warnings']:
            report += "\n## 警告列表\n"
            for warning in result['warnings']:
                report += f"- ⚠️ {warning}\n"
        
        return report


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建验证器
    validator = ServiceStartupValidator(project_root)
    
    # 执行验证
    result = validator.validate_startup_scripts()
    
    # 生成报告
    report = validator.generate_validation_report(result)
    
    # 保存报告
    report_file = project_root / "docs" / "opti_pre_prod" / "service_startup_validation_report.md"
    report_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 验证报告已保存: {report_file}")
    
    # 显示结果
    print(f"\n🎯 验证结果:")
    print(f"  - 测试通过: {result['tests_passed']}")
    print(f"  - 测试失败: {result['tests_failed']}")
    print(f"  - 总体结果: {'✅ 通过' if result['success'] else '❌ 失败'}")
    
    if not result['success']:
        print(f"\n❌ 验证失败，请检查错误:")
        for error in result['errors']:
            print(f"  - {error}")
        sys.exit(1)
    else:
        print("\n✅ 启动脚本验证通过!")


if __name__ == "__main__":
    main()
