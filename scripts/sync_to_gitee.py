#!/usr/bin/env python3
"""
OS X到Gitee全量推送脚本
安全地将OS X本地代码全量推送到Gitee仓库
"""

import subprocess
import sys
import json
from datetime import datetime
from pathlib import Path

class GiteeFullSync:
    def __init__(self):
        self.project_root = Path(__file__).resolve().parent.parent
        self.current_branch = None
        self.backup_branch = None
        
    def run_command(self, command, description=""):
        """执行Git命令并返回结果"""
        print(f"▶️ {description}")
        print(f"🔧 执行: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command, 
                cwd=self.project_root,
                capture_output=True, 
                text=True, 
                check=True
            )
            if result.stdout.strip():
                print(f"✅ {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ 错误: {e}")
            print(f"stderr: {e.stderr}")
            return None
    
    def get_current_branch(self):
        """获取当前分支名"""
        result = self.run_command(['git', 'branch', '--show-current'], "获取当前分支")
        if result:
            self.current_branch = result.stdout.strip()
            return self.current_branch
        return None
    
    def check_git_status(self):
        """检查Git工作区状态"""
        print("\n📋 检查Git工作区状态...")
        
        # 检查是否有未提交的更改
        result = self.run_command(['git', 'status', '--porcelain'], "检查工作区状态")
        if result and result.stdout.strip():
            print("⚠️ 发现未提交的更改:")
            print(result.stdout)
            return False
        
        print("✅ 工作区干净，可以安全同步")
        return True
    
    def create_backup_branch(self):
        """创建备份分支"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_branch = f"backup_before_sync_{timestamp}"
        
        print(f"\n💾 创建备份分支: {self.backup_branch}")
        result = self.run_command([
            'git', 'branch', self.backup_branch
        ], f"创建备份分支 {self.backup_branch}")
        
        if result:
            print(f"✅ 备份分支已创建: {self.backup_branch}")
            return True
        return False
    
    def push_backup_branch(self):
        """推送备份分支到远程"""
        print(f"\n📤 推送备份分支到远程...")
        result = self.run_command([
            'git', 'push', 'origin', self.backup_branch
        ], f"推送备份分支到远程")
        
        if result:
            print(f"✅ 备份分支已推送到远程: origin/{self.backup_branch}")
            return True
        return False
    
    def force_push_current_branch(self):
        """强制推送当前分支"""
        print(f"\n🚀 强制推送当前分支 {self.current_branch} 到远程...")
        
        # 使用 --force-with-lease 更安全
        result = self.run_command([
            'git', 'push', '--force-with-lease', 'origin', self.current_branch
        ], f"强制推送 {self.current_branch}")
        
        if result:
            print(f"✅ 当前分支 {self.current_branch} 已强制推送到远程")
            return True
        return False
    
    def show_recovery_instructions(self):
        """显示恢复指令"""
        print(f"\n📝 恢复指令 (如果需要):")
        print("=" * 60)
        print("如果需要恢复到推送前的状态，可以执行:")
        print(f"git checkout {self.backup_branch}")
        print(f"git branch -D {self.current_branch}")
        print(f"git checkout -b {self.current_branch}")
        print("或者在远程恢复:")
        print(f"git push --force origin {self.backup_branch}:{self.current_branch}")
        print("=" * 60)
    
    def sync_to_gitee(self):
        """执行完整的同步流程"""
        print("🚀 AQUA OS X -> Gitee 全量同步")
        print("=" * 70)
        print("⚠️  此操作将强制覆盖远程仓库内容")
        print("💾 自动创建备份分支以防意外")
        print()
        
        # 获取用户确认
        confirm = input("确认要执行全量推送吗? (yes/no): ").lower().strip()
        if confirm not in ['yes', 'y']:
            print("❌ 操作已取消")
            return False
        
        # 1. 获取当前分支
        if not self.get_current_branch():
            print("❌ 无法获取当前分支")
            return False
        
        print(f"📍 当前分支: {self.current_branch}")
        
        # 2. 检查工作区状态
        if not self.check_git_status():
            print("❌ 工作区有未提交的更改，请先提交或暂存")
            return False
        
        # 3. 创建备份分支
        if not self.create_backup_branch():
            print("❌ 创建备份分支失败")
            return False
        
        # 4. 推送备份分支
        if not self.push_backup_branch():
            print("⚠️  备份分支推送失败，但继续执行主分支推送")
        
        # 5. 强制推送当前分支
        if not self.force_push_current_branch():
            print("❌ 强制推送失败")
            return False
        
        # 6. 显示成功信息和恢复指令
        print("\n🎉 全量同步成功完成!")
        print(f"✅ OS X本地代码已全量推送到 origin/{self.current_branch}")
        print(f"💾 备份分支: origin/{self.backup_branch}")
        
        self.show_recovery_instructions()
        
        # 7. 生成Windows端拉取指令
        self.generate_windows_sync_commands()
        
        return True
    
    def generate_windows_sync_commands(self):
        """生成Windows端同步命令"""
        print(f"\n🪟 Windows端同步命令:")
        print("=" * 60)
        print("在Windows环境下执行以下命令完成全量拉取:")
        print()
        print("# 方法1: 使用脚本 (推荐)")
        print("python scripts/sync_from_gitee.py")
        print()
        print("# 方法2: 手动执行")
        print("git fetch --all")
        print(f"git reset --hard origin/{self.current_branch}")
        print("git clean -fd")
        print()
        print("⚠️  注意: 这将覆盖Windows端的所有本地更改!")
        print("=" * 60)


def main():
    syncer = GiteeFullSync()
    success = syncer.sync_to_gitee()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()