#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国网络环境优化模块
专门解决PyPI镜像源和依赖安装问题
"""

import os
import sys
import socket
import subprocess
import urllib.request
import ssl
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class ChinaNetworkOptimizer:
    """中国网络环境优化器"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.pip_conf_path = Path.home() / '.pip' / 'pip.conf'
        self.venv_path = self.project_root / '.venv'
        
        # 优化的镜像源配置（按可靠性排序）
        self.china_mirrors = [
            {
                'name': '清华大学',
                'url': 'https://pypi.tuna.tsinghua.edu.cn/simple',
                'host': 'pypi.tuna.tsinghua.edu.cn',
                'priority': 1,
                'ssl_verify': True
            },
            {
                'name': '阿里云',
                'url': 'https://mirrors.aliyun.com/pypi/simple/',
                'host': 'mirrors.aliyun.com',
                'priority': 2,
                'ssl_verify': True
            },
            {
                'name': '中科大',
                'url': 'https://pypi.mirrors.ustc.edu.cn/simple/',
                'host': 'pypi.mirrors.ustc.edu.cn',
                'priority': 3,
                'ssl_verify': True
            },
            {
                'name': '豆瓣',
                'url': 'https://pypi.douban.com/simple/',
                'host': 'pypi.douban.com',
                'priority': 4,
                'ssl_verify': False  # 豆瓣可能有SSL问题
            },
            {
                'name': '腾讯云',
                'url': 'https://mirrors.cloud.tencent.com/pypi/simple/',
                'host': 'mirrors.cloud.tencent.com',
                'priority': 5,
                'ssl_verify': True
            }
        ]
        
        # 国外备用镜像源
        self.global_mirrors = [
            {
                'name': 'PyPI官方',
                'url': 'https://pypi.org/simple/',
                'host': 'pypi.org',
                'priority': 10,
                'ssl_verify': True
            },
            {
                'name': 'PyPI Python.org',
                'url': 'https://pypi.python.org/simple/',
                'host': 'pypi.python.org',
                'priority': 11,
                'ssl_verify': True
            }
        ]
    
    def is_china_network(self, timeout: int = 3) -> bool:
        """
        检测是否在中国网络环境
        通过多个指标综合判断
        """
        china_indicators = [
            'pypi.tuna.tsinghua.edu.cn',  # 清华源
            'mirrors.aliyun.com',          # 阿里云
            'baidu.com',                   # 百度
        ]
        
        accessible_count = 0
        for host in china_indicators:
            try:
                socket.setdefaulttimeout(timeout)
                socket.gethostbyname(host)
                accessible_count += 1
            except Exception:
                continue
        
        # 如果能访问2个以上中国服务，认为在中国网络环境
        return accessible_count >= 2
    
    def test_mirror_advanced(self, mirror: Dict, timeout: int = 5) -> Tuple[bool, float, str]:
        """
        高级镜像源测试
        返回：(是否可用, 响应时间, 错误信息)
        """
        import time
        
        try:
            start_time = time.time()
            
            # 创建SSL上下文
            if mirror.get('ssl_verify', True):
                ssl_context = ssl.create_default_context()
            else:
                ssl_context = ssl._create_unverified_context()
            
            # 测试简单页面访问
            req = urllib.request.Request(
                mirror['url'],
                headers={
                    'User-Agent': 'AQUA-Python-Installer/1.0',
                    'Accept': 'text/html,application/json'
                }
            )
            
            with urllib.request.urlopen(req, timeout=timeout, context=ssl_context) as response:
                # 读取少量数据验证连接
                response.read(1024)
                response_time = time.time() - start_time
                return True, response_time, ""
                
        except urllib.error.HTTPError as e:
            if e.code == 200:
                response_time = time.time() - start_time
                return True, response_time, ""
            return False, 0, f"HTTP错误: {e.code}"
        except urllib.error.URLError as e:
            return False, 0, f"URL错误: {str(e)}"
        except socket.timeout:
            return False, 0, "连接超时"
        except Exception as e:
            return False, 0, f"未知错误: {str(e)}"
    
    def get_best_mirrors(self, max_mirrors: int = 3) -> List[Dict]:
        """
        获取最佳可用镜像源
        按响应时间和可靠性排序
        """
        print("🔍 开始全面检测PyPI镜像源...")
        
        # 检测网络环境
        is_china = self.is_china_network()
        print(f"🌍 网络环境: {'中国大陆' if is_china else '海外/其他'}")
        
        # 选择待测试的镜像源
        if is_china:
            test_mirrors = self.china_mirrors + self.global_mirrors
        else:
            test_mirrors = self.global_mirrors + self.china_mirrors
        
        available_mirrors = []
        
        for mirror in test_mirrors:
            print(f"  测试 {mirror['name']} ({mirror['url']})")
            is_available, response_time, error = self.test_mirror_advanced(mirror)
            
            if is_available:
                mirror['response_time'] = response_time
                available_mirrors.append(mirror)
                print(f"  ✅ {mirror['name']} 可用 (响应时间: {response_time:.2f}s)")
            else:
                print(f"  ❌ {mirror['name']} 不可用 ({error})")
        
        if not available_mirrors:
            print("  ⚠️ 所有镜像源均不可用，将尝试无镜像源安装")
            return []
        
        # 按响应时间和优先级排序
        available_mirrors.sort(key=lambda x: (x['response_time'], x['priority']))
        
        best_mirrors = available_mirrors[:max_mirrors]
        print(f"🎯 选中最佳镜像源: {[m['name'] for m in best_mirrors]}")
        
        return best_mirrors
    
    def install_loguru_optimized(self) -> bool:
        """
        优化的loguru安装方案
        """
        print("📦 开始优化安装 loguru...")
        
        # 获取最佳镜像源
        best_mirrors = self.get_best_mirrors()
        
        # 获取安装工具
        install_tools = self._get_install_tools()
        
        for tool_name, tool_cmd in install_tools.items():
            print(f"🔧 尝试使用 {tool_name} 安装...")
            
            if self._try_install_with_tool(tool_cmd, best_mirrors, 'loguru==0.7.2'):
                return True
        
        # 最后尝试离线安装
        return self._try_offline_install()
    
    def _get_install_tools(self) -> Dict[str, List[str]]:
        """获取可用的安装工具"""
        tools = {}
        
        # 1. 优先使用项目内的uv
        project_uv = self.venv_path / 'bin' / 'uv'
        if project_uv.exists():
            tools['uv (项目)'] = [str(project_uv), 'pip', 'install']
        
        # 2. 系统uv
        if self._command_exists('uv'):
            tools['uv (系统)'] = ['uv', 'pip', 'install']
        
        # 3. 虚拟环境pip
        project_pip = self.venv_path / 'bin' / 'pip'
        if project_pip.exists():
            tools['pip (项目)'] = [str(project_pip), 'install']
        
        # 4. 系统pip
        tools['pip (系统)'] = [sys.executable, '-m', 'pip', 'install']
        
        return tools
    
    def _command_exists(self, command: str) -> bool:
        """检查命令是否存在"""
        try:
            subprocess.run([command, '--version'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _try_install_with_tool(self, base_cmd: List[str], mirrors: List[Dict], package: str) -> bool:
        """使用指定工具和镜像源尝试安装"""
        
        # 如果没有可用镜像源，尝试直接安装
        if not mirrors:
            return self._execute_install(base_cmd + [package])
        
        # 尝试每个镜像源
        for mirror in mirrors:
            print(f"  📡 使用镜像源: {mirror['name']}")
            
            cmd = base_cmd + [
                package,
                '-i', mirror['url'],
                '--trusted-host', mirror['host']
            ]
            
            # 如果SSL有问题，添加额外参数
            if not mirror.get('ssl_verify', True):
                cmd.extend(['--trusted-host', mirror['host']])
            
            if self._execute_install(cmd):
                return True
        
        return False
    
    def _execute_install(self, cmd: List[str], timeout: int = 120) -> bool:
        """执行安装命令"""
        try:
            print(f"    执行: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                check=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            print(f"    ✅ 安装成功")
            if result.stdout:
                # 只显示重要信息
                lines = result.stdout.split('\n')
                important_lines = [line for line in lines if any(keyword in line.lower() 
                                 for keyword in ['successfully', 'installed', 'requirement'])]
                if important_lines:
                    print(f"    📋 {important_lines[-1]}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"    ❌ 安装失败: {e.returncode}")
            if e.stderr:
                # 提取关键错误信息
                error_lines = e.stderr.split('\n')
                key_errors = [line for line in error_lines if any(keyword in line.lower() 
                            for keyword in ['error', 'failed', 'timeout', 'connection'])]
                if key_errors:
                    print(f"    🔍 错误详情: {key_errors[-1]}")
            return False
            
        except subprocess.TimeoutExpired:
            print(f"    ⏰ 安装超时 ({timeout}秒)")
            return False
        except Exception as e:
            print(f"    ❌ 意外错误: {e}")
            return False
    
    def _try_offline_install(self) -> bool:
        """尝试离线安装loguru"""
        print("🔄 尝试离线安装方案...")
        
        # 创建临时目录
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 尝试下载wheel文件
            wheel_urls = [
                'https://files.pythonhosted.org/packages/03/0a/4f6fed21aa246c6b49b561ca55fae79b79e6a07b8d8ac6f1d605b2beac5ea/loguru-0.7.2-py3-none-any.whl',
                'https://pypi.org/packages/source/l/loguru/loguru-0.7.2.tar.gz'
            ]
            
            for url in wheel_urls:
                try:
                    print(f"  📥 尝试下载: {url}")
                    filename = url.split('/')[-1]
                    filepath = temp_path / filename
                    
                    urllib.request.urlretrieve(url, filepath)
                    print(f"  ✅ 下载成功: {filename}")
                    
                    # 尝试安装
                    cmd = [sys.executable, '-m', 'pip', 'install', str(filepath)]
                    if self._execute_install(cmd):
                        return True
                        
                except Exception as e:
                    print(f"  ❌ 下载失败: {e}")
                    continue
        
        return False
    
    def setup_permanent_mirror(self, mirror_name: str = 'tsinghua') -> bool:
        """
        设置永久镜像源配置
        """
        mirror_configs = {
            'tsinghua': {
                'url': 'https://pypi.tuna.tsinghua.edu.cn/simple',
                'host': 'pypi.tuna.tsinghua.edu.cn'
            },
            'aliyun': {
                'url': 'https://mirrors.aliyun.com/pypi/simple/',
                'host': 'mirrors.aliyun.com'
            },
            'ustc': {
                'url': 'https://pypi.mirrors.ustc.edu.cn/simple/',
                'host': 'pypi.mirrors.ustc.edu.cn'
            }
        }
        
        if mirror_name not in mirror_configs:
            print(f"❌ 不支持的镜像源: {mirror_name}")
            return False
        
        mirror = mirror_configs[mirror_name]
        
        try:
            # 创建.pip目录
            self.pip_conf_path.parent.mkdir(exist_ok=True)
            
            # 写入配置
            config_content = f"""[global]
index-url = {mirror['url']}
trusted-host = {mirror['host']}

[install]
trusted-host = {mirror['host']}
"""
            
            with open(self.pip_conf_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print(f"✅ 永久镜像源配置完成: {mirror_name}")
            print(f"📁 配置文件: {self.pip_conf_path}")
            return True
            
        except Exception as e:
            print(f"❌ 配置镜像源失败: {e}")
            return False
    
    def verify_loguru_installation(self) -> bool:
        """验证loguru安装是否成功"""
        try:
            result = subprocess.run([
                sys.executable, '-c', 
                'from loguru import logger; logger.info("Loguru test successful"); print("✅ loguru验证成功")'
            ], capture_output=True, text=True, check=True)
            
            print("🧪 loguru安装验证:")
            print(result.stdout)
            return True
            
        except subprocess.CalledProcessError as e:
            print("❌ loguru验证失败:")
            if e.stdout:
                print(e.stdout)
            if e.stderr:
                print(e.stderr)
            return False
    
    def get_installation_summary(self) -> Dict:
        """获取安装环境摘要"""
        return {
            'network_type': '中国大陆' if self.is_china_network() else '海外/其他',
            'python_version': sys.version,
            'pip_config': str(self.pip_conf_path) if self.pip_conf_path.exists() else 'None',
            'venv_path': str(self.venv_path),
            'available_mirrors': len(self.get_best_mirrors()),
            'project_root': str(self.project_root)
        }

def main():
    """主函数 - 用于独立运行"""
    optimizer = ChinaNetworkOptimizer()
    
    print("🚀 AQUA中国网络环境优化器")
    print("=" * 50)
    
    # 显示环境信息
    summary = optimizer.get_installation_summary()
    print("📋 环境信息:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    print()
    
    # 安装loguru
    if optimizer.install_loguru_optimized():
        print("\n🎉 loguru安装成功！")
        
        # 验证安装
        if optimizer.verify_loguru_installation():
            print("✅ 所有检查通过")
        else:
            print("⚠️ 安装成功但验证失败")
    else:
        print("\n❌ loguru安装失败")
        print("\n🔧 手动解决方案:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN")
        print("3. 手动下载wheel文件安装")
        print("4. 联系管理员协助")
        
        # 提供永久镜像源配置选项
        choice = input("\n是否配置永久镜像源？(y/n): ")
        if choice.lower() == 'y':
            optimizer.setup_permanent_mirror('tsinghua')

if __name__ == '__main__':
    main()