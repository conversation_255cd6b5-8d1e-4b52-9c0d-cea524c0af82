#!/usr/bin/env python3
"""
Windows环境初始化演示脚本
模拟Windows 11用户运行aqua init的过程
"""

import sys
import platform
from pathlib import Path
from unittest.mock import patch

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def demo_windows_init():
    """演示Windows环境下的初始化过程"""
    print("🚀 AQUA Windows 11 初始化演示")
    print("=" * 70)
    print("模拟用户在Windows 11环境下运行: python aqua.py init")
    print()
    
    try:
        from aqua.cli.init_helpers import _get_requirements_file, _get_project_root
        
        # 模拟Windows环境
        with patch('platform.system', return_value='Windows'):
            print("🪟 检测到Windows环境...")
            
            # 1. 显示选择的依赖文件
            requirements_file = _get_requirements_file()
            print(f"📦 依赖文件选择: {requirements_file.name}")
            
            if requirements_file.name == "requirements-windows.txt":
                print("✅ 使用Windows优化的依赖配置")
                print("   - 包含Windows专用包 (pywin32, colorama)")
                print("   - 排除不兼容包 (uvloop, httptools)")
                print("   - 包含完整开发工具链 (mypy, black, ruff)")
                print("   - 针对Windows环境优化的镜像源配置")
            
            # 2. 显示预期的安装过程
            print(f"\n🔄 依赖同步过程预览:")
            print("   1. 🔍 智能镜像源检测...")
            print("      📡 尝试镜像源 1/7: 清华大学 (优先)")
            print("      ✅ 连接成功，使用清华大学镜像源")
            print("   2. 📦 同步Windows优化依赖...")
            print("      uv pip sync requirements-windows.txt --index-url https://pypi.tuna.tsinghua.edu.cn/simple")
            print("   3. ✅ 依赖同步完成")
            
            # 3. 显示Windows专用优化
            print(f"\n🎯 Windows专用优化:")
            print("   ✅ 自动跳过Unix专用包 (uvloop)")
            print("   ✅ 包含Windows系统监控 (psutil)")
            print("   ✅ 支持Windows服务管理 (pywin32)")
            print("   ✅ 控制台彩色输出 (colorama)")
            print("   ✅ 完整开发工具链 (mypy, black, ruff)")
            
            # 4. 显示预期结果
            print(f"\n🎉 预期结果:")
            print("   - ⚡ 安装速度提升 (国内镜像源优先)")
            print("   - 🛡️ 兼容性保证 (排除问题包)")
            print("   - 🔧 功能完整 (包含所有必要工具)")
            print("   - 🪟 Windows优化 (专用依赖支持)")
            
            # 5. 与之前的对比
            print(f"\n📊 与通用版本对比:")
            general_req = _get_project_root() / "requirements.txt"
            windows_req = _get_project_root() / "requirements-windows.txt"
            
            if general_req.exists() and windows_req.exists():
                with open(general_req, 'r') as f:
                    general_lines = len([l for l in f.readlines() if l.strip() and not l.startswith('#')])
                with open(windows_req, 'r') as f:
                    windows_lines = len([l for l in f.readlines() if l.strip() and not l.startswith('#')])
                
                print(f"   📦 通用版本包数量: {general_lines}")
                print(f"   🪟 Windows版本包数量: {windows_lines}")
                print(f"   🔄 优化策略: 移除不兼容 + 添加Windows专用")
            
        print(f"\n" + "=" * 70)
        print("✅ Windows 11环境下AQUA将获得最佳的安装和使用体验！")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    demo_windows_init()