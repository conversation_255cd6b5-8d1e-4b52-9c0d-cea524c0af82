#!/usr/bin/env python3
"""
AQUA数据库完整初始化脚本
严格遵循DATA_DICTIONARY.md v4.0统一业务架构设计

功能：
1. 创建完整的数据表结构（包括主键、索引、约束）
2. 跨平台兼容（Windows + macOS）
3. 环境隔离（dev/test/prod）
4. 数据字典一致性验证
"""

import sys
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.connection_manager import DuckDBConnectionManager
from src.utils.config_loader_v2 import ConfigLoaderV2
from src.utils.exceptions import DatabaseException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseInitializer:
    """数据库初始化器 - 严格遵循DATA_DICTIONARY.md"""
    
    def __init__(self, environment: str = "test"):
        """
        初始化数据库初始化器
        
        Args:
            environment: 环境名称 (dev/test/prod)
        """
        self.environment = environment
        self.config_loader = ConfigLoaderV2()
        self.connection_manager = DuckDBConnectionManager(environment)
        
        logger.info(f"🚀 初始化AQUA数据库 - 环境: {environment}")
        
        # 验证配置
        self._validate_environment()
    
    def _validate_environment(self):
        """验证环境配置"""
        try:
            config = self.config_loader.get_config(self.environment)
            db_config = config.get("database", {})
            
            if not db_config.get("path"):
                raise ValueError(f"环境 {self.environment} 的数据库路径未配置")
                
            logger.info(f"✅ 环境配置验证通过: {self.environment}")
            
        except Exception as e:
            logger.error(f"❌ 环境配置验证失败: {e}")
            raise
    
    def create_unified_business_tables(self) -> bool:
        """
        创建统一业务表 - 严格遵循DATA_DICTIONARY.md v4.0
        
        Returns:
            bool: 是否成功创建所有表
        """
        logger.info("📊 开始创建统一业务表结构...")
        
        # 按DATA_DICTIONARY.md v4.0设计的表结构
        table_definitions = {
            # ========== 期货数据表 ==========
            "futures_main_contract_kline": '''
                CREATE TABLE IF NOT EXISTS futures_main_contract_kline (
                    -- 业务主键
                    contract_code     VARCHAR(20)      NOT NULL,           -- 标准化合约代码，如RB2501
                    product_code      VARCHAR(10)      NOT NULL,           -- 标准化品种代码，如RB
                    frequency         VARCHAR(10)      NOT NULL,           -- 数据频率：5min/15min/30min/1d
                    trade_datetime    TIMESTAMP        NOT NULL,           -- 标准化交易时间（北京时间）
                    
                    -- 价格数据（统一单位：元，精度4位小数）
                    open_price        DECIMAL(18,4)    NOT NULL,           -- 开盘价
                    high_price        DECIMAL(18,4)    NOT NULL,           -- 最高价
                    low_price         DECIMAL(18,4)    NOT NULL,           -- 最低价
                    close_price       DECIMAL(18,4)    NOT NULL,           -- 收盘价
                    settle_price      DECIMAL(18,4),                       -- 结算价（期货特有）
                    
                    -- 成交数据（标准化单位）
                    volume           BIGINT           NOT NULL,            -- 成交量（手）
                    amount           DECIMAL(22,4)    NOT NULL,            -- 成交额（元）
                    open_interest    BIGINT,                               -- 持仓量（手）
                    
                    -- 技术指标字段（预留扩展）
                    prev_close       DECIMAL(18,4),                        -- 前收盘价
                    change_amount    DECIMAL(18,4),                        -- 涨跌额
                    change_percent   DECIMAL(10,4),                        -- 涨跌幅(%)
                    
                    -- 数据源管理（透明化）
                    data_source      VARCHAR(20)      NOT NULL DEFAULT 'UNKNOWN',  -- 数据来源：CSV/TUSHARE/MYSQL
                    source_file      VARCHAR(200),                         -- 源文件路径（如适用）
                    import_batch     VARCHAR(50),                          -- 导入批次号
                    data_quality     VARCHAR(20)      DEFAULT 'NORMAL',    -- 数据质量：NORMAL/WARNING/ERROR
                    
                    -- 系统字段
                    created_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    
                    -- 主键约束
                    PRIMARY KEY (contract_code, frequency, trade_datetime)
                )
            ''',
            
            # ========== 期货基础信息表 ==========
            "futures_contract_info": '''
                CREATE TABLE IF NOT EXISTS futures_contract_info (
                    -- 基础信息
                    contract_code     VARCHAR(20)      PRIMARY KEY,        -- 合约代码
                    product_code      VARCHAR(10)      NOT NULL,           -- 品种代码
                    product_name      VARCHAR(50)      NOT NULL,           -- 品种名称
                    exchange_code     VARCHAR(10)      NOT NULL,           -- 交易所代码
                    exchange_name     VARCHAR(50)      NOT NULL,           -- 交易所名称
                    
                    -- 合约规格
                    contract_size     INTEGER          NOT NULL,           -- 合约大小
                    tick_size         DECIMAL(18,6)    NOT NULL,           -- 最小价格变动
                    price_limit_up    DECIMAL(18,4),                       -- 涨停价
                    price_limit_down  DECIMAL(18,4),                       -- 跌停价
                    
                    -- 时间信息
                    list_date         DATE,                                -- 上市日期
                    delist_date       DATE,                                -- 到期日期
                    delivery_month    VARCHAR(10),                         -- 交割月份
                    
                    -- 交易状态
                    trading_status    VARCHAR(20)      DEFAULT 'TRADING',  -- 交易状态
                    is_main_contract  BOOLEAN          DEFAULT FALSE,      -- 是否主力合约
                    
                    -- 数据源管理
                    data_source       VARCHAR(20)      NOT NULL DEFAULT 'UNKNOWN',
                    created_at        TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at        TIMESTAMP        DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            
            # ========== 股票数据表 ==========
            "stock_kline": '''
                CREATE TABLE IF NOT EXISTS stock_kline (
                    -- 业务主键
                    stock_code        VARCHAR(20)      NOT NULL,           -- 股票代码
                    frequency         VARCHAR(10)      NOT NULL,           -- 数据频率
                    trade_datetime    TIMESTAMP        NOT NULL,           -- 交易时间
                    
                    -- 价格数据
                    open_price        DECIMAL(18,4)    NOT NULL,
                    high_price        DECIMAL(18,4)    NOT NULL,
                    low_price         DECIMAL(18,4)    NOT NULL,
                    close_price       DECIMAL(18,4)    NOT NULL,
                    
                    -- 成交数据
                    volume           BIGINT           NOT NULL,
                    amount           DECIMAL(22,4)    NOT NULL,
                    
                    -- 复权数据
                    adj_factor       DECIMAL(18,6)    DEFAULT 1.0,         -- 复权因子
                    
                    -- 数据源管理
                    data_source      VARCHAR(20)      NOT NULL DEFAULT 'UNKNOWN',
                    created_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    
                    PRIMARY KEY (stock_code, frequency, trade_datetime)
                )
            ''',
            
            # ========== 股票基础信息表 ==========
            "stock_basic_info": '''
                CREATE TABLE IF NOT EXISTS stock_basic_info (
                    stock_code        VARCHAR(20)      PRIMARY KEY,
                    stock_name        VARCHAR(100)     NOT NULL,
                    exchange_code     VARCHAR(10)      NOT NULL,
                    exchange_name     VARCHAR(50)      NOT NULL,
                    industry          VARCHAR(100),
                    market_cap        DECIMAL(22,4),                       -- 总市值
                    pe_ratio          DECIMAL(10,4),                       -- 市盈率
                    pb_ratio          DECIMAL(10,4),                       -- 市净率
                    
                    list_date         DATE,
                    delist_date       DATE,
                    trading_status    VARCHAR(20)      DEFAULT 'TRADING',
                    
                    data_source       VARCHAR(20)      NOT NULL DEFAULT 'UNKNOWN',
                    created_at        TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at        TIMESTAMP        DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            
            # ========== 数据管理表 ==========
            "data_import_history": '''
                CREATE TABLE IF NOT EXISTS data_import_history (
                    import_id         VARCHAR(50)      PRIMARY KEY,
                    data_source       VARCHAR(20)      NOT NULL,
                    table_name        VARCHAR(100)     NOT NULL,
                    file_path         VARCHAR(500),
                    records_imported  INTEGER          DEFAULT 0,
                    records_failed    INTEGER          DEFAULT 0,
                    import_status     VARCHAR(20)      NOT NULL,           -- SUCCESS/FAILED/PARTIAL
                    start_time        TIMESTAMP        NOT NULL,
                    end_time          TIMESTAMP,
                    error_message     TEXT,
                    created_by        VARCHAR(50)      DEFAULT 'SYSTEM',
                    created_at        TIMESTAMP        DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            
            "data_quality_log": '''
                CREATE TABLE IF NOT EXISTS data_quality_log (
                    log_id            VARCHAR(50)      PRIMARY KEY,
                    table_name        VARCHAR(100)     NOT NULL,
                    check_type        VARCHAR(50)      NOT NULL,           -- DUPLICATE/MISSING/OUTLIER
                    severity          VARCHAR(20)      NOT NULL,           -- INFO/WARNING/ERROR
                    issue_count       INTEGER          DEFAULT 0,
                    description       TEXT,
                    check_time        TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    resolved          BOOLEAN          DEFAULT FALSE
                )
            '''
        }
        
        success_count = 0
        total_count = len(table_definitions)
        
        try:
            with self.connection_manager.get_cursor() as cursor:
                for table_name, create_sql in table_definitions.items():
                    try:
                        logger.info(f"📋 创建表: {table_name}")
                        cursor.execute(create_sql)
                        success_count += 1
                        logger.info(f"✅ 表 {table_name} 创建成功")
                        
                    except Exception as e:
                        logger.error(f"❌ 表 {table_name} 创建失败: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
        
        logger.info(f"📊 表创建完成: {success_count}/{total_count} 成功")
        return success_count == total_count
    
    def create_indexes(self) -> bool:
        """创建索引以优化查询性能"""
        logger.info("🚀 创建数据库索引...")
        
        index_definitions = [
            # 期货K线表索引
            "CREATE INDEX IF NOT EXISTS idx_futures_kline_product_time ON futures_main_contract_kline(product_code, trade_datetime)",
            "CREATE INDEX IF NOT EXISTS idx_futures_kline_source ON futures_main_contract_kline(data_source)",
            "CREATE INDEX IF NOT EXISTS idx_futures_kline_frequency ON futures_main_contract_kline(frequency)",
            
            # 期货基础信息索引
            "CREATE INDEX IF NOT EXISTS idx_futures_info_product ON futures_contract_info(product_code)",
            "CREATE INDEX IF NOT EXISTS idx_futures_info_exchange ON futures_contract_info(exchange_code)",
            "CREATE INDEX IF NOT EXISTS idx_futures_info_main ON futures_contract_info(is_main_contract)",
            
            # 股票K线表索引
            "CREATE INDEX IF NOT EXISTS idx_stock_kline_time ON stock_kline(trade_datetime)",
            "CREATE INDEX IF NOT EXISTS idx_stock_kline_source ON stock_kline(data_source)",
            
            # 数据管理表索引
            "CREATE INDEX IF NOT EXISTS idx_import_history_source ON data_import_history(source_info)",
            "CREATE INDEX IF NOT EXISTS idx_import_history_status ON data_import_history(import_status)",
            "CREATE INDEX IF NOT EXISTS idx_import_history_time ON data_import_history(start_time)",
            
            "CREATE INDEX IF NOT EXISTS idx_quality_log_table ON data_quality_log(table_name)",
            "CREATE INDEX IF NOT EXISTS idx_quality_log_severity ON data_quality_log(severity)",
        ]
        
        success_count = 0
        total_count = len(index_definitions)
        
        try:
            with self.connection_manager.get_cursor() as cursor:
                for index_sql in index_definitions:
                    try:
                        cursor.execute(index_sql)
                        success_count += 1
                        
                    except Exception as e:
                        logger.warning(f"⚠️ 索引创建警告: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"❌ 索引创建过程失败: {e}")
            return False
        
        logger.info(f"📈 索引创建完成: {success_count}/{total_count} 成功")
        return True
    
    def verify_database_structure(self) -> Dict[str, Any]:
        """验证数据库结构的完整性"""
        logger.info("🔍 验证数据库结构...")
        
        verification_result = {
            "valid": True,
            "tables_created": [],
            "tables_missing": [],
            "indexes_created": [],
            "total_records": 0,
            "issues": []
        }
        
        expected_tables = [
            "futures_main_contract_kline",
            "futures_contract_info", 
            "stock_kline",
            "stock_basic_info",
            "data_import_history",
            "data_quality_log"
        ]
        
        try:
            # 检查表是否存在
            existing_tables = self.connection_manager.get_all_tables()
            
            for table in expected_tables:
                if table in existing_tables:
                    verification_result["tables_created"].append(table)
                    # 统计记录数
                    count = self.connection_manager.get_table_count(table)
                    verification_result["total_records"] += count
                else:
                    verification_result["tables_missing"].append(table)
                    verification_result["valid"] = False
            
            # 检查表结构
            for table in verification_result["tables_created"]:
                try:
                    table_info = self.connection_manager.get_table_info(table)
                    if not table_info:
                        verification_result["issues"].append(f"表 {table} 结构信息获取失败")
                        verification_result["valid"] = False
                        
                except Exception as e:
                    verification_result["issues"].append(f"表 {table} 结构检查失败: {e}")
                    verification_result["valid"] = False
            
            logger.info(f"✅ 数据库结构验证完成")
            logger.info(f"📊 表统计: {len(verification_result['tables_created'])} 已创建, {len(verification_result['tables_missing'])} 缺失")
            logger.info(f"📈 总记录数: {verification_result['total_records']:,}")
            
            if verification_result["issues"]:
                logger.warning(f"⚠️ 发现 {len(verification_result['issues'])} 个问题")
                for issue in verification_result["issues"]:
                    logger.warning(f"  - {issue}")
                    
        except Exception as e:
            logger.error(f"❌ 数据库结构验证失败: {e}")
            verification_result["valid"] = False
            verification_result["issues"].append(f"验证过程异常: {e}")
        
        return verification_result
    
    def initialize_database(self) -> bool:
        """执行完整的数据库初始化流程"""
        logger.info("🚀 开始完整数据库初始化...")
        
        start_time = datetime.now()
        
        try:
            # 步骤1: 创建表结构
            if not self.create_unified_business_tables():
                logger.error("❌ 表结构创建失败")
                return False
            
            # 步骤2: 创建索引
            if not self.create_indexes():
                logger.warning("⚠️ 索引创建部分失败，但继续执行")
            
            # 步骤3: 验证结构
            verification = self.verify_database_structure()
            if not verification["valid"]:
                logger.error("❌ 数据库结构验证失败")
                return False
            
            # 完成
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info("🎉 数据库初始化完成！")
            logger.info(f"📊 统计信息:")
            logger.info(f"  - 环境: {self.environment}")
            logger.info(f"  - 表数量: {len(verification['tables_created'])}")
            logger.info(f"  - 总记录数: {verification['total_records']:,}")
            logger.info(f"  - 耗时: {duration:.2f}秒")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            return False
        
        finally:
            self.connection_manager.close_connection()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AQUA数据库完整初始化工具")
    parser.add_argument("--env", default="test", choices=["dev", "test", "prod"], 
                       help="目标环境 (默认: test)")
    parser.add_argument("--verify-only", action="store_true", 
                       help="仅验证数据库结构，不创建表")
    parser.add_argument("--force", action="store_true", 
                       help="强制重新创建所有表（危险操作）")
    
    args = parser.parse_args()
    
    try:
        initializer = DatabaseInitializer(args.env)
        
        if args.verify_only:
            logger.info("🔍 执行数据库结构验证...")
            result = initializer.verify_database_structure()
            if result["valid"]:
                logger.info("✅ 数据库结构验证通过")
                return 0 
            else:
                logger.error("❌ 数据库结构验证失败")
                return 1
        else:
            logger.info(f"🚀 执行数据库完整初始化 - 环境: {args.env}")
            if args.force:
                logger.warning("⚠️ 强制模式已启用")
            
            success = initializer.initialize_database()
            return 0 if success else 1
            
    except KeyboardInterrupt:
        logger.info("❌ 用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())