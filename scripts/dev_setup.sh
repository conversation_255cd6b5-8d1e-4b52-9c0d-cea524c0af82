#!/bin/bash

# AQUA开发环境快速设置 - Real Data Only版本

echo "🚀 AQUA开发环境快速设置（基于真实数据源）"
echo "================================================"

# 检查Python版本
echo "📍 检查Python版本..."
python_version=$(python --version 2>&1 | awk '{print $2}')
echo "Python版本: $python_version"

if ! python -c "import sys; exit(0 if sys.version_info >= (3, 11) else 1)"; then
    echo "❌ 需要Python 3.11+，当前版本不满足要求"
    exit 1
fi

# 检查并安装依赖
echo "📦 安装项目依赖..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    if [ $? -eq 0 ]; then
        echo "✅ 依赖安装成功"
    else
        echo "❌ 依赖安装失败"
        exit 1
    fi
else
    echo "❌ 未找到requirements.txt文件"
    exit 1
fi

# 检查TUSHARE Token
echo "🔑 检查TUSHARE Token配置..."
if [ -z "$TUSHARE_TOKEN" ]; then
    echo "❌ 未设置TUSHARE_TOKEN环境变量"
    echo ""
    echo "🔧 配置步骤："
    echo "1. 访问 https://tushare.pro/ 注册账户"
    echo "2. 在个人中心获取Token"
    echo "3. 设置环境变量："
    echo "   export TUSHARE_TOKEN='your_token_here'  # macOS/Linux"
    echo "   setx TUSHARE_TOKEN 'your_token_here'    # Windows"
    echo ""
    echo "请配置Token后重新运行此脚本"
    exit 1
else
    echo "✅ TUSHARE_TOKEN已设置"
fi

# 验证数据源连接
echo "🌐 验证数据源连接..."
python -m src.cli.main collect --check-capabilities > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据源连接验证成功"
else
    echo "❌ 数据源连接验证失败，请检查Token配置"
    echo "🔧 手动验证命令: python -m src.cli.main collect --check-capabilities"
    exit 1
fi

# 创建必要的目录
echo "📁 创建项目目录结构..."
mkdir -p data
mkdir -p logs
mkdir -p backups/real_data
mkdir -p cache/real_data

# 测试数据采集功能
echo "🧪 测试数据采集功能..."
python -m src.cli.main collect 000001.SZ --preview > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据采集功能测试成功"
else
    echo "❌ 数据采集功能测试失败"
    echo "🔧 手动测试命令: python -m src.cli.main collect 000001.SZ --preview"
    exit 1
fi

echo ""
echo "🎉 开发环境设置完成！"
echo "================================================"
echo "✅ Python 3.11+ 已确认"
echo "✅ 项目依赖已安装"
echo "✅ TUSHARE Token已配置"
echo "✅ 数据源连接已验证"
echo "✅ 数据采集功能正常"
echo ""
echo "💡 快速开始："
echo "# 预览数据采集"
echo "python -m src.cli.main collect 000001.SZ --preview"
echo ""
echo "# 实际采集数据"
echo "python -m src.cli.main collect 000001.SZ"
echo ""
echo "# 检查数据源状态"
echo "python -m src.cli.main collect --check-capabilities"
echo ""
echo "🔗 数据源: TUSHARE(Web API), CSV(本地文件), MySQL(数据库) - 100%真实数据"
echo "📊 设计理念: Multi-Source Real Data Only"
echo "📖 完整文档: docs/handbook/DataCollector/"