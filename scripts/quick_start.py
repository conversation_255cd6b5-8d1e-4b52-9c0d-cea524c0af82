#!/usr/bin/env python3
"""
AQUA快速启动脚本
跳过依赖编译，直接启动服务
"""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path

def main():
    print("🚀 AQUA快速启动")
    
    # 确保在项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    print(f"📁 项目目录: {project_root}")
    
    # 检查虚拟环境
    venv_python = project_root / ".venv" / "bin" / "python"
    if not venv_python.exists():
        print("❌ 虚拟环境不存在，请先运行: python3.11 -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt")
        sys.exit(1)
    
    print("✅ 虚拟环境检查通过")
    
    # 启动后端服务
    print("🐍 启动后端服务...")
    backend_cmd = [str(venv_python), "main.py"]
    backend_process = subprocess.Popen(backend_cmd, cwd=project_root)
    
    # 等待后端启动
    time.sleep(3)
    
    # 启动前端服务
    print("🎨 启动前端服务...")
    frontend_cmd = ["npm", "run", "dev", "--", "--port", "8080"]
    frontend_cwd = project_root / "frontend"
    frontend_process = subprocess.Popen(frontend_cmd, cwd=frontend_cwd)
    
    print("\n" + "="*60)
    print("🎉 AQUA服务启动完成！")
    print("="*60)
    print("📋 服务信息:")
    print("  • 后端API: http://localhost:8000")
    print("  • API文档: http://localhost:8000/docs")
    print("  • 前端界面: http://localhost:8080")
    print("\n⚡ 按 Ctrl+C 停止所有服务")
    print("="*60)
    
    def signal_handler(sig, frame):
        print("\n🛑 正在停止服务...")
        backend_process.terminate()
        frontend_process.terminate()
        try:
            backend_process.wait(timeout=5)
            frontend_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            backend_process.kill()
            frontend_process.kill()
        print("✅ 所有服务已停止")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # 等待用户中断
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        signal_handler(None, None)

if __name__ == "__main__":
    main()