#!/usr/bin/env python3
"""
健康检查工具模块
提供统一的系统健康检查和状态监控功能

特性：
- 系统资源检查
- 数据库连接检查
- 文件系统检查
- 配置完整性检查
"""

import os
import sys
import psutil
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """健康检查结果"""
    name: str
    status: HealthStatus
    message: str
    details: Optional[Dict[str, Any]] = None
    suggestion: Optional[str] = None

class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.checks: List[HealthCheck] = []
        
    def check_python_version(self, min_version: Tuple[int, int] = (3, 11)) -> HealthCheck:
        """检查Python版本"""
        current_version = sys.version_info[:2]
        
        if current_version >= min_version:
            status = HealthStatus.HEALTHY
            message = f"Python版本正常: {sys.version.split()[0]}"
        else:
            status = HealthStatus.CRITICAL
            message = f"Python版本过低: {sys.version.split()[0]}, 需要 >= {min_version[0]}.{min_version[1]}"
        
        return HealthCheck(
            name="Python版本",
            status=status,
            message=message,
            details={"current": current_version, "required": min_version}
        )
    
    def check_memory_usage(self, warning_threshold: float = 80.0, critical_threshold: float = 95.0) -> HealthCheck:
        """检查内存使用率"""
        memory = psutil.virtual_memory()
        usage_percent = memory.percent
        
        if usage_percent >= critical_threshold:
            status = HealthStatus.CRITICAL
            suggestion = "内存使用率过高，建议关闭不必要的程序"
        elif usage_percent >= warning_threshold:
            status = HealthStatus.WARNING
            suggestion = "内存使用率较高，建议监控"
        else:
            status = HealthStatus.HEALTHY
            suggestion = None
        
        return HealthCheck(
            name="内存使用率",
            status=status,
            message=f"内存使用率: {usage_percent:.1f}%",
            details={
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": usage_percent
            },
            suggestion=suggestion
        )
    
    def check_disk_space(self, path: str = ".", warning_threshold: float = 80.0, critical_threshold: float = 95.0) -> HealthCheck:
        """检查磁盘空间"""
        try:
            disk_usage = psutil.disk_usage(path)
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            
            if usage_percent >= critical_threshold:
                status = HealthStatus.CRITICAL
                suggestion = "磁盘空间不足，建议清理或扩容"
            elif usage_percent >= warning_threshold:
                status = HealthStatus.WARNING
                suggestion = "磁盘空间使用率较高，建议监控"
            else:
                status = HealthStatus.HEALTHY
                suggestion = None
            
            return HealthCheck(
                name="磁盘空间",
                status=status,
                message=f"磁盘使用率: {usage_percent:.1f}%",
                details={
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "percent": usage_percent,
                    "path": path
                },
                suggestion=suggestion
            )
        except Exception as e:
            return HealthCheck(
                name="磁盘空间",
                status=HealthStatus.UNKNOWN,
                message=f"无法检查磁盘空间: {e}"
            )
    
    def check_file_permissions(self, paths: List[str]) -> HealthCheck:
        """检查文件权限"""
        issues = []
        
        for path_str in paths:
            path = Path(path_str)
            
            if not path.exists():
                issues.append(f"路径不存在: {path}")
                continue
            
            if path.is_file():
                if not os.access(path, os.R_OK):
                    issues.append(f"文件不可读: {path}")
                if not os.access(path, os.W_OK):
                    issues.append(f"文件不可写: {path}")
            elif path.is_dir():
                if not os.access(path, os.R_OK):
                    issues.append(f"目录不可读: {path}")
                if not os.access(path, os.W_OK):
                    issues.append(f"目录不可写: {path}")
                if not os.access(path, os.X_OK):
                    issues.append(f"目录不可执行: {path}")
        
        if issues:
            status = HealthStatus.WARNING
            message = f"发现权限问题: {len(issues)}个"
            suggestion = "检查文件权限设置"
        else:
            status = HealthStatus.HEALTHY
            message = "文件权限正常"
            suggestion = None
        
        return HealthCheck(
            name="文件权限",
            status=status,
            message=message,
            details={"issues": issues, "checked_paths": paths},
            suggestion=suggestion
        )
    
    def check_environment_variables(self, required_vars: List[str]) -> HealthCheck:
        """检查环境变量"""
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            status = HealthStatus.WARNING
            message = f"缺少环境变量: {', '.join(missing_vars)}"
            suggestion = "设置缺少的环境变量"
        else:
            status = HealthStatus.HEALTHY
            message = "环境变量完整"
            suggestion = None
        
        return HealthCheck(
            name="环境变量",
            status=status,
            message=message,
            details={"missing": missing_vars, "required": required_vars},
            suggestion=suggestion
        )
    
    def check_network_connectivity(self, hosts: List[str] = None) -> HealthCheck:
        """检查网络连接"""
        if hosts is None:
            hosts = ["*******", "***************"]
        
        import socket
        
        reachable = []
        unreachable = []
        
        for host in hosts:
            try:
                socket.create_connection((host, 53), timeout=5)
                reachable.append(host)
            except Exception:
                unreachable.append(host)
        
        if not reachable:
            status = HealthStatus.CRITICAL
            message = "网络连接失败"
            suggestion = "检查网络连接"
        elif unreachable:
            status = HealthStatus.WARNING
            message = f"部分网络不可达: {', '.join(unreachable)}"
            suggestion = "检查网络配置"
        else:
            status = HealthStatus.HEALTHY
            message = "网络连接正常"
            suggestion = None
        
        return HealthCheck(
            name="网络连接",
            status=status,
            message=message,
            details={"reachable": reachable, "unreachable": unreachable},
            suggestion=suggestion
        )
    
    def run_all_checks(self, config: Optional[Dict[str, Any]] = None) -> List[HealthCheck]:
        """运行所有检查"""
        if config is None:
            config = {}
        
        checks = [
            self.check_python_version(),
            self.check_memory_usage(),
            self.check_disk_space(),
        ]
        
        # 可选检查
        if 'check_paths' in config:
            checks.append(self.check_file_permissions(config['check_paths']))
        
        if 'required_env_vars' in config:
            checks.append(self.check_environment_variables(config['required_env_vars']))
        
        if config.get('check_network', False):
            checks.append(self.check_network_connectivity())
        
        self.checks = checks
        return checks
    
    def get_summary(self) -> Dict[str, Any]:
        """获取健康检查总结"""
        if not self.checks:
            return {"status": "no_checks", "message": "未执行健康检查"}
        
        status_counts = {
            HealthStatus.HEALTHY: 0,
            HealthStatus.WARNING: 0,
            HealthStatus.CRITICAL: 0,
            HealthStatus.UNKNOWN: 0
        }
        
        for check in self.checks:
            status_counts[check.status] += 1
        
        # 确定整体状态
        if status_counts[HealthStatus.CRITICAL] > 0:
            overall_status = HealthStatus.CRITICAL
        elif status_counts[HealthStatus.WARNING] > 0:
            overall_status = HealthStatus.WARNING
        elif status_counts[HealthStatus.UNKNOWN] > 0:
            overall_status = HealthStatus.UNKNOWN
        else:
            overall_status = HealthStatus.HEALTHY
        
        return {
            "overall_status": overall_status,
            "total_checks": len(self.checks),
            "status_counts": {k.value: v for k, v in status_counts.items()},
            "checks": self.checks
        }
    
    def print_summary(self) -> None:
        """打印健康检查总结"""
        summary = self.get_summary()
        
        if summary["status"] == "no_checks":
            print("⚠️  未执行健康检查")
            return
        
        print("\n" + "=" * 50)
        print("🏥 系统健康检查报告")
        print("=" * 50)
        
        overall_status = summary["overall_status"]
        status_icons = {
            HealthStatus.HEALTHY: "✅",
            HealthStatus.WARNING: "⚠️",
            HealthStatus.CRITICAL: "❌",
            HealthStatus.UNKNOWN: "❓"
        }
        
        icon = status_icons[overall_status]
        print(f"{icon} 整体状态: {overall_status.value.upper()}")
        print(f"📊 检查项目: {summary['total_checks']}")
        
        counts = summary["status_counts"]
        print(f"✅ 正常: {counts['healthy']}")
        print(f"⚠️  警告: {counts['warning']}")
        print(f"❌ 严重: {counts['critical']}")
        print(f"❓ 未知: {counts['unknown']}")
        
        print("\n详细报告:")
        print("-" * 30)
        
        for check in self.checks:
            icon = status_icons[check.status]
            print(f"{icon} {check.name}: {check.message}")
            
            if check.suggestion:
                print(f"   💡 建议: {check.suggestion}")
            
            if check.details and len(str(check.details)) < 100:
                print(f"   📋 详情: {check.details}")

def check_system_health(config: Optional[Dict[str, Any]] = None) -> SystemHealthChecker:
    """
    快速系统健康检查
    
    Args:
        config: 检查配置
        
    Returns:
        SystemHealthChecker: 健康检查器
    """
    checker = SystemHealthChecker()
    checker.run_all_checks(config)
    return checker

def check_database_health(db_path: str) -> HealthCheck:
    """
    检查数据库健康状态
    
    Args:
        db_path: 数据库路径
        
    Returns:
        HealthCheck: 检查结果
    """
    db_path = Path(db_path)
    
    if not db_path.exists():
        return HealthCheck(
            name="数据库连接",
            status=HealthStatus.CRITICAL,
            message="数据库文件不存在",
            suggestion="运行数据库初始化脚本"
        )
    
    if not os.access(db_path, os.R_OK | os.W_OK):
        return HealthCheck(
            name="数据库连接",
            status=HealthStatus.CRITICAL,
            message="数据库文件权限不足",
            suggestion="检查文件权限设置"
        )
    
    try:
        # 尝试连接数据库
        import duckdb
        
        with duckdb.connect(str(db_path)) as conn:
            # 执行简单查询测试
            result = conn.execute("SELECT 1").fetchone()
            if result and result[0] == 1:
                file_size = db_path.stat().st_size
                return HealthCheck(
                    name="数据库连接",
                    status=HealthStatus.HEALTHY,
                    message="数据库连接正常",
                    details={
                        "path": str(db_path),
                        "size": file_size
                    }
                )
    except Exception as e:
        return HealthCheck(
            name="数据库连接",
            status=HealthStatus.CRITICAL,
            message=f"数据库连接失败: {e}",
            suggestion="检查数据库文件完整性"
        )

def check_config_health(config_path: str) -> HealthCheck:
    """
    检查配置文件健康状态
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        HealthCheck: 检查结果
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        return HealthCheck(
            name="配置文件",
            status=HealthStatus.CRITICAL,
            message="配置文件不存在",
            suggestion="检查配置文件路径"
        )
    
    try:
        import toml
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        # 检查必要的配置项
        required_sections = ['test', 'dev']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            return HealthCheck(
                name="配置文件",
                status=HealthStatus.WARNING,
                message=f"缺少配置节: {', '.join(missing_sections)}",
                suggestion="添加缺少的配置节"
            )
        
        return HealthCheck(
            name="配置文件",
            status=HealthStatus.HEALTHY,
            message="配置文件正常",
            details={"sections": list(config.keys())}
        )
    
    except Exception as e:
        return HealthCheck(
            name="配置文件",
            status=HealthStatus.CRITICAL,
            message=f"配置文件格式错误: {e}",
            suggestion="检查配置文件语法"
        )

def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息
    
    Returns:
        Dict[str, Any]: 系统信息
    """
    return {
        "python_version": sys.version,
        "platform": sys.platform,
        "cpu_count": psutil.cpu_count(),
        "memory": {
            "total": psutil.virtual_memory().total,
            "available": psutil.virtual_memory().available
        },
        "disk": {
            "total": psutil.disk_usage('.').total,
            "free": psutil.disk_usage('.').free
        },
        "timestamp": time.time()
    }