#!/usr/bin/env python3
"""
命令行工具模块
提供统一的命令行参数解析和处理功能

特性：
- 统一的参数解析器创建
- 环境参数处理
- 输出格式标准化
- 错误处理统一化
"""

import argparse
import sys
from typing import Dict, Any, Optional, List
from pathlib import Path

def create_common_parser(description: str, add_env: bool = True) -> argparse.ArgumentParser:
    """
    创建通用命令行参数解析器
    
    Args:
        description: 脚本描述
        add_env: 是否添加环境参数
        
    Returns:
        argparse.ArgumentParser: 配置好的参数解析器
    """
    parser = argparse.ArgumentParser(
        description=description,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    if add_env:
        parser.add_argument(
            '--env', '-e',
            choices=['dev', 'test', 'prod'],
            default='dev',
            help='运行环境 (默认: dev)'
        )
    
    parser.add_argument(
        '--config', '-c',
        default='config/settings.toml',
        help='配置文件路径 (默认: config/settings.toml)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='静默模式'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='模拟运行，不执行实际操作'
    )
    
    return parser

def add_data_import_args(parser: argparse.ArgumentParser) -> None:
    """
    添加数据导入相关参数
    
    Args:
        parser: 参数解析器
    """
    parser.add_argument(
        '--mode', '-m',
        choices=['full-reset', 'csv-only', 'mysql-only', 'check-only'],
        default='check-only',
        help='操作模式 (默认: check-only)'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='强制执行，跳过确认'
    )
    
    parser.add_argument(
        '--backup',
        action='store_true',
        help='执行前创建备份'
    )

def add_database_args(parser: argparse.ArgumentParser) -> None:
    """
    添加数据库相关参数
    
    Args:
        parser: 参数解析器
    """
    parser.add_argument(
        '--db-path',
        help='数据库文件路径'
    )
    
    parser.add_argument(
        '--reset',
        action='store_true',
        help='重置数据库'
    )
    
    parser.add_argument(
        '--init-only',
        action='store_true',
        help='仅初始化数据库结构'
    )

def validate_args(args: argparse.Namespace) -> None:
    """
    验证命令行参数
    
    Args:
        args: 解析后的参数
    """
    # 验证配置文件存在
    if hasattr(args, 'config') and args.config:
        config_path = Path(args.config)
        if not config_path.exists():
            print(f"❌ 错误: 配置文件不存在: {config_path}")
            sys.exit(1)
    
    # 验证数据库路径
    if hasattr(args, 'db_path') and args.db_path:
        db_path = Path(args.db_path)
        if not db_path.parent.exists():
            print(f"❌ 错误: 数据库目录不存在: {db_path.parent}")
            sys.exit(1)
    
    # 验证互斥参数
    if hasattr(args, 'verbose') and hasattr(args, 'quiet'):
        if args.verbose and args.quiet:
            print("❌ 错误: --verbose 和 --quiet 不能同时使用")
            sys.exit(1)

def print_header(title: str, args: argparse.Namespace) -> None:
    """
    打印标准化脚本头部
    
    Args:
        title: 脚本标题
        args: 命令行参数
    """
    if hasattr(args, 'quiet') and args.quiet:
        return
    
    print("=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)
    
    if hasattr(args, 'env'):
        print(f"📍 运行环境: {args.env}")
    
    if hasattr(args, 'config'):
        print(f"📄 配置文件: {args.config}")
    
    if hasattr(args, 'dry_run') and args.dry_run:
        print("⚠️  模拟运行模式")
    
    print()

def print_section(title: str, args: argparse.Namespace) -> None:
    """
    打印章节标题
    
    Args:
        title: 章节标题
        args: 命令行参数
    """
    if hasattr(args, 'quiet') and args.quiet:
        return
    
    print(f"\n📋 {title}")
    print("-" * 40)

def print_status(message: str, status: str = "info", args: Optional[argparse.Namespace] = None) -> None:
    """
    打印状态信息
    
    Args:
        message: 消息内容
        status: 状态类型 (info, success, warning, error)
        args: 命令行参数
    """
    if args and hasattr(args, 'quiet') and args.quiet:
        return
    
    icons = {
        'info': 'ℹ️',
        'success': '✅',
        'warning': '⚠️',
        'error': '❌'
    }
    
    icon = icons.get(status, 'ℹ️')
    print(f"{icon} {message}")

def print_footer(success: bool = True, args: Optional[argparse.Namespace] = None) -> None:
    """
    打印标准化脚本尾部
    
    Args:
        success: 是否成功
        args: 命令行参数
    """
    if args and hasattr(args, 'quiet') and args.quiet:
        return
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 操作完成!")
    else:
        print("💥 操作失败!")
    print("=" * 60)

def confirm_action(message: str, force: bool = False) -> bool:
    """
    确认操作
    
    Args:
        message: 确认消息
        force: 强制执行
        
    Returns:
        bool: 是否确认
    """
    if force:
        print(f"⚠️  强制执行: {message}")
        return True
    
    response = input(f"❓ {message} (y/N): ").strip().lower()
    return response in ['y', 'yes']

def get_user_input(prompt: str, default: str = "", choices: Optional[List[str]] = None) -> str:
    """
    获取用户输入
    
    Args:
        prompt: 提示信息
        default: 默认值
        choices: 可选值列表
        
    Returns:
        str: 用户输入
    """
    if choices:
        choices_str = "/".join(choices)
        prompt_with_choices = f"{prompt} ({choices_str})"
    else:
        prompt_with_choices = prompt
    
    if default:
        prompt_with_choices += f" [默认: {default}]"
    
    while True:
        response = input(f"❓ {prompt_with_choices}: ").strip()
        
        if not response and default:
            return default
        
        if not response:
            print("⚠️  请输入有效值")
            continue
        
        if choices and response not in choices:
            print(f"⚠️  请从以下选项中选择: {', '.join(choices)}")
            continue
        
        return response

def format_duration(seconds: float) -> str:
    """
    格式化持续时间
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        return f"{seconds/60:.1f}分钟"
    else:
        return f"{seconds/3600:.1f}小时"

def format_size(bytes_size: int) -> str:
    """
    格式化文件大小
    
    Args:
        bytes_size: 字节数
        
    Returns:
        str: 格式化的大小字符串
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_size < 1024.0:
            return f"{bytes_size:.1f}{unit}"
        bytes_size /= 1024.0
    return f"{bytes_size:.1f}PB"

def safe_exit(code: int = 0, message: str = "") -> None:
    """
    安全退出程序
    
    Args:
        code: 退出码
        message: 退出消息
    """
    if message:
        if code == 0:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    sys.exit(code)