"""
AQUA项目通用工具模块

此模块提供项目中脚本的通用功能，避免代码重复，遵循AQUA宪法第2.4条代码复用强制要求。

模块结构：
- cli_utils.py: 命令行工具和参数解析
- progress_utils.py: 进度显示和用户反馈
- health_utils.py: 健康检查和状态监控
- file_utils.py: 文件操作和路径处理

使用示例：
```python
from scripts.common.cli_utils import create_common_parser
from scripts.common.progress_utils import create_progress_bar
from scripts.common.health_utils import check_system_health
```
"""

__version__ = "1.0.0"
__author__ = "AQUA Project Team"