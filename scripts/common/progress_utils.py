#!/usr/bin/env python3
"""
进度显示工具模块
提供统一的进度显示和用户反馈功能

特性：
- 进度条显示
- 状态更新
- 时间估算
- 统计信息
"""

import time
import threading
from typing import Optional, Callable, Any, Dict
from datetime import datetime, timedelta

class ProgressBar:
    """进度条类"""
    
    def __init__(self, total: int, description: str = "", width: int = 50, 
                 show_eta: bool = True, show_speed: bool = True):
        self.total = total
        self.description = description
        self.width = width
        self.show_eta = show_eta
        self.show_speed = show_speed
        self.current = 0
        self.start_time = time.time()
        self.last_update = self.start_time
        self.unit = "项"
        self.precision = 1
        
    def update(self, count: int = 1, description: str = None) -> None:
        """更新进度"""
        self.current += count
        current_time = time.time()
        
        # 限制更新频率
        if current_time - self.last_update < 0.1 and self.current < self.total:
            return
        
        self.last_update = current_time
        
        if description:
            self.description = description
        
        self._display()
    
    def set_current(self, current: int, description: str = None) -> None:
        """设置当前进度"""
        self.current = current
        
        if description:
            self.description = description
        
        self._display()
    
    def _display(self) -> None:
        """显示进度条"""
        if self.total == 0:
            return
        
        progress = min(self.current / self.total, 1.0)
        filled_width = int(progress * self.width)
        
        # 创建进度条
        bar = "█" * filled_width + "░" * (self.width - filled_width)
        percent = progress * 100
        
        # 计算统计信息
        elapsed_time = time.time() - self.start_time
        
        info_parts = []
        
        # 百分比
        info_parts.append(f"{percent:.1f}%")
        
        # 数量
        info_parts.append(f"{self.current}/{self.total}")
        
        # 速度
        if self.show_speed and elapsed_time > 0:
            speed = self.current / elapsed_time
            info_parts.append(f"{speed:.1f}{self.unit}/s")
        
        # 预计剩余时间
        if self.show_eta and self.current > 0 and elapsed_time > 0:
            speed = self.current / elapsed_time
            if speed > 0:
                remaining = (self.total - self.current) / speed
                eta = str(timedelta(seconds=int(remaining)))
                info_parts.append(f"ETA: {eta}")
        
        info = " | ".join(info_parts)
        
        # 输出进度条
        print(f"\r{self.description}: {bar} {info}", end="", flush=True)
        
        # 完成时换行
        if self.current >= self.total:
            print()
    
    def close(self) -> None:
        """关闭进度条"""
        self.current = self.total
        self._display()

class SpinnerProgress:
    """旋转器进度显示"""
    
    def __init__(self, description: str = "处理中"):
        self.description = description
        self.spinner_chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
        self.running = False
        self.thread = None
        self.current_char = 0
        
    def start(self) -> None:
        """开始显示旋转器"""
        self.running = True
        self.thread = threading.Thread(target=self._spin)
        self.thread.daemon = True
        self.thread.start()
    
    def stop(self, final_message: str = "") -> None:
        """停止显示旋转器"""
        self.running = False
        if self.thread:
            self.thread.join()
        
        # 清除旋转器
        print(f"\r{' ' * (len(self.description) + 10)}", end="")
        
        if final_message:
            print(f"\r{final_message}")
        else:
            print()
    
    def _spin(self) -> None:
        """旋转器线程"""
        while self.running:
            char = self.spinner_chars[self.current_char]
            print(f"\r{char} {self.description}", end="", flush=True)
            self.current_char = (self.current_char + 1) % len(self.spinner_chars)
            time.sleep(0.1)

class StatusTracker:
    """状态跟踪器"""
    
    def __init__(self):
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'warnings': 0
        }
        self.start_time = time.time()
        self.messages = []
        
    def increment(self, status: str, message: str = "") -> None:
        """增加计数"""
        if status in self.stats:
            self.stats[status] += 1
        
        if message:
            self.messages.append({
                'status': status,
                'message': message,
                'timestamp': datetime.now()
            })
    
    def get_summary(self) -> Dict[str, Any]:
        """获取总结"""
        elapsed = time.time() - self.start_time
        
        return {
            'stats': self.stats.copy(),
            'elapsed_time': elapsed,
            'success_rate': self.stats['success'] / max(self.stats['total'], 1) * 100,
            'messages': self.messages.copy()
        }
    
    def print_summary(self) -> None:
        """打印总结"""
        summary = self.get_summary()
        stats = summary['stats']
        
        print("\n" + "=" * 50)
        print("📊 执行总结")
        print("=" * 50)
        
        print(f"⏱️  总用时: {timedelta(seconds=int(summary['elapsed_time']))}")
        print(f"📈 成功率: {summary['success_rate']:.1f}%")
        print()
        
        if stats['total'] > 0:
            print(f"📋 总数: {stats['total']}")
            print(f"✅ 成功: {stats['success']}")
            print(f"❌ 失败: {stats['failed']}")
            print(f"⏭️  跳过: {stats['skipped']}")
            print(f"⚠️  警告: {stats['warnings']}")
        
        # 显示最近的错误消息
        error_messages = [msg for msg in self.messages if msg['status'] == 'failed']
        if error_messages:
            print("\n🔍 最近的错误:")
            for msg in error_messages[-3:]:  # 只显示最后3个错误
                print(f"  • {msg['message']}")

def create_progress_bar(total: int, description: str = "", **kwargs) -> ProgressBar:
    """
    创建进度条
    
    Args:
        total: 总数
        description: 描述
        **kwargs: 其他参数
        
    Returns:
        ProgressBar: 进度条对象
    """
    return ProgressBar(total, description, **kwargs)

def create_spinner(description: str = "处理中") -> SpinnerProgress:
    """
    创建旋转器
    
    Args:
        description: 描述
        
    Returns:
        SpinnerProgress: 旋转器对象
    """
    return SpinnerProgress(description)

def create_status_tracker() -> StatusTracker:
    """
    创建状态跟踪器
    
    Returns:
        StatusTracker: 状态跟踪器对象
    """
    return StatusTracker()

def with_progress(func: Callable, items: list, description: str = "", **progress_kwargs) -> Any:
    """
    装饰器：为函数添加进度条
    
    Args:
        func: 要执行的函数
        items: 要处理的项目列表
        description: 描述
        **progress_kwargs: 进度条参数
        
    Returns:
        Any: 函数执行结果
    """
    progress = create_progress_bar(len(items), description, **progress_kwargs)
    results = []
    
    try:
        for item in items:
            result = func(item)
            results.append(result)
            progress.update(1)
    finally:
        progress.close()
    
    return results

def with_spinner(func: Callable, description: str = "处理中") -> Any:
    """
    装饰器：为函数添加旋转器
    
    Args:
        func: 要执行的函数
        description: 描述
        
    Returns:
        Any: 函数执行结果
    """
    spinner = create_spinner(description)
    
    try:
        spinner.start()
        return func()
    finally:
        spinner.stop()

class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 100, progress_desc: str = "处理中"):
        self.batch_size = batch_size
        self.progress_desc = progress_desc
        
    def process(self, items: list, func: Callable, **kwargs) -> list:
        """
        批处理项目
        
        Args:
            items: 要处理的项目列表
            func: 处理函数
            **kwargs: 额外参数
            
        Returns:
            list: 处理结果
        """
        results = []
        total_batches = (len(items) + self.batch_size - 1) // self.batch_size
        
        progress = create_progress_bar(total_batches, self.progress_desc)
        
        try:
            for i in range(0, len(items), self.batch_size):
                batch = items[i:i + self.batch_size]
                batch_results = func(batch, **kwargs)
                results.extend(batch_results)
                progress.update(1)
        finally:
            progress.close()
        
        return results

def format_progress_stats(stats: Dict[str, int]) -> str:
    """
    格式化进度统计信息
    
    Args:
        stats: 统计信息
        
    Returns:
        str: 格式化的统计信息
    """
    total = stats.get('total', 0)
    success = stats.get('success', 0)
    failed = stats.get('failed', 0)
    
    if total == 0:
        return "无数据"
    
    success_rate = (success / total) * 100
    
    return f"总数: {total}, 成功: {success}, 失败: {failed}, 成功率: {success_rate:.1f}%"