#!/usr/bin/env python3
"""
AQUA测试运行器

基于AQUA宪法TDD要求的测试执行脚本
"""

import sys
import subprocess
from pathlib import Path
import argparse


def run_command(cmd, description=""):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"执行: {description or cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        print(f"标准输出: {e.stdout}")
        return False


def main():
    parser = argparse.ArgumentParser(description="AQUA TDD测试运行器")
    parser.add_argument("--unit", action="store_true", help="只运行单元测试")
    parser.add_argument("--integration", action="store_true", help="只运行集成测试")
    parser.add_argument("--api", action="store_true", help="只运行API测试")
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--no-cov", action="store_true", help="不计算覆盖率")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--file", "-f", help="运行特定测试文件")
    
    args = parser.parse_args()
    
    # 确保reports目录存在
    Path("reports").mkdir(exist_ok=True)
    Path("htmlcov").mkdir(exist_ok=True)
    
    print("🧪 AQUA TDD测试运行器")
    print("基于AQUA宪法TDD工作流")
    
    # 构建pytest命令
    cmd_parts = ["python", "-m", "pytest"]
    
    if args.verbose:
        cmd_parts.append("-v")
    
    # 添加标记筛选
    if args.unit:
        cmd_parts.extend(["-m", "unit"])
    elif args.integration:
        cmd_parts.extend(["-m", "integration"])
    elif args.api:
        cmd_parts.extend(["-m", "api"])
    
    # 覆盖率设置
    if not args.no_cov:
        cmd_parts.extend([
            "--cov=src",
            "--cov=main",
            "--cov-branch",
            "--cov-report=term-missing"
        ])
        
        if args.coverage:
            cmd_parts.extend([
                "--cov-report=html:htmlcov",
                "--cov-report=xml:reports/coverage.xml"
            ])
    
    # 特定文件
    if args.file:
        cmd_parts.append(args.file)
    
    # 添加报告生成
    cmd_parts.extend([
        "--junit-xml=reports/junit.xml",
        "--html=reports/report.html",
        "--self-contained-html"
    ])
    
    # 执行测试
    cmd = " ".join(cmd_parts)
    success = run_command(cmd, "运行TDD测试套件")
    
    if success:
        print("\n✅ 测试执行完成")
        
        if args.coverage or not args.no_cov:
            print("\n📊 覆盖率报告生成完成")
            print("- HTML报告: htmlcov/index.html")
            print("- JUnit报告: reports/junit.xml")
            print("- HTML测试报告: reports/report.html")
            
        # 检查覆盖率是否达标
        if not args.no_cov:
            print("\n🎯 检查覆盖率达标情况...")
            coverage_cmd = "python -m coverage report --fail-under=80"
            coverage_success = run_command(coverage_cmd, "覆盖率达标检查")
            
            if coverage_success:
                print("✅ 覆盖率达标 (≥80%)")
            else:
                print("❌ 覆盖率未达标 (<80%)")
                return 1
        
        return 0
    else:
        print("\n❌ 测试执行失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())