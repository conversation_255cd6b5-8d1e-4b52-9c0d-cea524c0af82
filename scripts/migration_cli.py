#!/usr/bin/env python3
"""
数据迁移命令行工具

提供命令行接口执行数据表迁移操作
"""

import sys
import argparse
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data_import.table_migration import TableMigrationManager
from src.utils.logger_setup import setup_logger


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AQUA数据表迁移工具')
    
    parser.add_argument(
        '--environment', '-e',
        choices=['dev', 'test', 'prod'],
        default='dev',
        help='运行环境 (默认: dev)'
    )
    
    parser.add_argument(
        '--dry-run', '-d',
        action='store_true',
        help='试运行模式，不实际执行迁移'
    )
    
    parser.add_argument(
        '--scan-only', '-s',
        action='store_true', 
        help='仅扫描分散表，不执行迁移'
    )
    
    parser.add_argument(
        '--analyze-table', '-a',
        type=str,
        help='分析指定表的结构'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='输出报告到指定文件'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logger(
        name='migration_cli',
        level='DEBUG' if args.verbose else 'INFO'
    )
    
    try:
        # 创建迁移管理器
        migration_manager = TableMigrationManager(environment=args.environment)
        
        print(f"🏛️ AQUA数据表迁移工具")
        print(f"📊 运行环境: {args.environment}")
        print("=" * 50)
        
        if args.scan_only:
            # 仅扫描分散表
            print("🔍 扫描分散表...")
            scattered_tables = migration_manager.scan_scattered_futures_tables()
            
            if scattered_tables:
                print(f"\n📋 发现 {len(scattered_tables)} 个分散表需要迁移:")
                for i, table in enumerate(scattered_tables, 1):
                    print(f"  {i}. {table}")
            else:
                print("\n✅ 未发现需要迁移的分散表")
            
            return
        
        if args.analyze_table:
            # 分析指定表
            print(f"🔍 分析表结构: {args.analyze_table}")
            analysis = migration_manager.analyze_table_structure(args.analyze_table)
            
            print(f"\n📊 表结构分析结果:")
            print(f"  表名: {analysis['table_name']}")
            print(f"  记录数: {analysis['record_count']:,}")
            print(f"  字段数: {len(analysis['columns'])}")
            print(f"  目标业务表: {analysis['target_business_table']}")
            print(f"  迁移可行性: {'✅ 是' if analysis['migration_feasible'] else '❌ 否'}")
            
            if analysis['issues']:
                print(f"  ⚠️ 问题列表:")
                for issue in analysis['issues']:
                    print(f"    - {issue}")
            
            if args.verbose and analysis['columns']:
                print(f"  📋 字段列表:")
                for col in analysis['columns']:
                    print(f"    - {col}")
            
            return
        
        # 执行完整迁移
        if args.dry_run:
            print("🧪 试运行模式 - 分析迁移可行性，不实际执行...")
        else:
            print("⚡ 实际执行模式 - 将执行数据迁移...")
            confirm = input("\n❓ 确认执行迁移吗？这将修改数据库结构。(y/N): ")
            if confirm.lower() != 'y':
                print("❌ 迁移已取消")
                return
        
        print("\n🚀 开始执行迁移...")
        report = migration_manager.execute_full_migration(dry_run=args.dry_run)
        
        # 显示迁移报告
        print(f"\n📊 迁移报告:")
        print(f"  执行模式: {'🧪 试运行' if report['dry_run'] else '⚡ 实际执行'}")
        print(f"  总体状态: {'✅ 成功' if report['success'] else '❌ 失败'}")
        print(f"  总表数: {report['total_tables']}")
        print(f"  成功迁移: {report['migrated_tables']}")
        print(f"  失败表数: {report['error_tables']}")
        print(f"  总记录数: {report['total_records']:,}")
        print(f"  执行耗时: {report['duration_seconds']:.2f}秒")
        
        if report['errors']:
            print(f"\n❌ 错误列表:")
            for error in report['errors']:
                print(f"  - {error}")
        
        if args.verbose and report['results']:
            print(f"\n📋 详细结果:")
            for i, result in enumerate(report['results'], 1):
                if args.dry_run:
                    print(f"  {i}. {result['source_table']} -> {result['target_table']} ({result['records_count']:,} 条记录)")
                else:
                    status = "✅" if result.get('success', False) else "❌"
                    print(f"  {i}. {status} {result.get('source_table', 'N/A')} -> {result.get('target_table', 'N/A')} ({result.get('records_migrated', 0):,} 条记录)")
        
        # 输出报告到文件
        if args.output:
            output_path = Path(args.output)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            print(f"\n💾 报告已保存到: {output_path}")
        
        # 显示后续建议
        if not args.dry_run and report['success']:
            print(f"\n🎉 迁移完成！建议:")
            print(f"  1. 验证目标表数据完整性")
            print(f"  2. 更新应用程序配置，使用新的统一表名")
            print(f"  3. 备份表可在确认无误后清理")
        elif args.dry_run and report['success']:
            print(f"\n✅ 试运行成功！可以执行实际迁移:")
            print(f"  python scripts/migration_cli.py --environment {args.environment}")
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"迁移过程异常: {str(e)}")
        print(f"\n💥 迁移失败: {str(e)}")
        sys.exit(1)
    finally:
        if 'migration_manager' in locals():
            migration_manager.connection_manager.close_connection()


if __name__ == '__main__':
    main()