#!/bin/bash
# 前端服务健康监控脚本
# 作者: AI (CURSOR/GEMINI)
# 创建时间: 2025-07-02
# 版本: 1.0.0
# 变更记录:
#   - 2025-07-02: 创建初始版本

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# 使用相对路径而非硬编码，便于跨平台兼容
SETTINGS_PATH="$PROJECT_ROOT/config/settings.toml"
LOG_PATH="$PROJECT_ROOT/logs/monitor.log"

# 确保日志目录存在
mkdir -p "$(dirname "$LOG_PATH")"

# 读取端口和路径（仅支持简单grep，复杂可用python辅助）
FRONTEND_PORT=$(grep -A2 '\[frontend\]' "$SETTINGS_PATH" | grep port | awk -F'=' '{print $2}' | tr -d ' ')
FRONTEND_HOST=$(grep -A2 '\[frontend\]' "$SETTINGS_PATH" | grep host | awk -F'=' '{print $2}' | tr -d ' ')
FRONTEND_HOST=${FRONTEND_HOST:-127.0.0.1}
FRONTEND_PORT=${FRONTEND_PORT:-5173}

INTERVAL=30

log_info() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"INFO\", \"module\": \"monitor_frontend\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}
log_error() {
  local msg="$1"
  echo "{\"timestamp\": \"$(date '+%Y-%m-%dT%H:%M:%S+08:00')\", \"level\": \"ERROR\", \"module\": \"monitor_frontend\", \"message\": \"$msg\"}" >> "$LOG_PATH"
}

while true; do
  code=$(curl -s -o /dev/null -w "%{http_code}" "http://$FRONTEND_HOST:$FRONTEND_PORT/")
  if [ "$code" = "200" ]; then
    log_info "前端服务健康检查通过: http://$FRONTEND_HOST:$FRONTEND_PORT/"
  else
    log_error "前端服务异常: http://$FRONTEND_HOST:$FRONTEND_PORT/ 状态码: $code"
  fi
  sleep $INTERVAL
done 