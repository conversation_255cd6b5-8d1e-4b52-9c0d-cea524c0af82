#!/usr/bin/env python3
"""
Windows占位符目录修复脚本

专门用于清理Windows下由于占位符解析错误而创建的错误目录
例如：{logs_root}, {datacenter_dir}, {cache_root} 等

版本: 1.0.0
创建时间: 2025-08-01
"""

import os
import sys
import shutil
from pathlib import Path
from typing import List, Dict

def get_project_root() -> Path:
    """获取项目根目录"""
    current_file = Path(__file__).resolve()
    # 从scripts目录向上找到项目根目录
    return current_file.parent.parent

def detect_placeholder_dirs(root_path: Path) -> List[Dict[str, str]]:
    """
    检测错误的占位符目录
    
    Args:
        root_path: 项目根目录
        
    Returns:
        List[Dict]: 发现的占位符目录信息
    """
    placeholder_patterns = [
        "{logs_root}",
        "{datacenter_dir}", 
        "{cache_root}",
        "{backup_root}",
        "{temp_dir}",
        "{csv_root}",
        "{fromc2c_data}",
        "{platform_data_root}",
        "{platform_duckdb_dir}",
        "{platform_backup_dir}",
        "{platform_cache_dir}",
    ]
    
    detected_dirs = []
    
    for pattern in placeholder_patterns:
        error_dir = root_path / pattern
        if error_dir.exists():
            # 获取目录信息
            if error_dir.is_dir():
                try:
                    files_count = len(list(error_dir.rglob("*")))
                    size = sum(f.stat().st_size for f in error_dir.rglob("*") if f.is_file())
                except (PermissionError, OSError):
                    files_count = "unknown"
                    size = "unknown"
                
                detected_dirs.append({
                    "path": str(error_dir),
                    "pattern": pattern,
                    "type": "directory",
                    "files_count": files_count,
                    "size_bytes": size,
                    "is_empty": files_count == 0 if isinstance(files_count, int) else False
                })
            else:
                detected_dirs.append({
                    "path": str(error_dir),
                    "pattern": pattern,
                    "type": "file",
                    "files_count": 0,
                    "size_bytes": error_dir.stat().st_size,
                    "is_empty": True
                })
    
    return detected_dirs

def clean_placeholder_dirs(detected_dirs: List[Dict[str, str]], force: bool = False) -> Dict[str, int]:
    """
    清理占位符目录
    
    Args:
        detected_dirs: 检测到的目录列表
        force: 是否强制删除非空目录
        
    Returns:
        Dict: 清理结果统计
    """
    results = {
        "cleaned": 0,
        "skipped": 0,
        "errors": 0,
        "details": []
    }
    
    for dir_info in detected_dirs:
        path = Path(dir_info["path"])
        
        try:
            if dir_info["type"] == "file":
                # 删除文件
                path.unlink()
                results["cleaned"] += 1
                results["details"].append(f"✅ 已删除占位符文件: {path}")
                
            elif dir_info["type"] == "directory":
                if dir_info["is_empty"]:
                    # 删除空目录
                    path.rmdir()
                    results["cleaned"] += 1 
                    results["details"].append(f"✅ 已删除空的占位符目录: {path}")
                    
                elif force:
                    # 强制删除非空目录
                    shutil.rmtree(path)
                    results["cleaned"] += 1
                    results["details"].append(f"✅ 已强制删除占位符目录: {path} (包含 {dir_info['files_count']} 个文件)")
                    
                else:
                    # 跳过非空目录
                    results["skipped"] += 1
                    results["details"].append(f"⚠️ 跳过非空占位符目录: {path} (包含 {dir_info['files_count']} 个文件)")
                    
        except (PermissionError, OSError) as e:
            results["errors"] += 1
            results["details"].append(f"❌ 清理失败 {path}: {e}")
    
    return results

def backup_placeholder_dirs(detected_dirs: List[Dict[str, str]], backup_dir: Path) -> Dict[str, int]:
    """
    备份占位符目录到指定位置
    
    Args:
        detected_dirs: 检测到的目录列表
        backup_dir: 备份目录
        
    Returns:
        Dict: 备份结果统计
    """
    results = {
        "backed_up": 0,
        "errors": 0,
        "details": []
    }
    
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    for dir_info in detected_dirs:
        if dir_info["is_empty"]:
            continue  # 跳过空目录
            
        source_path = Path(dir_info["path"])
        backup_name = dir_info["pattern"].replace("{", "").replace("}", "") + "_backup"
        backup_path = backup_dir / backup_name
        
        try:
            if dir_info["type"] == "directory":
                shutil.copytree(source_path, backup_path, dirs_exist_ok=True)
            else:
                shutil.copy2(source_path, backup_path)
                
            results["backed_up"] += 1
            results["details"].append(f"✅ 已备份: {source_path} -> {backup_path}")
            
        except (PermissionError, OSError) as e:
            results["errors"] += 1
            results["details"].append(f"❌ 备份失败 {source_path}: {e}")
    
    return results

def main():
    """主函数"""
    print("🔧 AQUA Windows占位符目录修复工具")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = get_project_root()
    print(f"📁 项目根目录: {project_root}")
    
    # 检测平台
    if os.name != 'nt':
        print("⚠️ 此脚本专为Windows平台设计")
        print("💡 当前平台:", os.name, "- 建议在Windows下运行")
    
    # 检测占位符目录
    print("\n🔍 正在检测占位符目录...")
    detected_dirs = detect_placeholder_dirs(project_root)
    
    if not detected_dirs:
        print("✅ 未发现错误的占位符目录")
        return
    
    print(f"🚨 发现 {len(detected_dirs)} 个占位符目录/文件:")
    for dir_info in detected_dirs:
        status = "空" if dir_info["is_empty"] else f"包含{dir_info['files_count']}个文件"
        print(f"  📂 {dir_info['pattern']} -> {dir_info['path']} ({status})")
    
    # 询问用户操作
    print("\n🎯 请选择操作:")
    print("1. 仅清理空的占位符目录/文件")
    print("2. 备份并清理所有占位符目录")
    print("3. 强制清理所有占位符目录 (危险)")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            # 仅清理空目录
            print("\n🧹 正在清理空的占位符目录...")
            results = clean_placeholder_dirs(detected_dirs, force=False)
            
        elif choice == "2":
            # 备份并清理
            backup_dir = project_root / "backup_placeholder_dirs"
            print(f"\n💾 正在备份非空目录到: {backup_dir}")
            backup_results = backup_placeholder_dirs(detected_dirs, backup_dir)
            
            print("\n🧹 正在清理占位符目录...")
            results = clean_placeholder_dirs(detected_dirs, force=True)
            
            # 显示备份结果
            for detail in backup_results["details"]:
                print(detail)
                
        elif choice == "3":
            # 强制清理
            confirm = input("⚠️ 这将删除所有占位符目录，包括其中的文件。确认吗? (yes/no): ").strip().lower()
            if confirm in ["yes", "y"]:
                print("\n🧹 正在强制清理所有占位符目录...")
                results = clean_placeholder_dirs(detected_dirs, force=True)
            else:
                print("❌ 操作已取消")
                return
                
        elif choice == "4":
            print("👋 退出")
            return
            
        else:
            print("❌ 无效选择")
            return
        
        # 显示清理结果
        print(f"\n📊 清理结果:")
        print(f"  ✅ 已清理: {results['cleaned']} 个")
        print(f"  ⚠️ 已跳过: {results['skipped']} 个") 
        print(f"  ❌ 错误: {results['errors']} 个")
        
        if results["details"]:
            print("\n📋 详细信息:")
            for detail in results["details"]:
                print(f"  {detail}")
        
        print(f"\n🎉 占位符目录修复完成!")
        
    except KeyboardInterrupt:
        print("\n❌ 操作被用户取消")
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {e}")

if __name__ == "__main__":
    main()