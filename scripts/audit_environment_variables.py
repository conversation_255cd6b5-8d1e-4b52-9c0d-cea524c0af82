#!/usr/bin/env python3
"""
环境变量使用审计脚本
审计AQUA项目中所有环境变量的使用情况，识别问题和改进机会
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.paths import Paths


class EnvironmentVariableAuditor:
    """环境变量审计器"""
    
    def __init__(self):
        self.project_root = Paths.ROOT
        self.env_var_patterns = [
            r'os\.environ\.get\([\'"]([^\'\"]+)[\'"]',  # os.environ.get("VAR")
            r'os\.environ\[[\'"]([^\'\"]+)[\'"]\]',     # os.environ["VAR"]
            r'os\.getenv\([\'"]([^\'\"]+)[\'"]',        # os.getenv("VAR")
            r'\$\{([A-Z_][A-Z0-9_]*)\}',               # ${VAR} in strings
            r'\$([A-Z_][A-Z0-9_]*)',                   # $VAR in strings
            r'process\.env\.([A-Z_][A-Z0-9_]*)',       # process.env.VAR (JavaScript)
            r'import\.meta\.env\.([A-Z_][A-Z0-9_]*)',  # import.meta.env.VAR (Vite)
        ]
        
        # 文件扩展名过滤
        self.code_extensions = {'.py', '.js', '.ts', '.vue', '.toml', '.yaml', '.yml', '.json', '.sh', '.bat'}
        
        # 排除的目录
        self.excluded_dirs = {'.git', '__pycache__', 'node_modules', '.venv', 'dist', 'build'}
        
        # 审计结果
        self.found_variables: Dict[str, List[Dict]] = {}
        self.file_scan_results: List[Dict] = []
        self.issues: List[Dict] = []
        
    def scan_file(self, file_path: Path) -> Dict:
        """扫描单个文件中的环境变量使用"""
        result = {
            'file': str(file_path.relative_to(self.project_root)),
            'variables': [],
            'issues': []
        }
        
        try:
            content = file_path.read_text(encoding='utf-8', errors='ignore')
            
            # 扫描所有环境变量模式
            for pattern in self.env_var_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    var_name = match.group(1)
                    line_num = content[:match.start()].count('\n') + 1
                    
                    var_info = {
                        'name': var_name,
                        'line': line_num,
                        'pattern': pattern,
                        'context': self._get_context(content, match.start(), match.end())
                    }
                    
                    result['variables'].append(var_info)
                    
                    # 记录到全局变量列表
                    if var_name not in self.found_variables:
                        self.found_variables[var_name] = []
                    self.found_variables[var_name].append({
                        'file': result['file'],
                        'line': line_num,
                        'context': var_info['context']
                    })
            
            # 检查潜在问题
            self._check_file_issues(file_path, content, result)
            
        except Exception as e:
            result['issues'].append({
                'type': 'scan_error',
                'message': f"扫描文件失败: {str(e)}"
            })
        
        return result
    
    def _get_context(self, content: str, start: int, end: int, context_size: int = 50) -> str:
        """获取匹配位置的上下文"""
        context_start = max(0, start - context_size)
        context_end = min(len(content), end + context_size)
        return content[context_start:context_end].strip()
    
    def _check_file_issues(self, file_path: Path, content: str, result: Dict):
        """检查文件中的环境变量使用问题"""
        # 检查硬编码的敏感信息
        sensitive_patterns = [
            (r'password\s*=\s*[\'"][^\'"]+[\'"]', 'hardcoded_password'),
            (r'token\s*=\s*[\'"][^\'"]+[\'"]', 'hardcoded_token'),
            (r'secret\s*=\s*[\'"][^\'"]+[\'"]', 'hardcoded_secret'),
            (r'api_key\s*=\s*[\'"][^\'"]+[\'"]', 'hardcoded_api_key'),
        ]
        
        for pattern, issue_type in sensitive_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                result['issues'].append({
                    'type': issue_type,
                    'line': line_num,
                    'message': f"发现硬编码敏感信息: {match.group()}",
                    'severity': 'high'
                })
        
        # 检查缺少默认值的环境变量
        no_default_patterns = [
            r'os\.environ\[[\'"]([^\'\"]+)[\'"]\]',  # os.environ["VAR"] without .get()
        ]
        
        for pattern in no_default_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                var_name = match.group(1)
                result['issues'].append({
                    'type': 'no_default_value',
                    'line': line_num,
                    'variable': var_name,
                    'message': f"环境变量 {var_name} 缺少默认值，可能导致KeyError",
                    'severity': 'medium'
                })
    
    def scan_project(self) -> Dict:
        """扫描整个项目"""
        print("🔍 开始扫描项目中的环境变量使用...")
        
        scanned_files = 0
        for file_path in self._get_code_files():
            result = self.scan_file(file_path)
            self.file_scan_results.append(result)
            scanned_files += 1
            
            if scanned_files % 10 == 0:
                print(f"已扫描 {scanned_files} 个文件...")
        
        print(f"✅ 扫描完成，共扫描 {scanned_files} 个文件")
        
        # 分析结果
        return self._analyze_results()
    
    def _get_code_files(self):
        """获取所有需要扫描的代码文件"""
        for root, dirs, files in os.walk(self.project_root):
            # 排除特定目录
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix in self.code_extensions:
                    yield file_path
    
    def _analyze_results(self) -> Dict:
        """分析扫描结果"""
        analysis = {
            'summary': {
                'total_files_scanned': len(self.file_scan_results),
                'files_with_env_vars': len([r for r in self.file_scan_results if r['variables']]),
                'total_env_vars_found': len(self.found_variables),
                'total_issues_found': sum(len(r['issues']) for r in self.file_scan_results)
            },
            'variables': self.found_variables,
            'files': self.file_scan_results,
            'recommendations': self._generate_recommendations()
        }
        
        return analysis
    
    def _generate_recommendations(self) -> List[Dict]:
        """生成改进建议"""
        recommendations = []
        
        # 检查常见的环境变量命名问题
        for var_name in self.found_variables:
            if not var_name.isupper():
                recommendations.append({
                    'type': 'naming_convention',
                    'variable': var_name,
                    'message': f"环境变量 {var_name} 应该使用大写字母",
                    'severity': 'low'
                })
            
            if not var_name.startswith(('AQUA_', 'VITE_')):
                recommendations.append({
                    'type': 'naming_prefix',
                    'variable': var_name,
                    'message': f"建议为环境变量 {var_name} 添加项目前缀 (AQUA_ 或 VITE_)",
                    'severity': 'low'
                })
        
        # 检查是否有未使用的环境变量定义
        env_files = ['.env', '.env.development', '.env.production', '.env.local']
        for env_file in env_files:
            env_path = self.project_root / env_file
            if env_path.exists():
                try:
                    content = env_path.read_text()
                    defined_vars = re.findall(r'^([A-Z_][A-Z0-9_]*)=', content, re.MULTILINE)
                    
                    for var in defined_vars:
                        if var not in self.found_variables:
                            recommendations.append({
                                'type': 'unused_variable',
                                'variable': var,
                                'file': env_file,
                                'message': f"环境变量 {var} 在 {env_file} 中定义但未使用",
                                'severity': 'low'
                            })
                except Exception:
                    pass
        
        return recommendations
    
    def generate_report(self, analysis: Dict) -> str:
        """生成审计报告"""
        report = []
        report.append("# AQUA项目环境变量使用审计报告")
        report.append(f"生成时间: {self._get_timestamp()}")
        report.append("")
        
        # 摘要
        summary = analysis['summary']
        report.append("## 📊 审计摘要")
        report.append(f"- 扫描文件总数: {summary['total_files_scanned']}")
        report.append(f"- 包含环境变量的文件: {summary['files_with_env_vars']}")
        report.append(f"- 发现的环境变量总数: {summary['total_env_vars_found']}")
        report.append(f"- 发现的问题总数: {summary['total_issues_found']}")
        report.append("")
        
        # 环境变量列表
        report.append("## 🔧 发现的环境变量")
        for var_name, usages in analysis['variables'].items():
            report.append(f"### {var_name}")
            report.append(f"使用次数: {len(usages)}")
            for usage in usages[:3]:  # 只显示前3个使用位置
                report.append(f"- {usage['file']}:{usage['line']}")
            if len(usages) > 3:
                report.append(f"- ... 还有 {len(usages) - 3} 个使用位置")
            report.append("")
        
        # 问题报告
        all_issues = []
        for file_result in analysis['files']:
            for issue in file_result['issues']:
                issue['file'] = file_result['file']
                all_issues.append(issue)
        
        if all_issues:
            report.append("## ⚠️ 发现的问题")
            for issue in all_issues:
                severity_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(issue.get('severity', 'low'), '🔵')
                report.append(f"{severity_emoji} **{issue['type']}** - {issue['file']}:{issue.get('line', '?')}")
                report.append(f"   {issue['message']}")
                report.append("")
        
        # 改进建议
        recommendations = analysis['recommendations']
        if recommendations:
            report.append("## 💡 改进建议")
            for rec in recommendations:
                severity_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(rec.get('severity', 'low'), '🔵')
                report.append(f"{severity_emoji} **{rec['type']}**: {rec['message']}")
            report.append("")
        
        return "\n".join(report)
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    print("🔍 AQUA项目环境变量使用审计")
    print("=" * 50)
    
    auditor = EnvironmentVariableAuditor()
    analysis = auditor.scan_project()
    
    # 生成报告
    report = auditor.generate_report(analysis)
    
    # 保存报告
    report_path = Paths.DOCS / "opti_pre_prod" / "environment_variables_audit_report.md"
    report_path.parent.mkdir(parents=True, exist_ok=True)
    report_path.write_text(report, encoding='utf-8')
    
    print(f"📄 审计报告已保存到: {report_path}")
    
    # 显示摘要
    summary = analysis['summary']
    print("\n📊 审计摘要:")
    print(f"   扫描文件: {summary['total_files_scanned']}")
    print(f"   环境变量: {summary['total_env_vars_found']}")
    print(f"   发现问题: {summary['total_issues_found']}")
    
    # 保存详细数据（JSON格式）
    json_path = Paths.DOCS / "opti_pre_prod" / "environment_variables_audit_data.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False)
    
    print(f"📊 详细数据已保存到: {json_path}")
    print("✅ 环境变量审计完成")


if __name__ == "__main__":
    main()
