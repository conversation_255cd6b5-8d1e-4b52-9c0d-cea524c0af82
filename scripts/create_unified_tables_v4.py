#!/usr/bin/env python3
"""
AQUA数据字典v4.0统一表结构创建脚本

直接创建新的统一业务表，不迁移原有数据
基于DATA_DICTIONARY.md v4.0架构设计
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.connection_manager import DuckDBConnectionManager
from src.utils.config_loader_v2 import ConfigLoaderV2


class UnifiedTablesCreator:
    """统一业务表创建器"""
    
    def __init__(self, environment: str = "dev"):
        self.environment = environment
        self.config_loader = ConfigLoaderV2()
        self.config = self.config_loader.get_config(environment)
        self.connection_manager = DuckDBConnectionManager(environment)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        self.logger = logging.getLogger(__name__)
        
    def drop_old_tables(self):
        """删除旧的分层表结构"""
        old_tables = [
            # CSV分层表
            "csv_fut_main_contract_kline_5min",
            "csv_fut_main_contract_kline_15min", 
            "csv_fut_main_contract_kline_30min",
            
            # Tushare分层表
            "tushare_stock_daily",
            "tushare_stock_basic",
            "tushare_fut_daily",
            "tushare_fut_basic",
            
            # MySQL分层表
            "mysql_stk_daily_akshare"
        ]
        
        conn = self.connection_manager.get_connection()
        try:
            for table_name in old_tables:
                try:
                    conn.execute(f"DROP TABLE IF EXISTS {table_name}")
                    self.logger.info(f"✅ 删除旧表: {table_name}")
                except Exception as e:
                    self.logger.warning(f"⚠️  删除表失败 {table_name}: {e}")
        finally:
            conn.close()
    
    def create_unified_tables(self):
        """创建统一业务表结构"""
        
        # 统一表结构定义
        table_definitions = {
            "futures_main_contract_kline": """
                CREATE TABLE IF NOT EXISTS futures_main_contract_kline (
                    -- 业务主键
                    contract_code     VARCHAR(20)      NOT NULL,
                    product_code      VARCHAR(10)      NOT NULL,
                    frequency         VARCHAR(10)      NOT NULL,
                    trade_datetime    TIMESTAMP        NOT NULL,
                    
                    -- 价格数据
                    open_price        DECIMAL(18,4)    NOT NULL,
                    high_price        DECIMAL(18,4)    NOT NULL,
                    low_price         DECIMAL(18,4)    NOT NULL,
                    close_price       DECIMAL(18,4)    NOT NULL,
                    settle_price      DECIMAL(18,4),
                    
                    -- 成交数据
                    volume           BIGINT           NOT NULL,
                    amount           DECIMAL(22,4)    NOT NULL,
                    open_interest    BIGINT,
                    
                    -- 技术指标字段
                    prev_close       DECIMAL(18,4),
                    change_amount    DECIMAL(18,4),
                    change_percent   DECIMAL(10,4),
                    
                    -- 数据源管理
                    data_source      VARCHAR(50)      NOT NULL,
                    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
                    source_file      VARCHAR(255),
                    source_table     VARCHAR(100),
                    source_api       VARCHAR(100),
                    
                    -- 数据质量管理
                    last_verified    TIMESTAMP,
                    data_status      VARCHAR(20)      DEFAULT 'ACTIVE',
                    quality_score    DECIMAL(5,2),
                    
                    -- 审计字段
                    created_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    
                    PRIMARY KEY (contract_code, frequency, trade_datetime)
                );
            """,
            
            "futures_contract_basic": """
                CREATE TABLE IF NOT EXISTS futures_contract_basic (
                    contract_code     VARCHAR(20)      PRIMARY KEY,
                    product_code      VARCHAR(10)      NOT NULL,
                    contract_name     VARCHAR(50)      NOT NULL,
                    exchange_code     VARCHAR(20)      NOT NULL,
                    exchange_name     VARCHAR(50)      NOT NULL,
                    multiplier        DECIMAL(18,4)    NOT NULL,
                    tick_size         DECIMAL(18,6)    NOT NULL,
                    trade_unit        VARCHAR(50)      NOT NULL,
                    quote_unit        VARCHAR(20)      NOT NULL,
                    list_date         DATE             NOT NULL,
                    last_trade_date   DATE             NOT NULL,
                    delivery_month    VARCHAR(10)      NOT NULL,
                    margin_rate       DECIMAL(10,4),
                    limit_up_rate     DECIMAL(10,4),
                    limit_down_rate   DECIMAL(10,4),
                    data_source      VARCHAR(50)      NOT NULL,
                    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
                    created_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP
                );
            """,
            
            "futures_product_basic": """
                CREATE TABLE IF NOT EXISTS futures_product_basic (
                    product_code      VARCHAR(10)      PRIMARY KEY,
                    product_name      VARCHAR(50)      NOT NULL,
                    product_name_en   VARCHAR(50),
                    category          VARCHAR(30)      NOT NULL,
                    exchange_code     VARCHAR(20)      NOT NULL,
                    multiplier        DECIMAL(18,4)    NOT NULL,
                    tick_size         DECIMAL(18,6)    NOT NULL,
                    trade_unit        VARCHAR(50)      NOT NULL,
                    quote_unit        VARCHAR(20)      NOT NULL,
                    delivery_unit     VARCHAR(50),
                    delivery_grade    TEXT,
                    delivery_location TEXT,
                    list_date         DATE,
                    is_active         BOOLEAN          DEFAULT TRUE,
                    data_source      VARCHAR(50)      NOT NULL,
                    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
                    created_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP
                );
            """,
            
            "stocks_daily_kline": """
                CREATE TABLE IF NOT EXISTS stocks_daily_kline (
                    stock_code        VARCHAR(20)      NOT NULL,
                    trade_date        DATE             NOT NULL,
                    open_price        DECIMAL(18,4)    NOT NULL,
                    high_price        DECIMAL(18,4)    NOT NULL,
                    low_price         DECIMAL(18,4)    NOT NULL,
                    close_price       DECIMAL(18,4)    NOT NULL,
                    prev_close        DECIMAL(18,4),
                    volume           BIGINT           NOT NULL,
                    amount           DECIMAL(22,4)    NOT NULL,
                    change_amount    DECIMAL(18,4),
                    change_percent   DECIMAL(10,4),
                    turnover_rate    DECIMAL(10,4),
                    adj_factor       DECIMAL(18,6),
                    data_source      VARCHAR(50)      NOT NULL,
                    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
                    source_api       VARCHAR(100),
                    last_verified    TIMESTAMP,
                    data_status      VARCHAR(20)      DEFAULT 'ACTIVE',
                    quality_score    DECIMAL(5,2),
                    created_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    
                    PRIMARY KEY (stock_code, trade_date)
                );
            """,
            
            "stocks_basic_info": """
                CREATE TABLE IF NOT EXISTS stocks_basic_info (
                    stock_code        VARCHAR(20)      PRIMARY KEY,
                    stock_name        VARCHAR(50)      NOT NULL,
                    stock_name_en     VARCHAR(50),
                    symbol            VARCHAR(20)      NOT NULL,
                    market            VARCHAR(20)      NOT NULL,
                    industry          VARCHAR(50),
                    area              VARCHAR(30),
                    sector            VARCHAR(50),
                    list_date         DATE,
                    list_status       VARCHAR(20)      DEFAULT 'L',
                    delist_date       DATE,
                    total_share       BIGINT,
                    float_share       BIGINT,
                    data_source      VARCHAR(50)      NOT NULL,
                    source_quality   VARCHAR(20)      DEFAULT 'NORMAL',
                    created_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
                    updated_at       TIMESTAMP        DEFAULT CURRENT_TIMESTAMP
                );
            """
        }
        
        # 索引定义
        index_definitions = {
            "futures_main_contract_kline": [
                "CREATE INDEX IF NOT EXISTS idx_product_freq_time ON futures_main_contract_kline (product_code, frequency, trade_datetime);",
                "CREATE INDEX IF NOT EXISTS idx_data_source ON futures_main_contract_kline (data_source);",
                "CREATE INDEX IF NOT EXISTS idx_trade_datetime ON futures_main_contract_kline (trade_datetime);",
                "CREATE INDEX IF NOT EXISTS idx_quality ON futures_main_contract_kline (source_quality, data_status);",
                "CREATE INDEX IF NOT EXISTS idx_product_code ON futures_main_contract_kline (product_code);"
            ],
            "futures_contract_basic": [
                "CREATE INDEX IF NOT EXISTS idx_product_code ON futures_contract_basic (product_code);",
                "CREATE INDEX IF NOT EXISTS idx_exchange ON futures_contract_basic (exchange_code);",
                "CREATE INDEX IF NOT EXISTS idx_delivery_month ON futures_contract_basic (delivery_month);",
                "CREATE INDEX IF NOT EXISTS idx_list_date ON futures_contract_basic (list_date);"
            ],
            "futures_product_basic": [
                "CREATE INDEX IF NOT EXISTS idx_category ON futures_product_basic (category);",
                "CREATE INDEX IF NOT EXISTS idx_exchange ON futures_product_basic (exchange_code);",
                "CREATE INDEX IF NOT EXISTS idx_active ON futures_product_basic (is_active);"
            ],
            "stocks_daily_kline": [
                "CREATE INDEX IF NOT EXISTS idx_trade_date ON stocks_daily_kline (trade_date);",
                "CREATE INDEX IF NOT EXISTS idx_data_source ON stocks_daily_kline (data_source);",
                "CREATE INDEX IF NOT EXISTS idx_quality ON stocks_daily_kline (source_quality, data_status);"
            ],
            "stocks_basic_info": [
                "CREATE INDEX IF NOT EXISTS idx_symbol ON stocks_basic_info (symbol);",
                "CREATE INDEX IF NOT EXISTS idx_industry ON stocks_basic_info (industry);",
                "CREATE INDEX IF NOT EXISTS idx_market ON stocks_basic_info (market);",
                "CREATE INDEX IF NOT EXISTS idx_list_date ON stocks_basic_info (list_date);",
                "CREATE INDEX IF NOT EXISTS idx_list_status ON stocks_basic_info (list_status);"
            ]
        }
        
        self.logger.info("开始创建统一业务表结构...")
        
        conn = self.connection_manager.get_connection()
        try:
            # 1. 创建表
            for table_name, ddl in table_definitions.items():
                try:
                    conn.execute(ddl)
                    self.logger.info(f"✅ 创建表: {table_name}")
                except Exception as e:
                    self.logger.error(f"❌ 创建表失败 {table_name}: {e}")
                    raise
            
            # 2. 创建索引
            for table_name, indexes in index_definitions.items():
                for index_sql in indexes:
                    try:
                        conn.execute(index_sql)
                        self.logger.debug(f"✅ 创建索引: {table_name}")
                    except Exception as e:
                        self.logger.error(f"❌ 创建索引失败 {table_name}: {e}")
                        raise
            
            self.logger.info("✅ 统一业务表结构创建完成")
        finally:
            conn.close()
    
    def verify_tables(self):
        """验证表创建结果"""
        expected_tables = [
            "futures_main_contract_kline",
            "futures_contract_basic", 
            "futures_product_basic",
            "stocks_daily_kline",
            "stocks_basic_info"
        ]
        
        conn = self.connection_manager.get_connection()
        try:
            # 获取所有表名
            tables_result = conn.execute("SHOW TABLES").fetchall()
            existing_tables = [row[0] for row in tables_result]
            
            self.logger.info("验证表创建结果:")
            for table_name in expected_tables:
                if table_name in existing_tables:
                    # 获取表记录数
                    count_result = conn.execute(f"SELECT COUNT(*) FROM {table_name}").fetchone()
                    record_count = count_result[0] if count_result else 0
                    self.logger.info(f"✅ {table_name}: 已创建 ({record_count} 条记录)")
                else:
                    self.logger.error(f"❌ {table_name}: 创建失败")
        finally:
            conn.close()
    
    def run(self):
        """执行完整的表结构重建"""
        try:
            self.logger.info("🚀 开始AQUA数据字典v4.0统一表结构创建")
            
            # 1. 删除旧的分层表
            self.drop_old_tables()
            
            # 2. 创建统一业务表
            self.create_unified_tables()
            
            # 3. 验证创建结果
            self.verify_tables()
            
            self.logger.info("✅ AQUA数据字典v4.0统一表结构创建完成!")
            
        except Exception as e:
            self.logger.error(f"❌ 表结构创建失败: {str(e)}")
            raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="AQUA数据字典v4.0统一表结构创建")
    parser.add_argument("--env", default="dev", choices=["dev", "test", "prod"], 
                       help="目标环境 (默认: dev)")
    
    args = parser.parse_args()
    
    creator = UnifiedTablesCreator(args.env)
    creator.run()