#!/usr/bin/env node
/**
 * AQUA_V1.2 mock与TS类型一致性校验脚本
 * 仅提示不阻断，适配MVP阶段
 *
 * 规则：
 * - mock文件名如import_data.mock.json，对应TS类型如ImportDataResponse
 * - 支持多mock/多类型，自动匹配
 * - 仅校验字段名、类型、嵌套结构，不校验业务内容
 * - 输出详细提示，不退出非零码
 */
const fs = require('fs');
const path = require('path');
const ts = require('typescript');

const MOCK_DIR = path.join(__dirname, '../frontend/tests/mock');
const TYPE_FILE = path.join(__dirname, '../frontend/src/types/api.ts');

// mock文件与TS类型名映射规则
const mockTypeMap = {
  'import_data.mock.json': 'ImportDataResponse',
  'tables.mock.json': 'GetTablesResponse',
  'performance.mock.json': 'GetPerformanceResponse',
};

// 读取TS类型定义
function parseTypes(tsFile) {
  const source = fs.readFileSync(tsFile, 'utf-8');
  const sourceFile = ts.createSourceFile('api.ts', source, ts.ScriptTarget.ES2015, true);
  const types = {};
  function visit(node) {
    if (ts.isInterfaceDeclaration(node)) {
      const name = node.name.text;
      const fields = {};
      node.members.forEach(member => {
        if (ts.isPropertySignature(member) && member.type) {
          let typeStr = member.type.getText(sourceFile);
          fields[member.name.getText(sourceFile)] = typeStr;
        }
      });
      types[name] = fields;
    }
    ts.forEachChild(node, visit);
  }
  visit(sourceFile);
  return types;
}

// 简单类型映射
function jsTypeToTs(val) {
  if (Array.isArray(val)) return 'any[]';
  if (val === null) return 'any';
  switch (typeof val) {
    case 'string': return 'string';
    case 'number': return 'number';
    case 'boolean': return 'boolean';
    case 'object': return 'object';
    default: return 'any';
  }
}

// 递归校验结构
function checkStruct(mockObj, tsFields, prefix = '') {
  let ok = true;
  for (const key in tsFields) {
    if (!(key in mockObj)) {
      console.log(`\x1b[31m[校验] 缺少字段: ${prefix}${key}\x1b[0m`);
      ok = false;
      continue;
    }
    const mockVal = mockObj[key];
    const tsType = tsFields[key];
    const mockType = jsTypeToTs(mockVal);
    // 简单类型直接比对
    if (tsType.endsWith('[]')) {
      if (!Array.isArray(mockVal)) {
        console.log(`\x1b[31m[校验] 字段类型不符: ${prefix}${key} 期望数组\x1b[0m`);
        ok = false;
      }
    } else if (tsType === 'object' || tsType.startsWith('{')) {
      if (typeof mockVal !== 'object' || Array.isArray(mockVal)) {
        console.log(`\x1b[31m[校验] 字段类型不符: ${prefix}${key} 期望对象\x1b[0m`);
        ok = false;
      }
    } else if (tsType !== mockType && tsType !== 'any') {
      console.log(`\x1b[31m[校验] 字段类型不符: ${prefix}${key} 期望${tsType} 实际${mockType}\x1b[0m`);
      ok = false;
    }
    // 嵌套对象递归
    if (typeof mockVal === 'object' && !Array.isArray(mockVal) && tsType === 'object') {
      // 仅递归一层
      // 可扩展为更复杂类型
    }
  }
  // 检查mock多余字段
  for (const key in mockObj) {
    if (!(key in tsFields)) {
      console.log(`\x1b[33m[校验] mock多余字段: ${prefix}${key}\x1b[0m`);
    }
  }
  return ok;
}

function main() {
  const types = parseTypes(TYPE_FILE);
  let allOk = true;
  for (const [mockFile, typeName] of Object.entries(mockTypeMap)) {
    const mockPath = path.join(MOCK_DIR, mockFile);
    if (!fs.existsSync(mockPath)) {
      console.log(`\x1b[33m[跳过] 未找到mock文件: ${mockFile}\x1b[0m`);
      continue;
    }
    if (!(typeName in types)) {
      console.log(`\x1b[33m[跳过] TS类型未定义: ${typeName}\x1b[0m`);
      continue;
    }
    const mockObj = JSON.parse(fs.readFileSync(mockPath, 'utf-8'));
    console.log(`\n[校验] ${mockFile} <-> ${typeName}`);
    const ok = checkStruct(mockObj, types[typeName]);
    if (ok) {
      console.log(`\x1b[32m[通过] 结构一致\x1b[0m`);
    } else {
      allOk = false;
    }
  }
  if (!allOk) {
    console.log("\x1b[31m[提示] mock与TS类型存在不一致，请尽快修正。当前为MVP阶段，未阻断提交。\x1b[0m");
  } else {
    console.log("\x1b[32m[全部通过] mock与TS类型结构一致。\x1b[0m");
  }
}

main(); 