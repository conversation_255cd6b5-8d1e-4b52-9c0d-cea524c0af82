#!/usr/bin/env python3
"""
AQUA数据库重置脚本
使用DuckDBInitChecker进行数据库管理，基于数据字典自动创建表结构

特性：
- 基于数据字典自动创建所有表结构
- 支持数据库备份和强制重置
- 统一的数据库管理接口
- 详细的操作日志和状态反馈
"""

import sys
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.duckdb_init_check import DuckDBInitChecker


class Colors:
    """终端颜色定义"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


def print_header(text: str, color: str = Colors.HEADER):
    """打印彩色标题"""
    print(f"\n{color}{'='*60}{Colors.ENDC}")
    print(f"{color}{text.center(60)}{Colors.ENDC}")
    print(f"{color}{'='*60}{Colors.ENDC}")


def print_info(text: str):
    """打印信息"""
    print(f"{Colors.OKBLUE}ℹ️  {text}{Colors.ENDC}")


def print_success(text: str):
    """打印成功信息"""
    print(f"{Colors.OKGREEN}✅ {text}{Colors.ENDC}")


def print_warning(text: str):
    """打印警告信息"""
    print(f"{Colors.WARNING}⚠️  {text}{Colors.ENDC}")


def print_error(text: str):
    """打印错误信息"""
    print(f"{Colors.FAIL}❌ {text}{Colors.ENDC}")


def confirm_action(message: str) -> bool:
    """确认操作"""
    try:
        response = input(f"{Colors.WARNING}❓ {message} (y/N): {Colors.ENDC}")
        return response.lower() in ['y', 'yes']
    except EOFError:
        return False


def reset_database(environment: str = "test", backup: bool = True, force: bool = False):
    """重置数据库"""
    print_header("AQUA数据库重置")
    
    # 生产环境特殊检查
    if environment == "prod":
        print_warning("检测到生产环境操作！")
        print_error("生产环境数据库重置需要使用专用接口")
        print_info("请使用: python scripts/reset_database.py --env prod --production-reset")
        return False
    
    # 初始化数据库检测器
    db_checker = DuckDBInitChecker(
        config_path="config/settings.toml",
        environment=environment,
        dict_path="docs/database/DATA_DICTIONARY.md"
    )
    
    print_info(f"环境: {environment}")
    print_info(f"数据字典路径: docs/database/DATA_DICTIONARY.md")
    
    # 检查数据库连接
    print_info("检查数据库连接...")
    conn_result = db_checker.check_database_connection()
    if conn_result.success:
        print_success("数据库连接正常")
    else:
        print_warning(f"数据库连接失败: {conn_result.message}")
    
    # 非生产环境的确认操作
    if not force:
        warning_msg = f"确定要重置{environment}环境数据库吗？这将删除所有现有数据！"
        if not confirm_action(warning_msg):
            print_info("操作已取消")
            return
    
    # 备份数据库
    if backup:
        print_info("正在备份数据库...")
        backup_result = db_checker.backup_database()
        if backup_result.success:
            print_success(f"数据库备份完成: {backup_result.message}")
        else:
            print_warning(f"数据库备份失败: {backup_result.message}")
    
    # 删除现有数据库文件
    db_path = Path(db_checker.config.get("database", {}).get("path", ""))
    if db_path.exists():
        print_info(f"删除现有数据库文件: {db_path}")
        db_path.unlink()
        print_success("数据库文件已删除")
    else:
        print_info("数据库文件不存在，跳过删除")
    
    # 使用DuckDBInitChecker初始化数据库
    print_info("🏗️  基于数据字典初始化数据库...")
    # 非生产环境可以直接初始化
    init_result = db_checker.initialize_database(force=True, production_confirmed=False)
    
    if init_result.success:
        print_success("数据库初始化完成")
        
        # 验证数据库结构
        print_info("验证数据库结构...")
        validate_result = db_checker.validate_database_structure()
        if validate_result.success:
            print_success("数据库结构验证通过")
        else:
            print_warning(f"数据库结构验证失败: {validate_result.message}")
        
        # 显示数据库健康报告
        print_info("生成数据库健康报告...")
        health_report = db_checker.get_database_health_report()
        
        print_header("数据库健康报告", Colors.OKGREEN)
        print(f"连接状态: {health_report.get('connection_status', 'unknown')}")
        print(f"结构状态: {health_report.get('structure_status', 'unknown')}")
        print(f"数据完整性: {health_report.get('integrity_status', 'unknown')}")
        print(f"表数量: {health_report.get('table_count', 0)}")
        print(f"检查时间: {health_report.get('check_time', 'unknown')}")
        
        # 保存健康报告
        report_file = Path("logs") / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_file.parent.mkdir(exist_ok=True)
        
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(health_report, f, ensure_ascii=False, indent=2)
        
        print_success(f"健康报告已保存: {report_file}")
        
    else:
        print_error(f"数据库初始化失败: {init_result.message}")
        return False
    
    print_header("数据库重置完成", Colors.OKGREEN)
    return True


def reset_production_database():
    """生产环境数据库重置（带三次确认）"""
    print_header("🚨 AQUA生产数据库重置", Colors.FAIL)
    
    # 初始化数据库检测器
    db_checker = DuckDBInitChecker(
        config_path="config/settings.toml",
        environment="prod",
        dict_path="docs/database/DATA_DICTIONARY.md"
    )
    
    print_warning("您即将对生产环境数据库执行重置操作")
    print_error("这是一个高风险操作，将完全清空生产数据库！")
    
    # 使用带三次确认的初始化方法
    try:
        # 强制备份
        print_info("强制备份生产数据库...")
        backup_result = db_checker.backup_database()
        if backup_result.success:
            print_success(f"生产数据库备份完成: {backup_result.message}")
        else:
            print_error(f"备份失败: {backup_result.message}")
            print_error("没有备份，终止重置操作")
            return False
        
        # 删除现有数据库文件
        db_path = Path(db_checker.config.get("database", {}).get("path", ""))
        if db_path.exists():
            print_info(f"删除生产数据库文件: {db_path}")
            db_path.unlink()
            print_success("生产数据库文件已删除")
        
        # 使用三次确认的初始化方法
        init_result = db_checker.initialize_production_database_with_confirmation()
        
        if init_result.success:
            print_success("生产数据库重置完成")
            return True
        else:
            print_error(f"生产数据库重置失败: {init_result.message}")
            return False
            
    except Exception as e:
        print_error(f"生产数据库重置异常: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AQUA数据库重置工具")
    parser.add_argument("--env", default="test", help="环境名称 (默认: test)")
    parser.add_argument("--no-backup", action="store_true", help="不备份现有数据库")
    parser.add_argument("--force", action="store_true", help="强制重置，不确认")
    parser.add_argument("--production-reset", action="store_true", help="生产环境重置（需要三次确认）")
    
    args = parser.parse_args()
    
    try:
        # 检查是否为生产环境重置
        if args.production_reset:
            if args.env != "prod":
                print_error("生产环境重置只能在prod环境下使用")
                sys.exit(1)
            success = reset_production_database()
        else:
            success = reset_database(
                environment=args.env,
                backup=not args.no_backup,
                force=args.force
            )
        
        if success:
            print_success("数据库重置成功完成！")
            sys.exit(0)
        else:
            print_error("数据库重置失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}用户中断操作{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print_error(f"程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()