#!/bin/bash
#
# 统一部署脚本
#
# 用法:
#   ./scripts/deploy.sh [local|test|prod]
#
# 描述:
#   本脚本用于自动化部署AQUA项目到不同环境。
#   它首先会强制执行环境校验，然后根据传入的参数
#   执行对应环境的部署流程。
#
#   - local: 启动本地开发服务器，支持热重载。
#   - test:  (待实现) 构建测试环境产物并部署。
#   - prod:  (待实现) 构建生产环境产物并部署。
#
# 作者: CURSOR/GEMINI
# 创建日期: 2024-07-27
#

# --- 配置 ---
set -e # 任何命令执行失败则立即退出脚本
# 获取脚本所在目录的绝对路径
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)
BASE_DIR=$(dirname "$SCRIPT_DIR") # 获取项目根目录
cd "$BASE_DIR" || exit

# --- 函数 ---
log_info() {
    echo "$(date +'%Y-%m-%dT%H:%M:%S%z') [INFO] [deploy.sh] $1"
}

log_error() {
    echo "$(date +'%Y-%m-%dT%H:%M:%S%z') [ERROR] [deploy.sh] $1" >&2
}

# --- 主逻辑 ---

# 1. 解析环境参数
ENV=$1
if [[ -z "$ENV" ]]; then
    log_error "未提供环境参数。用法: ./scripts/deploy.sh [local|test|prod]"
    exit 1
fi

# 2. 强制环境校验
log_info "开始执行环境校验 (scripts/env_init.py)..."
# 使用 python3 是为了更明确，也可以是 python
if ! python3 scripts/env_init.py; then
    log_error "环境校验失败，中止部署。"
    exit 1
fi
log_info "环境校验通过。"

# 3. 根据环境执行不同部署逻辑
log_info "准备为 '$ENV' 环境执行部署..."

case "$ENV" in
    local)
        log_info "正在启动本地开发环境..."
        log_info "将分别启动前端和后端服务。请注意查看各自的终端输出。"

        # 复用已有的开发启动脚本
        # 使用 & 让它们在后台并行启动
        ./start_frontend.sh &
        FRONTEND_PID=$!
        ./start_backend.sh &
        BACKEND_PID=$!

        log_info "前端服务 (PID: $FRONTEND_PID) 和后端服务 (PID: $BACKEND_PID) 已启动。"
        log_info "使用 'kill $FRONTEND_PID $BACKEND_PID' 来停止服务。"
        # 等待后台进程，以便脚本不会立即退出，并且可以捕获 Ctrl+C
        wait $FRONTEND_PID $BACKEND_PID
        ;;

    test|prod)
        log_error "部署到 '$ENV' 环境的逻辑尚未实现。"
        log_info "TODO: 在此实现前端构建 (pnpm build) 和生产级后端服务启动 (gunicorn/waitress)。"
        # 示例:
        # log_info "正在构建前端生产资源..."
        # (cd frontend && pnpm install && pnpm build)
        # log_info "正在启动生产模式后端服务..."
        # gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app
        exit 1 # 暂时标记为失败，因为未实现
        ;;

    *)
        log_error "无效的环境参数 '$ENV'。请使用 'local', 'test', or 'prod'."
        exit 1
        ;;
esac

log_info "部署脚本执行完毕。" 