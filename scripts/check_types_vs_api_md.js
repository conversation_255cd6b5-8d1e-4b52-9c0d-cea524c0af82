#!/usr/bin/env node
/**
 * AQUA_V1.2 TS类型与api.md接口定义一致性校验脚本
 * 仅提示不阻断，适配MVP阶段
 *
 * 规则：
 * - 解析api.ts接口类型及注释，提取能力编号、方法、路径、字段、类型
 * - 解析api.md，提取能力编号、方法、路径、字段、类型
 * - 按能力编号一一比对，输出字段、类型、路径、方法不一致项
 * - 输出详细提示，不退出非零码
 */
const fs = require('fs');
const path = require('path');
const ts = require('typescript');

const TYPE_FILE = path.join(__dirname, '../frontend/src/types/api.ts');
const API_MD_FILE = path.join(__dirname, '../docs/old/api.md');

// 1. 解析api.ts接口类型及注释
function parseTsTypesWithMeta(tsFile) {
  const source = fs.readFileSync(tsFile, 'utf-8');
  const sourceFile = ts.createSourceFile('api.ts', source, ts.ScriptTarget.ES2015, true);
  const types = {};
  let lastComment = '';
  function visit(node) {
    if (ts.isJSDoc(node)) {
      lastComment = node.comment || '';
    }
    if (ts.isInterfaceDeclaration(node)) {
      // 查找前面注释
      let leading = ts.getLeadingCommentRanges(source, node.pos);
      let meta = { ability: '', method: '', path: '' };
      if (leading && leading.length > 0) {
        let commentText = source.substring(leading[0].pos, leading[0].end);
        // 能力编号
        let ab = commentText.match(/能力编号[:：]\s*(\d+)/);
        if (ab) meta.ability = ab[1];
        // 方法与路径
        let mp = commentText.match(/\[(GET|POST|PUT|DELETE)\]\s*([^\s*]+)/);
        if (mp) {
          meta.method = mp[1];
          meta.path = mp[2];
        }
      }
      const name = node.name.text;
      const fields = {};
      node.members.forEach(member => {
        if (ts.isPropertySignature(member) && member.type) {
          let typeStr = member.type.getText(sourceFile);
          fields[member.name.getText(sourceFile)] = typeStr;
        }
      });
      types[name] = { fields, meta };
    }
    ts.forEachChild(node, visit);
  }
  visit(sourceFile);
  return types;
}

// 2. 解析api.md能力编号、方法、路径、字段、类型
function parseApiMd(mdFile) {
  const md = fs.readFileSync(mdFile, 'utf-8');
  const apiDefs = {};
  // 按接口分段
  const blocks = md.split('---').map(b => b.trim());
  for (const block of blocks) {
    // 能力编号
    const ab = block.match(/能力编号[:：]\s*(\d+)/);
    if (!ab) continue;
    const ability = ab[1];
    // 方法与路径
    const mp = block.match(/\[(GET|POST|PUT|DELETE)\]\s*([^\s*]+)/);
    const method = mp ? mp[1] : '';
    const path_ = mp ? mp[2] : '';
    // 字段表（返回值）
    let fields = {};
    const returnTable = block.match(/返回值：([\s\S]*?)\| 示例：/);
    if (returnTable) {
      const lines = returnTable[1].split('\n').map(l => l.trim()).filter(l => l.startsWith('|'));
      for (const line of lines) {
        const cols = line.split('|').map(s => s.trim());
        if (cols.length >= 3 && cols[1] && cols[2]) {
          // | 字段名 | 类型 | 说明 |
          const key = cols[1];
          const type = cols[2];
          if (key !== '字段名' && key) fields[key] = type;
        }
      }
    }
    apiDefs[ability] = { method, path: path_, fields };
  }
  return apiDefs;
}

// 3. 类型映射
function normalizeType(t) {
  t = t.toLowerCase();
  if (t === 'int' || t === 'number') return 'number';
  if (t === 'string') return 'string';
  if (t === 'object') return 'object';
  if (t === 'list' || t.endsWith('[]')) return 'array';
  return t;
}

// 4. 比对并输出提示
function compare(tsTypes, apiDefs) {
  let allOk = true;
  for (const [typeName, { fields: tsFields, meta }] of Object.entries(tsTypes)) {
    const ab = meta.ability;
    if (!ab || !(ab in apiDefs)) {
      console.log(`\x1b[33m[跳过] TS类型${typeName}未找到能力编号或api.md无对应定义\x1b[0m`);
      continue;
    }
    const api = apiDefs[ab];
    // 方法、路径
    if (meta.method !== api.method || meta.path !== api.path) {
      console.log(`\x1b[31m[校验] 能力${ab} 方法/路径不一致: TS(${meta.method} ${meta.path}) <-> api.md(${api.method} ${api.path})\x1b[0m`);
      allOk = false;
    }
    // 字段
    for (const key in api.fields) {
      if (!(key in tsFields)) {
        console.log(`\x1b[31m[校验] 能力${ab} 字段缺失: ${key}\x1b[0m`);
        allOk = false;
        continue;
      }
      const tsType = normalizeType(tsFields[key]);
      const apiType = normalizeType(api.fields[key]);
      if (tsType !== apiType) {
        console.log(`\x1b[31m[校验] 能力${ab} 字段类型不符: ${key} TS(${tsType}) <-> api.md(${apiType})\x1b[0m`);
        allOk = false;
      }
    }
    for (const key in tsFields) {
      if (!(key in api.fields)) {
        console.log(`\x1b[33m[校验] 能力${ab} TS类型多余字段: ${key}\x1b[0m`);
      }
    }
    if (allOk) {
      console.log(`\x1b[32m[通过] 能力${ab} (${typeName}) 字段、类型、方法、路径一致\x1b[0m`);
    }
  }
  if (!allOk) {
    console.log("\x1b[31m[提示] TS类型与api.md存在不一致，请尽快修正。当前为MVP阶段，未阻断提交。\x1b[0m");
  } else {
    console.log("\x1b[32m[全部通过] TS类型与api.md结构一致。\x1b[0m");
  }
}

function main() {
  const tsTypes = parseTsTypesWithMeta(TYPE_FILE);
  const apiDefs = parseApiMd(API_MD_FILE);
  compare(tsTypes, apiDefs);
}

main(); 