#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境初始化与检测脚本
- 本项目强制UV环境管理，详细用法见docs/模块开发指南.md
- 自动检测/安装UV
- 检查并唯一激活项目.venv
- 自动退出Conda/其他虚拟环境
- 多轮依赖修复
- CLI输出用Rich美化（统一调用logger）
- 结构化日志输出（统一调用logger）
- 兼容Win11/OSX
"""
import os
import platform
import shutil
import subprocess
import sys
import tempfile
import traceback
import urllib.request
from pathlib import Path
import argparse
import duckdb
import socket

# --- Robust Path Setup ---
# Get the absolute path of the script's directory (scripts)
_SCRIPT_DIR = Path(__file__).resolve().parent
# Get the absolute path of the project root (one level up from scripts)
_PROJECT_ROOT = _SCRIPT_DIR.parent
# Add project root to the Python path
sys.path.insert(0, str(_PROJECT_ROOT))

# 全局常量
VENV_PATH = _PROJECT_ROOT / '.venv'
CONFIG_FILE_PATH = _PROJECT_ROOT / 'config' / 'settings.toml'
REQUIREMENTS_PATH = _PROJECT_ROOT / 'requirements.txt'

# 基础依赖列表（已迁移到配置文件，这里保留作为兜底）
# DEPRECATED: 使用ConfigDrivenDependencyManager.get_bootstrap_dependencies()替代
BOOTSTRAP_DEPS = [
    'toml==0.10.2',
    'rich>=13.7.0',
    'loguru==0.7.2'
]

# ========== macOS/Linux下pip自愈逻辑 ========== #
def ensure_pip_in_venv():
    """
    检查并修复.venv下pip缺失问题，仅在macOS/Linux下启用。
    若pip缺失，优先尝试ensurepip，失败则自动下载get-pip.py。
    pip3存在但pip缺失时自动软链接。
    """
    venv_pip = VENV_PATH / 'bin' / 'pip'
    venv_pip3 = VENV_PATH / 'bin' / 'pip3'
    venv_python = VENV_PATH / 'bin' / 'python'
    if platform.system() == 'Windows':
        return  # Windows下不处理
    # 检查pip或pip3
    if venv_pip.exists() or venv_pip3.exists():
        # 若pip缺失但pip3存在，自动软链接
        if not venv_pip.exists() and venv_pip3.exists():
            try:
                print(f"[自愈] 检测到pip缺失但pip3存在，自动创建软链接: {venv_pip} -> {venv_pip3}")
                os.symlink(venv_pip3, venv_pip)
                print("[自愈] pip软链接创建成功！")
            except Exception as e:
                print(f"[自愈] pip软链接创建失败: {e}")
                print("[自愈] 请手动执行: ln -s pip3 pip")
                sys.exit(1)
        print("[自愈] pip/pip3检测通过！")
        return
    print("[自愈] 检测到.venv下缺失pip，尝试自动修复...")
    # 1. 尝试ensurepip
    try:
        print("[自愈] 尝试: python -m ensurepip --upgrade")
        subprocess.run([str(venv_python), '-m', 'ensurepip', '--upgrade'], check=True)
    except Exception as e:
        print(f"[自愈] ensurepip失败: {e}")
        # 2. 下载get-pip.py
        try:
            print("[自愈] 尝试: 下载get-pip.py并安装pip")
            get_pip_url = 'https://bootstrap.pypa.io/get-pip.py'
            with tempfile.NamedTemporaryFile(delete=False) as tmpfile:
                urllib.request.urlretrieve(get_pip_url, tmpfile.name)
                subprocess.run([str(venv_python), tmpfile.name], check=True)
            print("[自愈] get-pip.py安装pip成功")
        except Exception as e2:
            print(f"[自愈] get-pip.py安装pip失败: {e2}")
            print("[自愈] 详细错误：")
            traceback.print_exc()
            print("[自愈] 自动修复pip失败，请手动激活.venv后执行：\n  python -m ensurepip --upgrade\n或参考官方文档 https://pip.pypa.io/en/stable/installation/")
            sys.exit(1)
    # 再次检测pip/pip3
    if venv_pip.exists() or venv_pip3.exists():
        if not venv_pip.exists() and venv_pip3.exists():
            try:
                print(f"[自愈] 检测到pip缺失但pip3存在，自动创建软链接: {venv_pip} -> {venv_pip3}")
                os.symlink(venv_pip3, venv_pip)
                print("[自愈] pip软链接创建成功！")
            except Exception as e:
                print(f"[自愈] pip软链接创建失败: {e}")
                print("[自愈] 请手动执行: ln -s pip3 pip")
                sys.exit(1)
        print("[自愈] pip/pip3检测通过！")
        return
    print("[自愈] pip依然缺失，自动修复失败，请手动处理！")
    sys.exit(1)

# ========== END pip自愈逻辑 ========== #

# 首先修复pip（仅macOS/Linux）
ensure_pip_in_venv()

# ========== 镜像源适配 ========== #
# DEPRECATED: 传统网络检测函数，使用ConfigDrivenNetworkManager替代
def is_china_network() -> bool:
    """传统网络检测函数（已废弃）"""
    try:
        # 通过ping清华源判断
        socket.setdefaulttimeout(2)
        socket.gethostbyname('pypi.tuna.tsinghua.edu.cn')
        return True
    except Exception:
        return False

# 多镜像源配置（已迁移到配置文件，这里保留作为兜底）
# DEPRECATED: 使用ConfigDrivenDependencyManager.get_china_mirrors()替代
CHINA_MIRRORS = [
    {
        'url': 'https://pypi.tuna.tsinghua.edu.cn/simple',
        'host': 'pypi.tuna.tsinghua.edu.cn',
        'name': '清华大学'
    },
    {
        'url': 'https://mirrors.aliyun.com/pypi/simple/',
        'host': 'mirrors.aliyun.com',
        'name': '阿里云'
    },
    {
        'url': 'https://pypi.mirrors.ustc.edu.cn/simple/',
        'host': 'pypi.mirrors.ustc.edu.cn',
        'name': '中科大'
    },
    {
        'url': 'https://pypi.douban.com/simple/',
        'host': 'pypi.douban.com',
        'name': '豆瓣'
    }
]

def test_mirror_connectivity(mirror_url, timeout=3):
    """测试镜像源连通性（已废弃，使用ConfigDrivenNetworkManager替代）"""
    try:
        import urllib.request
        req = urllib.request.Request(mirror_url)
        urllib.request.urlopen(req, timeout=timeout)
        return True
    except Exception:
        return False

def get_best_mirror():
    """获取最佳可用镜像源（已废弃，使用ConfigDrivenNetworkManager替代）"""
    if not is_china_network():
        return None
    
    print("🔍 正在检测最佳PyPI镜像源...")
    for mirror in CHINA_MIRRORS:
        print(f"  测试 {mirror['name']} 镜像源...")
        if test_mirror_connectivity(mirror['url']):
            print(f"  ✅ {mirror['name']} 镜像源可用")
            return mirror
        else:
            print(f"  ❌ {mirror['name']} 镜像源不可用")
    
    print("  ⚠️ 所有国内镜像源均不可用，将使用官方源")
    return None

# 获取项目UV路径（遵循AQUA配置驱动原则）
def get_project_uv_path():
    """获取项目UV路径，优先从配置文件读取"""
    try:
        # 尝试从配置文件读取UV路径
        if CONFIG_FILE_PATH.exists():
            import toml
            config = toml.load(CONFIG_FILE_PATH)
            if 'environment' in config and 'uv_path' in config['environment']:
                uv_path = _PROJECT_ROOT / config['environment']['uv_path']
                if uv_path.exists():
                    return uv_path
    except Exception as e:
        print(f"[警告] 读取UV配置失败: {e}")
    
    # 回退到默认路径 - 使用统一路径管理
    try:
        from src.utils.paths import Paths
        default_uv_path = Paths.VENV / 'bin' / 'uv'
        if default_uv_path.exists():
            return default_uv_path
    except ImportError:
        # 如果无法导入Paths类，使用原始方式
        default_uv_path = _PROJECT_ROOT / '.venv' / 'bin' / 'uv'
        if default_uv_path.exists():
            return default_uv_path
    
    # 最后回退（但会发出警告）
    print("⚠️ 项目UV未找到，这违反了AQUA UV管理规范！")
    return None

# 获取pip/uv pip安装命令（严格遵循AQUA UV管理规范）
def get_pip_install_cmd():
    """传统pip命令生成函数（兼容性保留）"""
    mirror = get_best_mirror()
    
    # 强制使用项目UV
    project_uv = get_project_uv_path()
    if project_uv:
        base_cmd = [str(project_uv), 'pip', 'install']
        print(f"✅ 使用项目UV: {project_uv}")
    else:
        print("❌ 错误：未找到项目UV，违反AQUA环境管理规范！")
        print("   请检查.venv/bin/uv是否存在")
        base_cmd = [sys.executable, '-m', 'pip', 'install']
        print(f"⚠️ 降级使用系统pip: {sys.executable}")
    
    if mirror:
        base_cmd += ['-i', mirror['url'], '--trusted-host', mirror['host']]
    return base_cmd

def get_pip_install_cmd_from_config(network_mgr=None):
    """配置驱动的pip命令生成函数"""
    if not network_mgr:
        # 如果没有传入网络管理器，回退到传统方式
        return get_pip_install_cmd()
    
    try:
        # 使用配置驱动的网络管理器
        mirror = network_mgr.get_best_mirror_from_config()
        
        # 强制使用项目UV
        project_uv = get_project_uv_path()
        if project_uv:
            base_cmd = [str(project_uv), 'pip', 'install']
            if RICH_AVAILABLE and console:
                console.print(f"[green]✅ 使用项目UV: {project_uv}[/green]")
            else:
                print(f"✅ 使用项目UV: {project_uv}")
        else:
            if RICH_AVAILABLE and console:
                console.print("[red]❌ 错误：未找到项目UV，违反AQUA环境管理规范！[/red]")
                console.print("[yellow]   请检查.venv/bin/uv是否存在[/yellow]")
                console.print(f"[yellow]⚠️ 降级使用系统pip: {sys.executable}[/yellow]")
            else:
                print("❌ 错误：未找到项目UV，违反AQUA环境管理规范！")
                print("   请检查.venv/bin/uv是否存在")
                print(f"⚠️ 降级使用系统pip: {sys.executable}")
            base_cmd = [sys.executable, '-m', 'pip', 'install']
        
        if mirror:
            base_cmd += ['-i', mirror['url'], '--trusted-host', mirror['host']]
        return base_cmd
    except Exception as e:
        # 回退到传统方式
        if RICH_AVAILABLE and console:
            console.print(f"[yellow]⚠️ 配置驱动pip命令生成失败，回退到传统方式: {e}[/yellow]")
        else:
            print(f"⚠️ 配置驱动pip命令生成失败，回退到传统方式: {e}")
        return get_pip_install_cmd()

def install_single_dependency(dep, max_retries=3):
    """安装单个依赖包，支持多次重试和镜像源切换（传统版本，兼容性保留）"""
    for attempt in range(max_retries):
        try:
            print(f"安装: {dep} (尝试 {attempt + 1}/{max_retries})")
            cmd = get_pip_install_cmd() + [dep]
            print(f"执行: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=60)
            print(f"✅ {dep} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {dep} 安装失败 (尝试 {attempt + 1}): {e.stderr}")
            if attempt < max_retries - 1:
                print("   重试中...")
            continue
        except subprocess.TimeoutExpired:
            print(f"❌ {dep} 安装超时 (尝试 {attempt + 1})")
            if attempt < max_retries - 1:
                print("   重试中...")
            continue

def install_single_dependency_from_config(dep, dependency_mgr=None, network_mgr=None):
    """配置驱动的单个依赖包安装 - 中国网络优化版"""
    
    # 特别优化loguru的安装
    if 'loguru' in dep.lower():
        print(f"🔧 检测到loguru依赖，启用中国网络优化安装...")
        try:
            from scripts.china_network_optimizer import ChinaNetworkOptimizer
            optimizer = ChinaNetworkOptimizer(_PROJECT_ROOT)
            if optimizer.install_loguru_optimized():
                print(f"✅ {dep} (中国网络优化) 安装成功")
                return True
        except Exception as e:
            print(f"⚠️ 中国网络优化器失败，回退到标准安装: {e}")
    
    if not dependency_mgr or not network_mgr:
        # 回退到传统方式
        return install_single_dependency(dep)
    
    try:
        # 从配置获取重试和超时设置
        retry_config = dependency_mgr.get_retry_config()
        timeouts = network_mgr.get_network_timeouts()
        max_retries = retry_config['max_retries']
        install_timeout = timeouts['install']
        
        for attempt in range(max_retries):
            try:
                if RICH_AVAILABLE and console:
                    console.print(f"[blue]安装: {dep} (尝试 {attempt + 1}/{max_retries})[/blue]")
                else:
                    print(f"安装: {dep} (尝试 {attempt + 1}/{max_retries})")
                
                cmd = get_pip_install_cmd_from_config(network_mgr) + [dep]
                
                if RICH_AVAILABLE and console:
                    console.print(f"执行: {' '.join(cmd)}")
                else:
                    print(f"执行: {' '.join(cmd)}")
                
                result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=install_timeout)
                
                if RICH_AVAILABLE and console:
                    console.print(f"[green]✅ {dep} 安装成功[/green]")
                else:
                    print(f"✅ {dep} 安装成功")
                return True
                
            except subprocess.CalledProcessError as e:
                error_msg = f"❌ {dep} 安装失败 (尝试 {attempt + 1}): {e.stderr}"
                if RICH_AVAILABLE and console:
                    console.print(f"[yellow]{error_msg}[/yellow]")
                else:
                    print(error_msg)
                if attempt < max_retries - 1:
                    print("   重试中...")
                continue
            except subprocess.TimeoutExpired:
                timeout_msg = f"❌ {dep} 安装超时 (尝试 {attempt + 1}) - 超时设置: {install_timeout}s"
                if RICH_AVAILABLE and console:
                    console.print(f"[yellow]{timeout_msg}[/yellow]")
                else:
                    print(timeout_msg)
                if attempt < max_retries - 1:
                    print("   重试中...")
                continue
        
        return False
    except Exception as e:
        if RICH_AVAILABLE and console:
            console.print(f"[yellow]⚠️ 配置驱动安装失败，回退到传统方式: {e}[/yellow]")
        else:
            print(f"⚠️ 配置驱动安装失败，回退到传统方式: {e}")
        return install_single_dependency(dep)
    
    # 所有重试都失败了，尝试使用官方源
    print(f"⚠️ 所有镜像源都失败，尝试使用官方PyPI源安装 {dep}")
    try:
        # 使用项目UV（符合AQUA规范）
        project_uv = get_project_uv_path()
        if project_uv:
            print(f"  清除项目UV缓存...")
            subprocess.run([str(project_uv), 'cache', 'clean'], capture_output=True)
            cmd = [str(project_uv), 'pip', 'install', '--no-cache-dir', dep]
            print(f"✅ 使用项目UV: {project_uv}")
        else:
            # 只有在项目UV不存在时才使用系统pip（发出警告）
            print(f"⚠️ 项目UV不存在，违反AQUA规范！降级使用系统pip")
            print(f"  清除系统pip缓存...")
            subprocess.run([sys.executable, '-m', 'pip', 'cache', 'purge'], capture_output=True)
            cmd = [sys.executable, '-m', 'pip', 'install', '--no-cache-dir', dep]
        
        print(f"  执行: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=120)
        print(f"✅ {dep} 通过官方源安装成功")
        return True
    except Exception as e:
        print(f"❌ {dep} 官方源安装也失败: {e}")
        if hasattr(e, 'stderr') and e.stderr:
            print(f"   错误详情: {e.stderr}")
        return False

def install_bootstrap_dependencies():
    """安装基础依赖包，支持多镜像源和重试机制（兼容性函数）"""
    print("📦 开始安装基础依赖...")
    
    failed_deps = []
    for dep in BOOTSTRAP_DEPS:
        if not install_single_dependency(dep):
            failed_deps.append(dep)
    
    if failed_deps:
        print(f"\n❌ 以下依赖安装失败: {', '.join(failed_deps)}")
        print("\n🔧 手动修复建议:")
        print("1. 检查网络连接")
        print("2. 手动执行: source .venv/bin/activate && pip install --upgrade pip")
        print("3. 再次运行启动脚本")
        return False
    else:
        print("✅ 所有基础依赖安装完成")
        return True

def install_bootstrap_dependencies_from_config(dependency_mgr=None, network_mgr=None):
    """配置驱动的基础依赖包安装"""
    if not dependency_mgr or not network_mgr:
        # 回退到传统方式
        return install_bootstrap_dependencies()
    
    try:
        # 从配置获取依赖列表
        bootstrap_deps = dependency_mgr.get_bootstrap_dependencies()
        
        if RICH_AVAILABLE and console:
            console.print(f"[blue]📦 开始安装 {len(bootstrap_deps)} 个配置化基础依赖...[/blue]")
        else:
            print(f"📦 开始安装 {len(bootstrap_deps)} 个配置化基础依赖...")
        
        failed_deps = []
        for dep in bootstrap_deps:
            if not install_single_dependency_from_config(dep, dependency_mgr, network_mgr):
                failed_deps.append(dep)
        
        if failed_deps:
            if RICH_AVAILABLE and console:
                console.print(f"[red]❌ 以下配置化依赖安装失败: {', '.join(failed_deps)}[/red]")
            else:
                print(f"❌ 以下配置化依赖安装失败: {', '.join(failed_deps)}")
            return False
        else:
            if RICH_AVAILABLE and console:
                console.print("[green]✅ 所有配置化基础依赖安装完成[/green]")
            else:
                print("✅ 所有配置化基础依赖安装完成")
            return True
    except Exception as e:
        if RICH_AVAILABLE and console:
            console.print(f"[yellow]⚠️ 配置驱动基础依赖安装失败，回退到传统方式: {e}[/yellow]")
        else:
            print(f"⚠️ 配置驱动基础依赖安装失败，回退到传统方式: {e}")
        return install_bootstrap_dependencies()

# 配置驱动的依赖安装函数（在Rich导入后执行）
def install_dependencies_with_config_manager():
    """使用配置管理器安装依赖（传统版本）"""
    try:
        # 获取项目UV路径
        project_uv = get_project_uv_path()
        if not project_uv:
            print("❌ 未找到项目UV，回退到传统安装方式")
            return install_bootstrap_dependencies()
        
        # 使用配置驱动的依赖管理器
        return dependency_manager.install_dependencies_with_config(project_uv)
    except Exception as e:
        print(f"❌ 配置驱动依赖安装失败: {e}")
        print("🔄 回退到传统安装方式...")
        return install_bootstrap_dependencies()

def install_dependencies_with_config_managers():
    """使用配置管理器安装依赖（增强版本，Phase 3）"""
    try:
        # 检查管理器是否可用
        if 'dependency_manager' not in globals() or 'network_manager' not in globals():
            if RICH_AVAILABLE and console:
                console.print("[yellow]⚠️ 配置管理器未初始化，回退到传统方式[/yellow]")
            else:
                print("⚠️ 配置管理器未初始化，回退到传统方式")
            return install_bootstrap_dependencies()
        
        # 使用新的配置驱动基础依赖安装
        return install_bootstrap_dependencies_from_config(dependency_manager, network_manager)
    except Exception as e:
        if RICH_AVAILABLE and console:
            console.print(f"[yellow]⚠️ 增强配置驱动依赖安装失败，回退到传统方式: {e}[/yellow]")
        else:
            print(f"⚠️ 增强配置驱动依赖安装失败，回退到传统方式: {e}")
        return install_bootstrap_dependencies()

# 首先安装基础依赖（传统方式，保持兼容性）
if not install_bootstrap_dependencies():
    print("基础依赖安装失败，退出程序")
    sys.exit(1)

# 预留配置驱动依赖管理器初始化位置
# 将在Rich导入后初始化dependency_manager

# 动态导入依赖包
try:
    import toml
    from rich import box
    from rich.console import Console
    from rich.panel import Panel
    from rich.progress import (BarColumn, Progress, SpinnerColumn,
                               TaskProgressColumn, TextColumn)
    from rich.table import Table
    from rich.text import Text
    from rich.tree import Tree

    from scripts.logger import (exception_logger, log_error, log_info,
                                log_success, log_warning, set_log_file,
                                set_sensitive_keys)
    
    # 导入新的配置管理器
    from scripts.config_manager import AQUAConfigManager
    from scripts.dependency_manager import ConfigDrivenDependencyManager
    from scripts.network_manager import ConfigDrivenNetworkManager
    from scripts.config_validator import AQUAConfigValidator

    # 初始化Rich控制台
    console = Console()
    
    # 初始化配置管理器
    config_manager = AQUAConfigManager(CONFIG_FILE_PATH)
    dependency_manager = ConfigDrivenDependencyManager(config_manager)
    network_manager = ConfigDrivenNetworkManager(config_manager)
    config_validator = AQUAConfigValidator(config_manager)

    # 在Rich导入后，验证配置驱动管理器
    console.print("🔄 验证配置驱动管理器功能...")
    try:
        # 测试配置驱动功能
        bootstrap_deps = dependency_manager.get_bootstrap_dependencies()
        china_mirrors = dependency_manager.get_china_mirrors()
        timeouts = dependency_manager.get_network_timeouts()
        
        console.print(f"[green]✅ 从配置读取到 {len(bootstrap_deps)} 个基础依赖[/green]")
        console.print(f"[green]✅ 从配置读取到 {len(china_mirrors)} 个镜像源[/green]")
        
        # 验证网络管理器
        network_manager.validate_network_config()
        
        # 执行全面配置验证
        console.print("🔍 执行全面配置验证...")
        config_is_valid = config_validator.validate_all_configurations()
        
        if config_is_valid:
            console.print("[green]✅ 配置驱动管理器验证成功[/green]")
        else:
            validation_summary = config_validator.get_validation_summary()
            console.print(f"[yellow]⚠️ 配置验证发现问题: {validation_summary['critical']}个致命错误, {validation_summary['warning']}个警告[/yellow]")
            if validation_summary['critical'] > 0:
                console.print("[red]❌ 致命配置错误，可能影响系统运行[/red]")
                
    except Exception as e:
        console.print(f"[yellow]⚠️ 配置驱动管理器验证失败: {e}[/yellow]")
        console.print("[yellow]🔄 将继续使用传统方式...[/yellow]")

    # 设置日志文件和敏感信息
    set_log_file('env_init')
    set_sensitive_keys(['password', 'token', 'api_key'])

    # 显示启动信息
    console.print(Panel(
        "[bold blue]AQUA 躺赢之路[/bold blue]\n[dim]环境初始化与依赖检测[/dim]",
        title="🚀 启动",
        border_style="blue"
    ))

    # ... 其余代码保持不变 ...

except ImportError as e:
    print(f"导入依赖失败: {e}")
    print("请确保已正确安装所有基础依赖")
    sys.exit(1)

def print_system_info():
    """打印系统信息表格"""
    console.print("[blue]🔍 系统环境信息:[/blue]")
    table = Table(box=box.SIMPLE)
    table.add_column("项目", style="cyan")
    table.add_column("值", style="green")

    table.add_row("操作系统", platform.system())
    table.add_row("系统版本", platform.version())
    table.add_row("Python版本", sys.version.split()[0])
    table.add_row("Python路径", sys.executable)
    table.add_row("项目根目录", str(_PROJECT_ROOT))
    table.add_row("工作目录", str(Path.cwd()))

    console.print(table)

def print_config_info():
    """打印配置信息（使用统一配置管理器）"""
    try:
        console.print("[blue]🔍 项目配置信息:[/blue]")
        table = Table(box=box.SIMPLE)
        table.add_column("配置项", style="cyan")
        table.add_column("值", style="green")

        # 应用信息
        app_config = config_manager.get_app_config()
        table.add_row("应用名称", app_config.get('name', 'N/A'))
        table.add_row("版本", app_config.get('version', 'N/A'))
        table.add_row("默认环境", app_config.get('default_environment', 'N/A'))

        # 环境检测配置
        env_detection_config = config_manager.get_environment_detection_config()
        detection_sources = env_detection_config.get('detection_sources', [])
        table.add_row("环境检测源", ', '.join(detection_sources) if detection_sources else 'N/A')

        # 基础依赖配置
        bootstrap_config = config_manager.get_bootstrap_config()
        deps_count = len(bootstrap_config.get('dependencies', []))
        table.add_row("配置化依赖数", str(deps_count) if deps_count > 0 else 'N/A')

        # 网络配置
        network_config = config_manager.get_network_config()
        mirrors_count = len(network_config.get('china_mirrors', []))
        table.add_row("配置化镜像源数", str(mirrors_count) if mirrors_count > 0 else 'N/A')

        console.print(table)
    except Exception as e:
        console.print(f"[yellow]⚠️ 配置读取失败: {e}[/yellow]")

def print_project_structure():
    """打印项目结构树"""
    tree = Tree("📁 AQUA_V1.0")

    # 添加主要目录
    config_tree = tree.add("📋 config/")
    config_tree.add("settings.toml")

    docs_tree = tree.add("📚 docs/")
    docs_tree.add("AK_MYSQL_DATA_DICT.MD")

    logs_tree = tree.add("📝 logs/")
    logs_tree.add("*.log")

    scripts_tree = tree.add("🔧 scripts/")
    scripts_tree.add("env_init.py")
    scripts_tree.add("logger.py")

    src_tree = tree.add("💻 src/")
    src_tree.add("core/")
    src_tree.add("interfaces/")
    src_tree.add("modules/")
    src_tree.add("utils/")

    tests_tree = tree.add("🧪 tests/")
    tests_tree.add("test_env_init.py")

    # 添加根目录文件
    tree.add("📄 requirements.txt")
    tree.add("📄 .gitignore")

    console.print(Panel(tree, title="📂 项目结构", border_style="blue"))

@exception_logger
def check_uv_installed() -> bool:
    """检查 uv 命令是否在系统 PATH 中可用。"""
    try:
        result = subprocess.run(['uv', '--version'], check=True, capture_output=True, text=True)
        version = result.stdout.strip()
        console.print(f"[green]✅ UV工具已安装: {version}[/green]")
        log_info('UV工具已安装', {'version': version, 'path': shutil.which("uv")})
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        console.print("[red]❌ UV工具未安装[/red]")
        return False

@exception_logger
def install_uv():
    """尝试使用 pip 或 pipx 安装 uv。"""
    console.print("[yellow]🔄 正在安装UV工具...[/yellow]")
    log_info('正在安装UV工具...')

    install_commands = [
        [sys.executable, '-m', 'pip', 'install', 'uv'],
        ['pipx', 'install', 'uv']
    ]

    for i, cmd in enumerate(install_commands, 1):
        try:
            console.print(f"[blue]📦 尝试方法 {i}: {' '.join(cmd)}[/blue]")
            log_info(f'尝试安装命令: {" ".join(cmd)}')
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            if check_uv_installed():
                console.print("[green]✅ UV工具安装成功[/green]")
                log_success('UV工具安装成功')
                return
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            console.print(f"[yellow]⚠️ 方法 {i} 失败: {e}[/yellow]")
            log_warning(f'UV安装失败: {" ".join(cmd)}', {'error': str(e)})

    console.print("[red]❌ UV工具安装失败[/red]")
    log_error('UV工具安装失败', {'suggestion': '请访问 https://github.com/astral-sh/uv 手动安装'})
    sys.exit(1)

def is_conda_env_active() -> bool:
    """检查是否在 Conda 环境中。"""
    return 'CONDA_PREFIX' in os.environ

def detect_all_venvs() -> list[Path]:
    """检测项目目录下所有的 venv 和 .venv 文件夹。"""
    return list(Path.cwd().glob('**/venv')) + list(Path.cwd().glob('**/.venv'))

@exception_logger
def clean_other_venvs(venvs: list[Path], project_venv_path: Path):
    """清理除项目主 .venv 之外的其他虚拟环境。"""
    if len(venvs) <= 1:
        return

    console.print("[yellow]🧹 检测到多个虚拟环境，开始清理...[/yellow]")

    for venv in venvs:
        resolved_venv = venv.resolve()
        if resolved_venv != project_venv_path:
            try:
                console.print(f"[blue]🗑️ 删除: {resolved_venv}[/blue]")
                log_info(f'正在删除多余的虚拟环境: {resolved_venv}')
                shutil.rmtree(resolved_venv)
                console.print(f"[green]✅ 已删除: {resolved_venv}[/green]")
                log_success(f'已删除虚拟环境: {resolved_venv}')
            except Exception as e:
                console.print(f"[red]❌ 删除失败: {resolved_venv}[/red]")
                log_error(f'虚拟环境删除失败: {resolved_venv}，请手动删除', {'error': str(e)})
                sys.exit(1)

def project_venv_exists() -> bool:
    """检查项目的 .venv 是否存在。"""
    return VENV_PATH.is_dir()

def get_pip_index_url() -> str:
    """从 config/settings.toml 读取 pip 镜像源，如果失败则返回默认值。"""
    default_url = "https://mirrors.aliyun.com/pypi/simple/"
    if not CONFIG_FILE_PATH.exists():
        return default_url
    try:
        config = toml.load(CONFIG_FILE_PATH)
        return config.get('pip', {}).get('index_url', default_url)
    except Exception as e:
        console.print(f"[yellow]⚠️ 配置文件解析失败，使用默认镜像源: {e}[/yellow]")
        log_warning(f'配置文件解析失败，使用默认pip镜像源', {'error': str(e), 'path': str(CONFIG_FILE_PATH)})
        return default_url

def verify_site_packages() -> bool:
    """验证 site-packages 路径是否正确指向虚拟环境，仅作警告不阻断。"""
    try:
        # 移除 check=True，手动检查返回码
        result = subprocess.run(
            [sys.executable, '-c', 'import site; print("\\n".join(site.getsitepackages()))'],
            capture_output=True, text=True, encoding='utf-8'
        )

        if result.returncode != 0:
            console.print(f"[red]❌ site-packages 验证命令执行失败 (返回码: {result.returncode})[/red]")
            console.print(Panel(result.stderr, title="详细错误信息", border_style="red"))
            log_warning('site-packages 验证命令失败', {'returncode': result.returncode, 'stderr': result.stderr})
            return False

        site_packages_list = [Path(p.strip()) for p in result.stdout.strip().split('\n') if p]
        expected_path = VENV_PATH / ('Lib' if platform.system() == 'Windows' else 'lib') / 'site-packages'

        console.print("[blue]🔍 Site-packages 路径验证:[/blue]")
        table = Table(box=box.SIMPLE)
        table.add_column("项目", style="cyan")
        table.add_column("值", style="green")
        table.add_row("当前路径", "\n".join(str(p) for p in site_packages_list))
        table.add_row("预期路径", str(expected_path))
        console.print(table)

        paths_match = any(p.resolve() == expected_path.resolve() for p in site_packages_list)
        console.print(f"路径匹配: {'✅' if paths_match else '❌'}")

        if not paths_match:
            log_warning('site-packages 路径不匹配', {
                'current': [str(p) for p in site_packages_list],
                'expected': str(expected_path)
            })
            console.print("[yellow]⚠️ site-packages路径不匹配，仅警告不阻断。[/yellow]")

        # 即使不匹配也返回True，仅作警告
        return True

    except Exception as e:
        console.print(f"[red]❌ site-packages 验证时发生意外错误: {str(e)}[/red]")
        log_warning('site-packages 验证时发生意外错误', {'error': str(e)})
        return False

def is_project_venv_activated() -> bool:
    """
    检查项目的 .venv 是否被激活。
    最佳实践：比较 sys.prefix 和 sys.base_prefix，并确保 sys.prefix 指向本项目的 .venv
    """
    # 在测试模式下，始终返回 True
    if os.environ.get('TEST_MODE') == '1':
        return True

    # 1. 检查是否有任何虚拟环境处于激活状态
    # 如果 sys.prefix 和 sys.base_prefix 相同，则表示在全局环境中运行
    is_any_venv_active = sys.prefix != sys.base_prefix

    # 2. 检查激活的是否为本项目的虚拟环境
    is_project_venv_active = is_any_venv_active and (Path(sys.prefix).resolve() == VENV_PATH.resolve())

    # 3. 打印详细的诊断信息
    console.print("[blue]🔍 虚拟环境激活状态诊断 (最佳实践版):[/blue]")
    table = Table(box=box.SIMPLE, show_header=False)
    table.add_column("项目", style="cyan", no_wrap=True)
    table.add_column("值", style="green")

    table.add_row("项目.venv路径", str(VENV_PATH.resolve()))
    table.add_row("当前sys.prefix", str(Path(sys.prefix).resolve()))
    table.add_row("全局sys.base_prefix", str(Path(sys.base_prefix).resolve()))
    table.add_row("任何venv已激活?", "✅" if is_any_venv_active else "❌")
    table.add_row("本项目.venv已激活?", "✅" if is_project_venv_active else "❌")
    console.print(table)

    # 4. 如果未激活，给出明确提示
    if not is_project_venv_active:
        if is_any_venv_active:
            console.print(f"[yellow]⚠️  当前在另一个虚拟环境中运行: {sys.prefix}[/yellow]")
        else:
            console.print("[yellow]⚠️  当前在全局Python环境中运行。[/yellow]")

        console.print(Panel(
            "脚本将尝试自动切换到项目虚拟环境...",
            title="提示",
            border_style="yellow"
        ))

    return is_project_venv_active

def _reexec_into_venv():
    """如果脚本当前不在.venv中运行，则尝试找到.venv的python并使用execv重启自身。"""
    vpy = VENV_PATH / ('Scripts' if os.name == 'nt' else 'bin') / ('python.exe' if os.name == 'nt' else 'python')
    console.print(f"DEBUG: 尝试自举到 {vpy}")
    if vpy.exists():
        console.print("[yellow]⚠️ 检测到未激活，将自动切换至 .venv 重新执行脚本[/yellow]")
        try:
            os.execv(str(vpy), [str(vpy), *sys.argv])
        except OSError as e:
            console.print(f"[red]❌ 自举失败: {e}[/red]")
            log_error('自举到.venv失败', {'error': str(e)})
            # 如果失败，回退手动提示
            activate_project_venv()
            sys.exit(1)
    else:
        console.print("[red]❌ 未找到.venv解释器，请手动激活虚拟环境。[/red]")
        sys.exit(1)

def activate_project_venv():
    """为用户提供清晰的虚拟环境激活指令。"""
    os_type = platform.system()

    console.print("[red]❌ 虚拟环境未激活[/red]")

    if os_type == 'Windows':
        activate_script = VENV_PATH / 'Scripts' / 'activate'
        console.print(Panel(
            f"请在当前终端执行:\n[green]{activate_script}[/green]",
            title="🔧 激活虚拟环境",
            border_style="red"
        ))
    else:  # Linux or macOS
        activate_script = VENV_PATH / 'bin' / 'activate'
        console.print(Panel(
            f"请在当前终端执行:\n[green]source {activate_script}[/green]",
            title="🔧 激活虚拟环境",
            border_style="red"
        ))

    log_error('虚拟环境未激活', {
        'action': '请在终端执行',
        'command': f'{activate_script}' if os_type == 'Windows' else f'source {activate_script}'
    })

@exception_logger
def compile_dependencies() -> bool:
    """
    使用 uv pip compile 生成精确的依赖清单，具备跨平台兼容性和回退机制。
    """
    if not REQUIREMENTS_PATH.exists():
        console.print("[yellow]⚠️ 未找到requirements.txt，跳过依赖编译[/yellow]")
        log_info('未找到requirements.txt，跳过依赖编译')
        return True

    # 检测是否需要编译（如果requirements.txt已经是编译后的版本，跳过编译）
    try:
        with open(REQUIREMENTS_PATH, 'r', encoding='utf-8') as f:
            content = f.read()
            if "# This file was autogenerated by uv" in content:
                console.print("[green]✅ requirements.txt已经是编译版本，跳过编译[/green]")
                log_info('requirements.txt已经是编译版本，跳过编译')
                return True
    except Exception:
        pass

    # 使用中国网络优化器获取最佳镜像源
    mirror_sources = []
    try:
        from scripts.china_network_optimizer import ChinaNetworkOptimizer
        optimizer = ChinaNetworkOptimizer(_PROJECT_ROOT)
        best_mirrors = optimizer.get_best_mirrors(max_mirrors=4)
        mirror_sources = [mirror['url'] for mirror in best_mirrors]
        console.print("[blue]🌐 使用中国网络优化器获取的镜像源[/blue]")
    except Exception as e:
        console.print(f"[yellow]⚠️ 中国网络优化器不可用，使用默认镜像源: {e}[/yellow]")
        # 回退到默认镜像源配置
        mirror_sources = [
            "https://pypi.tuna.tsinghua.edu.cn/simple",
            "https://mirrors.aliyun.com/pypi/simple/",
            "https://pypi.python.org/simple/",
            "https://pypi.org/simple/"
        ]
    
    # 尝试从配置文件读取首选镜像源
    try:
        if CONFIG_FILE_PATH.exists():
            config = toml.load(CONFIG_FILE_PATH)
            configured_url = config.get('pip', {}).get('index_url')
            if configured_url and configured_url not in mirror_sources:
                mirror_sources.insert(0, configured_url)
    except Exception:
        pass

    # 备份原始requirements.txt
    backup_path = REQUIREMENTS_PATH.with_suffix('.txt.bak')
    try:
        shutil.copy2(REQUIREMENTS_PATH, backup_path)
        console.print(f"[blue]💾 已备份原始依赖文件至: {backup_path}[/blue]")
    except Exception as e:
        console.print(f"[yellow]⚠️ 备份失败，继续执行: {e}[/yellow]")

    console.print("[blue]📦 开始编译依赖（支持多镜像源回退）...[/blue]")

    # 尝试多个镜像源
    for i, index_url in enumerate(mirror_sources, 1):
        try:
            console.print(f"[yellow]🔄 尝试镜像源 {i}/{len(mirror_sources)}: {index_url}[/yellow]")
            
            # 添加trusted-host参数以解决SSL问题
            trusted_host = index_url.split('/')[2]  # 提取主机名
            compile_cmd = [
                'uv', 'pip', 'compile',
                str(REQUIREMENTS_PATH),
                '--output-file', str(REQUIREMENTS_PATH),
                '--index-url', index_url,
                '--trusted-host', trusted_host,
                '--upgrade',
                '--no-cache'  # 避免缓存问题
            ]

            # 设置合理的超时时间
            result = subprocess.run(
                compile_cmd, 
                check=True, 
                capture_output=True, 
                text=True,
                timeout=120  # 2分钟超时
            )

            # 编译成功
            console.print("[green]✅ 依赖编译成功[/green]")
            if result.stdout:
                console.print("[blue]📋 编译输出:[/blue]")
                console.print(Panel(result.stdout, title="编译详情", border_style="blue"))
            
            log_success('依赖编译成功', {'mirror': index_url, 'attempt': i})
            return True

        except subprocess.TimeoutExpired:
            console.print(f"[red]❌ 镜像源 {i} 超时（2分钟）[/red]")
            log_warning(f'镜像源{i}编译超时', {'mirror': index_url})
            continue
            
        except subprocess.CalledProcessError as e:
            console.print(f"[red]❌ 镜像源 {i} 编译失败[/red]")
            if e.stderr:
                console.print(Panel(e.stderr, title=f"镜像源 {i} 错误详情", border_style="red"))
            log_warning(f'镜像源{i}编译失败', {'mirror': index_url, 'error': e.stderr})
            
            # 如果是网络连接问题，继续尝试下一个镜像源
            if any(keyword in str(e.stderr).lower() for keyword in ['connection', 'timeout', 'network', 'refused']):
                continue
            # 如果是包版本冲突等非网络问题，也尝试下一个镜像源
            elif any(keyword in str(e.stderr).lower() for keyword in ['version', 'conflict', 'incompatible']):
                continue
            else:
                # 其他类型错误也继续尝试
                continue
                
        except Exception as e:
            console.print(f"[red]❌ 镜像源 {i} 发生意外错误: {e}[/red]")
            log_warning(f'镜像源{i}意外错误', {'mirror': index_url, 'error': str(e)})
            continue

    # 所有镜像源都失败了
    console.print("[red]❌ 所有镜像源编译失败，启用回退策略[/red]")
    
    # 回退策略：恢复原始文件并跳过编译
    try:
        if backup_path.exists():
            shutil.copy2(backup_path, REQUIREMENTS_PATH)
            console.print("[yellow]🔄 已恢复原始requirements.txt[/yellow]")
    except Exception:
        pass
    
    # 提供用户友好的建议
    console.print(Panel(
        "编译失败的可能原因和解决方案:\n\n"
        "🌐 网络问题:\n"
        "  • 检查网络连接是否正常\n"
        "  • 确认防火墙或代理设置\n"
        "  • 尝试使用VPN或更换网络环境\n\n"
        "📦 包依赖问题:\n"
        "  • 检查requirements.txt中包名是否正确\n"
        "  • 验证包版本兼容性\n"
        "  • 考虑使用requirements-dev.txt替代\n\n"
        "🔧 回退方案:\n"
        "  • 依赖编译已跳过，将使用现有依赖版本\n"
        "  • 可以手动运行: pip install -r requirements.txt\n"
        "  • 或使用快速启动: python quick_start.py",
        title="🔧 故障排除建议",
        border_style="yellow"
    ))
    
    log_warning('依赖编译失败，已启用回退策略', {
        'suggestion': '建议检查网络连接或手动安装依赖',
        'fallback': '跳过编译继续执行'
    })
    
    # 返回True表示虽然编译失败，但不阻止后续流程
    return True

@exception_logger
def sync_dependencies() -> bool:
    """
    使用 uv pip sync 同步依赖，具备多镜像源回退和跨平台兼容性。
    """
    if not REQUIREMENTS_PATH.exists():
        console.print("[yellow]⚠️ 未找到requirements.txt，跳过依赖同步[/yellow]")
        log_info('未找到requirements.txt，跳过依赖同步')
        return True

    # 使用中国网络优化器获取最佳镜像源（与compile_dependencies保持一致）
    mirror_sources = []
    try:
        from scripts.china_network_optimizer import ChinaNetworkOptimizer
        optimizer = ChinaNetworkOptimizer(_PROJECT_ROOT)
        best_mirrors = optimizer.get_best_mirrors(max_mirrors=4)
        mirror_sources = [mirror['url'] for mirror in best_mirrors]
        console.print("[blue]🌐 使用中国网络优化器获取的镜像源[/blue]")
    except Exception as e:
        console.print(f"[yellow]⚠️ 中国网络优化器不可用，使用默认镜像源: {e}[/yellow]")
        # 回退到默认镜像源配置
        mirror_sources = [
            "https://pypi.tuna.tsinghua.edu.cn/simple",
            "https://mirrors.aliyun.com/pypi/simple/",
            "https://pypi.python.org/simple/",
            "https://pypi.org/simple/"
        ]
    
    # 尝试从配置文件读取首选镜像源
    try:
        if CONFIG_FILE_PATH.exists():
            config = toml.load(CONFIG_FILE_PATH)
            configured_url = config.get('pip', {}).get('index_url')
            if configured_url and configured_url not in mirror_sources:
                mirror_sources.insert(0, configured_url)
    except Exception:
        pass

    console.print("[blue]📦 开始同步依赖（支持多镜像源回退）...[/blue]")

    # 尝试多个镜像源
    for i, index_url in enumerate(mirror_sources, 1):
        console.print(f"[yellow]🔄 尝试镜像源 {i}/{len(mirror_sources)}: {index_url}[/yellow]")
        
        # 每个镜像源尝试最多2次
        for attempt in range(1, 3):
            try:
                # 添加trusted-host参数以解决SSL问题
                trusted_host = index_url.split('/')[2]  # 提取主机名
                command = ['uv', 'pip', 'sync', str(REQUIREMENTS_PATH), '--index-url', index_url, '--trusted-host', trusted_host]
                
                console.print(f"[blue]📦 镜像源 {i} 第 {attempt} 次尝试...[/blue]")
                log_info(f'镜像源{i}第{attempt}次同步尝试', {'command': ' '.join(command), 'mirror': index_url})

                # 使用进度条显示同步过程
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TaskProgressColumn(),
                    console=console
                ) as progress:
                    task = progress.add_task(f"同步依赖包（镜像源 {i}）...", total=None)

                    result = subprocess.run(
                        command, 
                        check=True, 
                        capture_output=True, 
                        text=True, 
                        encoding='utf-8',
                        timeout=180  # 3分钟超时
                    )
                    progress.update(task, completed=True)

                # 同步成功
                console.print("[green]✅ 依赖同步成功[/green]")
                log_success('依赖同步成功', {'mirror': index_url, 'mirror_index': i, 'attempt': attempt})

                # 显示安装的包信息
                if result.stdout:
                    console.print("[blue]📋 安装详情:[/blue]")
                    console.print(Panel(result.stdout, title="安装输出", border_style="green"))
                if result.stderr:
                    console.print("[blue]📋 警告信息:[/blue]")
                    console.print(Panel(result.stderr, title="警告输出", border_style="yellow"))

                return True

            except subprocess.TimeoutExpired:
                console.print(f"[red]❌ 镜像源 {i} 第 {attempt} 次超时（3分钟）[/red]")
                log_warning(f'镜像源{i}第{attempt}次同步超时', {'mirror': index_url})
                if attempt < 2:
                    console.print("[yellow]🔄 重试中...[/yellow]")
                    continue
                else:
                    break  # 尝试下一个镜像源
                    
            except subprocess.CalledProcessError as e:
                console.print(f"[red]❌ 镜像源 {i} 第 {attempt} 次同步失败[/red]")
                if e.stderr:
                    console.print(Panel(e.stderr, title=f"镜像源 {i} 错误详情", border_style="red"))
                log_warning(f'镜像源{i}第{attempt}次同步失败', {'mirror': index_url, 'error': e.stderr})
                
                # 如果是网络连接问题，继续重试或尝试下一个镜像源
                if any(keyword in str(e.stderr).lower() for keyword in ['connection', 'timeout', 'network', 'refused']):
                    if attempt < 2:
                        console.print("[yellow]🔄 网络问题，重试中...[/yellow]")
                        continue
                    else:
                        break  # 尝试下一个镜像源
                # 如果是包版本冲突等问题
                elif any(keyword in str(e.stderr).lower() for keyword in ['version', 'conflict', 'incompatible', 'not found']):
                    if attempt < 2:
                        console.print("[yellow]🔄 依赖问题，重试中...[/yellow]")
                        continue
                    else:
                        break  # 尝试下一个镜像源
                else:
                    # 其他类型错误
                    if attempt < 2:
                        continue
                    else:
                        break  # 尝试下一个镜像源
                        
            except Exception as e:
                console.print(f"[red]❌ 镜像源 {i} 第 {attempt} 次发生意外错误: {e}[/red]")
                log_warning(f'镜像源{i}第{attempt}次意外错误', {'mirror': index_url, 'error': str(e)})
                if attempt < 2:
                    continue
                else:
                    break  # 尝试下一个镜像源

    # 所有镜像源都失败了，启用回退策略
    console.print("[red]❌ 所有镜像源同步失败，尝试回退策略[/red]")
    
    # 回退策略1：尝试使用标准pip安装
    try:
        console.print("[yellow]🔄 尝试回退策略：使用pip安装...[/yellow]")
        pip_command = [sys.executable, '-m', 'pip', 'install', '-r', str(REQUIREMENTS_PATH)]
        
        # 尝试清华源
        pip_command.extend(['-i', 'https://pypi.tuna.tsinghua.edu.cn/simple'])
        
        result = subprocess.run(pip_command, check=True, capture_output=True, text=True, timeout=300)
        console.print("[green]✅ pip回退安装成功[/green]")
        log_success('pip回退安装成功')
        return True
        
    except Exception as e:
        console.print(f"[red]❌ pip回退安装也失败: {e}[/red]")
        log_warning('pip回退安装失败', {'error': str(e)})

    # 最终失败，提供用户友好的建议
    console.print(Panel(
        "依赖同步失败的可能原因和解决方案:\n\n"
        "🌐 网络问题:\n"
        "  • 检查网络连接是否正常\n"
        "  • 确认防火墙或代理设置\n"
        "  • 尝试使用VPN或更换网络环境\n\n"
        "📦 依赖问题:\n"
        "  • 检查requirements.txt格式是否正确\n"
        "  • 验证Python版本与依赖包兼容性\n"
        "  • 考虑删除.venv重新创建虚拟环境\n\n"
        "🔧 手动解决方案:\n"
        "  • 手动运行: pip install -r requirements.txt\n"
        "  • 或逐个安装主要依赖: pip install fastapi duckdb polars\n"
        "  • 使用快速启动: python quick_start.py",
        title="🔧 故障排除建议",
        border_style="yellow"
    ))
    
    log_error('依赖同步失败', {
        'suggestion': '建议检查网络连接或手动安装依赖',
        'fallback_available': 'python quick_start.py'
    })
    
    # 在开发环境下，即使同步失败也不阻止启动流程
    current_env = os.environ.get('AQUA_ENV', 'dev')
    if current_env == 'dev':
        console.print("[yellow]⚠️ 开发环境模式：跳过依赖同步失败，继续启动流程[/yellow]")
        console.print("[blue]💡 提示：可以稍后手动安装依赖或使用 python quick_start.py[/blue]")
        return True
    
    return False

def create_project_venv():
    """
    自动创建项目主虚拟环境，支持Win/OSX，路径和参数从settings.toml读取。
    """
    if VENV_PATH.exists():
        console.print("[green]✅ .venv虚拟环境已存在，无需创建[/green]")
        return
    console.print("[yellow]🔧 .venv虚拟环境不存在，正在自动创建...[/yellow]")
    log_info("开始创建.venv虚拟环境", {"venv_path": str(VENV_PATH)})
    try:
        # 读取python版本（如有）
        python_version = None
        if CONFIG_FILE_PATH.exists():
            config = toml.load(CONFIG_FILE_PATH)
            python_version = config.get("python", {}).get("version", None)
        # 优先用当前python解释器
        python_exec = sys.executable
        # 兼容Win/OSX
        subprocess.run([python_exec, "-m", "venv", str(VENV_PATH)], check=True)
        console.print("[green]✅ .venv虚拟环境创建成功[/green]")
        log_success(".venv虚拟环境创建成功", {"venv_path": str(VENV_PATH)})
    except Exception as e:
        console.print(f"[red]❌ .venv虚拟环境创建失败: {e}[/red]")
        log_error(".venv虚拟环境创建失败", {"error": str(e)})
        sys.exit(1)

import duckdb

def get_current_environment() -> str:
    """
    自动检测当前环境（使用统一配置管理器）
    """
    try:
        return config_manager.detect_environment()
    except Exception as e:
        console.print(f"[yellow]⚠️ 环境检测失败，使用默认环境: dev ({e})[/yellow]")
        log_warning(f'环境检测失败，使用默认环境', {'error': str(e)})
        return 'dev'

def validate_environment_config(environment: str) -> bool:
    """验证环境配置完整性（使用统一配置管理器）"""
    try:
        return config_manager.validate_environment_config(environment)
    except Exception as e:
        console.print(f"[red]❌ 环境配置验证失败: {e}[/red]")
        log_error('环境配置验证失败', {'error': str(e)})
        return False

def init_database(environment: str = None):
    """
    自动初始化数据库（DuckDB），支持多环境配置，路径从settings.toml读取。
    """
    if not environment:
        environment = get_current_environment()
    
    console.print(f"[blue]🔧 初始化数据库 - 环境: {environment}[/blue]")
    
    # 验证环境配置
    if not validate_environment_config(environment):
        log_error(f"环境配置验证失败: {environment}")
        sys.exit(1)
    
    try:
        # 使用配置管理器获取环境配置
        env_config = config_manager.get_environment_config(environment)
        db_conf = env_config.database
        
        # 获取数据库路径 - 使用占位符而非硬编码
        db_path = db_conf.get("path", f"{{datacenter_dir}}/aqua_{environment}.duckdb")

        # 支持绝对路径和相对路径
        if Path(db_path).is_absolute():
            db_path_abs = Path(db_path).resolve()
        else:
            # 如果包含占位符，需要先解析
            if '{' in db_path:
                try:
                    from src.utils.paths import Paths
                    resolved_path = Paths.resolve_placeholder(db_path)
                    db_path_abs = Path(resolved_path).resolve()
                except ImportError:
                    # 回退到原始方式
                    db_path_abs = (_PROJECT_ROOT / db_path).resolve()
            else:
                db_path_abs = (_PROJECT_ROOT / db_path).resolve()
        
        db_dir = db_path_abs.parent
        
        # 确保目录存在
        if not db_dir.exists():
            console.print(f"[blue]📁 创建数据库目录: {db_dir}[/blue]")
            db_dir.mkdir(parents=True, exist_ok=True)
            log_info(f"创建数据库目录", {"dir": str(db_dir)})
        
        # 检查数据库是否已存在
        if db_path_abs.exists():
            console.print(f"[green]✅ 数据库已存在: {db_path_abs}[/green]")
            log_info(f"数据库已存在", {"db_path": str(db_path_abs), "environment": environment})
            return
        
        # 检查是否允许自动创建
        auto_create = db_conf.get("auto_create", True)
        if not auto_create:
            console.print(f"[yellow]⚠️ 环境 '{environment}' 不允许自动创建数据库[/yellow]")
            console.print(f"[yellow]   请手动创建数据库文件: {db_path_abs}[/yellow]")
            log_warning(f"数据库自动创建被禁用", {"environment": environment, "path": str(db_path_abs)})
            return
        
        # 创建数据库文件
        console.print(f"[yellow]🔧 正在初始化数据库: {db_path_abs}[/yellow]")
        log_info(f"正在初始化数据库", {"db_path": str(db_path_abs), "environment": environment})
        
        # 连接即自动创建
        conn = duckdb.connect(str(db_path_abs))
        
        # 可扩展：如有初始化SQL，可在此执行
        if "init_sql" in db_conf:
            console.print(f"[blue]📝 执行初始化SQL...[/blue]")
            conn.execute(db_conf["init_sql"])
            log_info("执行数据库初始化SQL")
        
        conn.close()
        console.print(f"[green]✅ 数据库初始化完成: {db_path_abs}[/green]")
        log_success(f"数据库初始化完成", {"db_path": str(db_path_abs), "environment": environment})
        
    except Exception as e:
        console.print(f"[red]❌ 数据库初始化失败: {e}[/red]")
        log_error(f"数据库初始化失败", {"error": str(e), "environment": environment})
        sys.exit(1)

# ========== 目录初始化逻辑 ========== #
def init_project_dirs(root_dir=None):
    """自动创建项目标准目录结构，并为每个目录生成README.md
    Args:
        root_dir (Path or None): 目录根路径，None时用项目根目录
    """
    if root_dir is None:
        root_dir = _PROJECT_ROOT
    # 使用统一路径管理定义目录结构
    try:
        from src.utils.paths import Paths
        # 基于Paths类定义的标准目录结构
        dirs = [
            "src/core", "src/modules", "src/interfaces", "src/utils", "src/jobs",
            "frontend/src/modules", "frontend/src/components", "frontend/src/stores", "frontend/src/api", "frontend/src/router", "frontend/src/assets",
            "frontend/tests/mock", "frontend/dist",
            str(Paths.CONFIG.relative_to(Paths.ROOT) / "env"),
            str(Paths.RAW_DATA.relative_to(Paths.ROOT)),
            str(Paths.PROCESSED_DATA.relative_to(Paths.ROOT)),
            str(Paths.BACKUP.relative_to(Paths.ROOT)),
            "scripts/git_hooks",
            str(Paths.DOCS.relative_to(Paths.ROOT) / "ai_prompts"),
            str(Paths.DOCS.relative_to(Paths.ROOT) / "tasks"),
            str(Paths.LOGS.relative_to(Paths.ROOT)),
            str(Paths.TESTS.relative_to(Paths.ROOT) / "unit"),
            str(Paths.TESTS.relative_to(Paths.ROOT) / "integration"),
            str(Paths.TESTS.relative_to(Paths.ROOT) / "e2e")
        ]
    except ImportError:
        # 回退到硬编码方式
        dirs = [
            "src/core", "src/modules", "src/interfaces", "src/utils", "src/jobs",
            "frontend/src/modules", "frontend/src/components", "frontend/src/stores", "frontend/src/api", "frontend/src/router", "frontend/src/assets",
            "frontend/tests/mock", "frontend/dist",
            "config/env",
            "data/raw", "data/processed", "data/backup",
            "scripts/git_hooks",
            "docs/ai_prompts", "docs/tasks",
            "logs",
            "tests/unit", "tests/integration", "tests/e2e"
        ]
    for d in dirs:
        path = root_dir / d
        path.mkdir(parents=True, exist_ok=True)
        # 自动生成README.md
        readme = path / "README.md"
        if not readme.exists():
            readme.write_text(f"# {d} 目录说明\n\n用途：详见项目README和蓝图。", encoding="utf-8")

def main(check_only: bool = False):
    """主函数
    Args:
        check_only (bool): 是否仅检测模式（不做修复/安装/变更）
    """
    print("DEBUG: 脚本开始执行")

    # 0. 环境检测（新增）
    current_environment = get_current_environment()
    console.print(f"\n[bold blue]🔍 当前工作环境: {current_environment}[/bold blue]")
    
    # 0.1 目录初始化（TDD模式，幂等）
    init_project_dirs()

    log_info('=== AQUA环境初始化开始 ===', {
        'os': platform.system(), 
        'check_only': check_only,
        'environment': current_environment
    })

    # 1. 显示系统信息
    print("DEBUG: 开始显示系统信息")
    print_system_info()
    print_config_info()
    print_project_structure()

    console.print("\n" + "="*60 + "\n")

    # 2. 确保 uv 已安装
    console.print("[bold]🔧 步骤 1: 检查UV工具[/bold]")
    uv_ok = check_uv_installed()
    if not uv_ok and check_only:
        console.print("[red]❌ UV工具未安装（仅检测模式）[/red]")
        sys.exit(1)
    elif not uv_ok:
        install_uv()

    # 3. 检查并处理 Conda 环境
    console.print("\n[bold]🔧 步骤 2: 检查Conda环境[/bold]")
    conda_conflict = is_conda_env_active()
    if conda_conflict:
        console.print(Panel(
            "检测到Conda环境，请执行以下步骤:\n1. conda deactivate\n2. 重新运行此脚本",
            title="⚠️ Conda环境冲突",
            border_style="red"
        ))
        log_error('检测到Conda环境', {
            'action': '请执行以下步骤',
            'step1': 'conda deactivate',
            'step2': '重新运行此脚本'
        })
        sys.exit(1)
    else:
        console.print("[green]✅ 未检测到Conda环境[/green]")

    # 4. 清理多余的虚拟环境
    console.print("\n[bold]🔧 步骤 3: 检查虚拟环境[/bold]")
    venvs = detect_all_venvs()
    if len(venvs) > 1 and not check_only:
        clean_other_venvs(venvs, VENV_PATH)
    else:
        console.print("[green]✅ 虚拟环境状态正常[/green]")

    # 5. 创建主虚拟环境（如果不存在）
    venv_exists = project_venv_exists()
    if not venv_exists and check_only:
        console.print("[red]❌ .venv虚拟环境不存在（仅检测模式）[/red]")
        sys.exit(1)
    elif not venv_exists:
        create_project_venv()
    else:
        console.print("[green]✅ .venv虚拟环境已存在[/green]")

    # 新增：数据库初始化（多环境支持）
    console.print("\n[bold]🔧 步骤 3.5: 初始化数据库[/bold]")
    if not check_only:
        init_database(current_environment)
    else:
        console.print(f"[yellow]跳过数据库初始化（仅检测模式 - 环境: {current_environment}）[/yellow]")

    # 6. 检查虚拟环境是否激活
    console.print("\n[bold]🔧 步骤 4: 检查环境激活状态[/bold]")
    venv_activated = is_project_venv_activated()
    if not venv_activated and check_only:
        console.print("[red]❌ 虚拟环境未激活（仅检测模式）[/red]")
        sys.exit(1)
    elif not venv_activated:
        _reexec_into_venv()
    else:
        console.print("[green]✅ 虚拟环境已激活[/green]")

    # 7. 编译依赖
    console.print("\n[bold]🔧 步骤 5: 编译项目依赖[/bold]")
    if not check_only:
        if not compile_dependencies():
            sys.exit(1)
    else:
        console.print("[yellow]跳过依赖编译（仅检测模式）[/yellow]")

    # 8. 同步依赖
    console.print("\n[bold]🔧 步骤 6: 同步项目依赖[/bold]")
    if not check_only:
        if not sync_dependencies():
            sys.exit(1)
    else:
        console.print("[yellow]跳过依赖同步（仅检测模式）[/yellow]")

    # 9. 完成
    if check_only:
        console.print("\n[bold green]🎉 检测通过：环境满足要求！[/bold green]")
        sys.exit(0)
    else:
        console.print("\n" + "="*60)
        console.print(Panel(
            "[bold green]🎉 AQUA环境初始化完成！[/bold green]\n\n"
            "您现在可以开始开发了。\n"
            "运行以下命令启动应用:\n"
            "[blue]python main.py[/blue]",
            title="✅ 成功",
            border_style="green"
        ))
        log_success('=== AQUA环境初始化完成 ===')

def get_environment_choice():
    """CLI交互选择环境类型"""
    try:
        from rich.prompt import Prompt, Confirm
        from rich import print as rprint
        
        # 检查是否已设置环境变量
        current_env = os.environ.get('AQUA_ENV', '').strip()
        if current_env:
            rprint(f"[green]✅ 检测到环境变量: AQUA_ENV={current_env}[/green]")
            use_existing = Confirm.ask(f"是否使用当前环境 '{current_env}'?", default=True)
            if use_existing:
                return current_env
        
        # 读取配置文件获取可用环境
        available_envs = ['dev', 'test', 'prod']
        try:
            import toml
            if CONFIG_FILE_PATH.exists():
                config = toml.load(CONFIG_FILE_PATH)
                # 检测配置文件中定义的环境
                config_envs = [key for key in config.keys() 
                             if key not in ['app', 'environment', 'bootstrap', 'network'] 
                             and isinstance(config[key], dict)]
                if config_envs:
                    available_envs = config_envs
        except Exception:
            pass
        
        rprint("\n[bold cyan]🎯 请选择AQUA环境类型：[/bold cyan]")
        for i, env in enumerate(available_envs, 1):
            env_desc = {
                'dev': '开发环境 - 适合日常开发和测试',
                'test': '测试环境 - 适合集成测试和验证', 
                'prod': '生产环境 - 生产级配置，操作需谨慎'
            }.get(env, f'{env} - 自定义环境')
            rprint(f"  [bold]{i}[/bold]. {env} - {env_desc}")
        
        while True:
            try:
                choice = Prompt.ask(
                    f"请输入环境编号 (1-{len(available_envs)})",
                    default="1"
                )
                idx = int(choice) - 1
                if 0 <= idx < len(available_envs):
                    selected_env = available_envs[idx]
                    
                    # 生产环境特殊确认
                    if selected_env == 'prod':
                        rprint("[yellow]⚠️  您选择了生产环境，请谨慎操作！[/yellow]")
                        confirm = Confirm.ask("确认要在生产环境初始化?", default=False)
                        if not confirm:
                            continue
                    
                    return selected_env
                else:
                    rprint("[red]❌ 无效选择，请重新输入[/red]")
            except (ValueError, KeyboardInterrupt):
                rprint("[red]❌ 输入无效或取消操作[/red]")
                sys.exit(1)
                
    except ImportError:
        # 如果rich.prompt不可用，回退到基本输入
        return os.environ.get('AQUA_ENV', 'dev')

def display_final_summary(check_only=False, selected_env=None):
    """显示最终执行结果摘要"""
    try:
        from rich.table import Table
        from rich.panel import Panel
        from rich import print as rprint
        
        # 获取当前配置信息
        config_info = {}
        try:
            from scripts.config_manager import AQUAConfigManager
            config_manager = AQUAConfigManager()
            current_env = config_manager.get_current_environment()
            env_config = config_manager.get_environment_config(current_env)
            app_config = config_manager.get_app_config()
            
            config_info = {
                'current_env': current_env,
                'app_name': app_config.get('name', 'Unknown'),
                'app_version': app_config.get('version', 'Unknown'),
                'database_path': env_config.database.get('path', 'Unknown'),
                'data_dir': env_config.csv.get('data_dir', 'Unknown'),
                'batch_size': env_config.csv.get('batch_size', 'Unknown')
            }
        except Exception as e:
            config_info = {'error': str(e)}
        
        # 创建摘要表格
        summary_table = Table(title="🎯 AQUA环境初始化结果摘要", show_header=True, header_style="bold magenta")
        summary_table.add_column("配置项", style="cyan", width=20)
        summary_table.add_column("当前值", style="white", width=50)
        summary_table.add_column("状态", justify="center", width=10)
        
        if 'error' not in config_info:
            summary_table.add_row("🌍 当前环境", config_info['current_env'], "✅")
            summary_table.add_row("📱 应用名称", config_info['app_name'], "✅")
            summary_table.add_row("🔖 应用版本", config_info['app_version'], "✅")
            summary_table.add_row("🗄️ 数据库路径", config_info['database_path'], "✅")
            summary_table.add_row("📁 数据目录", config_info['data_dir'], "✅")
            summary_table.add_row("📦 批处理大小", str(config_info['batch_size']), "✅")
        else:
            summary_table.add_row("❌ 配置读取", "失败", "❌")
            summary_table.add_row("🔍 错误信息", config_info['error'], "❌")
        
        # 显示操作模式
        mode_info = "🔍 检测模式 (仅检查)" if check_only else "🔧 完整初始化模式"
        summary_table.add_row("⚙️ 执行模式", mode_info, "ℹ️")
        
        # 显示环境变量设置建议
        current_env = config_info.get('current_env', selected_env or 'dev')
        export_cmd = f"export AQUA_ENV={current_env}"
        summary_table.add_row("🔧 环境变量", export_cmd, "💡")
        
        console.print(summary_table)
        
        # 下一步建议
        if check_only:
            next_steps = (
                "✅ 环境检测完成\n\n"
                "🚀 下一步建议:\n"
                f"• 设置环境变量: [green]{export_cmd}[/green]\n"
                "• 启动后端服务: [green]./start_backend.sh[/green]\n"
                "• 启动前端服务: [green]./start_frontend.sh[/green]\n"
                "• 查看配置指南: [green]docs/CONFIGURATION_GUIDE.md[/green]"
            )
        else:
            next_steps = (
                "🎉 环境初始化完成！\n\n"
                "🚀 立即开始:\n"
                f"• 当前环境: [bold green]{current_env}[/bold green]\n"
                "• 启动应用: [green]python main.py[/green]\n"
                "• 启动后端: [green]./start_backend.sh[/green]\n"
                "• 启动前端: [green]./start_frontend.sh[/green]\n"
                "• 数据导入: [green]python scripts/data_import_manager.py[/green]"
            )
        
        console.print(Panel(
            next_steps,
            title="🎯 执行结果与下一步",
            border_style="green" if not check_only else "blue"
        ))
        
    except Exception as e:
        console.print(f"[red]❌ 显示摘要时出错: {e}[/red]")

# ================= 智能双入口：启动模式支持 (模块化) =================
# 启动模式相关功能已移至 service_manager.py 模块，实现代码精简和职责分离
from scripts.service_manager import startup_mode_handler

if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='AQUA环境初始化与检测脚本 - 配置驱动的智能环境管理 + 智能启动引擎'
    )
    parser.add_argument(
        '--check-only', 
        action='store_true', 
        help='仅检测环境，不做修复/安装/变更'
    )
    parser.add_argument(
        '--env', 
        type=str, 
        help='指定环境类型 (dev/test/prod)，如不指定将进入交互选择模式'
    )
    parser.add_argument(
        '--non-interactive', 
        action='store_true', 
        help='非交互模式，使用默认或环境变量设置'
    )
    
    # ========== 智能双入口：启动模式参数 ==========
    parser.add_argument(
        '--startup-mode',
        action='store_true',
        help='启动模式：环境初始化 + 服务启动一体化'
    )
    parser.add_argument(
        '--wait-services',
        action='store_true', 
        help='等待服务启动完成'
    )
    parser.add_argument(
        '--enable-monitor',
        action='store_true',
        help='启用服务监控'
    )
    parser.add_argument(
        '--service-timeout',
        type=int,
        default=30,
        help='服务启动超时时间(秒)'
    )
    # --env参数已在上方定义，此处移除重复定义
    
    args = parser.parse_args()
    
    # ========== 智能双入口：启动模式分支处理 ==========
    if args.startup_mode:
        # 启动模式：自动使用dev环境，非交互式
        selected_env = args.env if args.env else 'dev'
        os.environ['AQUA_ENV'] = selected_env
        
        try:
            startup_mode_handler(args, main)
        except KeyboardInterrupt:
            console.print("\n[yellow]⚠️ 启动被用户取消[/yellow]")
            sys.exit(0)
        except Exception as e:
            console.print(f"\n[red]❌ 启动模式执行失败: {e}[/red]")
            sys.exit(1)
    else:
        # 传统模式：纯环境初始化
        if args.env:
            os.environ['AQUA_ENV'] = args.env
            selected_env = args.env
        elif args.non_interactive:
            selected_env = os.environ.get('AQUA_ENV', 'dev')
        else:
            selected_env = get_environment_choice()
            os.environ['AQUA_ENV'] = selected_env
        
        try:
            main(check_only=args.check_only)
            display_final_summary(check_only=args.check_only, selected_env=selected_env)
        except KeyboardInterrupt:
            console.print("\n[yellow]⚠️ 用户取消操作[/yellow]")
            sys.exit(1)
        except Exception as e:
            console.print(f"\n[red]❌ 执行失败: {e}[/red]")
            display_final_summary(check_only=args.check_only, selected_env=selected_env)
            sys.exit(1)
