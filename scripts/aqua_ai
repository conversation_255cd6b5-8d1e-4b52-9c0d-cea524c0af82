#!/bin/bash
# AQUA项目AI工具快速启动脚本
# 使用方法: ./scripts/aqua_ai claude | gemini | cursor

cd "$(dirname "$0")/.." || exit 1

case "$1" in
    "claude"|"c")
        echo "🤖 启动Claude Code..."
        python scripts/ai_tools_launcher.py --tool claude
        ;;
    "gemini"|"g") 
        echo "💎 启动Gemini CLI..."
        python scripts/ai_tools_launcher.py --tool gemini
        ;;
    "cursor"|"cur")
        echo "🎯 启动Cursor AI..."
        python scripts/ai_tools_launcher.py --tool cursor
        ;; 
    "check"|"status")
        echo "🔍 检查项目合规状态..."
        python scripts/compliance_checker.py
        ;;
    "monitor"|"m")
        echo "📊 启动实时监控..."
        echo "监控宪法执行日志..."
        tail -f logs/constitutional_enforcement.jsonl &
        echo "监控审批请求..."
        tail -f logs/approval_requests.jsonl &
        wait
        ;;
    "report"|"r")
        echo "📋 生成合规报告..."
        python scripts/claude_constitutional_enforcer.py
        ;;
    "help"|"h"|"")
        echo "🏛️ AQUA项目AI工具管理器"
        echo ""
        echo "使用方法:"
        echo "  ./scripts/aqua_ai <command>"
        echo ""
        echo "命令:"
        echo "  claude, c     启动Claude Code"
        echo "  gemini, g     启动Gemini CLI" 
        echo "  cursor, cur   启动Cursor AI"
        echo "  check         检查项目合规状态"
        echo "  monitor, m    启动实时监控"
        echo "  report, r     生成合规报告"
        echo "  help, h       显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  ./scripts/aqua_ai claude     # 启动Claude Code"
        echo "  ./scripts/aqua_ai check      # 检查合规状态"
        echo "  ./scripts/aqua_ai monitor    # 实时监控"
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "💡 使用 './scripts/aqua_ai help' 查看帮助"
        exit 1
        ;;
esac