#!/usr/bin/env python3
"""
AQUA数据备份脚本
用于备份数据库、配置文件和重要数据
"""

import os
import sys
import shutil
import platform
import subprocess
from pathlib import Path
from datetime import datetime
import zipfile
import json
from typing import List, Dict, Optional

# 颜色输出支持
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message: str, color: str = Colors.WHITE, bold: bool = False):
    """打印带颜色的消息"""
    prefix = Colors.BOLD if bold else ""
    print(f"{prefix}{color}{message}{Colors.RESET}")

def print_banner():
    """打印备份横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    💾 AQUA数据备份工具                        ║
    ║                  期货与A股数据分析平台                         ║
    ║                                                            ║
    ║  支持: 数据库备份、配置备份、日志备份、增量备份                    ║
    ║                                                            ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print_colored(banner, Colors.CYAN, bold=True)

class BackupManager:
    """备份管理器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.backup_root = self.project_root / "data" / "backup"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建备份目录
        self.backup_root.mkdir(parents=True, exist_ok=True)
        
        # 备份配置
        self.backup_config = {
            "database": {
                "enabled": True,
                "patterns": ["*.duckdb", "*.db"],
                "exclude": ["*.wal", "*.tmp"]
            },
            "config": {
                "enabled": True,
                "patterns": ["*.toml", "*.json", "*.yaml", "*.yml"],
                "exclude": [".env", "*.local.*"]
            },
            "logs": {
                "enabled": False,  # 默认不备份日志
                "patterns": ["*.log"],
                "exclude": ["*.tmp"]
            },
            "data": {
                "enabled": True,
                "patterns": ["*.csv", "*.xlsx", "*.json"],
                "exclude": ["*.tmp", "*.temp"]
            },
            "code": {
                "enabled": False,  # 默认不备份代码（应该用Git管理）
                "patterns": ["*.py", "*.js", "*.ts", "*.vue"],
                "exclude": ["__pycache__", "node_modules", ".git"]
            }
        }

    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            import toml
            config_file = self.project_root / "config" / "settings.toml"
            if config_file.exists():
                config = toml.load(config_file)
                # 使用开发环境配置
                return config.get("dev", {})
            return {}
        except Exception as e:
            print_colored(f"⚠️  配置加载失败: {e}", Colors.YELLOW)
            return {}

    def get_backup_path(self, backup_type: str) -> Path:
        """获取备份路径"""
        backup_name = f"aqua_{backup_type}_{self.timestamp}"
        return self.backup_root / backup_name

    def create_backup_info(self, backup_path: Path, backup_type: str, files: List[Path]) -> Dict:
        """创建备份信息"""
        info = {
            "timestamp": self.timestamp,
            "backup_type": backup_type,
            "platform": platform.system(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "total_files": len(files),
            "total_size": sum(f.stat().st_size for f in files if f.exists()),
            "files": [str(f.relative_to(self.project_root)) for f in files]
        }
        
        # 保存备份信息
        info_file = backup_path / "backup_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
        
        return info

    def find_files_by_pattern(self, base_path: Path, patterns: List[str], exclude: List[str] = None) -> List[Path]:
        """根据模式查找文件"""
        if exclude is None:
            exclude = []
        
        files = []
        for pattern in patterns:
            found_files = base_path.rglob(pattern)
            for file_path in found_files:
                # 检查是否需要排除
                should_exclude = False
                for exc_pattern in exclude:
                    if exc_pattern in str(file_path):
                        should_exclude = True
                        break
                
                if not should_exclude and file_path.is_file():
                    files.append(file_path)
        
        return files

    def copy_files_to_backup(self, files: List[Path], backup_path: Path) -> bool:
        """复制文件到备份目录"""
        try:
            backup_path.mkdir(parents=True, exist_ok=True)
            
            for file_path in files:
                if not file_path.exists():
                    print_colored(f"⚠️  文件不存在: {file_path}", Colors.YELLOW)
                    continue
                
                # 计算相对路径
                rel_path = file_path.relative_to(self.project_root)
                dest_path = backup_path / rel_path
                
                # 创建目标目录
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(file_path, dest_path)
                print_colored(f"📄 备份: {rel_path}", Colors.GREEN)
            
            return True
            
        except Exception as e:
            print_colored(f"❌ 复制文件失败: {e}", Colors.RED)
            return False

    def create_zip_backup(self, backup_path: Path, zip_name: str) -> bool:
        """创建ZIP备份"""
        try:
            zip_path = self.backup_root / f"{zip_name}.zip"
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in backup_path.rglob('*'):
                    if file_path.is_file():
                        arc_name = file_path.relative_to(backup_path)
                        zipf.write(file_path, arc_name)
            
            print_colored(f"📦 ZIP备份创建: {zip_path}", Colors.GREEN)
            
            # 删除原始备份目录
            shutil.rmtree(backup_path)
            
            return True
            
        except Exception as e:
            print_colored(f"❌ 创建ZIP备份失败: {e}", Colors.RED)
            return False

    def backup_database(self, create_zip: bool = True) -> bool:
        """备份数据库"""
        print_colored("🗄️  开始数据库备份...", Colors.BLUE)
        
        config = self.backup_config["database"]
        if not config["enabled"]:
            print_colored("⚠️  数据库备份已禁用", Colors.YELLOW)
            return True
        
        # 查找数据库文件
        db_files = self.find_files_by_pattern(
            self.project_root / "data",
            config["patterns"],
            config["exclude"]
        )
        
        if not db_files:
            print_colored("⚠️  未找到数据库文件", Colors.YELLOW)
            return True
        
        # 创建备份
        backup_path = self.get_backup_path("database")
        
        if not self.copy_files_to_backup(db_files, backup_path):
            return False
        
        # 创建备份信息
        self.create_backup_info(backup_path, "database", db_files)
        
        # 创建ZIP备份
        if create_zip:
            return self.create_zip_backup(backup_path, f"aqua_database_{self.timestamp}")
        
        print_colored(f"✅ 数据库备份完成: {backup_path}", Colors.GREEN)
        return True

    def backup_config(self, create_zip: bool = True) -> bool:
        """备份配置文件"""
        print_colored("⚙️  开始配置备份...", Colors.BLUE)
        
        config = self.backup_config["config"]
        if not config["enabled"]:
            print_colored("⚠️  配置备份已禁用", Colors.YELLOW)
            return True
        
        # 查找配置文件
        config_files = self.find_files_by_pattern(
            self.project_root / "config",
            config["patterns"],
            config["exclude"]
        )
        
        # 添加根目录的配置文件
        root_config_files = self.find_files_by_pattern(
            self.project_root,
            ["*.toml", "*.json", "requirements.txt", "pyproject.toml"],
            ["node_modules", ".git", ".venv"]
        )
        
        all_config_files = config_files + root_config_files
        
        if not all_config_files:
            print_colored("⚠️  未找到配置文件", Colors.YELLOW)
            return True
        
        # 创建备份
        backup_path = self.get_backup_path("config")
        
        if not self.copy_files_to_backup(all_config_files, backup_path):
            return False
        
        # 创建备份信息
        self.create_backup_info(backup_path, "config", all_config_files)
        
        # 创建ZIP备份
        if create_zip:
            return self.create_zip_backup(backup_path, f"aqua_config_{self.timestamp}")
        
        print_colored(f"✅ 配置备份完成: {backup_path}", Colors.GREEN)
        return True

    def backup_logs(self, create_zip: bool = True) -> bool:
        """备份日志文件"""
        print_colored("📝 开始日志备份...", Colors.BLUE)
        
        config = self.backup_config["logs"]
        if not config["enabled"]:
            print_colored("⚠️  日志备份已禁用", Colors.YELLOW)
            return True
        
        # 查找日志文件
        log_files = self.find_files_by_pattern(
            self.project_root / "logs",
            config["patterns"],
            config["exclude"]
        )
        
        if not log_files:
            print_colored("⚠️  未找到日志文件", Colors.YELLOW)
            return True
        
        # 创建备份
        backup_path = self.get_backup_path("logs")
        
        if not self.copy_files_to_backup(log_files, backup_path):
            return False
        
        # 创建备份信息
        self.create_backup_info(backup_path, "logs", log_files)
        
        # 创建ZIP备份
        if create_zip:
            return self.create_zip_backup(backup_path, f"aqua_logs_{self.timestamp}")
        
        print_colored(f"✅ 日志备份完成: {backup_path}", Colors.GREEN)
        return True

    def backup_data(self, create_zip: bool = True) -> bool:
        """备份数据文件"""
        print_colored("📊 开始数据备份...", Colors.BLUE)
        
        config = self.backup_config["data"]
        if not config["enabled"]:
            print_colored("⚠️  数据备份已禁用", Colors.YELLOW)
            return True
        
        # 查找数据文件（排除备份目录）
        data_files = []
        data_dirs = [
            self.project_root / "data" / "raw",
            self.project_root / "data" / "processed"
        ]
        
        for data_dir in data_dirs:
            if data_dir.exists():
                files = self.find_files_by_pattern(
                    data_dir,
                    config["patterns"],
                    config["exclude"]
                )
                data_files.extend(files)
        
        if not data_files:
            print_colored("⚠️  未找到数据文件", Colors.YELLOW)
            return True
        
        # 创建备份
        backup_path = self.get_backup_path("data")
        
        if not self.copy_files_to_backup(data_files, backup_path):
            return False
        
        # 创建备份信息
        self.create_backup_info(backup_path, "data", data_files)
        
        # 创建ZIP备份
        if create_zip:
            return self.create_zip_backup(backup_path, f"aqua_data_{self.timestamp}")
        
        print_colored(f"✅ 数据备份完成: {backup_path}", Colors.GREEN)
        return True

    def backup_full(self, create_zip: bool = True) -> bool:
        """完整备份"""
        print_colored("🔄 开始完整备份...", Colors.BLUE)
        
        # 创建完整备份目录
        full_backup_path = self.get_backup_path("full")
        full_backup_path.mkdir(parents=True, exist_ok=True)
        
        # 收集所有文件
        all_files = []
        backup_types = ["database", "config", "data"]
        
        for backup_type in backup_types:
            config = self.backup_config[backup_type]
            if not config["enabled"]:
                continue
            
            if backup_type == "database":
                files = self.find_files_by_pattern(
                    self.project_root / "data",
                    config["patterns"],
                    config["exclude"]
                )
            elif backup_type == "config":
                files = self.find_files_by_pattern(
                    self.project_root / "config",
                    config["patterns"],
                    config["exclude"]
                )
                # 添加根目录配置文件
                root_files = self.find_files_by_pattern(
                    self.project_root,
                    ["*.toml", "*.json", "requirements.txt", "pyproject.toml"],
                    ["node_modules", ".git", ".venv"]
                )
                files.extend(root_files)
            elif backup_type == "data":
                files = []
                data_dirs = [
                    self.project_root / "data" / "raw",
                    self.project_root / "data" / "processed"
                ]
                for data_dir in data_dirs:
                    if data_dir.exists():
                        dir_files = self.find_files_by_pattern(
                            data_dir,
                            config["patterns"],
                            config["exclude"]
                        )
                        files.extend(dir_files)
            
            all_files.extend(files)
        
        if not all_files:
            print_colored("⚠️  未找到需要备份的文件", Colors.YELLOW)
            return False
        
        # 复制所有文件
        if not self.copy_files_to_backup(all_files, full_backup_path):
            return False
        
        # 创建备份信息
        self.create_backup_info(full_backup_path, "full", all_files)
        
        # 创建ZIP备份
        if create_zip:
            return self.create_zip_backup(full_backup_path, f"aqua_full_{self.timestamp}")
        
        print_colored(f"✅ 完整备份完成: {full_backup_path}", Colors.GREEN)
        return True

    def list_backups(self) -> List[Dict]:
        """列出所有备份"""
        backups = []
        
        for backup_file in self.backup_root.glob("*.zip"):
            try:
                # 解析备份文件名
                name_parts = backup_file.stem.split('_')
                if len(name_parts) >= 3:
                    backup_type = name_parts[1]
                    timestamp = '_'.join(name_parts[2:])
                    
                    backup_info = {
                        "name": backup_file.name,
                        "type": backup_type,
                        "timestamp": timestamp,
                        "size": backup_file.stat().st_size,
                        "created": datetime.fromtimestamp(backup_file.stat().st_ctime),
                        "path": backup_file
                    }
                    backups.append(backup_info)
            except Exception as e:
                print_colored(f"⚠️  解析备份文件失败 {backup_file}: {e}", Colors.YELLOW)
        
        # 按时间排序
        backups.sort(key=lambda x: x["created"], reverse=True)
        return backups

    def restore_backup(self, backup_path: Path, restore_path: Optional[Path] = None) -> bool:
        """恢复备份"""
        if restore_path is None:
            restore_path = self.project_root / "restored"
        
        print_colored(f"🔄 恢复备份: {backup_path}", Colors.BLUE)
        
        try:
            if backup_path.suffix == '.zip':
                # 解压ZIP文件
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(restore_path)
            else:
                # 复制目录
                shutil.copytree(backup_path, restore_path, dirs_exist_ok=True)
            
            print_colored(f"✅ 备份恢复完成: {restore_path}", Colors.GREEN)
            return True
            
        except Exception as e:
            print_colored(f"❌ 恢复备份失败: {e}", Colors.RED)
            return False

    def cleanup_old_backups(self, keep_days: int = 7) -> bool:
        """清理旧备份"""
        print_colored(f"🧹 清理{keep_days}天前的备份...", Colors.BLUE)
        
        try:
            cutoff_time = datetime.now().timestamp() - (keep_days * 24 * 60 * 60)
            cleaned_count = 0
            
            for backup_file in self.backup_root.glob("*.zip"):
                if backup_file.stat().st_ctime < cutoff_time:
                    backup_file.unlink()
                    cleaned_count += 1
                    print_colored(f"🗑️  删除旧备份: {backup_file.name}", Colors.YELLOW)
            
            print_colored(f"✅ 清理完成，删除了{cleaned_count}个旧备份", Colors.GREEN)
            return True
            
        except Exception as e:
            print_colored(f"❌ 清理旧备份失败: {e}", Colors.RED)
            return False

    def print_backup_summary(self):
        """打印备份摘要"""
        print_colored("\n📋 备份摘要", Colors.CYAN, bold=True)
        print_colored("=" * 60, Colors.CYAN)
        
        backups = self.list_backups()
        
        if not backups:
            print_colored("⚠️  未找到备份文件", Colors.YELLOW)
            return
        
        total_size = sum(b["size"] for b in backups)
        
        print_colored(f"📊 总备份数: {len(backups)}", Colors.WHITE)
        print_colored(f"💾 总大小: {self.format_size(total_size)}", Colors.WHITE)
        print_colored(f"📁 备份目录: {self.backup_root}", Colors.WHITE)
        
        print_colored("\n最近的备份:", Colors.CYAN)
        for backup in backups[:5]:  # 显示最近5个备份
            size_str = self.format_size(backup["size"])
            print_colored(f"  {backup['name']} ({size_str})", Colors.GREEN)
            print_colored(f"    类型: {backup['type']}, 时间: {backup['created']}", Colors.BLUE)

    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f}{unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f}TB"

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AQUA数据备份工具")
    parser.add_argument("--type", "-t", choices=["database", "config", "logs", "data", "full"], 
                       default="full", help="备份类型")
    parser.add_argument("--no-zip", action="store_true", help="不创建ZIP文件")
    parser.add_argument("--list", "-l", action="store_true", help="列出所有备份")
    parser.add_argument("--restore", "-r", help="恢复指定备份")
    parser.add_argument("--cleanup", "-c", type=int, metavar="DAYS", 
                       help="清理N天前的备份")
    
    args = parser.parse_args()
    
    print_banner()
    
    manager = BackupManager()
    
    try:
        if args.list:
            manager.print_backup_summary()
        elif args.restore:
            restore_path = Path(args.restore)
            if restore_path.exists():
                manager.restore_backup(restore_path)
            else:
                print_colored(f"❌ 备份文件不存在: {restore_path}", Colors.RED)
                sys.exit(1)
        elif args.cleanup:
            manager.cleanup_old_backups(args.cleanup)
        else:
            # 执行备份
            create_zip = not args.no_zip
            
            if args.type == "database":
                success = manager.backup_database(create_zip)
            elif args.type == "config":
                success = manager.backup_config(create_zip)
            elif args.type == "logs":
                success = manager.backup_logs(create_zip)
            elif args.type == "data":
                success = manager.backup_data(create_zip)
            elif args.type == "full":
                success = manager.backup_full(create_zip)
            
            if success:
                print_colored("🎉 备份完成！", Colors.GREEN, bold=True)
                manager.print_backup_summary()
            else:
                print_colored("❌ 备份失败！", Colors.RED, bold=True)
                sys.exit(1)
    
    except KeyboardInterrupt:
        print_colored("\n⚠️  用户中断备份", Colors.YELLOW)
        sys.exit(1)
    except Exception as e:
        print_colored(f"❌ 备份过程异常: {e}", Colors.RED, bold=True)
        sys.exit(1)

if __name__ == "__main__":
    main()