#!/usr/bin/env python3
"""
KanbanLocalWithGitee 项目结构创建脚本
基于AQUA项目标准，创建独立的项目管理工具

遵循AQUA宪法:
- 配置驱动: settings.toml为唯一事实来源
- 规则先行: 严格遵循AQUA目录结构标准
- 自动化: 一键创建完整项目结构
- 跨平台: Windows + macOS 兼容
"""

import os
import sys
from pathlib import Path
import shutil
from typing import Dict, List

# 添加AQUA项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_kanban_project_structure():
    """创建KanbanLocalWithGitee项目目录结构"""
    
    # 项目根目录 (在AQUA项目外部创建)
    kanban_root = project_root.parent / "kanban-local-with-gitee"
    
    print(f"🚀 创建KanbanLocalWithGitee项目: {kanban_root}")
    
    # 创建根目录
    kanban_root.mkdir(exist_ok=True)
    
    # 基于AQUA标准的目录结构
    directories = [
        # 核心目录 (基于AQUA标准)
        "src/core",                    # 核心组件 (从AQUA复用)
        "src/cli",                     # CLI界面
        "src/integrations",            # 第三方集成 (Gitee/GitHub)
        "src/models",                  # 数据模型
        "src/utils",                   # 工具模块
        
        # 测试目录 (遵循AQUA测试架构)
        "tests/unit",                  # 单元测试
        "tests/integration",           # 集成测试
        "tests/e2e",                   # 端到端测试
        
        # 配置目录 (AQUA配置驱动原则)
        "config/env",                  # 多环境配置
        
        # 数据目录 (AQUA数据管理标准)
        "data/projects",               # 项目数据
        "data/backup",                 # 备份数据
        "data/cache",                  # 缓存数据
        
        # 文档目录 (AQUA文档标准)
        "docs/api",                    # API文档
        "docs/user",                   # 用户文档
        
        # 日志目录 (AQUA日志标准)
        "logs/app",                    # 应用日志
        "logs/audit",                  # 审计日志
        
        # 脚本目录 (AQUA自动化标准)
        "scripts/build",               # 构建脚本
        "scripts/deploy",              # 部署脚本
        
        # 模板目录
        "templates/projects",          # 项目模板
        "templates/reports",           # 报告模板
    ]
    
    # 创建目录结构
    for directory in directories:
        dir_path = kanban_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建.gitkeep文件 (AQUA标准)
        gitkeep_file = dir_path / ".gitkeep"
        gitkeep_file.touch()
        
        print(f"  ✅ 创建目录: {directory}")
    
    return kanban_root

def copy_aqua_core_components(kanban_root: Path):
    """从AQUA项目复用核心组件"""
    
    print("📦 从AQUA复用核心组件...")
    
    # 复用映射 (AQUA组件 -> Kanban组件)
    copy_mappings = [
        # 核心组件复用
        {
            "source": "src/storage/priority_queue_manager.py",
            "target": "src/core/task_queue_engine.py",
            "description": "任务队列引擎 (95%复用)"
        },
        {
            "source": "src/data_import/task_control_manager.py", 
            "target": "src/core/task_status_manager.py",
            "description": "任务状态管理器 (90%复用)"
        },
        {
            "source": "src/utils/config_loader.py",
            "target": "src/core/project_config_manager.py", 
            "description": "项目配置管理器 (95%复用)"
        },
        {
            "source": "src/utils/logger.py",
            "target": "src/utils/logger.py",
            "description": "日志系统 (100%复用)"
        },
        {
            "source": "src/utils/exceptions.py",
            "target": "src/utils/exceptions.py", 
            "description": "异常处理 (100%复用)"
        },
        {
            "source": "src/utils/time_utils.py",
            "target": "src/utils/time_utils.py",
            "description": "时间工具 (100%复用)"
        },
        
        # CLI框架复用
        {
            "source": "src/cli/main.py",
            "target": "src/cli/main.py",
            "description": "CLI主框架 (100%复用)"
        },
        
        # 测试框架复用
        {
            "source": "pytest.ini",
            "target": "pytest.ini", 
            "description": "测试配置 (100%复用)"
        },
        {
            "source": "tests/conftest.py",
            "target": "tests/conftest.py",
            "description": "测试配置 (100%复用)"
        }
    ]
    
    # 执行复制
    for mapping in copy_mappings:
        source_path = project_root / mapping["source"]
        target_path = kanban_root / mapping["target"]
        
        if source_path.exists():
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            print(f"  ✅ 复用: {mapping['description']}")
        else:
            print(f"  ⚠️ 源文件不存在: {source_path}")

def create_basic_files(kanban_root: Path):
    """创建基础项目文件"""
    
    print("📝 创建基础项目文件...")
    
    # 创建README.md
    readme_content = """# KanbanLocalWithGitee

> **个人开发者项目管理工具** - 基于AQUA架构的跨平台看板管理系统

## 快速开始

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化项目
kanban init my-project

# 3. 创建第一个Epic
kanban epic create "用户认证系统"

# 4. 查看项目状态
kanban status
```

## 特性

- ✅ 本地优先数据管理
- ✅ Gitee双向同步
- ✅ 跨平台CLI界面
- ✅ Epic/Feature/Task三层管理
- ✅ 85%代码复用自AQUA架构

## 文档

- [项目计划](docs/handbook/Kanban/PLAN.md)
- [实施指南](docs/handbook/Kanban/GUIDE.md)
"""
    
    (kanban_root / "README.md").write_text(readme_content, encoding="utf-8")
    
    # 创建requirements.txt
    requirements_content = """# KanbanLocalWithGitee 依赖清单
# 基于AQUA项目依赖，专为项目管理优化

# CLI框架
click>=8.1.0
rich>=13.0.0
typer>=0.9.0

# 数据处理
sqlite3
pandas>=2.0.0

# HTTP客户端 (Gitee API)
requests>=2.31.0
httpx>=0.24.0

# 配置管理
toml>=0.10.2
pydantic>=2.0.0

# 测试框架
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# 代码质量
black>=23.0.0
isort>=5.12.0
pylint>=2.17.0
mypy>=1.5.0

# 开发工具
pre-commit>=3.3.0
"""
    
    (kanban_root / "requirements.txt").write_text(requirements_content, encoding="utf-8")
    
    # 创建基础配置文件
    config_content = """# KanbanLocalWithGitee 配置文件
# 基于AQUA配置驱动原则

[app]
name = "KanbanLocalWithGitee"
version = "1.0.0"
description = "个人开发者项目管理工具"
default_environment = "dev"
environments = ["dev", "test", "prod"]

[database]
type = "sqlite"
filename = "kanban.db"

[gitee]
api_base_url = "https://gitee.com/api/v5"
# token = "your_gitee_token"  # 在环境变量中设置

[cli]
default_output_format = "table"
enable_colors = true
max_items_per_page = 20

[logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_max_size = "10MB"
backup_count = 5

[dev]
debug = true
log_level = "DEBUG"

[test]
debug = false
log_level = "INFO"
database_filename = "kanban_test.db"

[prod]
debug = false
log_level = "WARNING"
"""
    
    (kanban_root / "config" / "settings.toml").write_text(config_content, encoding="utf-8")
    
    print("  ✅ 创建基础文件完成")

def main():
    """主函数"""
    print("🎯 KanbanLocalWithGitee 项目初始化")
    print("=" * 50)
    
    try:
        # 1. 创建项目结构
        kanban_root = create_kanban_project_structure()
        
        # 2. 复用AQUA核心组件
        copy_aqua_core_components(kanban_root)
        
        # 3. 创建基础文件
        create_basic_files(kanban_root)
        
        print("\n🎉 项目创建成功!")
        print(f"📁 项目位置: {kanban_root}")
        print("\n📋 下一步:")
        print("1. cd kanban-local-with-gitee")
        print("2. pip install -r requirements.txt")
        print("3. 开始Feature 1.1.2 - 重构核心组件")
        
    except Exception as e:
        print(f"❌ 项目创建失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
