// 验证导入修复脚本
const fs = require('fs');
const path = require('path');

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否还有错误的导入
    const badImports = [
      'services/apiClient',
      'from "../../services/apiClient"',
      "from '../../services/apiClient'"
    ];
    
    let hasErrors = false;
    badImports.forEach(badImport => {
      if (content.includes(badImport)) {
        console.log(`❌ ${filePath} 仍然包含错误导入: ${badImport}`);
        hasErrors = true;
      }
    });
    
    // 检查是否有正确的导入  
    if (content.includes("from '@/api/index'")) {
      console.log(`✅ ${filePath} 使用正确的API导入`);
    }
    
    return !hasErrors;
  } catch (error) {
    console.log(`⚠️ 无法读取文件: ${filePath}`);
    return false;
  }
}

console.log('🔍 验证导入修复...\n');

const filesToCheck = [
  'src/modules/data-import/components/steps/Csv_Step_2_PreCheck.vue',
  'src/modules/data-import/components/steps/Csv_Step_3_Execute.vue', 
  'src/modules/data-import/components/steps/Mysql_Step_1_Credentials.vue',
  'src/modules/data-import/components/steps/Mysql_Step_2_SelectTables.vue'
];

let allGood = true;
filesToCheck.forEach(file => {
  if (!checkFile(file)) {
    allGood = false;
  }
});

console.log('\n' + '='.repeat(50));
if (allGood) {
  console.log('🎉 所有导入修复验证通过！');
} else {
  console.log('⚠️ 还有文件需要修复');
}