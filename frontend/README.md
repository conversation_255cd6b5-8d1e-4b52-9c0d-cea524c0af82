# 前端目录结构说明

本目录为AQUA项目前端工程主目录，所有结构、命名、用途均需严格对齐AQUA_GUIDE.md#[GUIDE-03]和开发规范。

## 目录结构

- src/    ：前端源代码（api、assets、components、modules、router、stores、types等）
- public/ ：静态资源目录（如favicon、logo等）
- tests/  ：前端测试目录（mock、unit、integration、e2e等，mock数据结构与后端接口严格对齐）
- mock/   ：（可选）前端mock数据目录，推荐将mock数据统一放在tests/mock/，如有历史遗留可保留此目录

## 合规要求
- 所有目录、文件、命名、mock、类型、测试等，必须严格对齐AQUA_GUIDE.md和.rules下各项标准。
- 目录结构变更需同步更新AQUA_GUIDE.md和logs/dev_log.md，形成闭环。
- mock数据结构、类型、接口需与后端和数据字典严格一致，禁止自创字段。
- 每个目录下建议保留.gitkeep文件，确保空目录可被版本控制。

## 参考文档
- AQUA_GUIDE.md#[GUIDE-03] 目录结构
- development_standards.mdc
- mvp_rules.mdc 