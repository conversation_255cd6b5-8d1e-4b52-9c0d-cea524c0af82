// 简单的API连接测试脚本
const axios = require('axios');

async function testAPIConnection() {
  console.log('🔍 测试API连接...');
  
  try {
    // 从环境变量获取API基础URL，避免硬编码
    const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:8000';

    // 测试健康检查
    const healthResponse = await axios.get(`${API_BASE_URL}/api/data/health`);
    console.log('✅ 健康检查:', healthResponse.status === 200 ? '成功' : '失败');

    // 测试表列表获取
    const tablesResponse = await axios.get(`${API_BASE_URL}/api/data/tables?environment=test`);
    console.log('✅ 表列表获取:', tablesResponse.status === 200 ? '成功' : '失败');
    console.log('📊 表数量:', tablesResponse.data?.data?.tables?.length || '未知');
    
    // 测试MySQL连接测试API
    const mysqlTestConfig = {
      host: 'localhost',
      port: 3306,
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass'
    };
    
    try {
      const mysqlResponse = await axios.post('http://localhost:8000/api/data/mysql/test-connection', mysqlTestConfig);
      console.log('✅ MySQL连接测试API:', '可访问');
    } catch (error) {
      console.log('⚠️ MySQL连接测试API:', '可访问但连接失败（预期行为）');
    }
    
    console.log('\n🎉 所有API端点测试完成！');
    
  } catch (error) {
    console.error('❌ API测试失败:', error.message);
  }
}

testAPIConnection();