{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test": "vitest --config vitest.config.ts", "lint:fix": "prettier --write \"src/**/*.{ts,js,vue}\" && eslint --fix \"src/**/*.{ts,js,vue}\""}, "dependencies": {"@vicons/ionicons5": "^0.13.0", "@vicons/tabler": "^0.13.0", "axios": "^1.10.0", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "naive-ui": "^2.42.0", "pinia": "^2.3.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-vue": "^6.0.0", "@vitest/coverage-v8": "3.2.4", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^8.23.0", "eslint-plugin-vue": "9.18.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.0", "vite": "^7.0.0", "vitest": "^3.2.4", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^2.2.10", "vue-typescript": "^0.7.0"}, "engines": {"node": ">=22.16.0", "pnpm": ">=8.0.0"}}