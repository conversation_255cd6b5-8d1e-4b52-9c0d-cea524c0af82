{
  "compilerOptions": {
    "strict": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["./*"],
      "#/*": ["../types/*"],
      "@components/*": ["components/*"],
      "@modules/*": ["modules/*"],
      "@stores/*": ["stores/*"],
      "@api/*": ["api/*"]
    },
    "target": "ESNext",
    "module": "ESNext",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "preserve",
    "noEmit": true,
    "forceConsistentCasingInFileNames": true,
    "types": ["vite/client", "node"]
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
}
