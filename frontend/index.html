<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>AQUA - 个人量化数据平台</title>
  <meta name="description" content="AQUA个人量化开发者数据平台 - 期货与A股数据处理工具" />
  <meta name="keywords" content="量化交易,数据分析,期货,A股,个人开发,AQUA" />
  
  <!-- 预加载关键资源 -->
  <link rel="preconnect" href="http://localhost:8000" />
  
  <!-- 基础样式，避免闪烁 -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, Oxygen, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif;
      background-color: #f5f5f5;
      color: #333;
      line-height: 1.6;
    }
    
    #app {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* 加载动画 */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #f5f5f5;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e1e1e1;
      border-top: 4px solid #18a058;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    
    .loading-text {
      color: #666;
      font-size: 14px;
      text-align: center;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 隐藏加载页面的类 */
    .loading-container.hidden {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
    }
  </style>
</head>
<body>
  <!-- 应用容器 -->
  <div id="app">
    <!-- 初始加载动画 -->
    <div id="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">
        <h3>🚀 AQUA 个人量化数据平台</h3>
        <p>正在加载应用程序...</p>
        <small>为个人量化开发者提供专业数据处理工具</small>
      </div>
    </div>
  </div>

  <!-- 主应用脚本 -->
  <script type="module" src="/src/main.ts"></script>
  
  <!-- 隐藏加载动画的脚本 -->
  <script>
    // 监听Vue应用加载完成
    window.addEventListener('DOMContentLoaded', function() {
      // 设置最小加载时间，避免闪烁
      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.classList.add('hidden');
          // 完全移除加载元素
          setTimeout(function() {
            if (loading.parentNode) {
              loading.parentNode.removeChild(loading);
            }
          }, 300);
        }
      }, 800);
    });
    
    // 错误处理
    window.addEventListener('error', function(e) {
      console.error('AQUA应用加载错误:', e.error);
      const loading = document.getElementById('loading');
      if (loading) {
        const loadingText = loading.querySelector('.loading-text');
        if (loadingText) {
          loadingText.innerHTML = `
            <h3>⚠️ 应用加载失败</h3>
            <p>请检查网络连接或刷新页面重试</p>
            <small>错误: ${e.message}</small>
          `;
        }
      }
    });
    
    // 开发环境调试信息
    if (import.meta.env?.DEV) {
      console.log('🔧 AQUA开发模式已启用');
      console.log('📱 前端服务:', window.location.origin);
      console.log('🖥️ 后端API: http://localhost:8000');
    }
  </script>
</body>
</html>