import { createApp } from 'vue';
import './style.css';
import App from './App.vue';
import router from './router';
import pinia from './stores';

const app = createApp(App);
app.use(router);
app.use(pinia);

/**
 * 全局错误处理钩子
 * 用于捕获运行时异常，便于统一日志与用户提示
 * 详见AQUA开发规范与mvp_rules.mdc
 */
app.config.errorHandler = (err, instance, info) => {
  // 可扩展为结构化日志、上报、UI提示等
  // eslint-disable-next-line no-console
  console.error('[全局错误]', err, info);
};

app.mount('#app');
