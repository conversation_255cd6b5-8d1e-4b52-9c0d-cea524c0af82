/**
 * 个人开发者配置管理器
 * 为个人量化开发者提供零配置、自适应的开发环境
 */

import CrossPlatformHelper, { type OSType } from '@/utils/platform/CrossPlatformHelper';

export interface PersonalDeveloperConfig {
  version: string;
  platform: {
    os: OSType;
    homeDir: string;
    pathSeparator: string;
  };
  dataPaths: {
    root: string;
    csvData: string;
    fromC2C: string;
    duckdb: string;
    logs: string;
    cache: string;
  };
  preferences: {
    quickStart: boolean;
    defaultEnvironment: 'dev' | 'test' | 'prod';
    autoCreateDirectories: boolean;
    enableDebugMode: boolean;
    preferredCsvEncoding: 'UTF-8' | 'GBK' | 'auto';
    maxFileSize: number;
    batchConcurrency: number;
  };
  development: {
    frontendPort: number;
    backendPort: number;
    hotReload: boolean;
    mockDataEnabled: boolean;
  };
  integration: {
    fromC2C: {
      enabled: boolean;
      defaultValidation: boolean;
      autoReports: boolean;
      maxFiles: number;
    };
    mysql: {
      enabled: boolean;
      defaultTimeout: number;
      maxConnections: number;
    };
  };
}

export class PersonalConfigManager {
  private static _instance: PersonalConfigManager;
  private _config: PersonalDeveloperConfig | null = null;
  private readonly CONFIG_VERSION = '1.0.0';

  static getInstance(): PersonalConfigManager {
    if (!PersonalConfigManager._instance) {
      PersonalConfigManager._instance = new PersonalConfigManager();
    }
    return PersonalConfigManager._instance;
  }

  /**
   * 获取或创建个人配置
   */
  async getConfig(): Promise<PersonalDeveloperConfig> {
    if (!this._config) {
      this._config = await this.loadOrCreateConfig();
    }
    return this._config;
  }

  /**
   * 加载或创建默认配置
   */
  private async loadOrCreateConfig(): Promise<PersonalDeveloperConfig> {
    try {
      // 尝试从本地存储加载配置
      const savedConfig = this.loadFromLocalStorage();
      if (savedConfig && this.validateConfig(savedConfig)) {
        console.log('✅ 已加载个人开发者配置');
        return savedConfig;
      }
    } catch (error) {
      console.warn('⚠️ 加载个人配置失败，将创建默认配置', error);
    }

    // 创建默认配置
    const defaultConfig = this.createDefaultConfig();
    await this.saveConfig(defaultConfig);
    console.log('🚀 已创建个人开发者默认配置');

    return defaultConfig;
  }

  /**
   * 创建默认配置 - 严格遵循项目目录原则
   */
  private createDefaultConfig(): PersonalDeveloperConfig {
    const platformInfo = CrossPlatformHelper.getPlatformInfo();
    const projectRoot = CrossPlatformHelper.getProjectRoot();

    return {
      version: this.CONFIG_VERSION,
      platform: {
        os: platformInfo.os,
        homeDir: platformInfo.homeDir,
        pathSeparator: platformInfo.pathSeparator
      },
      dataPaths: {
        root: CrossPlatformHelper.joinPath(projectRoot, 'data'),
        csvData: CrossPlatformHelper.joinPath(projectRoot, 'data', 'csv_imports'),
        fromC2C: CrossPlatformHelper.joinPath(projectRoot, 'data', 'FromC2C'),
        duckdb: CrossPlatformHelper.joinPath(projectRoot, 'data', 'databases'),
        logs: CrossPlatformHelper.joinPath(projectRoot, 'logs'),
        cache: CrossPlatformHelper.joinPath(projectRoot, 'cache')
      },
      preferences: {
        quickStart: true,
        defaultEnvironment: 'dev',
        autoCreateDirectories: true,
        enableDebugMode: CrossPlatformHelper.isDevelopment(),
        preferredCsvEncoding: 'UTF-8',
        maxFileSize: 500 * 1024 * 1024, // 500MB
        batchConcurrency: 3
      },
      development: {
        frontendPort: 8080,
        backendPort: 8000,
        hotReload: true,
        mockDataEnabled: CrossPlatformHelper.isDevelopment()
      },
      integration: {
        fromC2C: {
          enabled: true,
          defaultValidation: true,
          autoReports: true,
          maxFiles: 500
        },
        mysql: {
          enabled: true,
          defaultTimeout: 30,
          maxConnections: 5
        }
      }
    };
  }

  /**
   * 验证配置对象
   */
  private validateConfig(config: PersonalDeveloperConfig): boolean {
    try {
      return (
        config &&
        typeof config === 'object' &&
        config.version &&
        config.platform &&
        config.dataPaths &&
        config.preferences &&
        config.development &&
        config.integration
      );
    } catch {
      return false;
    }
  }

  /**
   * 从localStorage加载配置
   */
  private loadFromLocalStorage(): PersonalDeveloperConfig | null {
    if (typeof window === 'undefined') return null;

    try {
      const saved = localStorage.getItem('aqua_personal_config');
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  }

  /**
   * 保存配置
   */
  async saveConfig(config: PersonalDeveloperConfig): Promise<void> {
    this._config = config;

    // 保存到localStorage
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('aqua_personal_config', JSON.stringify(config));
      } catch (error) {
        console.warn('⚠️ 无法保存配置到localStorage', error);
      }
    }

    // 如果在Node.js环境，可以考虑保存到文件
    await this.saveToFile();
  }

  /**
   * 保存配置到文件（Node.js环境）
   */
  private async saveToFile(): Promise<void> {
    // 这里可以实现文件保存逻辑
    // 由于是前端代码，实际的文件操作需要通过API调用后端
    console.log('💾 配置已更新到内存，如需持久化请通过API保存');
  }

  /**
   * 更新配置项
   */
  async updateConfig(updates: Partial<PersonalDeveloperConfig>): Promise<void> {
    const currentConfig = await this.getConfig();
    const newConfig = this.deepMerge(currentConfig, updates);
    await this.saveConfig(newConfig);
  }

  /**
   * 深度合并配置对象
   */
  private deepMerge(
    target: Record<string, unknown>,
    source: Record<string, unknown>
  ): Record<string, unknown> {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  }

  /**
   * 重置为默认配置
   */
  async resetToDefault(): Promise<void> {
    const defaultConfig = this.createDefaultConfig();
    await this.saveConfig(defaultConfig);
    console.log('🔄 已重置为默认个人配置');
  }

  /**
   * 获取特定的配置项
   */
  async getConfigValue<T>(path: string): Promise<T | undefined> {
    const config = await this.getConfig();
    return this.getNestedValue(config, path);
  }

  /**
   * 设置特定的配置项
   */
  async setConfigValue(path: string, value: unknown): Promise<void> {
    const config = await this.getConfig();
    this.setNestedValue(config, path, value);
    await this.saveConfig(config);
  }

  /**
   * 获取嵌套对象值
   */
  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 设置嵌套对象值
   */
  private setNestedValue(obj: Record<string, unknown>, path: string, value: unknown): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key] as Record<string, unknown>;
    }, obj);
    target[lastKey] = value;
  }

  /**
   * 获取数据路径的完整映射
   */
  async getDataPathsMapping(): Promise<Record<string, string>> {
    const config = await this.getConfig();
    return {
      ...config.dataPaths,
      // 添加一些常用的路径别名
      import: CrossPlatformHelper.getRecommendedImportPath(),
      temp: CrossPlatformHelper.getTempDir(),
      config: CrossPlatformHelper.getConfigDir()
    };
  }

  /**
   * 检查并创建必要的目录
   */
  async ensureDirectories(): Promise<void> {
    const config = await this.getConfig();

    if (!config.preferences.autoCreateDirectories) {
      return;
    }

    const pathsToCreate = [
      config.dataPaths.root,
      config.dataPaths.csvData,
      config.dataPaths.fromC2C,
      config.dataPaths.cache,
      CrossPlatformHelper.joinPath(config.dataPaths.root, 'databases'),
      CrossPlatformHelper.joinPath(config.dataPaths.root, 'logs')
    ];

    // 这里需要通过API调用后端来创建目录
    console.log('📁 需要创建的目录:', pathsToCreate);

    // 前端不能直接创建目录，需要通过API
    // 这个功能将在后端实现
  }

  /**
   * 导出配置用于调试
   */
  async exportConfig(): Promise<string> {
    const config = await this.getConfig();
    const diagnosticInfo = CrossPlatformHelper.getDiagnosticInfo();

    return JSON.stringify(
      {
        personalConfig: config,
        platformDiagnostic: diagnosticInfo,
        timestamp: new Date().toISOString(),
        version: this.CONFIG_VERSION
      },
      null,
      2
    );
  }

  /**
   * 获取快速启动参数
   */
  async getQuickStartSettings(): Promise<Record<string, unknown>> {
    const config = await this.getConfig();

    return {
      enabled: config.preferences.quickStart,
      environment: config.preferences.defaultEnvironment,
      ports: {
        frontend: config.development.frontendPort,
        backend: config.development.backendPort
      },
      dataPaths: await this.getDataPathsMapping(),
      fromC2CSettings: config.integration.fromC2C
    };
  }

  /**
   * 获取平台优化建议
   */
  async getPlatformOptimizations(): Promise<string[]> {
    const config = await this.getConfig();
    const suggestions: string[] = [];

    // 基于平台的优化建议
    switch (config.platform.os) {
      case 'windows':
        suggestions.push('Windows系统建议使用PowerShell作为默认终端');
        suggestions.push('建议启用WSL2以获得更好的开发体验');
        if (CrossPlatformHelper.hasChineseChars(config.platform.homeDir)) {
          suggestions.push('检测到中文用户路径，建议将数据目录设置为纯英文路径');
        }
        break;

      case 'macos':
        suggestions.push('macOS系统建议安装Homebrew包管理器');
        suggestions.push('推荐使用iTerm2替代默认终端');
        break;

      case 'linux':
        suggestions.push('Linux系统建议检查Python和Node.js版本兼容性');
        suggestions.push('确保有足够的文件描述符限制');
        break;
    }

    // 基于配置的建议
    if (config.preferences.maxFileSize > 1024 * 1024 * 1024) {
      // > 1GB
      suggestions.push('文件大小限制较高，建议在生产环境中降低此设置');
    }

    if (config.preferences.batchConcurrency > 5) {
      suggestions.push('批处理并发数较高，请确保系统性能足够');
    }

    return suggestions;
  }

  /**
   * 检查配置健康状态
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'warning' | 'error'; issues: string[] }> {
    const config = await this.getConfig();
    const issues: string[] = [];

    // 检查版本兼容性
    if (config.version !== this.CONFIG_VERSION) {
      issues.push(`配置版本不匹配: ${config.version} != ${this.CONFIG_VERSION}`);
    }

    // 检查路径有效性
    if (CrossPlatformHelper.hasChineseChars(config.dataPaths.root)) {
      issues.push('数据根路径包含中文字符，可能导致兼容性问题');
    }

    // 检查端口冲突
    if (config.development.frontendPort === config.development.backendPort) {
      issues.push('前后端端口冲突');
    }

    const status =
      issues.length === 0
        ? 'healthy'
        : issues.some((issue) => issue.includes('版本') || issue.includes('端口'))
          ? 'error'
          : 'warning';

    return { status, issues };
  }
}

// 默认导出单例实例
export default PersonalConfigManager.getInstance();

export const validateConfig = (config: GeminiConfig): boolean => {
  return Object.values(config).every((v) => typeof v !== 'undefined');
};
