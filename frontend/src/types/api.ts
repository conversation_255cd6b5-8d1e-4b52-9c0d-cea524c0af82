/**
 * 能力编号：010101
 * [POST] /api/data/import
 */
export interface ImportDataRequest {
  file: File;
  data_type?: string;
}
export interface ImportDataResponse {
  code: number;
  message: string;
  details: { rows: number; table: string };
}

/**
 * 能力编号：010102
 * [GET] /api/data/tables
 */
export interface DataTable {
  name: string;
  columns: string[];
}
export interface GetTablesResponse {
  code: number;
  tables: DataTable[];
}

/**
 * 能力编号：030201
 * [GET] /api/backtest/performance
 */
export interface GetPerformanceRequest {
  backtest_id: string;
}
export interface GetPerformanceResponse {
  code: number;
  performance: {
    return: number;
    max_drawdown: number;
    sharpe: number;
  };
}

export interface GeminiOptimizationPayload {
  configVersion: string;
  optimizationTargets: {
    module: 'data_center' | 'trade_engine';
    parameters: Record<string, number>;
  }[];
  timestamp: number;
}

export type ApiResponse<T = unknown> = {
  code: 200 | 400 | 500;
  data: T;
  message?: string;
};
