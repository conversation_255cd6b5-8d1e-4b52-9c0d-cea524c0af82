import { defineStore } from 'pinia';
import { darkTheme } from 'naive-ui';

type ThemeState = {
  theme: 'light' | 'dark';
};

/**
 * 主题管理store，负责全局明暗模式切换与持久化
 * - 默认主题为light
 * - 切换后写入localStorage，页面class同步
 */
export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    theme: (localStorage.getItem('theme') as 'light' | 'dark') || 'light'
  }),
  getters: {
    naiveTheme(state) {
      return state.theme === 'dark' ? darkTheme : null;
    },
    naiveThemeOverrides() {
      // You can define and return theme overrides here if needed
      return {};
    }
  },
  actions: {
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', this.theme);
      // 同步body class，便于全局样式切换
      document.body.classList.remove('light', 'dark');
      document.body.classList.add(this.theme);
    },
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme;
      localStorage.setItem('theme', this.theme);
      document.body.classList.remove('light', 'dark');
      document.body.classList.add(theme);
    }
  }
});
