/* AQUA项目全局样式 - 支持三明治布局和响应式设计 */

:root {
  /* 字体配置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 主题变量 */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-text: #1f2937;
  --color-text-secondary: #6b7280;
  --color-background: #ffffff;
  --color-background-secondary: #f8f9fa;
  --color-border: #e5e7eb;
  --color-shadow: rgba(0, 0, 0, 0.1);

  /* 布局变量 */
  --header-height: 64px;
  --sidebar-width: 240px;
  --sidebar-width-collapsed: 60px;
  --content-padding: 24px;

  /* 响应式断点 */
  --breakpoint-mobile: 768px;
  --breakpoint-tablet: 1024px;
  --breakpoint-desktop: 1280px;

  /* 动画变量 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  color-scheme: light dark;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 重置和基础样式 */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  min-height: 100vh;
  color: var(--color-text);
  background-color: var(--color-background);
  overflow-x: hidden;
}

/* 应用容器重置 */
#app {
  height: 100vh;
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
  text-align: left;
  display: flex;
  flex-direction: column;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-hover);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.25;
  color: var(--color-text);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* 按钮基础样式重置 */
button {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  outline: none;
}

button:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 输入框样式 */
input, textarea, select {
  font-family: inherit;
  font-size: 14px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 8px 12px;
  transition: border-color var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* 实用工具类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text: #f9fafb;
    --color-text-secondary: #9ca3af;
    --color-background: #111827;
    --color-background-secondary: #1f2937;
    --color-border: #374151;
    --color-shadow: rgba(0, 0, 0, 0.3);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --sidebar-width: 100%;
    --content-padding: 16px;
  }
}

@media (max-width: 480px) {
  :root {
    --content-padding: 12px;
  }
  
  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}
