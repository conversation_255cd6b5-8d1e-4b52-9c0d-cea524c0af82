<template>
  <div class="virtual-table" :style="{ height: containerHeight + 'px' }">
    <div
      class="virtual-table-container"
      :style="{ height: totalHeight + 'px' }"
      @scroll="handleScroll"
      ref="scrollContainer"
    >
      <div class="virtual-table-content" :style="{ transform: `translateY(${offsetY}px)` }">
        <n-data-table
          :columns="columns"
          :data="visibleData"
          :loading="loading"
          :pagination="false"
          :scroll-x="scrollX"
          :row-key="rowKey"
          @update:checked-row-keys="handleCheckedRowsChange"
          :checked-row-keys="checkedRowKeys"
        />
      </div>
    </div>

    <!-- 加载更多指示器 -->
    <div v-if="hasMore && !loading" class="load-more-trigger" ref="loadMoreTrigger">
      <n-button @click="loadMore" :loading="loadingMore"> 加载更多 </n-button>
    </div>

    <!-- 加载中指示器 -->
    <div v-if="loading" class="loading-indicator">
      <n-spin size="medium" />
      <span class="loading-text">正在加载数据...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { NDataTable, NButton, NSpin, type DataTableProps } from 'naive-ui';
import type { DataTableRowKey } from 'naive-ui';
import { debounce } from 'lodash-es';

type RowData = Record<string, any>;

interface Props {
  data: RowData[];
  columns: DataTableProps['columns'];
  itemHeight?: number;
  containerHeight?: number;
  loading?: boolean;
  hasMore?: boolean;
  loadingMore?: boolean;
  scrollX?: number;
  rowKey?: DataTableProps['rowKey'];
  checkedRowKeys?: DataTableRowKey[];
  pageSize?: number;
  enableVirtualization?: boolean;
  maxCacheSize?: number;
  enablePerformanceMonitoring?: boolean;
}

interface Emits {
  (e: 'load-more'): void;
  (e: 'update:checked-row-keys', keys: DataTableRowKey[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 50,
  containerHeight: 400,
  loading: false,
  hasMore: false,
  loadingMore: false,
  scrollX: undefined,
  rowKey: (row: RowData) => row.id,
  checkedRowKeys: () => [],
  pageSize: 50,
  enableVirtualization: true,
  maxCacheSize: 1000,
  enablePerformanceMonitoring: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const scrollContainer = ref<HTMLElement>();
const loadMoreTrigger = ref<HTMLElement>();
const scrollTop = ref(0);
const startIndex = ref(0);
const endIndex = ref(0);
const visibleCount = ref(0);
const intersectionObserver = ref<IntersectionObserver>();

// 性能监控
const performanceMetrics = ref({
  renderTime: 0,
  scrollTime: 0,
  visibleItemCount: 0,
  cacheHitRate: 0,
  lastRenderTimestamp: 0
});

// 缓存机制
const renderedItemsCache = ref(new Map<string, any>());
const cacheHitCount = ref(0);
const cacheMissCount = ref(0);

// 计算属性
const totalHeight = computed(() => props.data.length * props.itemHeight);

const offsetY = computed(() => startIndex.value * props.itemHeight);

const visibleData = computed(() => {
  if (!props.enableVirtualization) {
    return props.data;
  }

  const startTime = performance.now();

  // 如果启用缓存，尝试从缓存获取
  if (props.maxCacheSize > 0) {
    const cacheKey = `${startIndex.value}-${endIndex.value}`;
    if (renderedItemsCache.value.has(cacheKey)) {
      cacheHitCount.value++;
      const cachedData = renderedItemsCache.value.get(cacheKey);

      // 更新性能指标
      if (props.enablePerformanceMonitoring) {
        performanceMetrics.value.renderTime = performance.now() - startTime;
        performanceMetrics.value.cacheHitRate =
          cacheHitCount.value / (cacheHitCount.value + cacheMissCount.value);
        performanceMetrics.value.visibleItemCount = cachedData.length;
      }

      return cachedData;
    }
  }

  // 缓存未命中，计算数据
  cacheMissCount.value++;
  const slicedData = props.data.slice(startIndex.value, endIndex.value + 1);

  // 保存到缓存
  if (props.maxCacheSize > 0) {
    const cacheKey = `${startIndex.value}-${endIndex.value}`;

    // 如果缓存满了，删除最旧的项
    if (renderedItemsCache.value.size >= props.maxCacheSize) {
      const firstKey = renderedItemsCache.value.keys().next().value;
      renderedItemsCache.value.delete(firstKey);
    }

    renderedItemsCache.value.set(cacheKey, slicedData);
  }

  // 更新性能指标
  if (props.enablePerformanceMonitoring) {
    performanceMetrics.value.renderTime = performance.now() - startTime;
    performanceMetrics.value.cacheHitRate =
      cacheHitCount.value / (cacheHitCount.value + cacheMissCount.value);
    performanceMetrics.value.visibleItemCount = slicedData.length;
    performanceMetrics.value.lastRenderTimestamp = Date.now();
  }

  return slicedData;
});

// 计算可见范围
const calculateVisibleRange = () => {
  if (!scrollContainer.value) return;

  const scrollTop = scrollContainer.value.scrollTop;
  const containerHeight = props.containerHeight;
  const itemHeight = props.itemHeight;

  // 计算可见区域
  const start = Math.floor(scrollTop / itemHeight);
  const visibleCount = Math.ceil(containerHeight / itemHeight);

  // 添加缓冲区，避免滚动时闪烁
  const bufferSize = Math.max(5, Math.floor(visibleCount / 2));

  startIndex.value = Math.max(0, start - bufferSize);
  endIndex.value = Math.min(props.data.length - 1, start + visibleCount + bufferSize);

  // 检查是否接近底部，触发懒加载
  const isNearBottom = scrollTop + containerHeight >= totalHeight.value - itemHeight * 5;
  if (isNearBottom && props.hasMore && !props.loadingMore) {
    emit('load-more');
  }
};

// 防抖的滚动处理
const handleScroll = debounce(() => {
  const scrollStartTime = performance.now();

  calculateVisibleRange();

  // 更新滚动性能指标
  if (props.enablePerformanceMonitoring) {
    performanceMetrics.value.scrollTime = performance.now() - scrollStartTime;
  }
}, 16); // 约60fps

// 加载更多
const loadMore = () => {
  if (!props.hasMore || props.loadingMore) return;
  emit('load-more');
};

// 处理选中行变化
const handleCheckedRowsChange = (keys: DataTableRowKey[]) => {
  emit('update:checked-row-keys', keys);
};

// 监听数据变化
watch(
  () => props.data.length,
  () => {
    nextTick(() => {
      calculateVisibleRange();
    });
  }
);

// 设置交叉观察器来实现无限滚动
const setupIntersectionObserver = () => {
  if (!loadMoreTrigger.value) return;

  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && props.hasMore && !props.loadingMore) {
          loadMore();
        }
      });
    },
    {
      rootMargin: '100px'
    }
  );

  intersectionObserver.value.observe(loadMoreTrigger.value);
};

// 生命周期
onMounted(() => {
  calculateVisibleRange();

  nextTick(() => {
    setupIntersectionObserver();
  });
});

onUnmounted(() => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }
});

// 清理缓存
const clearCache = () => {
  renderedItemsCache.value.clear();
  cacheHitCount.value = 0;
  cacheMissCount.value = 0;
};

// 获取性能指标
const getPerformanceMetrics = () => {
  return {
    ...performanceMetrics.value,
    cacheSize: renderedItemsCache.value.size,
    cacheHitCount: cacheHitCount.value,
    cacheMissCount: cacheMissCount.value
  };
};

// 导出方法供父组件调用
defineExpose({
  scrollToTop: () => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = 0;
      calculateVisibleRange();
    }
  },
  scrollToIndex: (index: number) => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = index * props.itemHeight;
      calculateVisibleRange();
    }
  },
  refresh: () => {
    calculateVisibleRange();
  },
  clearCache,
  getPerformanceMetrics
});
</script>

<script lang="ts">
export default {
  name: 'VirtualTable'
};
</script>

<style scoped>
.virtual-table {
  position: relative;
  overflow: hidden;
}

.virtual-table-container {
  overflow-y: auto;
  overflow-x: hidden;
}

.virtual-table-content {
  position: relative;
}

.load-more-trigger {
  display: flex;
  justify-content: center;
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  gap: 12px;
}

.loading-text {
  color: var(--text-color-2);
  font-size: 14px;
}

/* 滚动条样式 */
.virtual-table-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-table-container::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color);
  border-radius: 4px;
}

.virtual-table-container::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color);
  border-radius: 4px;
}

.virtual-table-container::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color);
}
</style>
