<template>
  <n-breadcrumb class="breadcrumb">
    <n-breadcrumb-item v-for="route in routes" :key="route.path">
      <n-icon v-if="route.meta?.icon" :component="route.meta.icon" />
      {{ route.meta?.title }}
    </n-breadcrumb-item>
  </n-breadcrumb>
</template>

<script setup lang="ts">
import { NBreadcrumb, NBreadcrumbItem, NIcon } from 'naive-ui';

// 定义props类型
interface BreadcrumbRoute {
  path: string;
  meta?: {
    title?: string;
    icon?: any;
    [key: string]: any;
  };
}

defineProps<{
  routes?: BreadcrumbRoute[];
}>();
</script>

<style scoped>
.breadcrumb {
  margin-bottom: 16px;
  font-size: 1rem;
}
</style>
