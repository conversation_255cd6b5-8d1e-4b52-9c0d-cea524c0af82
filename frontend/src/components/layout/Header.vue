<!--
  Header.vue
  全局头部组件，包含侧边栏切换、Logo、导航菜单、主题切换按钮。
  职责：展示品牌Logo、主导航入口、侧边栏控制、主题切换功能。
-->
<script setup lang="ts">
import { useThemeStore } from '@/stores/theme_store';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';

// 定义emits
const emit = defineEmits<{
  toggleSidebar: [];
}>();

const themeStore = useThemeStore();
const { theme } = storeToRefs(themeStore);
const router = useRouter();

/**
 * 主题切换按钮点击事件
 */
function handleToggleTheme() {
  themeStore.toggleTheme();
}

/**
 * 侧边栏切换按钮点击事件
 */
function handleToggleSidebar() {
  emit('toggleSidebar');
}

/**
 * 导航点击事件
 */
function navigateTo(path: string) {
  router.push(path);
}
</script>

<template>
  <header class="header">
    <!-- 左侧：侧边栏切换按钮 + Logo -->
    <div class="header-left">
      <button class="sidebar-toggle" @click="handleToggleSidebar" aria-label="切换侧边栏">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          ></path>
        </svg>
      </button>
      <div class="logo-section">
        <div class="logo-icon">A</div>
        <span class="logo-text">AQUA</span>
      </div>
    </div>

    <!-- 中间：导航菜单区 -->
    <nav class="header-nav">
      <button class="nav-item" @click="navigateTo('/')">首页</button>
      <button class="nav-item" @click="navigateTo('/data-center')">数据中心</button>
    </nav>

    <!-- 右侧：主题切换按钮 -->
    <div class="header-right">
      <button class="theme-toggle" @click="handleToggleTheme" aria-label="切换主题">
        <svg
          v-if="theme === 'light'"
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          ></path>
        </svg>
        <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          ></path>
        </svg>
      </button>
    </div>
  </header>
</template>

<!--
  说明：
  - 集成侧边栏切换、主导航、主题切换功能
  - 使用SVG图标，支持主题切换
  - 响应式设计，移动端友好
  - 关键节点均有详细中文注释，符合AQUA合规要求。
-->

<style scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background: #f3f4f6;
  color: #374151;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 8px;
  font-weight: bold;
  font-size: 18px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  letter-spacing: 0.05em;
}

/* 中间导航区域 */
.header-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-item {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: #f3f4f6;
  color: #3b82f6;
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
}

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  .header {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  .logo-text {
    color: #f9fafb;
  }

  .sidebar-toggle:hover,
  .theme-toggle:hover {
    background: #374151;
    color: #f3f4f6;
  }

  .nav-item {
    color: #9ca3af;
  }

  .nav-item:hover {
    background: #374151;
    color: #60a5fa;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }

  .header-nav {
    display: none;
  }

  .logo-text {
    font-size: 20px;
  }
}
</style>
