<template>
  <!-- 侧边栏组件，展示多级菜单，支持路由导航 -->
  <nav class="sidebar">
    <div class="sidebar-content">
      <!-- 菜单标题 -->
      <div class="sidebar-header">
        <h3 class="sidebar-title">功能菜单</h3>
      </div>

      <!-- 菜单列表 -->
      <div class="sidebar-menu">
        <Menu :menu="menuData" />
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { MenuItem } from '@/types/menu';
import Menu from './Menu.vue';

// 菜单数据
const menuData = ref<MenuItem[]>([
  {
    id: 'home',
    name: '首页',
    icon: 'home',
    route: '/'
  },
  {
    id: 'data-center',
    name: '数据中心',
    icon: 'database',
    route: '/data-center',
    children: [
      {
        id: 'data-import',
        name: '数据导入',
        route: '/data-center/import'
      },
      {
        id: 'data-browse',
        name: '数据浏览',
        route: '/data-center/browse'
      },
      {
        id: 'data-view',
        name: '数据查看',
        route: '/data-center/view'
      }
    ]
  },
  {
    id: 'strategy',
    name: '策略管理',
    icon: 'strategy',
    children: [
      {
        id: 'strategy-list',
        name: '策略列表',
        route: '/strategy/list'
      },
      {
        id: 'strategy-create',
        name: '创建策略',
        route: '/strategy/create'
      }
    ]
  },
  {
    id: 'backtest',
    name: '回测分析',
    icon: 'chart',
    children: [
      {
        id: 'backtest-history',
        name: '历史回测',
        route: '/backtest/history'
      },
      {
        id: 'backtest-result',
        name: '回测结果',
        route: '/backtest/result'
      }
    ]
  },
  {
    id: 'simulator',
    name: '模拟交易',
    icon: 'simulator',
    children: [
      {
        id: 'simulator-dashboard',
        name: '交易看板',
        route: '/simulator/dashboard'
      },
      {
        id: 'simulator-orders',
        name: '订单管理',
        route: '/simulator/orders'
      }
    ]
  },
  {
    id: 'ai-agent',
    name: 'AI助手',
    icon: 'ai',
    route: '/ai-agent'
  }
]);
</script>

<style scoped>
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-right: 1px solid #e5e7eb;
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px 0;
}

.sidebar-header {
  padding: 0 20px 16px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.sidebar-title {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  .sidebar {
    background: #1f2937;
    border-right-color: #374151;
  }

  .sidebar-header {
    border-bottom-color: #374151;
  }

  .sidebar-title {
    color: #9ca3af;
  }
}
</style>
