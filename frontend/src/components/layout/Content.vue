<!--
  Content.vue
  内容区域通用布局组件，支持插槽与自适应布局。
  职责：作为主内容承载区，包裹页面主体内容，支持自适应宽高。
-->
<template>
  <!-- 根元素main，具备自适应布局class -->
  <main class="content-area">
    <!-- 面包屑导航，始终显示在主内容区顶部 -->
    <Breadcrumb v-if="routes && routes.length" :routes="routes" />
    <!-- 默认插槽，渲染任意内容 -->
    <slot />
  </main>
</template>

<script setup lang="ts">
// Content组件：用于承载主内容区域，支持插槽与自适应布局
import Breadcrumb from './Breadcrumb.vue';

// 定义props类型
interface BreadcrumbRoute {
  path: string;
  meta?: {
    title?: string;
    [key: string]: any;
  };
}

defineProps<{
  routes?: BreadcrumbRoute[];
}>();
</script>

<style scoped>
.content-area {
  flex: 1 1 auto;
  min-height: 0;
  min-width: 0;
  padding: 24px;
  box-sizing: border-box;
  width: 100%;
  /* 响应式断点可按需扩展 */
}
</style>
