<template>
  <nav class="menu">
    <template v-if="menu && menu.length">
      <MenuList :items="menu" :level="0" />
    </template>
    <div v-else class="menu-empty">暂无菜单</div>
  </nav>
</template>

<script setup lang="ts">
/**
 * 菜单组件入口，递归调用MenuList子组件
 * 结构与mock/type一致，支持多级、icon、权限等
 */
import type { MenuItem } from '@/types/menu';
import MenuList from './MenuList.vue';

// 定义props
defineProps<{
  menu?: MenuItem[];
}>();
</script>

<style scoped>
.menu {
  width: 100%;
}

.menu-empty {
  color: #9ca3af;
  padding: 16px;
  text-align: center;
  font-size: 14px;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  .menu-empty {
    color: #6b7280;
  }
}
</style>
