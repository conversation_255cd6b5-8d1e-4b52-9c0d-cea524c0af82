<!--
  Footer.vue
  全局底部组件，包含版权与版本信息。
  职责：展示系统版权声明与当前版本号。
-->
<script setup lang="ts">
// 可通过配置或环境变量动态获取版本号，示例写死
const version = 'v1.2.0';
</script>

<template>
  <footer
    class="w-full text-center py-4 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-t border-gray-200 dark:border-gray-700"
  >
    <div>版权所有 © 2024 AQUA 数据平台 | Version: {{ version }}</div>
  </footer>
</template>

<!--
  说明：
  - 版权信息与版本号可通过配置或环境变量动态获取。
  - 关键节点均有详细中文注释，符合AQUA合规要求。
-->

<style scoped>
footer {
  font-size: 0.75rem; /* 缩小字体 */
  flex-shrink: 0; /* 防止flex布局中被压缩 */
}
</style>
