<template>
  <ul :class="['menu-list', 'menu-level-' + level]">
    <li v-for="item in items" :key="item.id" class="menu-item-wrapper">
      <!-- 菜单项 -->
      <div
        :class="['menu-item', { 'has-children': hasChildren(item), 'is-active': isActive(item) }]"
        @click="handleItemClick(item)"
      >
        <div class="menu-item-content">
          <!-- 图标 -->
          <div v-if="item.icon" class="menu-icon">
            <MenuIcon :type="item.icon" />
          </div>
          <!-- 文本 -->
          <span class="menu-text">{{ item.name }}</span>
          <!-- 展开/收起图标 -->
          <div v-if="hasChildren(item)" class="menu-arrow">
            <svg
              :class="['arrow-icon', { expanded: expandedItems.has(item.id) }]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 子菜单 -->
      <Transition name="menu-slide">
        <MenuList
          v-if="hasChildren(item) && expandedItems.has(item.id)"
          :items="item.children!"
          :level="level + 1"
        />
      </Transition>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type { MenuItem } from '@/types/menu';
import MenuIcon from './MenuIcon.vue';

// 定义props
defineProps<{
  items: MenuItem[];
  level: number;
}>();

const router = useRouter();
const route = useRoute();

// 展开的菜单项 - 默认展开数据中心
const expandedItems = ref<Set<string>>(new Set(['data-center']));

/**
 * 判断菜单项是否有子项
 */
function hasChildren(item: MenuItem): boolean {
  return !!(item.children && item.children.length > 0);
}

/**
 * 判断菜单项是否激活
 */
function isActive(item: MenuItem): boolean {
  if (item.route) {
    return route.path === item.route || route.path.startsWith(item.route + '/');
  }
  return false;
}

/**
 * 处理菜单项点击
 */
function handleItemClick(item: MenuItem) {
  if (hasChildren(item)) {
    // 有子菜单，切换展开状态
    if (expandedItems.value.has(item.id)) {
      expandedItems.value.delete(item.id);
    } else {
      expandedItems.value.add(item.id);
    }
  } else if (item.route) {
    // 没有子菜单且有路由，导航到指定路由
    router.push(item.route);
  }
}
</script>

<style scoped>
.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item-wrapper {
  margin-bottom: 2px;
}

.menu-item {
  position: relative;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.menu-item:hover {
  background: #f1f5f9;
}

.menu-item.is-active {
  background: #eff6ff;
  color: #2563eb;
}

.menu-item.is-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: #2563eb;
  border-radius: 0 2px 2px 0;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  gap: 8px;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.menu-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  line-height: 1.2;
}

.menu-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.arrow-icon {
  width: 12px;
  height: 12px;
  color: #9ca3af;
  transition: transform 0.2s ease;
}

.arrow-icon.expanded {
  transform: rotate(90deg);
}

/* 不同层级的样式 */
.menu-level-1 .menu-item-content {
  padding-left: 28px;
}

.menu-level-2 .menu-item-content {
  padding-left: 44px;
}

.menu-level-3 .menu-item-content {
  padding-left: 60px;
}

/* 子菜单展开/收起动画 */
.menu-slide-enter-active,
.menu-slide-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.menu-slide-enter-from,
.menu-slide-leave-to {
  max-height: 0;
  opacity: 0;
}

.menu-slide-enter-to,
.menu-slide-leave-from {
  max-height: 500px;
  opacity: 1;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  .menu-item:hover {
    background: #374151;
  }

  .menu-item.is-active {
    background: #1e40af;
    color: #dbeafe;
  }

  .menu-item.is-active::before {
    background: #60a5fa;
  }

  .menu-icon {
    color: #9ca3af;
  }

  .menu-text {
    color: #e5e7eb;
  }

  .arrow-icon {
    color: #6b7280;
  }
}
</style>
