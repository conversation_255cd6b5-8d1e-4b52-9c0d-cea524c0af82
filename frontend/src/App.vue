<!--
  App.vue
  全局入口组件，实现完整的三明治布局结构。
  职责：作为AQUA前端SPA的根组件，承载Header-Sidebar-Content-Footer的完整布局。
-->
<script setup lang="ts">
// 集成主题切换与全局状态管理
import { useThemeStore } from './stores/theme_store';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { computed, ref } from 'vue';

// 导入布局组件
import Header from './components/layout/Header.vue';
import Sidebar from './components/layout/Sidebar.vue';
import Content from './components/layout/Content.vue';
import Footer from './components/layout/Footer.vue';
import NaiveUiProvider from './components/common/NaiveUiProvider.vue';

const themeStore = useThemeStore();
const { theme } = storeToRefs(themeStore);
const route = useRoute();

// 侧边栏展开/收起状态
const sidebarCollapsed = ref(false);

/**
 * 切换侧边栏展开状态
 */
function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value;
}

// 自动生成面包屑routes，取matched路由链
const breadcrumbRoutes = computed(() =>
  (route?.matched ?? []).map((r) => ({ path: r.path, meta: r.meta }))
);
</script>

<template>
  <NaiveUiProvider>
    <!-- 三明治布局：Header + Main(Sidebar + Content) + Footer -->
    <div id="app" class="app-container">
      <!-- 顶部Header -->
      <Header @toggle-sidebar="toggleSidebar" />

      <!-- 主体区域：Sidebar + Content -->
      <main class="main-layout">
        <!-- 左侧边栏 -->
        <aside
          :class="['sidebar-container', { collapsed: sidebarCollapsed }]"
          v-show="!sidebarCollapsed"
        >
          <Sidebar />
        </aside>

        <!-- 右侧内容区 -->
        <section class="content-container">
          <Transition name="fade" mode="out-in">
            <Content :routes="breadcrumbRoutes">
              <router-view />
            </Content>
          </Transition>
        </section>
      </main>

      <!-- 底部Footer -->
      <Footer />
    </div>
  </NaiveUiProvider>
</template>

<!--
  说明：
  - 采用Vue3 <script setup>语法，便于类型推断与IDE支持。
  - 实现完整的三明治布局：Header + Sidebar&Content + Footer
  - 支持侧边栏展开/收起功能，响应式布局
  - 路由页面切换动画采用<Transition name="fade">，基础淡入淡出，mode="out-in"保证切换流畅。
  - 详细注释见AQUA_GUIDE.md与mvp_rules.md。
-->

<style scoped>
/* 全局布局容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* 主体布局：侧边栏 + 内容区 */
.main-layout {
  display: flex;
  flex: 1;
  min-height: 0; /* 防止flex子元素溢出 */
}

/* 侧边栏容器 */
.sidebar-container {
  width: 240px;
  min-width: 240px;
  background: #f8f9fa;
  border-right: 1px solid #e5e7eb;
  transition:
    width 0.3s ease,
    min-width 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-container.collapsed {
  width: 60px;
  min-width: 60px;
}

/* 内容区容器 */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 防止flex子元素溢出 */
  background: #ffffff;
  overflow-y: auto; /* 允许内容区垂直滚动 */
}

/* 页面切换淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .sidebar-container {
    width: 100%;
    min-width: 100%;
    height: auto;
    max-height: 200px;
  }

  .sidebar-container.collapsed {
    display: none;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .sidebar-container {
    background: #1f2937;
    border-right-color: #374151;
  }

  .content-container {
    background: #111827;
  }
}
</style>
