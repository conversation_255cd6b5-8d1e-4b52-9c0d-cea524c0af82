<!--
  HomePage.vue
  首页内容组件，展示系统简介与核心功能入口卡片。
  职责：作为AQUA平台首页，提供简介与主要功能导航。
-->
<template>
  <section class="homepage">
    <!-- 系统简介 -->
    <div class="intro">
      <h1>AQUA</h1>
      <p>不能躺平不能摆烂那就想着躺赢吧。</p>
    </div>
    <!-- 核心功能入口卡片 -->
    <div class="features flex flex-col md:flex-row gap-4 md:gap-8 w-full max-w-4xl px-2">
      <div
        v-for="(item, idx) in features"
        :key="item.title"
        class="feature-card flex-1 mb-4 md:mb-0"
        :class="{ hovered: hoveredIdx === idx }"
        @mouseenter="hoveredIdx = idx"
        @mouseleave="hoveredIdx = null"
      >
        <h2 class="text-lg md:text-xl font-semibold mb-4">{{ item.title }}</h2>
        <button
          class="w-full md:w-auto text-sm md:text-base py-2 px-4 mt-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition"
          @click="item.onClick"
        >
          {{ item.btn }}
        </button>
      </div>
    </div>
  </section>
  <router-view v-slot="{ Component }">
    <transition name="fade">
      <component :is="Component" />
    </transition>
  </router-view>
</template>

<script setup lang="ts">
// HomePage组件：展示系统简介与核心功能入口卡片
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 功能卡片数据
const features = [
  {
    title: '数据中心',
    btn: '进入数据中心',
    onClick: () => {
      console.log('按钮被点击，准备跳转到数据中心');
      router
        .push('/data-center')
        .then(() => {
          console.log('路由跳转成功');
        })
        .catch((error) => {
          console.error('路由跳转失败:', error);
        });
    }
  },
  {
    title: '回测结果',
    btn: '查看回测',
    onClick: () => window.alert('回测结果功能开发中')
  },
  {
    title: 'AI助手',
    btn: '体验AI助手',
    onClick: () => window.alert('AI助手功能开发中')
  }
];
// 当前hover的卡片索引
const hoveredIdx = ref<number | null>(null);
</script>

<style scoped>
.homepage {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
}
.intro {
  margin-bottom: 32px;
  text-align: center;
}
/* features区布局已由Tailwind断点控制，无需再写display/gap */
.feature-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition:
    box-shadow 0.2s,
    transform 0.2s;
}
.feature-card.hovered {
  box-shadow: 0 6px 24px rgba(49, 130, 206, 0.18);
  transform: scale(1.04);
  /* 可扩展更多hover效果 */
}
.feature-card h2 {
  margin-bottom: 16px;
}
/* 按钮样式已由Tailwind断点控制 */
</style>
