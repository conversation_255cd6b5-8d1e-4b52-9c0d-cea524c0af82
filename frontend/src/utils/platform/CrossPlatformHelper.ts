/**
 * 跨平台帮助类
 * 支持 Windows, macOS, Linux 三大平台
 * 专为个人开发者设计的零配置跨平台解决方案
 */

export type OSType = 'windows' | 'macos' | 'linux';

export interface PlatformInfo {
  os: OSType;
  homeDir: string;
  pathSeparator: string;
  isProduction: boolean;
}

export class CrossPlatformHelper {
  private static _platformInfo: PlatformInfo | null = null;

  /**
   * 检测当前操作系统类型
   */
  static detectOS(): OSType {
    // 浏览器环境检测
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent.toLowerCase();
      if (userAgent.includes('win')) return 'windows';
      if (userAgent.includes('mac')) return 'macos';
      return 'linux';
    }

    // Node.js环境检测
    if (typeof process !== 'undefined' && process.platform) {
      const platform = process.platform;
      if (platform === 'darwin') return 'macos';
      if (platform === 'win32') return 'windows';
      return 'linux';
    }

    // 默认返回
    return 'macos';
  }

  /**
   * 获取平台信息
   */
  static getPlatformInfo(): PlatformInfo {
    if (!this._platformInfo) {
      const os = this.detectOS();
      this._platformInfo = {
        os,
        homeDir: this.getHomeDirectory(),
        pathSeparator: os === 'windows' ? '\\' : '/',
        isProduction: process.env.NODE_ENV === 'production'
      };
    }
    return this._platformInfo;
  }

  /**
   * 获取用户主目录
   */
  static getHomeDirectory(): string {
    if (typeof process !== 'undefined') {
      return process.env.HOME || process.env.USERPROFILE || '';
    }
    return '';
  }

  /**
   * 获取AQUA数据目录路径 - 严格在项目目录内
   */
  static getUserDataDir(): string {
    // 严格遵循项目目录原则，数据目录只能在项目内
    const projectRoot = this.getProjectRoot();
    return this.joinPath(projectRoot, 'data');
  }

  /**
   * 获取项目根目录
   */
  static getProjectRoot(): string {
    // 在浏览器环境中，假设项目根目录
    if (typeof window !== 'undefined') {
      return '/Users/<USER>/Documents/AQUA/Dev/AQUA'; // 临时硬编码，实际应通过API获取
    }

    // Node.js环境中获取项目根目录
    if (typeof process !== 'undefined') {
      return process.cwd();
    }

    return '/Users/<USER>/Documents/AQUA/Dev/AQUA';
  }

  /**
   * 获取应用配置目录 - 严格在项目目录内
   */
  static getConfigDir(): string {
    // 配置目录只能在项目内
    const projectRoot = this.getProjectRoot();
    return this.joinPath(projectRoot, 'config');
  }

  /**
   * 获取临时目录 - 严格在项目目录内
   */
  static getTempDir(): string {
    // 临时目录只能在项目内
    const projectRoot = this.getProjectRoot();
    return this.joinPath(projectRoot, 'temp');
  }

  /**
   * 跨平台路径拼接
   */
  static joinPath(...parts: string[]): string {
    const separator = this.getPlatformInfo().pathSeparator;
    return parts
      .filter((part) => part && part.trim())
      .map((part) => part.replace(/[\/\\]+/g, separator))
      .join(separator)
      .replace(new RegExp(`\\${separator}+`, 'g'), separator);
  }

  /**
   * 规范化路径
   */
  static normalizePath(path: string): string {
    if (!path) return '';

    const separator = this.getPlatformInfo().pathSeparator;
    return path.replace(/[\/\\]+/g, separator);
  }

  /**
   * 检查路径是否为绝对路径
   */
  static isAbsolutePath(path: string): boolean {
    if (!path) return false;

    const os = this.detectOS();
    if (os === 'windows') {
      return /^[A-Za-z]:[\/\\]/.test(path) || path.startsWith('\\\\');
    }
    return path.startsWith('/');
  }

  /**
   * 获取文件扩展名
   */
  static getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
  }

  /**
   * 生成平台特定的数据库文件路径
   */
  static getDatabasePath(dbName: string = 'aqua.db'): string {
    const dataDir = this.getUserDataDir();
    return this.joinPath(dataDir, 'databases', dbName);
  }

  /**
   * 生成平台特定的日志文件路径
   */
  static getLogPath(logName: string = 'aqua.log'): string {
    const dataDir = this.getUserDataDir();
    return this.joinPath(dataDir, 'logs', logName);
  }

  /**
   * 生成平台特定的缓存目录路径
   */
  static getCacheDir(): string {
    const os = this.detectOS();
    const homeDir = this.getHomeDirectory();

    switch (os) {
      case 'windows':
        return this.joinPath(homeDir, 'AppData', 'Local', 'AQUA', 'Cache');
      case 'macos':
        return this.joinPath(homeDir, 'Library', 'Caches', 'AQUA');
      case 'linux':
        return this.joinPath(homeDir, '.cache', 'AQUA');
      default:
        return this.joinPath(this.getUserDataDir(), 'cache');
    }
  }

  /**
   * 获取平台特定的默认CSV数据路径
   */
  static getDefaultCSVDataPath(): string {
    const dataDir = this.getUserDataDir();
    return this.joinPath(dataDir, 'csv_imports');
  }

  /**
   * 获取FromC2C数据的默认路径
   */
  static getFromC2CDataPath(): string {
    const dataDir = this.getUserDataDir();
    return this.joinPath(dataDir, 'FromC2C');
  }

  /**
   * 检查开发环境
   */
  static isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  /**
   * 获取环境变量，带默认值
   */
  static getEnvVar(key: string, defaultValue: string = ''): string {
    return process.env[key] || defaultValue;
  }

  /**
   * 格式化文件大小显示
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * 生成时间戳文件名
   */
  static generateTimestampFilename(prefix: string = 'file', extension: string = 'txt'): string {
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
    return `${prefix}_${timestamp}.${extension}`;
  }

  /**
   * 安全的文件名处理（移除特殊字符）
   */
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[<>:"/\\|?*]/g, '_') // Windows不允许的字符
      .replace(/\s+/g, '_') // 空格替换为下划线
      .replace(/_{2,}/g, '_') // 多个下划线合并为一个
      .trim();
  }

  /**
   * 检查路径中是否包含中文字符
   */
  static hasChineseChars(path: string): boolean {
    return /[\u4e00-\u9fff]/.test(path);
  }

  /**
   * 获取推荐的数据导入路径（避免中文路径问题）
   */
  static getRecommendedImportPath(): string {
    const dataDir = this.getUserDataDir();
    const importPath = this.joinPath(dataDir, 'import_data');

    // 如果路径包含中文，返回纯英文路径
    if (this.hasChineseChars(importPath)) {
      const os = this.detectOS();
      switch (os) {
        case 'windows':
          return 'C:\\AQUA_Data\\import_data';
        case 'macos':
        case 'linux':
          return '/tmp/AQUA_import_data';
        default:
          return '/tmp/AQUA_import_data';
      }
    }

    return importPath;
  }

  /**
   * 打印平台诊断信息
   */
  static getDiagnosticInfo(): Record<string, any> {
    const info = this.getPlatformInfo();
    return {
      platform: {
        os: info.os,
        homeDir: info.homeDir,
        pathSeparator: info.pathSeparator,
        isProduction: info.isProduction,
        isDevelopment: this.isDevelopment()
      },
      paths: {
        userData: this.getUserDataDir(),
        config: this.getConfigDir(),
        cache: this.getCacheDir(),
        temp: this.getTempDir(),
        database: this.getDatabasePath(),
        logs: this.getLogPath(),
        csvData: this.getDefaultCSVDataPath(),
        fromC2C: this.getFromC2CDataPath(),
        recommendedImport: this.getRecommendedImportPath()
      },
      environment: {
        nodeEnv: process.env.NODE_ENV || 'undefined',
        hasChineseInHome: this.hasChineseChars(info.homeDir),
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A'
      }
    };
  }
}

// 默认导出实例方法以便于使用
export default CrossPlatformHelper;
