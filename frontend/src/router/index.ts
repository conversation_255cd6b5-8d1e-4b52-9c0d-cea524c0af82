// src/router/index.ts
// 路由配置文件，负责定义SPA所有页面路由及嵌套结构
// 业务说明：支持首页、404、主布局嵌套、后续可扩展多级菜单

import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';

/**
 * 路由配置数组
 * - 支持基础路由（/、404）
 * - 支持主布局+子页面多级嵌套
 * - 所有组件均为懒加载，关键节点有详细中文注释
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../modules/home/<USER>'),
    meta: {
      title: '首页'
    }
  },
  // 数据中心路由组 - 已迁移至deprecated/data_import_legacy
  // TODO: Phase 3 - 使用V4.0数据采集MVP-CLI替代
  /*
  {
    path: '/data-center',
    name: 'DataCenter',
    component: () => import('../views/RouteView.vue'),
    meta: {
      title: '数据中心'
    },
    children: [
      // 数据中心子路由已移除，等待V4.0重新设计
    ]
  },
  */
  // 404处理
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFoundPage.vue')
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 全局路由守卫
router.beforeEach((to, from, next) => {
  if (to.matched.length === 0) {
    console.error(`路由匹配失败: ${to.path}`);
    return next('/');
  }
  next();
});

export default router;
