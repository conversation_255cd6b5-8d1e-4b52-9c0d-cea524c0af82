#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA 项目统一管理CLI - 入口脚本
Windows和Unix跨平台兼容版本
自动检测并使用虚拟环境
"""
import sys
import os
import subprocess
from pathlib import Path

def detect_virtual_env():
    """检测并返回虚拟环境Python路径"""
    project_root = Path(__file__).resolve().parent
    
    # 检查是否已在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        return sys.executable
    
    # 检查.venv目录
    venv_dir = project_root / ".venv"
    if venv_dir.exists():
        if os.name == 'nt':  # Windows
            venv_python = venv_dir / "Scripts" / "python.exe"
        else:  # macOS/Linux
            venv_python = venv_dir / "bin" / "python"
        
        if venv_python.exists():
            return str(venv_python)
    
    return None

def setup_python_path():
    """设置Python导入路径，支持Windows和Unix"""
    # 获取项目根目录
    project_root = Path(__file__).resolve().parent
    src_dir = project_root / "src"
    
    # 确保src目录存在
    if not src_dir.exists():
        print(f"错误: src目录不存在: {src_dir}", file=sys.stderr)
        sys.exit(1)
    
    # 添加src目录到Python路径的最前面
    src_path = str(src_dir)
    if src_path in sys.path:
        sys.path.remove(src_path)
    sys.path.insert(0, src_path)
    
    # Windows特定的UTF-8设置
    if os.name == 'nt':  # Windows
        os.environ.setdefault('PYTHONUTF8', '1')
        os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    return project_root, src_dir

def main():
    """
    CLI主入口函数。
    跨平台兼容，支持Windows和Unix系统。
    自动检测并使用虚拟环境。
    """
    # 检测虚拟环境
    venv_python = detect_virtual_env()
    
    # 如果找到虚拟环境Python但当前不是虚拟环境，重新执行
    if venv_python and venv_python != sys.executable:
        try:
            # 使用虚拟环境Python重新执行脚本
            cmd = [venv_python] + sys.argv
            env = os.environ.copy()
            src_path = str(Path(__file__).resolve().parent / "src")
            env['PYTHONPATH'] = src_path + ":" + env.get('PYTHONPATH', '')
            result = subprocess.run(cmd, env=env, cwd=str(Path(__file__).resolve().parent))
            sys.exit(result.returncode)
        except Exception as e:
            print(f"警告: 无法使用虚拟环境Python: {e}", file=sys.stderr)
            # 继续使用当前Python
    
    try:
        # 设置Python路径
        project_root, src_dir = setup_python_path()
        
        # 调试信息（可选）
        if os.environ.get('AQUA_DEBUG'):
            print(f"使用Python: {sys.executable}")
            print(f"项目根目录: {project_root}")
            print(f"src目录: {src_dir}")
            print(f"Python路径: {sys.path[:3]}...")
        
        # 导入AQUA CLI应用
        from aqua.main import app
        app()
        
    except ImportError as e:
        print(f"错误: 无法导入AQUA CLI应用。请确保项目结构正确且依赖已安装。", file=sys.stderr)
        print(f"详细信息: {e}", file=sys.stderr)
        print(f"", file=sys.stderr)
        print(f"故障排除步骤:", file=sys.stderr)
        print(f"1. 确保在AQUA项目根目录运行此脚本", file=sys.stderr)
        
        # 跨平台兼容的虚拟环境激活提示
        if os.name == 'nt':  # Windows
            print(f"2. 确保虚拟环境已激活: .venv\\Scripts\\activate", file=sys.stderr) 
            print(f"3. 确保依赖已安装: uv pip install -r requirements-windows.txt", file=sys.stderr)
        else:  # macOS/Linux
            print(f"2. 确保虚拟环境已激活: source .venv/bin/activate", file=sys.stderr)
            print(f"3. 确保依赖已安装: uv pip install -r requirements.txt", file=sys.stderr)
            
        print(f"4. 检查src/aqua/main.py文件是否存在", file=sys.stderr)
        sys.exit(1)
        
    except Exception as e:
        print(f"执行过程中发生未知错误: {e}", file=sys.stderr)
        import traceback
        if os.environ.get('AQUA_DEBUG'):
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
