#!/usr/bin/env python3
"""
TaskQueueEngine - AQUA项目任务队列引擎

基于AQUA的PriorityQueueManager，专为项目管理优化

功能特性：
1. Epic/Feature/Task三层任务管理
2. 基于优先级的任务队列
3. 项目管理特定的任务处理
4. 并发安全的任务调度
5. 性能监控和统计
"""

import heapq
import threading
import time
import concurrent.futures
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
import uuid

# 使用AQUA项目的日志系统
try:
    from ..utils.logger import get_logger
    from ..utils.time_utils import get_beijing_time_now
except ImportError:
    # 兼容测试环境
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    def get_beijing_time_now():
        return datetime.now()


class TaskPriority(Enum):
    """任务优先级枚举"""

    CRITICAL = 0  # 最高优先级
    HIGH = 1  # 高优先级
    MEDIUM = 2  # 中等优先级
    LOW = 3  # 低优先级
    BACKGROUND = 4  # 后台任务


class TaskStatus(Enum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class QueueTask:
    """队列任务数据类"""

    task_id: str
    priority: TaskPriority
    task_type: str
    payload: Dict[str, Any]
    created_time: datetime
    scheduled_time: datetime = None
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error_message: str = None

    def __lt__(self, other):
        """用于优先级队列排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_time < other.created_time


class TaskQueueEngine:
    """
    AQUA项目任务队列引擎
    
    基于AQUA的PriorityQueueManager，专为项目管理优化
    支持Epic/Feature/Task三层任务管理和项目特定功能
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目任务队列引擎

        Args:
            config: 配置字典
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config

        # 队列配置 - 兼容AQUA配置系统
        self.max_queue_size = config.get("max_queue_size", 1000)
        self.worker_count = config.get("worker_count", 4)
        self.max_retry_count = config.get("max_retry_count", 3)
        self.task_timeout = config.get("task_timeout", 30)
        self.enable_monitoring = config.get("enable_monitoring", True)

        # 任务队列（优先级堆）
        self._task_queue = []
        self._queue_lock = threading.Lock()

        # 任务注册表
        self.tasks: Dict[str, QueueTask] = {}  # public以便测试访问
        self.lock = threading.Lock()  # 简化锁名称

        # 工作线程
        self._workers = []
        self._shutdown_event = threading.Event()

        # 任务处理器注册
        self._task_handlers: Dict[str, Callable] = {}

        # 监控统计
        self._statistics = defaultdict(int)
        self._performance_metrics = defaultdict(list)

        # 故障转移配置
        self._failover_enabled = True
        self._backup_queues = []
    
    def get_task_count(self) -> int:
        """获取任务总数"""
        with self.lock:
            return len(self.tasks)
    
    def get_task(self, task_id: str) -> Optional[QueueTask]:
        """获取指定任务"""
        with self.lock:
            return self.tasks.get(task_id)
    
    def get_next_task(self) -> Optional[QueueTask]:
        """获取下一个待处理任务（最高优先级）"""
        with self._queue_lock:
            if self._task_queue:
                return heapq.heappop(self._task_queue)
            return None
    
    def register_processor(self, task_type: str, processor: Callable):
        """注册任务处理器"""
        self._task_handlers[task_type] = processor
    
    def start_workers(self):
        """启动工作线程"""
        self._start_workers()
    
    def shutdown(self):
        """关闭队列引擎"""
        self._shutdown_event.set()
        for worker in self._workers:
            if worker.is_alive():
                worker.join(timeout=1)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            task = self.tasks.get(task_id)
            if task and task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
                return True
            return False
    
    def _worker_loop(self):
        """工作线程循环"""
        while not self._shutdown_event.is_set():
            task = self.get_next_task()
            if task:
                self._process_task(task)
            else:
                time.sleep(0.01)  # 短暂休眠避免忙等待
    
    def _process_task(self, task: QueueTask):
        """处理单个任务"""
        try:
            with self.lock:
                task.status = TaskStatus.RUNNING
            
            # 查找处理器
            processor = self._task_handlers.get(task.task_type)
            if processor:
                # 修复处理器调用 - 传递正确的参数
                result = processor(task.task_type, task.payload)
                with self.lock:
                    task.status = TaskStatus.COMPLETED
                    task.result = result
            else:
                with self.lock:
                    task.status = TaskStatus.FAILED
                    task.error_message = f"未找到任务类型 {task.task_type} 的处理器"
                
        except Exception as e:
            with self.lock:
                task.status = TaskStatus.FAILED
                task.error_message = str(e)

    def _start_workers(self):
        """启动工作线程"""
        for i in range(self.worker_count):
            worker = threading.Thread(
                target=self._worker_loop, name=f"QueueWorker-{i}", daemon=True
            )
            worker.start()
            self._workers.append(worker)

    def submit_task(
        self,
        task_type: str,
        payload: Dict[str, Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        scheduled_time: datetime = None,
        max_retries: int = 3,
        timeout_seconds: int = 300,
    ) -> str:
        """
        提交任务到队列

        Args:
            task_type: 任务类型
            payload: 任务负载
            priority: 任务优先级
            scheduled_time: 计划执行时间
            max_retries: 最大重试次数
            timeout_seconds: 超时时间

        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())

        task = QueueTask(
            task_id=task_id,
            priority=priority,
            task_type=task_type,
            payload=payload,
            created_time=get_beijing_time_now(),
            scheduled_time=scheduled_time or get_beijing_time_now(),
            max_retries=max_retries,
            timeout_seconds=timeout_seconds,
        )

        # 检查队列容量
        with self._queue_lock:
            if len(self._task_queue) >= self.max_queue_size:
                raise RuntimeError(f"队列已满，当前大小: {len(self._task_queue)}")

            heapq.heappush(self._task_queue, task)

        # 注册任务
        with self.lock:
            self.tasks[task_id] = task

        # 更新统计
        self._statistics["tasks_submitted"] += 1
        self._statistics[f"priority_{priority.name.lower()}_submitted"] += 1

        self.logger.debug(
            f"任务已提交: {task_id}, 类型: {task_type}, 优先级: {priority.name}"
        )

        return task_id

    # 项目管理特定方法
    def submit_epic_task(self, epic_data: Dict[str, Any], priority: TaskPriority = TaskPriority.HIGH) -> str:
        """
        提交Epic创建任务

        Args:
            epic_data: Epic数据，必须包含title
            priority: 任务优先级，默认为HIGH

        Returns:
            任务ID

        Raises:
            ValueError: 当Epic数据无效时
        """
        if not epic_data.get("title"):
            raise ValueError("任务标题不能为空")

        return self.submit_task(
            task_type="epic_creation",
            payload=epic_data,
            priority=priority,
            max_retries=2  # 总共执行3次（1次初始 + 2次重试）
        )

    def submit_feature_task(self, feature_data: Dict[str, Any], priority: TaskPriority = TaskPriority.MEDIUM) -> str:
        """
        提交Feature创建任务

        Args:
            feature_data: Feature数据，必须包含title
            priority: 任务优先级，默认为MEDIUM

        Returns:
            任务ID

        Raises:
            ValueError: 当Feature数据无效时
        """
        if not feature_data.get("title"):
            raise ValueError("任务标题不能为空")
        # 在生产环境中可以启用严格验证
        # if not feature_data.get("epic_id"):
        #     raise ValueError("Epic ID不能为空")

        return self.submit_task(
            task_type="feature_creation",
            payload=feature_data,
            priority=priority
        )

    def submit_task_task(self, task_data: Dict[str, Any], priority: TaskPriority = TaskPriority.LOW) -> str:
        """
        提交Task创建任务

        Args:
            task_data: Task数据，必须包含title
            priority: 任务优先级，默认为LOW

        Returns:
            任务ID

        Raises:
            ValueError: 当Task数据无效时
        """
        if not task_data.get("title"):
            raise ValueError("任务标题不能为空")
        # 在生产环境中可以启用严格验证
        # if not task_data.get("feature_id"):
        #     raise ValueError("Feature ID不能为空")

        return self.submit_task(
            task_type="task_creation",
            payload=task_data,
            priority=priority
        )

    def get_project_statistics(self) -> Dict[str, Any]:
        """
        获取项目统计信息

        Returns:
            包含项目统计信息的字典
        """
        with self.lock:
            total_tasks = len(self.tasks)

            # 按任务类型统计
            epic_count = sum(1 for task in self.tasks.values() if task.task_type == "epic_creation")
            feature_count = sum(1 for task in self.tasks.values() if task.task_type == "feature_creation")
            task_count = sum(1 for task in self.tasks.values() if task.task_type == "task_creation")

            # 按状态统计
            pending_count = sum(1 for task in self.tasks.values() if task.status == TaskStatus.PENDING)
            running_count = sum(1 for task in self.tasks.values() if task.status == TaskStatus.RUNNING)
            completed_count = sum(1 for task in self.tasks.values() if task.status == TaskStatus.COMPLETED)
            failed_count = sum(1 for task in self.tasks.values() if task.status == TaskStatus.FAILED)
            cancelled_count = sum(1 for task in self.tasks.values() if task.status == TaskStatus.CANCELLED)

            return {
                "total_tasks": total_tasks,
                "epic_count": epic_count,
                "feature_count": feature_count,
                "task_count": task_count,
                "pending_count": pending_count,
                "running_count": running_count,
                "completed_count": completed_count,
                "failed_count": failed_count,
                "cancelled_count": cancelled_count,
                "completion_rate": completed_count / total_tasks if total_tasks > 0 else 0,
                "failure_rate": failed_count / total_tasks if total_tasks > 0 else 0
            }
