#!/usr/bin/env python3
"""
优先级队列管理器

功能特性：
1. 基于优先级的任务队列管理
2. 支持动态优先级调整
3. 任务调度和负载均衡
4. 故障转移和恢复机制
5. 性能监控和统计
"""

import heapq
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
import uuid

from ..utils.logger import get_logger
from ..utils.time_utils import get_beijing_time_now


class TaskPriority(Enum):
    """任务优先级枚举"""

    CRITICAL = 0  # 最高优先级
    HIGH = 1  # 高优先级
    MEDIUM = 2  # 中等优先级
    LOW = 3  # 低优先级
    BACKGROUND = 4  # 后台任务


class TaskStatus(Enum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class QueueTask:
    """队列任务数据类"""

    task_id: str
    priority: TaskPriority
    task_type: str
    payload: Dict[str, Any]
    created_time: datetime
    scheduled_time: datetime = None
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error_message: str = None

    def __lt__(self, other):
        """用于优先级队列排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_time < other.created_time


class PriorityQueueManager:
    """
    优先级队列管理器主类

    提供任务队列管理、调度、监控等功能。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化优先级队列管理器

        Args:
            config: 配置字典
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config

        # 队列配置
        self.queue_config = config.get("priority_queue", {})
        self.max_queue_size = self.queue_config.get("max_queue_size", 1000)
        self.worker_threads = self.queue_config.get("worker_threads", 4)
        self.enable_monitoring = self.queue_config.get("enable_monitoring", True)

        # 任务队列（优先级堆）
        self._task_queue = []
        self._queue_lock = threading.Lock()

        # 任务注册表
        self._task_registry: Dict[str, QueueTask] = {}
        self._registry_lock = threading.Lock()

        # 工作线程
        self._workers = []
        self._shutdown_event = threading.Event()

        # 任务处理器注册
        self._task_handlers: Dict[str, Callable] = {}

        # 监控统计
        self._statistics = defaultdict(int)
        self._performance_metrics = defaultdict(list)

        # 故障转移配置
        self._failover_enabled = True
        self._backup_queues = []

        # 初始化工作线程
        self._start_workers()

        self.logger.info(
            f"优先级队列管理器初始化完成，工作线程数: {self.worker_threads}"
        )

    def _start_workers(self):
        """启动工作线程"""
        for i in range(self.worker_threads):
            worker = threading.Thread(
                target=self._worker_loop, name=f"QueueWorker-{i}", daemon=True
            )
            worker.start()
            self._workers.append(worker)

    def _worker_loop(self):
        """工作线程循环"""
        while not self._shutdown_event.is_set():
            try:
                task = self._get_next_task(timeout=1.0)
                if task:
                    self._execute_task(task)
            except Exception as e:
                self.logger.error(f"工作线程异常: {e}")

    def submit_task(
        self,
        task_type: str,
        payload: Dict[str, Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        scheduled_time: datetime = None,
        max_retries: int = 3,
        timeout_seconds: int = 300,
    ) -> str:
        """
        提交任务到队列

        Args:
            task_type: 任务类型
            payload: 任务负载
            priority: 任务优先级
            scheduled_time: 计划执行时间
            max_retries: 最大重试次数
            timeout_seconds: 超时时间

        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())

        task = QueueTask(
            task_id=task_id,
            priority=priority,
            task_type=task_type,
            payload=payload,
            created_time=datetime.now(),
            scheduled_time=scheduled_time or datetime.now(),
            max_retries=max_retries,
            timeout_seconds=timeout_seconds,
        )

        # 检查队列容量
        with self._queue_lock:
            if len(self._task_queue) >= self.max_queue_size:
                raise RuntimeError(f"队列已满，当前大小: {len(self._task_queue)}")

            heapq.heappush(self._task_queue, task)

        # 注册任务
        with self._registry_lock:
            self._task_registry[task_id] = task

        # 更新统计
        self._statistics["tasks_submitted"] += 1
        self._statistics[f"priority_{priority.name.lower()}_submitted"] += 1

        self.logger.debug(
            f"任务已提交: {task_id}, 类型: {task_type}, 优先级: {priority.name}"
        )

        return task_id

    def _get_next_task(self, timeout: float = None) -> Optional[QueueTask]:
        """获取下一个待执行任务"""
        start_time = time.time()

        while True:
            with self._queue_lock:
                # 查找可执行的任务
                current_time = datetime.now()
                available_tasks = []
                delayed_tasks = []

                while self._task_queue:
                    task = heapq.heappop(self._task_queue)

                    if task.scheduled_time <= current_time:
                        available_tasks.append(task)
                        break
                    else:
                        delayed_tasks.append(task)

                # 将延迟任务放回队列
                for task in delayed_tasks:
                    heapq.heappush(self._task_queue, task)

                if available_tasks:
                    return available_tasks[0]

            # 检查超时
            if timeout and (time.time() - start_time) >= timeout:
                return None

            time.sleep(0.1)

    def _execute_task(self, task: QueueTask):
        """执行任务"""
        start_time = time.time()

        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            self._statistics["tasks_running"] += 1

            # 获取任务处理器
            handler = self._task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"未找到任务类型 {task.task_type} 的处理器")

            # 执行任务
            result = handler(task.payload)

            # 更新任务结果
            task.status = TaskStatus.COMPLETED
            task.result = result

            execution_time = time.time() - start_time

            # 更新统计
            self._statistics["tasks_completed"] += 1
            self._statistics["tasks_running"] -= 1
            self._performance_metrics["execution_times"].append(execution_time)

            self.logger.debug(
                f"任务执行成功: {task.task_id}, 耗时: {execution_time:.3f}s"
            )

        except Exception as e:
            # 任务执行失败
            task.error_message = str(e)
            execution_time = time.time() - start_time

            self.logger.error(f"任务执行失败: {task.task_id}, 错误: {e}")

            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                task.scheduled_time = datetime.now() + timedelta(
                    seconds=2**task.retry_count
                )

                # 重新加入队列
                with self._queue_lock:
                    heapq.heappush(self._task_queue, task)

                self._statistics["tasks_retried"] += 1
                self.logger.info(
                    f"任务重试: {task.task_id}, 重试次数: {task.retry_count}"
                )
            else:
                # 重试次数用尽，任务失败
                task.status = TaskStatus.FAILED
                self._statistics["tasks_failed"] += 1

            self._statistics["tasks_running"] -= 1

    def register_task_handler(self, task_type: str, handler: Callable):
        """
        注册任务处理器

        Args:
            task_type: 任务类型
            handler: 处理器函数
        """
        self._task_handlers[task_type] = handler
        self.logger.info(f"任务处理器已注册: {task_type}")

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict: 任务状态信息
        """
        with self._registry_lock:
            task = self._task_registry.get(task_id)

            if not task:
                return None

            return {
                "task_id": task.task_id,
                "task_type": task.task_type,
                "priority": task.priority.name,
                "status": task.status.value,
                "created_time": task.created_time.isoformat(),
                "scheduled_time": (
                    task.scheduled_time.isoformat() if task.scheduled_time else None
                ),
                "retry_count": task.retry_count,
                "result": task.result,
                "error_message": task.error_message,
            }

    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        with self._registry_lock:
            task = self._task_registry.get(task_id)

            if not task:
                return False

            if task.status in [
                TaskStatus.COMPLETED,
                TaskStatus.FAILED,
                TaskStatus.CANCELLED,
            ]:
                return False

            if task.status == TaskStatus.RUNNING:
                # 运行中的任务无法取消
                return False

            # 标记为取消
            task.status = TaskStatus.CANCELLED
            self._statistics["tasks_cancelled"] += 1

            return True

    def update_task_priority(self, task_id: str, new_priority: TaskPriority) -> bool:
        """
        更新任务优先级

        Args:
            task_id: 任务ID
            new_priority: 新优先级

        Returns:
            bool: 是否成功更新
        """
        with self._registry_lock:
            task = self._task_registry.get(task_id)

            if not task or task.status != TaskStatus.PENDING:
                return False

            # 从队列中移除并重新插入
            with self._queue_lock:
                # 移除任务
                self._task_queue = [t for t in self._task_queue if t.task_id != task_id]
                heapq.heapify(self._task_queue)

                # 更新优先级并重新插入
                task.priority = new_priority
                heapq.heappush(self._task_queue, task)

            self.logger.info(
                f"任务优先级已更新: {task_id}, 新优先级: {new_priority.name}"
            )
            return True

    def get_queue_statistics(self) -> Dict[str, Any]:
        """
        获取队列统计信息

        Returns:
            Dict: 统计信息
        """
        with self._queue_lock:
            queue_size = len(self._task_queue)

        with self._registry_lock:
            total_tasks = len(self._task_registry)

            # 按状态统计
            status_counts = defaultdict(int)
            for task in self._task_registry.values():
                status_counts[task.status.value] += 1

            # 按优先级统计
            priority_counts = defaultdict(int)
            for task in self._task_registry.values():
                if task.status == TaskStatus.PENDING:
                    priority_counts[task.priority.name] += 1

        # 性能指标
        execution_times = self._performance_metrics.get("execution_times", [])
        avg_execution_time = (
            sum(execution_times) / len(execution_times) if execution_times else 0
        )

        return {
            "queue_size": queue_size,
            "total_tasks": total_tasks,
            "status_distribution": dict(status_counts),
            "priority_distribution": dict(priority_counts),
            "throughput": self._calculate_throughput(),
            "average_execution_time": avg_execution_time,
            "success_rate": self._calculate_success_rate(),
            "worker_threads": self.worker_threads,
            "statistics": dict(self._statistics),
        }

    def _calculate_throughput(self) -> float:
        """计算吞吐量（任务/分钟）"""
        completed = self._statistics.get("tasks_completed", 0)
        # 简化实现：假设从启动开始计算
        runtime_minutes = 1  # 实际应该是运行时间
        return completed / runtime_minutes if runtime_minutes > 0 else 0

    def _calculate_success_rate(self) -> float:
        """计算成功率"""
        completed = self._statistics.get("tasks_completed", 0)
        failed = self._statistics.get("tasks_failed", 0)
        total = completed + failed

        return completed / total if total > 0 else 1.0

    def get_pending_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取待处理任务列表

        Args:
            limit: 返回任务数量限制

        Returns:
            List: 待处理任务列表
        """
        pending_tasks = []

        with self._queue_lock:
            # 创建队列副本以避免修改原队列
            queue_copy = self._task_queue.copy()

            count = 0
            while queue_copy and count < limit:
                task = heapq.heappop(queue_copy)
                if task.status == TaskStatus.PENDING:
                    pending_tasks.append(
                        {
                            "task_id": task.task_id,
                            "task_type": task.task_type,
                            "priority": task.priority.name,
                            "created_time": task.created_time.isoformat(),
                            "scheduled_time": (
                                task.scheduled_time.isoformat()
                                if task.scheduled_time
                                else None
                            ),
                        }
                    )
                    count += 1

        return pending_tasks

    def clear_completed_tasks(self, older_than_hours: int = 24) -> int:
        """
        清理已完成任务

        Args:
            older_than_hours: 清理多少小时前的任务

        Returns:
            int: 清理的任务数量
        """
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
        cleared_count = 0

        with self._registry_lock:
            to_remove = []

            for task_id, task in self._task_registry.items():
                if (
                    task.status
                    in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
                    and task.created_time < cutoff_time
                ):
                    to_remove.append(task_id)

            for task_id in to_remove:
                del self._task_registry[task_id]
                cleared_count += 1

        self.logger.info(f"已清理 {cleared_count} 个完成的任务")
        return cleared_count

    def pause_queue(self):
        """暂停队列处理"""
        self._shutdown_event.set()
        self.logger.info("队列处理已暂停")

    def resume_queue(self):
        """恢复队列处理"""
        if self._shutdown_event.is_set():
            self._shutdown_event.clear()
            # 重启工作线程
            self._workers.clear()
            self._start_workers()
            self.logger.info("队列处理已恢复")

    def shutdown(self, timeout: int = 30):
        """
        关闭队列管理器

        Args:
            timeout: 等待关闭的超时时间
        """
        self.logger.info("正在关闭优先级队列管理器...")

        # 设置关闭标志
        self._shutdown_event.set()

        # 等待工作线程结束
        for worker in self._workers:
            worker.join(timeout=timeout)

        self.logger.info("优先级队列管理器已关闭")

    def enable_failover(self, backup_queues: List[str]):
        """
        启用故障转移

        Args:
            backup_queues: 备用队列列表
        """
        self._failover_enabled = True
        self._backup_queues = backup_queues
        self.logger.info(f"故障转移已启用，备用队列: {backup_queues}")

    def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            Dict: 健康状态
        """
        active_workers = sum(1 for worker in self._workers if worker.is_alive())

        with self._queue_lock:
            queue_size = len(self._task_queue)

        is_healthy = (
            active_workers > 0 and queue_size < self.max_queue_size * 0.9  # 队列未满
        )

        return {
            "status": "healthy" if is_healthy else "unhealthy",
            "active_workers": active_workers,
            "expected_workers": self.worker_threads,
            "queue_size": queue_size,
            "max_queue_size": self.max_queue_size,
            "queue_utilization": queue_size / self.max_queue_size,
            "last_check": get_beijing_time_now(),
        }
