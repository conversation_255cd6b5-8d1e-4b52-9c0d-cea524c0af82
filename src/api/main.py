#!/usr/bin/env python3
"""
AQUA API 主应用

FastAPI 主应用，整合所有路由和中间件
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
import time
from typing import Dict, Any

from ..utils.logger import get_logger
from ..utils.time_utils import get_beijing_time_now
from ..utils.exceptions import (
    AquaException,
    DatabaseException,
    CacheException,
    DataValidationException,
    BusinessLogicException,
    get_exception_handler,
)
from .routers import data_router, performance_router

# 配置日志
logger = get_logger("AQUA_API")


# 生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    logger.info("🚀 AQUA API 服务启动")

    # 初始化缓存
    try:
        from ..cache.cache_manager import get_cache_manager

        cache_manager = get_cache_manager()
        logger.info("✅ 缓存管理器初始化成功")
    except Exception as e:
        logger.error(f"❌ 缓存管理器初始化失败: {e}")

    # 初始化数据库优化器
    try:
        from ..database.performance_optimizer import DatabasePerformanceOptimizer

        optimizer = DatabasePerformanceOptimizer()
        logger.info("✅ 数据库优化器初始化成功")
    except Exception as e:
        logger.error(f"❌ 数据库优化器初始化失败: {e}")

    yield

    # 关闭时
    logger.info("🛑 AQUA API 服务关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="AQUA API",
    description="AQUA 期货与A股数据平台 API",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
)

# CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()

    # 记录请求信息
    logger.info(f"📨 {request.method} {request.url.path} - {request.client.host}")

    try:
        response = await call_next(request)

        # 记录响应时间
        process_time = time.time() - start_time
        logger.info(
            f"✅ {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s"
        )

        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Timestamp"] = get_beijing_time_now().isoformat()

        return response

    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"❌ {request.method} {request.url.path} - Error: {str(e)} - {process_time:.3f}s"
        )
        raise


# 全局异常处理
@app.exception_handler(AquaException)
async def aqua_exception_handler(request: Request, exc: AquaException):
    """AQUA异常处理"""
    logger.error(f"AQUA异常: {exc.error_code.name} - {exc.message}")

    # 根据异常类型确定HTTP状态码
    status_code = 500
    if isinstance(exc, DataValidationException):
        status_code = 400
    elif isinstance(exc, DatabaseException):
        status_code = 500
    elif isinstance(exc, CacheException):
        status_code = 503
    elif isinstance(exc, BusinessLogicException):
        status_code = 422

    error_response = exc.to_dict()
    error_response["success"] = False
    error_response["path"] = request.url.path

    return JSONResponse(status_code=status_code, content=error_response)


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": exc.status_code,
            "timestamp": get_beijing_time_now().isoformat(),
            "path": request.url.path,
        },
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理"""
    logger.error(f"请求验证异常: {exc.errors()}")

    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "请求参数验证失败",
            "errors": exc.errors(),
            "timestamp": get_beijing_time_now().isoformat(),
            "path": request.url.path,
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)

    # 使用统一异常处理器
    exception_handler = get_exception_handler()
    error_info = exception_handler.handle_exception(
        exc,
        {
            "request_path": request.url.path,
            "request_method": request.method,
            "request_headers": dict(request.headers),
        },
    )

    error_info["success"] = False
    error_info["path"] = request.url.path

    return JSONResponse(status_code=500, content=error_info)


# 根路由
@app.get("/", response_model=Dict[str, Any])
async def root():
    """API 根路由"""
    return {
        "message": "AQUA API 服务正在运行",
        "version": "1.0.0",
        "timestamp": get_beijing_time_now().isoformat(),
        "docs_url": "/docs",
        "redoc_url": "/redoc",
    }


# 健康检查路由
@app.get("/health", response_model=Dict[str, Any])
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        from ..database.duckdb_init_check import DuckDBInitChecker

        checker = DuckDBInitChecker()
        health_report = checker.get_database_health_report()

        # 检查缓存状态
        try:
            from ..cache.cache_manager import get_cache_manager

            cache_manager = get_cache_manager()
            cache_stats = cache_manager.get_stats()
            cache_status = "healthy"
        except Exception as e:
            cache_stats = {"error": str(e)}
            cache_status = "unhealthy"

        return {
            "status": "healthy",
            "database": {
                "status": health_report.get("connection_status", "unknown"),
                "path": health_report.get("database_path", "unknown"),
            },
            "cache": {"status": cache_status, "stats": cache_stats},
            "timestamp": get_beijing_time_now().isoformat(),
        }

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": get_beijing_time_now().isoformat(),
            },
        )


# 注册路由
app.include_router(data_router)
app.include_router(performance_router)


# API 信息路由
@app.get("/api/info", response_model=Dict[str, Any])
async def api_info():
    """API 信息"""
    return {
        "title": "AQUA API",
        "version": "1.0.0",
        "description": "AQUA 期货与A股数据平台 API",
        "endpoints": {"data": "/api/data", "performance": "/api/performance"},
        "features": [
            "高性能数据查询",
            "多级缓存架构",
            "数据库优化",
            "虚拟滚动支持",
            "实时性能监控",
            "统一异常处理",
        ],
        "timestamp": get_beijing_time_now().isoformat(),
    }


# 错误统计路由
@app.get("/api/errors/stats", response_model=Dict[str, Any])
async def get_error_stats():
    """获取错误统计信息"""
    try:
        exception_handler = get_exception_handler()
        stats = exception_handler.get_error_statistics()

        return {
            "success": True,
            "data": stats,
            "message": "错误统计信息获取成功",
            "timestamp": get_beijing_time_now().isoformat(),
        }
    except Exception as e:
        logger.error(f"获取错误统计失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"获取错误统计失败: {str(e)}",
                "timestamp": get_beijing_time_now().isoformat(),
            },
        )


@app.post("/api/errors/reset")
async def reset_error_stats():
    """重置错误统计"""
    try:
        exception_handler = get_exception_handler()
        exception_handler.reset_statistics()

        return {
            "success": True,
            "message": "错误统计已重置",
            "timestamp": get_beijing_time_now().isoformat(),
        }
    except Exception as e:
        logger.error(f"重置错误统计失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"重置错误统计失败: {str(e)}",
                "timestamp": get_beijing_time_now().isoformat(),
            },
        )


if __name__ == "__main__":
    import uvicorn

    # 开发环境启动
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True, log_level="info")
