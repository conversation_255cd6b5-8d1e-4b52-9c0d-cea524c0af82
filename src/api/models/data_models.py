"""
数据API模型定义

定义数据库浏览API的请求和响应数据结构
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Any, Dict
from datetime import datetime


class TableInfo(BaseModel):
    """表基本信息模型"""

    name: str = Field(..., description="表名")
    record_count: int = Field(..., description="记录数")
    columns: List[Dict[str, str]] = Field(..., description="列信息")
    last_updated: Optional[datetime] = Field(None, description="最后更新时间")
    size_mb: Optional[float] = Field(None, description="表大小(MB)")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat() if v else None}


class TableListResponse(BaseModel):
    """表列表响应模型"""

    success: bool = Field(..., description="请求是否成功")
    data: List[TableInfo] = Field(..., description="表列表数据")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(..., description="响应时间")
    total_count: int = Field(..., description="总表数量")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class TableDataResponse(BaseModel):
    """表数据响应模型"""

    success: bool = Field(..., description="请求是否成功")
    data: List[Dict[str, Any]] = Field(..., description="表数据")
    pagination: Dict[str, int] = Field(..., description="分页信息")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(..., description="响应时间")
    table_name: str = Field(..., description="表名")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class TableMetaResponse(BaseModel):
    """表元数据响应模型"""

    success: bool = Field(..., description="请求是否成功")
    data: Dict[str, Any] = Field(..., description="表元数据")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(..., description="响应时间")
    table_name: str = Field(..., description="表名")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ImportResponse(BaseModel):
    """数据导入响应模型"""

    success: bool = Field(..., description="导入是否成功")
    data: Dict[str, Any] = Field(..., description="导入结果数据")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(..., description="响应时间")
    task_id: Optional[str] = Field(None, description="任务ID")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ErrorResponse(BaseModel):
    """错误响应模型"""

    success: bool = Field(False, description="请求是否成功")
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    timestamp: datetime = Field(..., description="响应时间")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}
