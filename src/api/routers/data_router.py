"""
数据库浏览API路由

提供数据库表列表查询、表数据查询等API端点
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from fastapi import (
    APIRouter,
    HTTPException,
    Query,
    UploadFile,
    File,
    Form,
    BackgroundTasks,
)
from pydantic import BaseModel, Field

from ...database.duckdb_init_check import DuckDBInitChecker
from ...database.connection_manager import DuckDBConnectionManager
from ...data_import.csv_importer import CSVImporter
from ...data_import.fromc2c_importer_logic import (
    check_environment_logic,
    check_data_source_logic,
    execute_import_logic,
)
from ...data_import.mysql_importer_logic import (
    test_mysql_connection_logic,
    get_mysql_tables_logic,
    preview_mysql_table_logic,
    execute_mysql_import_logic,
)
from ...data_import.import_history_manager import ImportHistoryManager
from ...data_import.task_control_manager import TaskControlManager
from ...data_import.websocket_manager import get_websocket_manager
from ...utils.time_utils import get_beijing_time_now
from ...utils.config_loader import ConfigLoader
from ...utils.exceptions import (
    raise_database_error,
    raise_validation_error,
)
from ..models.data_models import (
    TableListResponse,
    TableDataResponse,
    TableMetaResponse,
    ImportResponse,
    TableInfo,
)

# 创建路由器
router = APIRouter(prefix="/api/data", tags=["数据库浏览"])

# 配置日志
logger = logging.getLogger(__name__)

# 全局配置加载器实例
_config_loader = None


def get_config_loader():
    """获取配置加载器实例"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader


def get_default_environment():
    """从配置文件获取默认环境"""
    try:
        config_loader = get_config_loader()
        app_config = config_loader._load_config().get("app", {})
        return app_config.get("default_environment", "dev")
    except:
        return "dev"  # 回退默认值


# 增强版CSV导入任务存储（生产环境应使用Redis或数据库）
enhanced_csv_tasks = {}

# FromC2C导入任务存储（与import_router统一）
fromc2c_tasks = {}

# MySQL导入任务存储
mysql_import_tasks = {}

# 全局任务控制管理器
_task_control_manager = None

# 全局WebSocket管理器
_websocket_manager = None


def get_task_control_manager():
    """获取任务控制管理器实例"""
    global _task_control_manager
    if _task_control_manager is None:
        default_env = get_default_environment()
        _task_control_manager = TaskControlManager(default_env)

        # 注册任务存储
        _task_control_manager.register_task_storage("csv", enhanced_csv_tasks)
        _task_control_manager.register_task_storage("fromc2c", fromc2c_tasks)
        _task_control_manager.register_task_storage("mysql", mysql_import_tasks)

    return _task_control_manager


def get_websocket_manager_instance():
    """获取WebSocket管理器实例"""
    global _websocket_manager
    if _websocket_manager is None:
        default_env = get_default_environment()
        _websocket_manager = get_websocket_manager(default_env)
        # 设置任务控制管理器引用
        task_control = get_task_control_manager()
        _websocket_manager.set_task_control_manager(task_control)
    return _websocket_manager


async def broadcast_task_progress(task_id: str, progress_data: Dict[str, Any]):
    """广播任务进度更新"""
    try:
        websocket_manager = get_websocket_manager_instance()
        await websocket_manager.broadcast_task_progress(task_id, progress_data)
    except Exception as e:
        logger.warning(f"广播任务进度失败: {e}")


async def broadcast_task_status_change(
    task_id: str, old_status: str, new_status: str, metadata: Optional[Dict] = None
):
    """广播任务状态变更"""
    try:
        websocket_manager = get_websocket_manager_instance()
        await websocket_manager.broadcast_task_status_change(
            task_id, old_status, new_status, metadata
        )
    except Exception as e:
        logger.warning(f"广播任务状态变更失败: {e}")


class EnhancedCSVImportConfig(BaseModel):
    """增强版CSV导入配置"""

    environment: str = "test"
    mode: str = "replace"  # replace, append, skip
    enableValidation: bool = True
    deduplicateStrategy: str = "skip"  # skip, replace, error
    batchConcurrency: int = 4
    batchSize: int = 1000
    retryCount: int = 3


class FromC2CImportRequest(BaseModel):
    """FromC2C导入请求模型"""

    env: str = "dev"
    path: Optional[str] = None
    max_files: Optional[int] = None
    table_filter: Optional[str] = None


class MySQLConnectionConfig(BaseModel):
    """MySQL连接配置模型"""

    host: str
    port: int = 3306
    database: str
    username: str
    password: str
    connectTimeout: Optional[int] = 30
    queryTimeout: Optional[int] = 30
    charset: str = "utf8mb4"
    useSSL: bool = False
    customParams: Optional[str] = None


class MySQLTablesRequest(BaseModel):
    """MySQL表查询请求模型"""

    host: str
    port: int = 3306
    database: str
    username: str
    password: str
    tablePattern: Optional[str] = None


class MySQLPreviewRequest(BaseModel):
    """MySQL表预览请求模型"""

    host: str
    port: int = 3306
    database: str
    username: str
    password: str
    tableName: str
    limit: int = 100
    charset: str = "utf8mb4"
    connectTimeout: Optional[int] = 30
    queryTimeout: Optional[int] = 30


class MySQLImportRequest(BaseModel):
    """MySQL导入请求模型"""

    connectionConfig: MySQLConnectionConfig
    tables: List[str]
    environment: str = None


class ConflictResolutionRequest(BaseModel):
    """冲突解决请求模型"""

    sessionId: str = Field(..., description="会话/任务ID")
    conflictIndex: int = Field(..., ge=0, description="冲突索引")
    resolution: str = Field(
        ..., pattern="^(keep|replace|skip)$", description="解决策略"
    )


# 包装函数，使 BackgroundTasks 能够调用 async 函数
def sync_run_enhanced_csv_import_task(
    task_id: str,
    file_path: Path,
    config: EnhancedCSVImportConfig,
    table_name: Optional[str] = None,
):
    """BackgroundTasks兼容的CSV导入任务包装函数"""
    import asyncio

    asyncio.create_task(
        run_enhanced_csv_import_task(task_id, file_path, config, table_name)
    )


def sync_run_fromc2c_import_task(task_id: str, request: FromC2CImportRequest):
    """BackgroundTasks兼容的FromC2C导入任务包装函数"""
    import asyncio

    asyncio.create_task(run_fromc2c_import_task(task_id, request))


def sync_run_mysql_import_task(task_id: str, request: MySQLImportRequest):
    """BackgroundTasks兼容的MySQL导入任务包装函数"""
    import asyncio

    asyncio.create_task(run_mysql_import_task(task_id, request))


# 全局冲突存储（生产环境应使用Redis或数据库）
conflict_storage = {}  # {session_id: {"conflicts": [...], "metadata": {...}}}


def get_session_conflicts(session_id: str) -> Optional[Dict[str, Any]]:
    """获取会话冲突信息"""
    return conflict_storage.get(session_id)


def store_session_conflicts(
    session_id: str, conflicts: List[Dict], metadata: Optional[Dict] = None
):
    """存储会话冲突信息"""
    conflict_storage[session_id] = {
        "conflicts": conflicts,
        "metadata": metadata or {},
        "created_at": get_beijing_time_now().isoformat(),
        "status": "pending",
    }


def resolve_session_conflict(
    session_id: str, conflict_index: int, resolution: str
) -> Dict[str, Any]:
    """解决会话冲突"""
    session_data = conflict_storage.get(session_id)
    if not session_data:
        return {"success": False, "message": f"未找到会话 {session_id} 的冲突信息"}

    conflicts = session_data.get("conflicts", [])
    if conflict_index >= len(conflicts):
        return {
            "success": False,
            "message": f"冲突索引 {conflict_index} 超出范围，总冲突数: {len(conflicts)}",
        }

    # 更新冲突解决状态
    conflicts[conflict_index]["resolution"] = resolution
    conflicts[conflict_index]["resolved_at"] = get_beijing_time_now().isoformat()

    # 检查是否所有冲突都已解决
    resolved_count = sum(1 for c in conflicts if "resolution" in c)
    all_resolved = resolved_count == len(conflicts)

    if all_resolved:
        session_data["status"] = "resolved"

    return {
        "success": True,
        "message": f"冲突 {conflict_index} 已解决，策略: {resolution}",
        "conflict_index": conflict_index,
        "resolution": resolution,
        "resolved_count": resolved_count,
        "total_conflicts": len(conflicts),
        "all_resolved": all_resolved,
    }


def detect_import_conflicts(
    data_source: str, target_table: str, sample_data: List[Dict]
) -> List[Dict]:
    """
    检测导入冲突（模拟实现）

    Args:
        data_source: 数据源标识
        target_table: 目标表名
        sample_data: 样本数据

    Returns:
        冲突列表
    """
    conflicts = []

    # 模拟检测冲突逻辑
    for i, row in enumerate(sample_data[:5]):  # 只检查前5条记录
        # 模拟不同类型的冲突
        if i % 3 == 0:  # 数据类型冲突
            conflicts.append(
                {
                    "type": "data_type_mismatch",
                    "row_index": i,
                    "column": list(row.keys())[0] if row else "unknown",
                    "expected_type": "INTEGER",
                    "actual_type": "STRING",
                    "expected_value": "123",
                    "actual_value": (
                        str(row.get(list(row.keys())[0], "unknown"))
                        if row
                        else "unknown"
                    ),
                    "description": f"第 {i+1} 行数据类型不匹配",
                    "severity": "medium",
                }
            )
        elif i % 3 == 1:  # 重复数据冲突
            conflicts.append(
                {
                    "type": "duplicate_key",
                    "row_index": i,
                    "column": "id",
                    "existing_value": f"existing_id_{i}",
                    "new_value": f"new_id_{i}",
                    "description": f"第 {i+1} 行存在重复键值",
                    "severity": "high",
                }
            )
        elif i % 3 == 2:  # 约束冲突
            conflicts.append(
                {
                    "type": "constraint_violation",
                    "row_index": i,
                    "constraint": "NOT NULL",
                    "column": "required_field",
                    "value": None,
                    "description": f"第 {i+1} 行违反非空约束",
                    "severity": "high",
                }
            )

    return conflicts


def generate_conflict_suggestions(conflict: Dict) -> List[Dict]:
    """
    生成冲突解决建议

    Args:
        conflict: 冲突信息

    Returns:
        解决建议列表
    """
    conflict_type = conflict.get("type", "unknown")

    if conflict_type == "data_type_mismatch":
        return [
            {
                "action": "keep",
                "description": "保持原始数据类型",
                "pros": ["保持数据完整性"],
                "cons": ["可能导致查询性能问题"],
            },
            {
                "action": "replace",
                "description": "转换为目标数据类型",
                "pros": ["提高查询性能", "统一数据格式"],
                "cons": ["可能丢失精度"],
            },
            {
                "action": "skip",
                "description": "跳过此行数据",
                "pros": ["避免数据污染"],
                "cons": ["数据丢失"],
            },
        ]
    elif conflict_type == "duplicate_key":
        return [
            {
                "action": "keep",
                "description": "保持原有数据",
                "pros": ["保持历史数据"],
                "cons": ["丢失新数据"],
            },
            {
                "action": "replace",
                "description": "更新为新数据",
                "pros": ["获取最新信息"],
                "cons": ["丢失历史信息"],
            },
            {
                "action": "skip",
                "description": "跳过重复数据",
                "pros": ["保持数据一致性"],
                "cons": ["新数据丢失"],
            },
        ]
    elif conflict_type == "constraint_violation":
        return [
            {
                "action": "replace",
                "description": "使用默认值替换",
                "pros": ["保持数据完整性"],
                "cons": ["可能不正确"],
            },
            {
                "action": "skip",
                "description": "跳过无效数据",
                "pros": ["保持数据质量"],
                "cons": ["数据丢失"],
            },
        ]
    else:
        return [
            {
                "action": "skip",
                "description": "跳过问题数据",
                "pros": ["安全策略"],
                "cons": ["数据丢失"],
            }
        ]


def simulate_conflict_detection_in_import(
    task_id: str, import_type: str, sample_data: Optional[List[Dict]] = None
):
    """
    在导入过程中模拟冲突检测

    Args:
        task_id: 任务ID
        import_type: 导入类型
        sample_data: 样本数据
    """
    if not sample_data:
        # 生成模拟数据
        sample_data = [
            {"id": 1, "name": "test1", "value": "abc"},
            {"id": 2, "name": "test2", "value": None},
            {"id": 1, "name": "test1_new", "value": "def"},  # 重复键
            {"id": 4, "name": "", "value": "123"},
            {"id": 5, "name": "test5", "value": "xyz"},
        ]

    # 检测冲突
    conflicts = detect_import_conflicts(
        data_source=import_type, target_table="target_table", sample_data=sample_data
    )

    if conflicts:
        # 为每个冲突生成解决建议
        for conflict in conflicts:
            conflict["suggestions"] = generate_conflict_suggestions(conflict)

        # 存储冲突信息
        metadata = {
            "import_type": import_type,
            "task_id": task_id,
            "detection_time": get_beijing_time_now().isoformat(),
            "sample_size": len(sample_data),
        }

        store_session_conflicts(task_id, conflicts, metadata)

        logger.info(f"任务 {task_id} 检测到 {len(conflicts)} 个冲突")

        return {
            "has_conflicts": True,
            "conflict_count": len(conflicts),
            "conflicts": conflicts,
        }
    else:
        return {"has_conflicts": False, "conflict_count": 0, "conflicts": []}


async def run_enhanced_csv_import_task(
    task_id: str,
    file_path: Path,
    config: EnhancedCSVImportConfig,
    table_name: Optional[str] = None,
):
    """后台运行增强版CSV导入任务（支持WebSocket实时进度）"""
    initial_status = {
        "status": "running",
        "progress": 0,
        "message": "开始处理CSV文件...",
        "result": None,
        "error": None,
    }
    enhanced_csv_tasks[task_id] = initial_status

    # 广播任务开始
    await broadcast_task_status_change(
        task_id,
        "pending",
        "running",
        {"task_type": "csv_enhanced", "file_name": file_path.name},
    )

    # 获取任务控制管理器
    control_manager = get_task_control_manager()

    # 初始化历史记录管理器
    history_manager = ImportHistoryManager(config.environment)

    # 记录导入开始
    source_info = {
        "type": "csv_enhanced",
        "file_name": file_path.name,
        "file_path": str(file_path),
    }

    import_config = {
        "mode": config.mode,
        "enableValidation": config.enableValidation,
        "deduplicateStrategy": config.deduplicateStrategy,
        "batchConcurrency": config.batchConcurrency,
        "batchSize": config.batchSize,
        "retryCount": config.retryCount,
    }

    session_info = {"task_id": task_id}

    import_id = history_manager.record_import_start(
        import_type="csv",
        source_info=source_info,
        target_tables=[table_name] if table_name else None,
        import_config=import_config,
        session_info=session_info,
    )

    try:
        # 检查控制信号
        control_signal = control_manager.check_control_signal(task_id)
        if control_signal == "cancel":
            enhanced_csv_tasks[task_id]["status"] = "cancelled"
            enhanced_csv_tasks[task_id]["message"] = "任务已取消"
            await broadcast_task_status_change(task_id, "running", "cancelled")
            return

        # 初始化CSV导入器
        csv_importer = CSVImporter(environment=config.environment)

        # 更新进度并检查控制信号
        enhanced_csv_tasks[task_id]["progress"] = 20
        enhanced_csv_tasks[task_id]["message"] = "正在验证CSV文件格式..."

        # 广播进度更新
        await broadcast_task_progress(
            task_id,
            {
                "progress": 20,
                "message": "正在验证CSV文件格式...",
                "stage": "validation",
            },
        )

        # 模拟冲突检测（在实际导入前）
        enhanced_csv_tasks[task_id]["progress"] = 40
        enhanced_csv_tasks[task_id]["message"] = "正在检测数据冲突..."

        await broadcast_task_progress(
            task_id,
            {
                "progress": 40,
                "message": "正在检测数据冲突...",
                "stage": "conflict_detection",
            },
        )

        # 执行冲突检测
        conflict_result = simulate_conflict_detection_in_import(task_id, "csv_enhanced")

        if conflict_result["has_conflicts"]:
            # 如果有冲突，暂停任务等待用户解决
            enhanced_csv_tasks[task_id]["status"] = "conflicts_detected"
            enhanced_csv_tasks[task_id][
                "message"
            ] = f"检测到 {conflict_result['conflict_count']} 个冲突，等待用户解决"
            enhanced_csv_tasks[task_id]["conflicts"] = conflict_result["conflicts"]

            await broadcast_task_status_change(
                task_id,
                "running",
                "conflicts_detected",
                {"conflict_count": conflict_result["conflict_count"]},
            )

            # 等待冲突解决
            while enhanced_csv_tasks[task_id]["status"] == "conflicts_detected":
                await asyncio.sleep(2)

                # 检查是否所有冲突都已解决
                session_conflicts = get_session_conflicts(task_id)
                if session_conflicts and session_conflicts.get("status") == "resolved":
                    enhanced_csv_tasks[task_id]["status"] = "running"
                    enhanced_csv_tasks[task_id]["message"] = "冲突已解决，继续导入..."

                    await broadcast_task_status_change(
                        task_id,
                        "conflicts_detected",
                        "running",
                        {"message": "冲突已解决"},
                    )
                    break

                # 检查取消信号
                control_signal = control_manager.check_control_signal(task_id)
                if control_signal == "cancel":
                    enhanced_csv_tasks[task_id]["status"] = "cancelled"
                    enhanced_csv_tasks[task_id]["message"] = "任务已取消"
                    await broadcast_task_status_change(
                        task_id, "conflicts_detected", "cancelled"
                    )
                    return

        # 再次检查控制信号
        control_signal = control_manager.check_control_signal(task_id)
        if control_signal == "cancel":
            enhanced_csv_tasks[task_id]["status"] = "cancelled"
            enhanced_csv_tasks[task_id]["message"] = "任务已取消"
            await broadcast_task_status_change(task_id, "running", "cancelled")
            return
        elif control_signal == "pause":
            enhanced_csv_tasks[task_id]["status"] = "paused"
            enhanced_csv_tasks[task_id]["message"] = "任务已暂停"
            await broadcast_task_status_change(task_id, "running", "paused")
            # 等待恢复信号
            while enhanced_csv_tasks[task_id]["status"] == "paused":
                import asyncio

                await asyncio.sleep(1)
                control_signal = control_manager.check_control_signal(task_id)
                if control_signal == "resume":
                    enhanced_csv_tasks[task_id]["status"] = "running"
                    enhanced_csv_tasks[task_id]["message"] = "任务已恢复"
                    await broadcast_task_status_change(task_id, "paused", "running")
                elif control_signal == "cancel":
                    enhanced_csv_tasks[task_id]["status"] = "cancelled"
                    enhanced_csv_tasks[task_id]["message"] = "任务已取消"
                    await broadcast_task_status_change(task_id, "paused", "cancelled")
                    return

        # 执行增强导入（基于配置参数）
        import_result = csv_importer.import_single_file(
            file_path,
            table_name=table_name,
            mode=config.mode,
            batch_size=config.batchSize,
            enable_validation=config.enableValidation,
            deduplicate_strategy=config.deduplicateStrategy,
            retry_count=config.retryCount,
        )

        # 检查最终控制信号
        control_signal = control_manager.check_control_signal(task_id)
        if control_signal == "cancel":
            enhanced_csv_tasks[task_id]["status"] = "cancelled"
            enhanced_csv_tasks[task_id]["message"] = "任务已取消"
            await broadcast_task_status_change(task_id, "running", "cancelled")
            return

        # 更新进度
        enhanced_csv_tasks[task_id]["progress"] = 80
        enhanced_csv_tasks[task_id]["message"] = "正在完成导入..."
        await broadcast_task_progress(
            task_id,
            {"progress": 90, "message": "正在完成导入...", "stage": "completing"},
        )

        # 完成任务
        enhanced_csv_tasks[task_id]["status"] = "completed"
        enhanced_csv_tasks[task_id]["progress"] = 100
        enhanced_csv_tasks[task_id]["message"] = "CSV导入完成"
        enhanced_csv_tasks[task_id]["result"] = import_result

        # 广播任务完成
        await broadcast_task_status_change(
            task_id,
            "running",
            "completed",
            {
                "total_records": import_result.get("total_records", 0),
                "success_records": import_result.get("success_records", 0),
            },
        )

        await broadcast_task_progress(
            task_id,
            {
                "progress": 100,
                "message": "CSV导入完成",
                "stage": "completed",
                "result": import_result,
            },
        )

        # 记录导入完成
        total_records = import_result.get("total_records", 0)
        success_records = import_result.get("success_records", 0)
        error_records = (
            total_records - success_records if total_records > success_records else 0
        )

        history_manager.complete_import(
            import_id=import_id,
            status="completed",
            total_records=total_records,
            success_records=success_records,
            error_records=error_records,
        )

    except Exception as e:
        logger.error(f"增强版CSV导入任务失败: {e}")
        enhanced_csv_tasks[task_id]["status"] = "failed"
        enhanced_csv_tasks[task_id]["error"] = str(e)
        enhanced_csv_tasks[task_id]["message"] = f"导入失败: {str(e)}"

        # 广播任务失败
        await broadcast_task_status_change(
            task_id, "running", "failed", {"error_message": str(e)}
        )

        # 记录导入失败
        history_manager.complete_import(
            import_id=import_id, status="failed", error_message=str(e)
        )

    finally:
        # 清理临时文件
        if file_path.exists():
            file_path.unlink(missing_ok=True)


async def run_fromc2c_import_task(task_id: str, request: FromC2CImportRequest):
    """后台运行FromC2C导入任务（支持WebSocket实时进度）"""
    initial_status = {
        "status": "running",
        "result": None,
        "progress": 0,
        "message": "开始FromC2C数据导入...",
    }
    fromc2c_tasks[task_id] = initial_status

    # 广播任务开始
    await broadcast_task_status_change(
        task_id, "pending", "running", {"task_type": "fromc2c", "env": request.env}
    )

    # 初始化历史记录管理器
    history_manager = ImportHistoryManager(request.env)

    # 记录导入开始
    source_info = {
        "type": "fromc2c",
        "path": request.path,
        "max_files": request.max_files,
        "table_filter": request.table_filter,
    }

    import_config = request.dict()
    session_info = {"task_id": task_id}

    import_id = history_manager.record_import_start(
        import_type="fromc2c",
        source_info=source_info,
        import_config=import_config,
        session_info=session_info,
    )

    try:
        # 更新进度
        fromc2c_tasks[task_id]["progress"] = 20
        fromc2c_tasks[task_id]["message"] = "正在执行环境检查..."

        # 广播进度更新
        await broadcast_task_progress(
            task_id,
            {
                "progress": 20,
                "message": "正在执行环境检查...",
                "stage": "environment_check",
            },
        )

        # 执行FromC2C导入逻辑
        result = execute_import_logic(
            env=request.env,
            path=request.path,
            max_files=request.max_files,
            table_filter=request.table_filter,
        )

        # 更新最终状态
        fromc2c_tasks[task_id]["status"] = "completed"
        fromc2c_tasks[task_id]["result"] = result
        fromc2c_tasks[task_id]["progress"] = 100
        fromc2c_tasks[task_id]["message"] = "FromC2C数据导入完成"

        # 广播任务完成
        await broadcast_task_status_change(
            task_id,
            "running",
            "completed",
            {
                "total_records_imported": result.get("total_records_imported", 0),
                "total_files_processed": result.get("total_files_processed", 0),
            },
        )

        await broadcast_task_progress(
            task_id,
            {
                "progress": 100,
                "message": "FromC2C数据导入完成",
                "stage": "completed",
                "result": result,
            },
        )

        # 记录导入完成
        total_records = result.get("total_records_imported", 0)
        success_records = result.get("total_files_processed", 0)

        history_manager.complete_import(
            import_id=import_id,
            status="completed",
            total_records=total_records,
            success_records=success_records,
        )

    except Exception as e:
        logger.error(f"FromC2C导入任务失败: {e}")
        fromc2c_tasks[task_id]["status"] = "failed"
        fromc2c_tasks[task_id]["result"] = {"success": False, "error": str(e)}
        fromc2c_tasks[task_id]["message"] = f"导入失败: {str(e)}"

        # 广播任务失败
        await broadcast_task_status_change(
            task_id, "running", "failed", {"error_message": str(e)}
        )

        # 记录导入失败
        history_manager.complete_import(
            import_id=import_id, status="failed", error_message=str(e)
        )


async def run_mysql_import_task(task_id: str, request: MySQLImportRequest):
    """后台运行MySQL导入任务（支持WebSocket实时进度）"""
    initial_status = {
        "status": "running",
        "progress": 0,
        "message": "开始MySQL数据导入...",
        "result": None,
        "error": None,
    }
    mysql_import_tasks[task_id] = initial_status

    # 广播任务开始
    await broadcast_task_status_change(
        task_id,
        "pending",
        "running",
        {
            "task_type": "mysql",
            "host": request.connectionConfig.host,
            "database": request.connectionConfig.database,
            "tables_count": len(request.tables),
        },
    )

    # 使用配置驱动的环境
    if request.environment is None:
        request.environment = get_default_environment()

    # 初始化历史记录管理器
    history_manager = ImportHistoryManager(request.environment)

    # 记录导入开始
    source_info = {
        "type": "mysql",
        "host": request.connectionConfig.host,
        "port": request.connectionConfig.port,
        "database": request.connectionConfig.database,
        "username": request.connectionConfig.username,
        "tables": request.tables,
    }

    import_config = {
        "connection_config": request.connectionConfig.dict(),
        "environment": request.environment,
    }

    session_info = {"task_id": task_id}

    import_id = history_manager.record_import_start(
        import_type="mysql",
        source_info=source_info,
        target_tables=request.tables,
        import_config=import_config,
        session_info=session_info,
    )

    try:
        # 更新进度
        mysql_import_tasks[task_id]["progress"] = 20
        mysql_import_tasks[task_id]["message"] = "正在连接MySQL数据库..."

        # 广播进度更新
        await broadcast_task_progress(
            task_id,
            {
                "progress": 20,
                "message": "正在连接MySQL数据库...",
                "stage": "connecting",
            },
        )

        # 构建配置
        config_dict = {
            "connectionConfig": request.connectionConfig.dict(),
            "tables": request.tables,
            "environment": request.environment,
        }

        # 执行MySQL导入逻辑
        result = execute_mysql_import_logic(config_dict)

        # 更新最终状态
        final_status = "completed" if result.get("success") else "failed"
        mysql_import_tasks[task_id]["status"] = final_status
        mysql_import_tasks[task_id]["result"] = result
        mysql_import_tasks[task_id]["progress"] = 100
        mysql_import_tasks[task_id]["message"] = result["message"]

        # 广播任务完成
        await broadcast_task_status_change(
            task_id,
            "running",
            final_status,
            {
                "total_records_imported": result.get("total_records_imported", 0),
                "successful_tables": result.get("successful_tables", 0),
                "failed_tables": result.get("failed_tables", 0),
            },
        )

        await broadcast_task_progress(
            task_id,
            {
                "progress": 100,
                "message": result["message"],
                "stage": "completed",
                "result": result,
            },
        )

        # 记录导入完成
        total_records = result.get("total_records_imported", 0)
        success_tables = result.get("successful_tables", 0)
        failed_tables = result.get("failed_tables", 0)

        history_manager.complete_import(
            import_id=import_id,
            status="completed" if result.get("success") else "failed",
            total_records=total_records,
            success_records=success_tables,
            error_records=failed_tables,
            error_message=result.get("message") if not result.get("success") else None,
        )

    except Exception as e:
        logger.error(f"MySQL导入任务失败: {e}")
        mysql_import_tasks[task_id]["status"] = "failed"
        mysql_import_tasks[task_id]["result"] = {"success": False, "error": str(e)}
        mysql_import_tasks[task_id]["message"] = f"导入失败: {str(e)}"

        # 广播任务失败
        await broadcast_task_status_change(
            task_id, "running", "failed", {"error_message": str(e)}
        )

        # 记录导入失败
        history_manager.complete_import(
            import_id=import_id, status="failed", error_message=str(e)
        )


@router.get("/health", response_model=Dict[str, Any])
async def health_check():
    """数据服务健康检查"""
    try:
        # 检查数据库连接
        checker = DuckDBInitChecker()
        health_report = checker.get_database_health_report()

        return {
            "status": "healthy",
            "database_status": health_report.get("connection_status", "unknown"),
            "timestamp": get_beijing_time_now().isoformat(),
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="数据服务不可用")


@router.get("/tables", response_model=TableListResponse)
async def get_tables(environment: str = Query(None, description="环境名称")):
    """
    获取数据库表列表

    Args:
        environment: 环境名称 (test/dev/prod)

    Returns:
        TableListResponse: 表列表响应
    """
    try:
        # 使用配置驱动的环境
        if environment is None:
            environment = get_default_environment()

        # 初始化数据库检查器
        checker = DuckDBInitChecker(environment=environment)
        connection_manager = DuckDBConnectionManager(environment=environment)

        # 获取所有表名
        table_names = connection_manager.get_all_tables()

        # 构建表信息列表
        table_info_list = []
        for table_name in table_names:
            try:
                # 获取表记录数
                record_count = connection_manager.get_table_count(table_name)

                # 获取表结构信息
                table_info = connection_manager.get_table_info(table_name)

                # 构建列信息
                columns = []
                for col in table_info:
                    columns.append(
                        {
                            "name": col["column_name"],
                            "type": col["column_type"],
                            "nullable": col.get("null", "YES"),
                            "key": col.get("key", ""),
                            "default": col.get("default", ""),
                        }
                    )

                table_info_obj = TableInfo(
                    name=table_name,
                    record_count=record_count,
                    columns=columns,
                    last_updated=None,  # 暂时不获取更新时间
                    size_mb=None,  # 暂时不计算表大小
                )
                table_info_list.append(table_info_obj)

            except Exception as e:
                logger.warning(f"获取表 {table_name} 信息失败: {e}")
                # 即使某个表信息获取失败，也继续处理其他表
                continue

        return TableListResponse(
            success=True,
            data=table_info_list,
            message=f"成功获取 {len(table_info_list)} 个表的信息",
            timestamp=get_beijing_time_now(),
            total_count=len(table_info_list),
        )

    except Exception as e:
        logger.error(f"获取表列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取表列表失败: {str(e)}")


@router.get("/tables/{table_name}", response_model=TableDataResponse)
async def get_table_data(
    table_name: str,
    environment: str = Query(None, description="环境名称"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    columns: Optional[List[str]] = Query(None, description="指定查询的列"),
):
    """
    获取指定表的数据

    Args:
        table_name: 表名
        environment: 环境名称
        limit: 返回记录数限制
        offset: 偏移量
        columns: 指定查询的列

    Returns:
        TableDataResponse: 表数据响应
    """
    try:
        # 使用配置驱动的环境
        if environment is None:
            environment = get_default_environment()

        connection_manager = DuckDBConnectionManager(environment=environment)

        # 检查表是否存在
        if not connection_manager.table_exists(table_name):
            raise_database_error(
                f"表 '{table_name}' 不存在", table_name=table_name, cause=None
            )

        # 构建查询语句
        if columns:
            # 验证列名（防止SQL注入）
            table_info = connection_manager.get_table_info(table_name)
            valid_columns = [col["column_name"] for col in table_info]

            for col in columns:
                if col not in valid_columns:
                    raise_validation_error(
                        f"列 '{col}' 不存在于表 '{table_name}' 中",
                        field_name="columns",
                        field_value=col,
                        validation_rules=["column_exists"],
                    )

            columns_str = ", ".join(columns)
        else:
            columns_str = "*"

        # 执行查询
        query = f"""
        SELECT {columns_str} 
        FROM {table_name} 
        LIMIT {limit} OFFSET {offset}
        """

        result = connection_manager.execute_query(query)

        # 获取列信息用于构建响应
        table_info = connection_manager.get_table_info(table_name)
        column_names = [col["column_name"] for col in table_info]

        # 构建数据列表
        data_list = []
        for row in result:
            row_dict = {}
            for i, value in enumerate(row):
                if i < len(column_names):
                    # 处理特殊数据类型
                    if isinstance(value, datetime):
                        row_dict[column_names[i]] = value.isoformat()
                    else:
                        row_dict[column_names[i]] = value
            data_list.append(row_dict)

        # 获取总记录数
        total_count = connection_manager.get_table_count(table_name)

        return TableDataResponse(
            success=True,
            data=data_list,
            pagination={
                "limit": limit,
                "offset": offset,
                "total": total_count,
                "has_next": offset + limit < total_count,
            },
            message=f"成功获取表 '{table_name}' 的 {len(data_list)} 条记录",
            timestamp=get_beijing_time_now(),
            table_name=table_name,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取表数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取表数据失败: {str(e)}")


@router.get("/tables/{table_name}/meta", response_model=TableMetaResponse)
async def get_table_meta(
    table_name: str, environment: str = Query(None, description="环境名称")
):
    """
    获取表的元数据信息

    Args:
        table_name: 表名
        environment: 环境名称

    Returns:
        TableMetaResponse: 表元数据响应
    """
    try:
        connection_manager = DuckDBConnectionManager(environment=environment)

        # 检查表是否存在
        if not connection_manager.table_exists(table_name):
            raise_database_error(
                f"表 '{table_name}' 不存在", table_name=table_name, cause=None
            )

        # 获取表信息
        table_info = connection_manager.get_table_info(table_name)
        record_count = connection_manager.get_table_count(table_name)

        # 构建元数据
        meta_data = {
            "table_name": table_name,
            "record_count": record_count,
            "column_count": len(table_info),
            "columns": table_info,
            "indexes": [],  # 暂时不获取索引信息
            "constraints": [],  # 暂时不获取约束信息
        }

        return TableMetaResponse(
            success=True,
            data=meta_data,
            message=f"成功获取表 '{table_name}' 的元数据",
            timestamp=get_beijing_time_now(),
            table_name=table_name,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取表元数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取表元数据失败: {str(e)}")


@router.post("/import/csv", response_model=ImportResponse)
async def import_csv_data(
    file: UploadFile = File(...),
    environment: str = Query(None, description="环境名称"),
    table_name: Optional[str] = Query(None, description="目标表名"),
):
    """
    导入CSV数据

    Args:
        file: CSV文件
        environment: 环境名称
        table_name: 目标表名（如果不指定，将使用文件名）

    Returns:
        ImportResponse: 导入结果响应
    """
    try:
        # 验证文件类型
        if not file.filename.endswith(".csv"):
            raise_validation_error(
                "只支持CSV文件格式",
                field_name="file_format",
                field_value=file.filename,
                validation_rules=["csv_format"],
            )

        # 创建临时目录
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)

        # 保存上传的文件
        temp_file_path = temp_dir / file.filename
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 初始化CSV导入器
        csv_importer = CSVImporter(environment=environment)

        # 执行导入
        result = csv_importer.import_single_file(temp_file_path, table_name=table_name)

        # 清理临时文件
        temp_file_path.unlink(missing_ok=True)

        return ImportResponse(
            success=result.get("success", False),
            data=result,
            message=result.get("message", "导入完成"),
            timestamp=get_beijing_time_now(),
            task_id=None,  # 暂时不支持异步任务
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CSV导入失败: {e}")
        raise HTTPException(status_code=500, detail=f"CSV导入失败: {str(e)}")


@router.post("/import/csv-enhanced", response_model=ImportResponse)
async def import_csv_enhanced(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    environment: str = Form(None),
    mode: str = Form("replace"),
    enableValidation: bool = Form(True),
    deduplicateStrategy: str = Form("skip"),
    batchConcurrency: int = Form(4),
    batchSize: int = Form(1000),
    retryCount: int = Form(3),
    table_name: Optional[str] = Form(None),
):
    """
    增强版CSV数据导入API

    Args:
        background_tasks: FastAPI后台任务
        file: CSV文件
        environment: 环境名称
        mode: 导入模式 (replace, append, skip)
        enableValidation: 是否启用数据验证
        deduplicateStrategy: 去重策略 (skip, replace, error)
        batchConcurrency: 批处理并发数
        batchSize: 批处理大小
        retryCount: 重试次数
        table_name: 目标表名

    Returns:
        ImportResponse: 导入任务响应
    """
    try:
        # 验证文件类型
        if not file.filename.endswith(".csv"):
            raise_validation_error(
                "只支持CSV文件格式",
                field_name="file_format",
                field_value=file.filename,
                validation_rules=["csv_format"],
            )

        # 创建临时目录
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)

        # 保存上传的文件
        temp_file_path = temp_dir / file.filename
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 生成任务ID
        import uuid

        task_id = str(uuid.uuid4())

        # 使用配置驱动的环境
        if environment is None:
            environment = get_default_environment()

        # 构建配置
        config = EnhancedCSVImportConfig(
            environment=environment,
            mode=mode,
            enableValidation=enableValidation,
            deduplicateStrategy=deduplicateStrategy,
            batchConcurrency=batchConcurrency,
            batchSize=batchSize,
            retryCount=retryCount,
        )

        # 启动后台任务
        background_tasks.add_task(
            sync_run_enhanced_csv_import_task,
            task_id,
            temp_file_path,
            config,
            table_name,
        )

        return ImportResponse(
            success=True,
            data={"task_id": task_id},
            message="增强版CSV导入任务已启动",
            timestamp=get_beijing_time_now(),
            task_id=task_id,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"增强版CSV导入启动失败: {e}")
        raise HTTPException(status_code=500, detail=f"增强版CSV导入启动失败: {str(e)}")


@router.get("/import/csv-enhanced/tasks/{task_id}")
async def get_enhanced_csv_task_status(task_id: str):
    """
    获取增强版CSV导入任务状态

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    task = enhanced_csv_tasks.get(task_id)
    if not task:
        raise_validation_error(
            "增强CSV导入任务不存在",
            field_name="task_id",
            field_value=task_id,
            validation_rules=["task_exists"],
        )

    return {
        "success": True,
        "data": task,
        "timestamp": get_beijing_time_now(),
        "task_id": task_id,
    }


@router.post("/import/fromc2c/pre-check")
async def fromc2c_pre_check(env: str = None):
    """
    FromC2C导入预检查API

    Args:
        env: 环境名称

    Returns:
        预检查结果
    """
    try:
        # 使用配置驱动的环境
        if env is None:
            env = get_default_environment()

        # 执行环境检查
        env_check = check_environment_logic(env)
        if not env_check["success"]:
            return {
                "success": False,
                "checks": [env_check],
                "timestamp": get_beijing_time_now(),
            }

        # 执行数据源检查
        source_check = check_data_source_logic()

        return {
            "success": env_check["success"] and source_check["success"],
            "checks": [env_check, source_check],
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"FromC2C预检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"FromC2C预检查失败: {str(e)}")


@router.post("/import/fromc2c/execute", response_model=ImportResponse)
async def fromc2c_execute(
    background_tasks: BackgroundTasks, request: FromC2CImportRequest
):
    """
    执行FromC2C导入任务API

    Args:
        background_tasks: FastAPI后台任务
        request: FromC2C导入请求

    Returns:
        ImportResponse: 导入任务响应
    """
    try:
        # 生成任务ID
        import uuid

        task_id = str(uuid.uuid4())

        # 初始化任务状态
        fromc2c_tasks[task_id] = {"status": "pending", "result": None}

        # 启动后台任务
        background_tasks.add_task(sync_run_fromc2c_import_task, task_id, request)

        return ImportResponse(
            success=True,
            data={"task_id": task_id},
            message="FromC2C导入任务已启动",
            timestamp=get_beijing_time_now(),
            task_id=task_id,
        )

    except Exception as e:
        logger.error(f"FromC2C导入启动失败: {e}")
        raise HTTPException(status_code=500, detail=f"FromC2C导入启动失败: {str(e)}")


@router.get("/import/fromc2c/tasks/{task_id}")
async def get_fromc2c_task_status(task_id: str):
    """
    获取FromC2C导入任务状态

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    task = fromc2c_tasks.get(task_id)
    if not task:
        raise_validation_error(
            "FromC2C导入任务不存在",
            field_name="task_id",
            field_value=task_id,
            validation_rules=["task_exists"],
        )

    return {
        "success": True,
        "data": task,
        "timestamp": get_beijing_time_now(),
        "task_id": task_id,
    }


@router.post("/mysql/test-connection")
async def test_mysql_connection(connection_config: MySQLConnectionConfig):
    """
    测试MySQL数据库连接

    Args:
        connection_config: MySQL连接配置

    Returns:
        连接测试结果
    """
    try:
        # 转换为字典格式
        config_dict = connection_config.dict()

        # 调用测试逻辑
        result = test_mysql_connection_logic(config_dict)

        return {
            "success": result["success"],
            "data": result,
            "message": result["message"],
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"MySQL连接测试失败: {e}")
        raise_database_error(f"MySQL连接测试失败: {str(e)}", table_name=None, cause=e)


@router.post("/mysql/tables")
async def get_mysql_tables(tables_request: MySQLTablesRequest):
    """
    获取MySQL数据库表列表

    Args:
        tables_request: MySQL表查询请求

    Returns:
        MySQL表列表信息
    """
    try:
        # 转换为字典格式
        config_dict = tables_request.dict()

        # 调用获取表列表逻辑
        result = get_mysql_tables_logic(config_dict)

        return {
            "success": result["success"],
            "data": result,
            "message": result["message"],
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"获取MySQL表列表失败: {e}")
        raise_database_error(f"获取MySQL表列表失败: {str(e)}", table_name=None, cause=e)


@router.post("/mysql/preview")
async def preview_mysql_table(preview_request: MySQLPreviewRequest):
    """
    预览MySQL表数据

    Args:
        preview_request: MySQL表预览请求

    Returns:
        MySQL表数据预览结果
    """
    try:
        # 验证表名
        if not preview_request.tableName.strip():
            raise_validation_error(
                "表名不能为空",
                field_name="tableName",
                field_value=preview_request.tableName,
                validation_rules=["not_empty"],
            )

        # 转换为字典格式
        config_dict = preview_request.dict()

        # 调用预览表数据逻辑
        result = preview_mysql_table_logic(config_dict)

        return {
            "success": result["success"],
            "data": result.get("data", {}),
            "message": result["message"],
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"预览MySQL表数据失败: {e}")
        raise_database_error(
            f"预览MySQL表数据失败: {str(e)}",
            table_name=(
                preview_request.tableName
                if hasattr(preview_request, "tableName")
                else None
            ),
            cause=e,
        )


@router.post("/mysql/import", response_model=ImportResponse)
async def import_mysql_data(
    background_tasks: BackgroundTasks, import_request: MySQLImportRequest
):
    """
    执行MySQL数据导入任务

    Args:
        background_tasks: FastAPI后台任务
        import_request: MySQL导入请求

    Returns:
        ImportResponse: 导入任务响应
    """
    try:
        # 验证请求
        if not import_request.tables:
            raise_validation_error(
                "导入表列表不能为空",
                field_name="tables",
                field_value=import_request.tables,
                validation_rules=["not_empty"],
            )

        # 生成任务ID
        import uuid

        task_id = str(uuid.uuid4())

        # 使用配置驱动的环境
        if import_request.environment is None:
            import_request.environment = get_default_environment()

        # 初始化任务状态
        mysql_import_tasks[task_id] = {"status": "pending", "result": None}

        # 启动后台任务
        background_tasks.add_task(sync_run_mysql_import_task, task_id, import_request)

        return ImportResponse(
            success=True,
            data={"task_id": task_id, "tables": import_request.tables},
            message="MySQL导入任务已启动",
            timestamp=get_beijing_time_now(),
            task_id=task_id,
        )

    except Exception as e:
        logger.error(f"MySQL导入启动失败: {e}")
        raise_database_error(f"MySQL导入启动失败: {str(e)}", table_name=None, cause=e)


@router.get("/mysql/import/tasks/{task_id}")
async def get_mysql_import_task_status(task_id: str):
    """
    获取MySQL导入任务状态

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    task = mysql_import_tasks.get(task_id)
    if not task:
        raise_validation_error(
            "MySQL导入任务不存在",
            field_name="task_id",
            field_value=task_id,
            validation_rules=["task_exists"],
        )

    return {
        "success": True,
        "data": task,
        "timestamp": get_beijing_time_now(),
        "task_id": task_id,
    }


@router.get("/import/history")
async def get_import_history(
    environment: str = Query(None, description="环境名称"),
    importType: Optional[str] = Query(None, description="导入类型筛选"),
    startDate: Optional[str] = Query(None, description="开始日期"),
    endDate: Optional[str] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=1000, description="记录数限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
):
    """
    获取导入历史记录

    Args:
        environment: 环境名称
        importType: 导入类型筛选 (csv, mysql, fromc2c)
        startDate: 开始日期 (YYYY-MM-DD)
        endDate: 结束日期 (YYYY-MM-DD)
        limit: 记录数限制
        offset: 偏移量

    Returns:
        导入历史记录列表
    """
    try:
        # 使用配置驱动的环境
        if environment is None:
            environment = get_default_environment()

        # 初始化历史记录管理器
        history_manager = ImportHistoryManager(environment)

        # 获取历史记录
        result = history_manager.get_import_history(
            import_type=importType,
            start_date=startDate,
            end_date=endDate,
            limit=limit,
            offset=offset,
        )

        return {
            "success": result["success"],
            "data": result["data"],
            "message": result["message"],
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"获取导入历史失败: {e}")
        raise_database_error(f"获取导入历史失败: {str(e)}", table_name=None, cause=e)


@router.get("/import/stats")
async def get_import_stats(
    environment: str = Query(None, description="环境名称"),
    period: str = Query("day", description="统计周期"),
    startDate: Optional[str] = Query(None, description="开始日期"),
    endDate: Optional[str] = Query(None, description="结束日期"),
    importType: Optional[str] = Query(None, description="导入类型筛选"),
):
    """
    获取导入统计信息

    Args:
        environment: 环境名称
        period: 统计周期 (day, week, month)
        startDate: 开始日期 (YYYY-MM-DD)
        endDate: 结束日期 (YYYY-MM-DD)
        importType: 导入类型筛选 (csv, mysql, fromc2c)

    Returns:
        导入统计信息
    """
    try:
        # 验证统计周期
        valid_periods = ["day", "week", "month"]
        if period not in valid_periods:
            raise_validation_error(
                f"无效的统计周期，必须是: {', '.join(valid_periods)}",
                field_name="period",
                field_value=period,
                validation_rules=["valid_period"],
            )

        # 使用配置驱动的环境
        if environment is None:
            environment = get_default_environment()

        # 初始化历史记录管理器
        history_manager = ImportHistoryManager(environment)

        # 获取统计信息
        result = history_manager.get_import_stats(
            period=period,
            start_date=startDate,
            end_date=endDate,
            import_type=importType,
        )

        return {
            "success": result["success"],
            "data": result["data"],
            "message": result["message"],
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"获取导入统计失败: {e}")
        raise_database_error(f"获取导入统计失败: {str(e)}", table_name=None, cause=e)


@router.delete("/import/history/{import_id}")
async def delete_import_record(import_id: str):
    """
    删除导入历史记录

    Args:
        import_id: 导入记录ID

    Returns:
        删除结果
    """
    try:
        # 验证import_id
        if not import_id.strip():
            raise_validation_error(
                "导入记录ID不能为空",
                field_name="import_id",
                field_value=import_id,
                validation_rules=["not_empty"],
            )

        # 使用默认环境
        environment = get_default_environment()
        history_manager = ImportHistoryManager(environment)

        # 删除记录
        result = history_manager.delete_import_record(import_id)

        return {
            "success": result["success"],
            "message": result["message"],
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"删除导入记录失败: {e}")
        raise_database_error(f"删除导入记录失败: {str(e)}", table_name=None, cause=e)


@router.post("/import/cancel")
async def cancel_import_task(request: Dict[str, str]):
    """
    取消导入任务

    Args:
        request: 包含sessionId的请求

    Returns:
        取消操作结果
    """
    try:
        session_id = request.get("sessionId")
        if not session_id:
            raise_validation_error(
                "sessionId不能为空",
                field_name="sessionId",
                field_value=session_id,
                validation_rules=["not_empty"],
            )

        # 获取任务控制管理器
        control_manager = get_task_control_manager()

        # 执行取消操作
        result = control_manager.cancel_task(session_id)

        return {
            "success": result["success"],
            "message": result["message"],
            "data": result,
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"取消导入任务失败: {e}")
        raise_database_error(f"取消导入任务失败: {str(e)}", table_name=None, cause=e)


@router.post("/import/pause")
async def pause_import_task(request: Dict[str, str]):
    """
    暂停导入任务

    Args:
        request: 包含sessionId的请求

    Returns:
        暂停操作结果
    """
    try:
        session_id = request.get("sessionId")
        if not session_id:
            raise_validation_error(
                "sessionId不能为空",
                field_name="sessionId",
                field_value=session_id,
                validation_rules=["not_empty"],
            )

        # 获取任务控制管理器
        control_manager = get_task_control_manager()

        # 执行暂停操作
        result = control_manager.pause_task(session_id)

        return {
            "success": result["success"],
            "message": result["message"],
            "data": result,
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"暂停导入任务失败: {e}")
        raise_database_error(f"暂停导入任务失败: {str(e)}", table_name=None, cause=e)


@router.post("/import/resume")
async def resume_import_task(request: Dict[str, str]):
    """
    恢复导入任务

    Args:
        request: 包含sessionId的请求

    Returns:
        恢复操作结果
    """
    try:
        session_id = request.get("sessionId")
        if not session_id:
            raise_validation_error(
                "sessionId不能为空",
                field_name="sessionId",
                field_value=session_id,
                validation_rules=["not_empty"],
            )

        # 获取任务控制管理器
        control_manager = get_task_control_manager()

        # 执行恢复操作
        result = control_manager.resume_task(session_id)

        return {
            "success": result["success"],
            "message": result["message"],
            "data": result,
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"恢复导入任务失败: {e}")
        raise_database_error(f"恢复导入任务失败: {str(e)}", table_name=None, cause=e)


@router.post("/import/resolve-conflict")
async def resolve_import_conflict(request: ConflictResolutionRequest):
    """
    解决导入冲突

    Args:
        request: 冲突解决请求

    Returns:
        解决结果
    """
    try:
        # 验证请求参数
        if not request.sessionId.strip():
            raise_validation_error(
                "sessionId不能为空",
                field_name="sessionId",
                field_value=request.sessionId,
                validation_rules=["not_empty"],
            )

        if request.conflictIndex < 0:
            raise_validation_error(
                "conflictIndex必须大于等于0",
                field_name="conflictIndex",
                field_value=request.conflictIndex,
                validation_rules=["min_value"],
            )

        if request.resolution not in ["keep", "replace", "skip"]:
            raise_validation_error(
                "resolution必须是 keep, replace 或 skip 之一",
                field_name="resolution",
                field_value=request.resolution,
                validation_rules=["allowed_values"],
            )

        # 执行冲突解决
        result = resolve_session_conflict(
            request.sessionId, request.conflictIndex, request.resolution
        )

        # 广播冲突解决状态（如果支持WebSocket）
        try:
            await broadcast_task_progress(
                request.sessionId,
                {
                    "progress": (
                        result.get("resolved_count", 0)
                        / result.get("total_conflicts", 1)
                    )
                    * 100,
                    "message": f"已解决冲突 {result.get('resolved_count', 0)}/{result.get('total_conflicts', 0)}",
                    "stage": "conflict_resolution",
                    "conflict_resolution": {
                        "resolved_index": request.conflictIndex,
                        "resolution": request.resolution,
                        "all_resolved": result.get("all_resolved", False),
                    },
                },
            )
        except Exception as e:
            logger.warning(f"广播冲突解决进度失败: {e}")

        return {
            "success": result["success"],
            "message": result["message"],
            "data": {
                "conflict_index": result.get("conflict_index"),
                "resolution": result.get("resolution"),
                "resolved_count": result.get("resolved_count", 0),
                "total_conflicts": result.get("total_conflicts", 0),
                "all_resolved": result.get("all_resolved", False),
            },
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"解决导入冲突失败: {e}")
        raise_database_error(f"解决导入冲突失败: {str(e)}", table_name=None, cause=e)


@router.get("/import/conflicts/{session_id}")
async def get_import_conflicts(session_id: str):
    """
    获取导入会话的冲突信息

    Args:
        session_id: 会话/任务ID

    Returns:
        冲突信息列表
    """
    try:
        # 验证session_id
        if not session_id.strip():
            raise_validation_error(
                "session_id不能为空",
                field_name="session_id",
                field_value=session_id,
                validation_rules=["not_empty"],
            )

        # 获取冲突信息
        session_data = get_session_conflicts(session_id)

        if not session_data:
            return {
                "success": True,
                "message": f"未找到会话 {session_id} 的冲突信息",
                "data": {
                    "conflicts": [],
                    "total_conflicts": 0,
                    "resolved_count": 0,
                    "status": "no_conflicts",
                },
                "timestamp": get_beijing_time_now(),
            }

        conflicts = session_data.get("conflicts", [])
        resolved_count = sum(1 for c in conflicts if "resolution" in c)

        return {
            "success": True,
            "data": {
                "session_id": session_id,
                "conflicts": conflicts,
                "total_conflicts": len(conflicts),
                "resolved_count": resolved_count,
                "status": session_data.get("status", "pending"),
                "metadata": session_data.get("metadata", {}),
                "created_at": session_data.get("created_at"),
            },
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"获取导入冲突失败: {e}")
        raise_database_error(f"获取导入冲突失败: {str(e)}", table_name=None, cause=e)


@router.get("/import/session/{session_id}/status")
async def get_import_session_status(session_id: str):
    """
    获取导入会话状态

    Args:
        session_id: 会话ID

    Returns:
        会话状态信息
    """
    try:
        # 验证session_id
        if not session_id.strip():
            raise_validation_error(
                "会话ID不能为空",
                field_name="session_id",
                field_value=session_id,
                validation_rules=["not_empty"],
            )

        # 获取任务控制管理器
        control_manager = get_task_control_manager()

        # 获取任务状态
        result = control_manager.get_task_status(session_id)

        return {
            "success": result["success"],
            "data": result,
            "message": result.get("message", "获取状态成功"),
            "timestamp": get_beijing_time_now(),
        }

    except Exception as e:
        logger.error(f"获取导入会话状态失败: {e}")
        raise_database_error(
            f"获取导入会话状态失败: {str(e)}", table_name=None, cause=e
        )


# 注意：exception_handler 应该在主应用中注册，而不是在 APIRouter 中
