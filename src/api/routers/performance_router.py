#!/usr/bin/env python3
"""
高性能数据查询API路由

提供经过优化的数据查询API，整合缓存机制和数据库优化
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel, Field

from ...database.connection_manager import DuckDBConnectionManager
from ...database.performance_optimizer import DatabasePerformanceOptimizer
from ...cache.cache_manager import get_cache_manager, cache_result, CacheLevel
from ...utils.time_utils import get_beijing_time_now
from ...utils.logger import get_logger
from ..models.data_models import TableDataResponse

# 创建路由器
router = APIRouter(prefix="/api/performance", tags=["高性能数据查询"])

# 配置日志
logger = get_logger("PerformanceRouter")


# 响应模型
class PerformanceStatsResponse(BaseModel):
    """性能统计响应模型"""

    success: bool
    data: Dict[str, Any]
    message: str
    timestamp: datetime


class OptimizationResponse(BaseModel):
    """优化响应模型"""

    success: bool
    data: Dict[str, Any]
    message: str
    timestamp: datetime


class StockDataRequest(BaseModel):
    """股票数据请求模型"""

    symbol: Optional[str] = None
    symbols: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    limit: int = Field(default=100, ge=1, le=5000)
    offset: int = Field(default=0, ge=0)
    order_by: Optional[str] = "trade_date"
    order_desc: bool = True


class FuturesDataRequest(BaseModel):
    """期货数据请求模型"""

    contract_code: Optional[str] = None
    contract_codes: Optional[List[str]] = None
    start_datetime: Optional[str] = None
    end_datetime: Optional[str] = None
    limit: int = Field(default=100, ge=1, le=5000)
    offset: int = Field(default=0, ge=0)
    order_by: Optional[str] = "trade_datetime"
    order_desc: bool = True


# 缓存管理器
cache_manager = get_cache_manager()


@router.get("/health", response_model=Dict[str, Any])
async def performance_health_check():
    """高性能服务健康检查"""
    try:
        # 检查缓存状态
        cache_stats = cache_manager.get_stats()

        # 检查数据库优化状态
        optimizer = DatabasePerformanceOptimizer()
        db_stats = optimizer.get_database_statistics()

        return {
            "status": "healthy",
            "cache_stats": cache_stats,
            "database_stats": {
                "total_tables": db_stats.get("total_tables", 0),
                "total_indexes": db_stats.get("total_indexes", 0),
            },
            "timestamp": get_beijing_time_now().isoformat(),
        }
    except Exception as e:
        logger.error(f"性能服务健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="高性能服务不可用")


@router.get("/cache/stats", response_model=PerformanceStatsResponse)
async def get_cache_stats():
    """获取缓存统计信息"""
    try:
        stats = cache_manager.get_stats()

        return PerformanceStatsResponse(
            success=True,
            data=stats,
            message="缓存统计信息获取成功",
            timestamp=get_beijing_time_now(),
        )
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


@router.post("/cache/clear")
async def clear_cache():
    """清理缓存"""
    try:
        cache_manager.clear()
        logger.info("缓存已清理")

        return {
            "success": True,
            "message": "缓存清理成功",
            "timestamp": get_beijing_time_now().isoformat(),
        }
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")


@router.post("/optimize/database", response_model=OptimizationResponse)
async def optimize_database(
    background_tasks: BackgroundTasks,
    environment: str = Query("test", description="环境名称"),
):
    """数据库优化"""
    try:
        # 创建数据库优化器
        optimizer = DatabasePerformanceOptimizer(environment=environment)

        # 在后台执行优化
        def run_optimization():
            try:
                results = optimizer.optimize_database()
                logger.info(f"数据库优化完成: {results}")
            except Exception as e:
                logger.error(f"数据库优化失败: {e}")

        background_tasks.add_task(run_optimization)

        return OptimizationResponse(
            success=True,
            data={"status": "optimization_started"},
            message="数据库优化已启动，将在后台执行",
            timestamp=get_beijing_time_now(),
        )
    except Exception as e:
        logger.error(f"启动数据库优化失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动数据库优化失败: {str(e)}")


@cache_result(ttl=600, level=CacheLevel.L1_MEMORY)  # 10分钟缓存
def _get_stock_data_cached(
    environment: str,
    symbol: Optional[str] = None,
    symbols: Optional[List[str]] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    order_by: str = "trade_date",
    order_desc: bool = True,
) -> Dict[str, Any]:
    """缓存的股票数据查询"""
    try:
        connection_manager = DuckDBConnectionManager(environment=environment)

        # 构建查询条件
        conditions = []

        if symbol:
            conditions.append(f"symbol = '{symbol}'")
        elif symbols:
            symbol_list = "', '".join(symbols)
            conditions.append(f"symbol IN ('{symbol_list}')")

        if start_date:
            conditions.append(f"trade_date >= '{start_date}'")

        if end_date:
            conditions.append(f"trade_date <= '{end_date}'")

        # 构建WHERE子句
        where_clause = " AND ".join(conditions) if conditions else "1=1"

        # 构建ORDER BY子句
        order_direction = "DESC" if order_desc else "ASC"
        order_clause = f"ORDER BY {order_by} {order_direction}"

        # 构建查询语句
        query = f"""
        SELECT 
            symbol,
            trade_date,
            open,
            high,
            low,
            close,
            volume,
            amount,
            turnover_rate,
            created_at,
            updated_at
        FROM stock_kline_daily
        WHERE {where_clause}
        {order_clause}
        LIMIT {limit} OFFSET {offset}
        """

        # 执行查询
        result = connection_manager.execute_query(query)

        # 获取总记录数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM stock_kline_daily
        WHERE {where_clause}
        """
        total_result = connection_manager.execute_query(count_query)
        total_count = total_result[0][0] if total_result else 0

        # 转换数据格式
        data_list = []
        for row in result:
            data_list.append(
                {
                    "symbol": row[0],
                    "trade_date": (
                        row[1].isoformat() if isinstance(row[1], date) else row[1]
                    ),
                    "open": float(row[2]) if row[2] is not None else None,
                    "high": float(row[3]) if row[3] is not None else None,
                    "low": float(row[4]) if row[4] is not None else None,
                    "close": float(row[5]) if row[5] is not None else None,
                    "volume": int(row[6]) if row[6] is not None else None,
                    "amount": float(row[7]) if row[7] is not None else None,
                    "turnover_rate": float(row[8]) if row[8] is not None else None,
                    "created_at": (
                        row[9].isoformat() if isinstance(row[9], datetime) else row[9]
                    ),
                    "updated_at": (
                        row[10].isoformat()
                        if isinstance(row[10], datetime)
                        else row[10]
                    ),
                }
            )

        return {
            "data": data_list,
            "total": total_count,
            "has_more": offset + limit < total_count,
        }

    except Exception as e:
        logger.error(f"查询股票数据失败: {e}")
        raise e


@router.post("/stock/data", response_model=TableDataResponse)
async def get_stock_data(
    request: StockDataRequest, environment: str = Query("test", description="环境名称")
):
    """获取股票数据（带缓存优化）"""
    try:
        # 调用缓存的查询函数
        result = _get_stock_data_cached(
            environment=environment,
            symbol=request.symbol,
            symbols=request.symbols,
            start_date=request.start_date,
            end_date=request.end_date,
            limit=request.limit,
            offset=request.offset,
            order_by=request.order_by,
            order_desc=request.order_desc,
        )

        return TableDataResponse(
            success=True,
            data=result["data"],
            pagination={
                "limit": request.limit,
                "offset": request.offset,
                "total": result["total"],
                "has_next": result["has_more"],
            },
            message=f"成功获取 {len(result['data'])} 条股票数据",
            timestamp=get_beijing_time_now(),
            table_name="stock_kline_daily",
        )

    except Exception as e:
        logger.error(f"获取股票数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票数据失败: {str(e)}")


@cache_result(ttl=300, level=CacheLevel.L1_MEMORY)  # 5分钟缓存
def _get_futures_data_cached(
    environment: str,
    contract_code: Optional[str] = None,
    contract_codes: Optional[List[str]] = None,
    start_datetime: Optional[str] = None,
    end_datetime: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    order_by: str = "trade_datetime",
    order_desc: bool = True,
) -> Dict[str, Any]:
    """缓存的期货数据查询"""
    try:
        connection_manager = DuckDBConnectionManager(environment=environment)

        # 构建查询条件
        conditions = []

        if contract_code:
            conditions.append(f"contract_code = '{contract_code}'")
        elif contract_codes:
            contract_list = "', '".join(contract_codes)
            conditions.append(f"contract_code IN ('{contract_list}')")

        if start_datetime:
            conditions.append(f"trade_datetime >= '{start_datetime}'")

        if end_datetime:
            conditions.append(f"trade_datetime <= '{end_datetime}'")

        # 构建WHERE子句
        where_clause = " AND ".join(conditions) if conditions else "1=1"

        # 构建ORDER BY子句
        order_direction = "DESC" if order_desc else "ASC"
        order_clause = f"ORDER BY {order_by} {order_direction}"

        # 构建查询语句
        query = f"""
        SELECT 
            contract_code,
            trade_datetime,
            open,
            high,
            low,
            close,
            volume,
            amount,
            open_interest,
            created_at,
            updated_at
        FROM fut_main_contract_kline_15min
        WHERE {where_clause}
        {order_clause}
        LIMIT {limit} OFFSET {offset}
        """

        # 执行查询
        result = connection_manager.execute_query(query)

        # 获取总记录数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM fut_main_contract_kline_15min
        WHERE {where_clause}
        """
        total_result = connection_manager.execute_query(count_query)
        total_count = total_result[0][0] if total_result else 0

        # 转换数据格式
        data_list = []
        for row in result:
            data_list.append(
                {
                    "contract_code": row[0],
                    "trade_datetime": (
                        row[1].isoformat() if isinstance(row[1], datetime) else row[1]
                    ),
                    "open": float(row[2]) if row[2] is not None else None,
                    "high": float(row[3]) if row[3] is not None else None,
                    "low": float(row[4]) if row[4] is not None else None,
                    "close": float(row[5]) if row[5] is not None else None,
                    "volume": int(row[6]) if row[6] is not None else None,
                    "amount": float(row[7]) if row[7] is not None else None,
                    "open_interest": int(row[8]) if row[8] is not None else None,
                    "created_at": (
                        row[9].isoformat() if isinstance(row[9], datetime) else row[9]
                    ),
                    "updated_at": (
                        row[10].isoformat()
                        if isinstance(row[10], datetime)
                        else row[10]
                    ),
                }
            )

        return {
            "data": data_list,
            "total": total_count,
            "has_more": offset + limit < total_count,
        }

    except Exception as e:
        logger.error(f"查询期货数据失败: {e}")
        raise e


@router.post("/futures/data", response_model=TableDataResponse)
async def get_futures_data(
    request: FuturesDataRequest,
    environment: str = Query("test", description="环境名称"),
):
    """获取期货数据（带缓存优化）"""
    try:
        # 调用缓存的查询函数
        result = _get_futures_data_cached(
            environment=environment,
            contract_code=request.contract_code,
            contract_codes=request.contract_codes,
            start_datetime=request.start_datetime,
            end_datetime=request.end_datetime,
            limit=request.limit,
            offset=request.offset,
            order_by=request.order_by,
            order_desc=request.order_desc,
        )

        return TableDataResponse(
            success=True,
            data=result["data"],
            pagination={
                "limit": request.limit,
                "offset": request.offset,
                "total": result["total"],
                "has_next": result["has_more"],
            },
            message=f"成功获取 {len(result['data'])} 条期货数据",
            timestamp=get_beijing_time_now(),
            table_name="fut_main_contract_kline_15min",
        )

    except Exception as e:
        logger.error(f"获取期货数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取期货数据失败: {str(e)}")


@router.get("/benchmark", response_model=Dict[str, Any])
async def run_benchmark(environment: str = Query("test", description="环境名称")):
    """运行性能基准测试"""
    try:
        optimizer = DatabasePerformanceOptimizer(environment=environment)
        results = optimizer.benchmark_queries()

        # 格式化结果
        benchmark_data = []
        for result in results:
            benchmark_data.append(
                {
                    "query": (
                        result.query[:100] + "..."
                        if len(result.query) > 100
                        else result.query
                    ),
                    "execution_time": result.execution_time,
                    "rows_returned": result.rows_returned,
                    "index_used": result.index_used,
                    "timestamp": result.timestamp.isoformat(),
                }
            )

        return {
            "success": True,
            "data": benchmark_data,
            "average_time": sum(r.execution_time for r in results) / len(results),
            "total_queries": len(results),
            "timestamp": get_beijing_time_now().isoformat(),
        }

    except Exception as e:
        logger.error(f"性能基准测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"性能基准测试失败: {str(e)}")
