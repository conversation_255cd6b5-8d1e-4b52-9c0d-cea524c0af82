"""
AQUA统一数据导入路由器 (v4.0架构)

整合所有数据相关API，提供一致的接口体验
- 数据库浏览
- 多源数据导入 (CSV/MySQL/FromC2C)
- 实时进度监控
- 任务管理
"""

import logging
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import (
    APIRouter,
    HTTPException,
    Query,
    BackgroundTasks,
    WebSocket,
    WebSocketDisconnect,
)
from pydantic import BaseModel, Field

# 导入所有必要的模块
from ...database.duckdb_init_check import DuckDBInitChecker
from ...database.connection_manager import DuckDBConnectionManager
from ...data_import.fromc2c_importer_logic import (
    check_environment_logic,
    check_data_source_logic,
    execute_import_logic,
)
from ...data_import.mysql_importer_logic import (
    test_mysql_connection_logic,
    get_mysql_tables_logic,
    execute_mysql_import_logic,
)
from ...data_import.task_control_manager import TaskControlManager
from ...data_import.websocket_manager import get_websocket_manager
from ...utils.time_utils import get_beijing_time_now
from ...utils.config_loader import ConfigLoader
from ...utils.mysql_config_manager import get_mysql_config_manager, MySQLConfig
from ...utils.exceptions import (
    handle_exception,
)
from ..models.data_models import (
    TableListResponse,
    TableDataResponse,
    ImportResponse,
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建统一路由器
router = APIRouter(prefix="/api/data", tags=["统一数据服务"])

# 全局任务管理器和WebSocket管理器
task_manager = TaskControlManager()
websocket_manager = get_websocket_manager()
mysql_config_manager = get_mysql_config_manager()

# ==================== 请求/响应模型 ====================


class UnifiedImportRequest(BaseModel):
    """统一导入请求模型"""

    import_type: str = Field(..., description="导入类型: csv, mysql, fromc2c")
    environment: str = Field(default="dev", description="目标环境")

    # CSV特定参数
    encoding: Optional[str] = Field(default="utf-8", description="文件编码")
    delimiter: Optional[str] = Field(default=",", description="分隔符")

    # MySQL特定参数
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    database: Optional[str] = None
    tables: Optional[List[str]] = None

    # FromC2C特定参数
    source_path: Optional[str] = None
    max_files: Optional[int] = None
    table_filter: Optional[str] = None

    # 通用参数
    batch_size: Optional[int] = Field(default=1000, description="批处理大小")
    enable_validation: bool = Field(default=True, description="启用数据验证")
    generate_report: bool = Field(default=True, description="生成导入报告")


class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""

    task_id: str
    status: str  # pending, running, completed, failed, cancelled
    progress: float
    message: str
    created_at: datetime
    updated_at: datetime
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class PreCheckResponse(BaseModel):
    """预检查响应模型"""

    success: bool
    checks: List[Dict[str, Any]]
    recommendations: List[str] = []


class MySQLConfigResponse(BaseModel):
    """MySQL配置响应模型"""

    success: bool
    environment: str
    config: Optional[Dict[str, Any]] = None
    message: str = ""


class MySQLConfigUpdateRequest(BaseModel):
    """MySQL配置更新请求模型"""

    host: str
    port: int = Field(ge=1, le=65535)
    username: str
    password: str
    database: str
    charset: str = "utf8mb4"
    connect_timeout: int = Field(default=30, ge=1, le=300)
    read_timeout: int = Field(default=30, ge=1, le=300)


# ==================== 健康检查端点 ====================


@router.get("/health", response_model=Dict[str, Any])
async def health_check():
    """
    系统健康检查
    检查数据库连接、配置加载等关键组件状态
    """
    try:
        # 检查数据库连接
        checker = DuckDBInitChecker()
        db_status = checker.check_database_health()

        # 检查配置加载
        config_loader = ConfigLoader()
        config_status = config_loader.validate_config()

        # 检查任务管理器状态
        active_tasks = len(task_manager.get_active_tasks())

        return {
            "status": "healthy",
            "timestamp": get_beijing_time_now().isoformat(),
            "database": db_status,
            "configuration": config_status,
            "active_tasks": active_tasks,
            "websocket_connections": len(websocket_manager.get_active_connections()),
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


# ==================== 数据库浏览端点 ====================


@router.get("/tables", response_model=TableListResponse)
async def get_tables(
    environment: str = Query(default="dev", description="环境名称"),
    schema_pattern: Optional[str] = Query(default=None, description="模式筛选"),
    include_stats: bool = Query(default=False, description="包含统计信息"),
):
    """
    获取数据库表列表
    支持按环境筛选和统计信息
    """
    try:
        checker = DuckDBInitChecker()
        tables = checker.get_tables_with_stats(
            environment=environment,
            schema_pattern=schema_pattern,
            include_stats=include_stats,
        )

        return TableListResponse(
            success=True, data=tables, total=len(tables), environment=environment
        )
    except Exception as e:
        logger.error(f"Failed to get tables: {e}")
        return handle_exception(e)


@router.get("/tables/{table_name}", response_model=TableDataResponse)
async def get_table_data(
    table_name: str,
    environment: str = Query(default="dev", description="环境名称"),
    limit: int = Query(default=100, ge=1, le=10000, description="返回记录数"),
    offset: int = Query(default=0, ge=0, description="偏移量"),
    columns: Optional[str] = Query(default=None, description="指定列，逗号分隔"),
    where_clause: Optional[str] = Query(default=None, description="WHERE条件"),
):
    """
    获取表数据
    支持分页、列筛选和条件查询
    """
    try:
        connection_manager = DuckDBConnectionManager()

        # 构建查询
        select_columns = columns if columns else "*"
        query = f"SELECT {select_columns} FROM {table_name}"

        if where_clause:
            query += f" WHERE {where_clause}"

        query += f" LIMIT {limit} OFFSET {offset}"

        # 执行查询
        with connection_manager.get_connection(environment) as conn:
            result = conn.execute(query).fetchall()
            columns_info = [desc[0] for desc in conn.description]

        # 转换为字典列表
        data = [dict(zip(columns_info, row)) for row in result]

        return TableDataResponse(
            success=True,
            data=data,
            columns=columns_info,
            total=len(data),
            table_name=table_name,
        )
    except Exception as e:
        logger.error(f"Failed to get table data: {e}")
        return handle_exception(e)


# ==================== MySQL配置管理端点 ====================


@router.get("/config/mysql/{environment}", response_model=MySQLConfigResponse)
async def get_mysql_config(environment: str):
    """
    获取指定环境的MySQL配置
    解决前端重复输入配置的问题
    """
    try:
        config = mysql_config_manager.get_environment_mysql_config(environment)

        if config:
            # 为安全起见，不返回密码字段
            config_dict = config.to_dict()
            config_dict["password"] = "***" if config_dict["password"] else ""

            return MySQLConfigResponse(
                success=True,
                environment=environment,
                config=config_dict,
                message=f"成功获取环境 {environment} 的MySQL配置",
            )
        else:
            return MySQLConfigResponse(
                success=False,
                environment=environment,
                message=f"未找到环境 {environment} 的MySQL配置",
            )

    except Exception as e:
        logger.error(f"获取MySQL配置失败: {e}")
        return MySQLConfigResponse(
            success=False,
            environment=environment,
            message=f"获取MySQL配置失败: {str(e)}",
        )


@router.get("/config/mysql", response_model=Dict[str, Any])
async def get_all_mysql_configs():
    """获取所有环境的MySQL配置列表"""
    try:
        configs = mysql_config_manager.get_all_environments_mysql_config()

        # 隐藏密码信息
        safe_configs = {}
        for env, config in configs.items():
            config_dict = config.to_dict()
            config_dict["password"] = "***" if config_dict["password"] else ""
            safe_configs[env] = config_dict

        return {
            "success": True,
            "configs": safe_configs,
            "environments": list(safe_configs.keys()),
            "total": len(safe_configs),
        }

    except Exception as e:
        logger.error(f"获取所有MySQL配置失败: {e}")
        return {"success": False, "error": str(e)}


@router.post("/config/mysql/{environment}/test", response_model=Dict[str, Any])
async def test_mysql_config(
    environment: str, config_data: Optional[MySQLConfigUpdateRequest] = None
):
    """
    测试MySQL配置连接
    支持测试环境配置或临时配置
    """
    try:
        if config_data:
            # 测试临时配置
            temp_config = MySQLConfig.from_dict(config_data.dict())
            result = await mysql_config_manager.test_mysql_connection(
                temp_config, environment
            )
        else:
            # 测试环境配置
            result = await mysql_config_manager.test_environment_mysql_connection(
                environment
            )

        return result

    except Exception as e:
        logger.error(f"测试MySQL连接失败: {e}")
        return {"success": False, "error": str(e), "message": "MySQL连接测试异常"}


@router.put("/config/mysql/{environment}", response_model=MySQLConfigResponse)
async def update_mysql_config(environment: str, config_data: MySQLConfigUpdateRequest):
    """
    更新指定环境的MySQL配置
    需要管理员权限
    """
    try:
        # 验证配置数据
        validation_errors = mysql_config_manager.validate_mysql_config(
            config_data.dict()
        )
        if validation_errors:
            return MySQLConfigResponse(
                success=False,
                environment=environment,
                message=f"配置验证失败: {'; '.join(validation_errors)}",
            )

        # 更新配置
        success = mysql_config_manager.update_environment_mysql_config(
            environment, config_data.dict()
        )

        if success:
            return MySQLConfigResponse(
                success=True,
                environment=environment,
                message=f"成功更新环境 {environment} 的MySQL配置",
            )
        else:
            return MySQLConfigResponse(
                success=False, environment=environment, message="更新MySQL配置失败"
            )

    except Exception as e:
        logger.error(f"更新MySQL配置失败: {e}")
        return MySQLConfigResponse(
            success=False,
            environment=environment,
            message=f"更新MySQL配置失败: {str(e)}",
        )


@router.get("/config/mysql/presets", response_model=Dict[str, Any])
async def get_mysql_config_presets():
    """获取MySQL配置预设模板"""
    try:
        presets = mysql_config_manager.get_mysql_config_presets()
        template = mysql_config_manager.get_mysql_config_template()

        return {
            "success": True,
            "presets": presets,
            "template": template,
            "message": "成功获取MySQL配置预设",
        }

    except Exception as e:
        logger.error(f"获取MySQL配置预设失败: {e}")
        return {"success": False, "error": str(e)}


# ==================== 统一导入预检查 ====================


@router.post("/import/pre-check", response_model=PreCheckResponse)
async def pre_check_import(request: UnifiedImportRequest):
    """
    统一导入预检查
    根据导入类型执行相应的预检查逻辑
    """
    try:
        checks = []
        recommendations = []

        if request.import_type == "fromc2c":
            # FromC2C预检查
            env_check = check_environment_logic(request.environment)
            checks.append(
                {
                    "type": "environment",
                    "success": env_check["success"],
                    "message": env_check["message"],
                }
            )

            if env_check["success"]:
                data_check = check_data_source_logic()  # 该函数不需要参数
                checks.append(
                    {
                        "type": "fromc2c_files",
                        "success": data_check["success"],
                        "message": data_check["message"],
                    }
                )

        elif request.import_type == "mysql":
            # MySQL预检查
            mysql_config = {
                "host": request.host,
                "port": request.port,
                "username": request.username,
                "password": request.password,
                "database": request.database,
                "charset": getattr(request, "charset", "utf8mb4"),
            }
            mysql_check = test_mysql_connection_logic(mysql_config)
            checks.append(
                {
                    "type": "mysql_connection",
                    "success": mysql_check["success"],
                    "message": mysql_check["message"],
                }
            )

            if mysql_check["success"] and request.tables:
                # 检查表是否存在
                tables_check = get_mysql_tables_logic(
                    environment=request.environment, table_filter=request.tables
                )
                checks.append(tables_check)

        elif request.import_type == "csv":
            # CSV预检查
            csv_check = {
                "type": "csv_validation",
                "success": True,
                "message": "CSV导入预检查通过",
                "details": {
                    "encoding": request.encoding,
                    "delimiter": request.delimiter,
                    "batch_size": request.batch_size,
                },
            }
            checks.append(csv_check)
            recommendations.append("建议使用UTF-8编码确保最佳兼容性")

        else:
            raise ValidationError(f"不支持的导入类型: {request.import_type}")

        success = all(check["success"] for check in checks)

        return PreCheckResponse(
            success=success, checks=checks, recommendations=recommendations
        )

    except Exception as e:
        logger.error(f"Pre-check failed: {e}")
        return handle_exception(e)


# ==================== 统一导入执行 ====================


@router.post("/import/execute", response_model=ImportResponse)
async def execute_import(
    background_tasks: BackgroundTasks, request: UnifiedImportRequest
):
    """
    统一导入执行
    支持多种数据源的异步导入
    """
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 创建任务
        task_info = task_manager.create_task(
            task_id=task_id,
            task_type=request.import_type,
            environment=request.environment,
        )

        # 根据导入类型执行相应逻辑
        if request.import_type == "fromc2c":
            background_tasks.add_task(execute_fromc2c_import_task, task_id, request)
        elif request.import_type == "mysql":
            background_tasks.add_task(execute_mysql_import_task, task_id, request)
        elif request.import_type == "csv":
            background_tasks.add_task(execute_csv_import_task, task_id, request)
        else:
            task_manager.fail_task(task_id, f"不支持的导入类型: {request.import_type}")
            raise HTTPException(
                status_code=400, detail=f"不支持的导入类型: {request.import_type}"
            )

        return ImportResponse(
            success=True,
            message="导入任务已启动",
            task_id=task_id,
            data={
                "import_type": request.import_type,
                "environment": request.environment,
                "created_at": get_beijing_time_now().isoformat(),
            },
        )

    except Exception as e:
        logger.error(f"Import execution failed: {e}")
        return handle_exception(e)


# ==================== 任务状态查询 ====================


@router.get("/import/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        task_info = task_manager.get_task(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务未找到: {task_id}")

        return TaskStatusResponse(
            task_id=task_info.task_id,
            status=task_info.status.value,
            progress=task_info.progress,
            message=task_info.message,
            created_at=task_info.created_at,
            updated_at=task_info.updated_at,
            result=task_info.result,
            error=task_info.error,
        )

    except Exception as e:
        logger.error(f"Failed to get task status: {e}")
        return handle_exception(e)


@router.post("/import/tasks/{task_id}/cancel")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        success = task_manager.cancel_task(task_id)
        if not success:
            raise HTTPException(
                status_code=404, detail=f"任务未找到或无法取消: {task_id}"
            )

        # 通过WebSocket广播取消状态
        await websocket_manager.broadcast_to_task(
            task_id,
            {
                "type": "task_cancelled",
                "task_id": task_id,
                "message": "任务已被用户取消",
            },
        )

        return {"success": True, "message": "任务已取消"}

    except Exception as e:
        logger.error(f"Failed to cancel task: {e}")
        return handle_exception(e)


# ==================== WebSocket进度监控 ====================


@router.websocket("/import/progress/{task_id}")
async def websocket_progress(websocket: WebSocket, task_id: str):
    """WebSocket进度监控"""
    await websocket_manager.connect(websocket, task_id)

    try:
        while True:
            # 保持连接，实际进度更新由后台任务推送
            data = await websocket.receive_text()

            # 处理客户端消息（如取消任务请求）
            if data == "cancel":
                await cancel_task(task_id)
                break

    except WebSocketDisconnect:
        await websocket_manager.disconnect(websocket, task_id)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket_manager.disconnect(websocket, task_id)


# ==================== 后台任务执行函数 ====================


async def execute_fromc2c_import_task(task_id: str, request: UnifiedImportRequest):
    """执行FromC2C导入任务"""
    try:
        task_manager.update_task_progress(task_id, 10.0, "开始FromC2C导入...")

        result = execute_import_logic(
            env=request.environment,
            path=request.source_path,
            max_files=request.max_files,
            table_filter=request.table_filter,
        )

        task_manager.complete_task(task_id, result)

        await websocket_manager.broadcast_to_task(
            task_id,
            {
                "type": "task_completed",
                "task_id": task_id,
                "progress": 100.0,
                "result": result,
            },
        )

    except Exception as e:
        logger.error(f"FromC2C import task failed: {e}")
        task_manager.fail_task(task_id, str(e))

        await websocket_manager.broadcast_to_task(
            task_id, {"type": "task_failed", "task_id": task_id, "error": str(e)}
        )


async def execute_mysql_import_task(task_id: str, request: UnifiedImportRequest):
    """执行MySQL导入任务"""
    try:
        task_manager.update_task_progress(task_id, 10.0, "开始MySQL导入...")

        result = execute_mysql_import_logic(
            host=request.host,
            port=request.port,
            username=request.username,
            password=request.password,
            database=request.database,
            tables=request.tables,
            environment=request.environment,
            batch_size=request.batch_size,
        )

        task_manager.complete_task(task_id, result)

        await websocket_manager.broadcast_to_task(
            task_id,
            {
                "type": "task_completed",
                "task_id": task_id,
                "progress": 100.0,
                "result": result,
            },
        )

    except Exception as e:
        logger.error(f"MySQL import task failed: {e}")
        task_manager.fail_task(task_id, str(e))

        await websocket_manager.broadcast_to_task(
            task_id, {"type": "task_failed", "task_id": task_id, "error": str(e)}
        )


async def execute_csv_import_task(task_id: str, request: UnifiedImportRequest):
    """执行CSV导入任务"""
    try:
        task_manager.update_task_progress(task_id, 10.0, "开始CSV导入...")

        # CSV导入逻辑需要文件上传，这里是框架
        # 具体实现需要配合文件上传端点

        result = {
            "type": "csv_import",
            "message": "CSV导入功能开发中",
            "encoding": request.encoding,
            "delimiter": request.delimiter,
        }

        task_manager.complete_task(task_id, result)

        await websocket_manager.broadcast_to_task(
            task_id,
            {
                "type": "task_completed",
                "task_id": task_id,
                "progress": 100.0,
                "result": result,
            },
        )

    except Exception as e:
        logger.error(f"CSV import task failed: {e}")
        task_manager.fail_task(task_id, str(e))

        await websocket_manager.broadcast_to_task(
            task_id, {"type": "task_failed", "task_id": task_id, "error": str(e)}
        )


# ==================== 工具函数 ====================


def validate_import_request(request: UnifiedImportRequest) -> List[str]:
    """验证导入请求参数"""
    errors = []

    if request.import_type == "mysql":
        if not all([request.host, request.username, request.database]):
            errors.append("MySQL导入需要提供host, username, database参数")

    elif request.import_type == "fromc2c":
        if not request.environment:
            errors.append("FromC2C导入需要指定环境")

    return errors


# 导出路由器
__all__ = ["router"]
