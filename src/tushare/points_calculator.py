#!/usr/bin/env python3
"""
TUSHARE积分消耗计算引擎 - Feature 2.2.1

提供精确的TUSHARE Pro API积分消耗计算和预估功能。
基于官方API文档和实际使用经验，建立完整的积分映射表。

核心功能：
- API积分消耗映射和查询
- 批量请求积分预估
- 实时积分预算检查
- 使用统计和效率监控

设计原则：
- 数据驱动：基于真实API积分消耗
- 保守估计：未知API使用较高默认值
- 实时监控：持续跟踪积分使用情况
- 预算保护：防止积分意外耗尽
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from collections import defaultdict

from ..utils.time_utils import get_beijing_time_now


class ApiPointsMap:
    """
    TUSHARE API积分映射表

    基于TUSHARE Pro官方文档建立的API积分消耗映射表。
    定期更新以确保准确性。
    """

    @staticmethod
    def get_default_map() -> Dict[str, int]:
        """
        获取默认API积分映射表

        Returns:
            Dict[str, int]: API名称到积分消耗的映射
        """
        return {
            # 基础数据接口
            "stock_basic": 1,  # 股票基础信息
            "trade_cal": 1,  # 交易日历
            "namechange": 1,  # 股票曾用名
            "hs_const": 1,  # 沪深港通成分股
            "stk_limit": 1,  # 涨跌停价格
            # 股票行情数据
            "daily": 1,  # 股票日线行情
            "weekly": 5,  # 股票周线行情
            "monthly": 5,  # 股票月线行情
            "adj_factor": 1,  # 复权因子
            "suspend_d": 1,  # 停复牌信息
            "daily_basic": 1,  # 每日基本面
            # 期货行情数据
            "fut_basic": 1,  # 期货基础信息
            "fut_daily": 5,  # 期货日线行情
            "fut_weekly": 10,  # 期货周线行情
            "fut_monthly": 10,  # 期货月线行情
            "fut_settle": 2,  # 期货结算参数
            "fut_holding": 2,  # 期货持仓排名
            # 指数数据
            "index_basic": 1,  # 指数基础信息
            "index_daily": 1,  # 指数日线行情
            "index_weekly": 5,  # 指数周线行情
            "index_monthly": 5,  # 指数月线行情
            "index_weight": 10,  # 指数成分和权重
            # 财务数据（积分消耗较高）
            "income": 15,  # 利润表
            "balancesheet": 15,  # 资产负债表
            "cashflow": 15,  # 现金流量表
            "forecast": 10,  # 业绩预告
            "express": 10,  # 业绩快报
            "dividend": 5,  # 分红送股
            "fina_indicator": 20,  # 财务指标
            "fina_audit": 5,  # 审计意见
            "fina_mainbz": 5,  # 主营业务构成
            # 市场参考数据
            "moneyflow": 10,  # 资金流向
            "stk_holdernumber": 5,  # 股东人数
            "stk_holdertrade": 10,  # 股东增减持
            "top10_holders": 10,  # 前十大股东
            "top10_floatholders": 10,  # 前十大流通股东
            # 特殊数据（高积分消耗）
            "concept": 5,  # 概念股分类
            "concept_detail": 10,  # 概念股明细
            "limit_list_d": 20,  # 每日涨跌停统计
            "stk_factor": 30,  # 技术因子
            "ccass_hold": 20,  # 中央结算系统持股统计
            "ccass_hold_detail": 30,  # 中央结算系统持股明细
        }

    @staticmethod
    def get_api_points(api_name: str) -> int:
        """
        获取指定API的积分消耗

        Args:
            api_name: API接口名称

        Returns:
            int: 积分消耗，未知API返回保守估计值
        """
        api_map = ApiPointsMap.get_default_map()
        return api_map.get(api_name, 10)  # 未知API使用保守估计10积分


@dataclass
class PointsUsageStats:
    """
    积分使用统计类

    跟踪积分使用情况，计算使用效率，提供预算预警。
    """

    def __init__(self, total_budget: int = 2100):
        """
        初始化使用统计

        Args:
            total_budget: 总积分预算，默认2100
        """
        self.total_budget = total_budget
        self.available_points = total_budget
        self.total_consumed = 0
        self.call_count = 0

        # API使用统计
        self.api_usage: Dict[str, Dict[str, int]] = defaultdict(
            lambda: {"calls": 0, "points": 0}
        )

        # 每日使用记录
        self.daily_usage: Dict[str, Dict[str, int]] = defaultdict(
            lambda: {"calls": 0, "points": 0}
        )

        # 时间统计
        self.first_call_time: Optional[datetime] = None
        self.last_call_time: Optional[datetime] = None

    def update_available_points(self, points: int) -> None:
        """更新可用积分数（重置统计）"""
        self.available_points = points
        self.total_consumed = 0  # 重置消耗统计
        self.call_count = 0  # 重置调用计数

    def record_consumption(self, api_name: str, points: int) -> None:
        """
        记录积分消耗

        Args:
            api_name: API名称
            points: 消耗的积分数
        """
        # 更新总体统计
        self.available_points -= points
        self.total_consumed += points
        self.call_count += 1

        # 更新API统计
        self.api_usage[api_name]["calls"] += 1
        self.api_usage[api_name]["points"] += points

        # 更新每日统计
        today = get_beijing_time_now().strftime("%Y-%m-%d")
        self.daily_usage[today]["calls"] += 1
        self.daily_usage[today]["points"] += points

        # 更新时间记录
        current_time = get_beijing_time_now()
        if self.first_call_time is None:
            self.first_call_time = current_time
        self.last_call_time = current_time

    def get_efficiency_rate(self) -> float:
        """
        计算积分使用效率

        Returns:
            float: 效率率（成功调用次数 / 消耗积分数）
        """
        if self.total_consumed == 0:
            return 0.0
        return self.call_count / self.total_consumed

    def is_budget_exhausted(self) -> bool:
        """检查预算是否耗尽"""
        return self.available_points <= 0

    def get_remaining_budget_percentage(self) -> float:
        """获取剩余预算百分比"""
        if self.total_budget == 0:
            return 0.0
        return (self.available_points / self.total_budget) * 100.0

    def get_daily_usage_today(self) -> Dict[str, int]:
        """获取今日使用统计"""
        today = get_beijing_time_now().strftime("%Y-%m-%d")
        return dict(self.daily_usage[today])

    def is_daily_limit_exceeded(self, daily_limit: int) -> bool:
        """检查是否超过每日限制"""
        today_usage = self.get_daily_usage_today()
        return today_usage.get("points", 0) >= daily_limit


class PointsCalculator:
    """
    TUSHARE积分消耗计算引擎 - Feature 2.2.1

    提供精确的积分消耗计算、预估和预算管理功能。
    支持批量请求积分预估和实时使用监控。
    """

    def __init__(self, total_budget: int = 2100):
        """
        初始化积分计算器

        Args:
            total_budget: 总积分预算
        """
        self.logger = logging.getLogger(__name__)
        self.api_points_map = ApiPointsMap.get_default_map()
        self.usage_stats = PointsUsageStats(total_budget)

        self.logger.info(f"积分计算器初始化完成，总预算: {total_budget}积分")

    def calculate_api_call_points(self, api_name: str) -> int:
        """
        计算单次API调用的积分消耗

        Args:
            api_name: API接口名称

        Returns:
            int: 积分消耗数量
        """
        points = ApiPointsMap.get_api_points(api_name)
        self.logger.debug(f"API {api_name} 消耗积分: {points}")
        return points

    def estimate_batch_points(self, api_name: str, batch_count: int) -> int:
        """
        预估批量请求的积分消耗

        Args:
            api_name: API接口名称
            batch_count: 批量请求次数

        Returns:
            int: 预估总积分消耗
        """
        single_call_points = self.calculate_api_call_points(api_name)
        total_points = single_call_points * batch_count

        self.logger.debug(
            f"批量请求预估 - API: {api_name}, 次数: {batch_count}, 总积分: {total_points}"
        )
        return total_points

    def can_afford_api_call(self, api_name: str, batch_count: int = 1) -> bool:
        """
        检查是否有足够积分进行API调用

        Args:
            api_name: API接口名称
            batch_count: 调用次数，默认1

        Returns:
            bool: 是否有足够积分
        """
        required_points = self.estimate_batch_points(api_name, batch_count)
        can_afford = self.usage_stats.available_points >= required_points

        if not can_afford:
            self.logger.warning(
                f"积分不足 - 需要: {required_points}, 可用: {self.usage_stats.available_points}"
            )

        return can_afford

    def get_required_points(self, api_name: str, batch_count: int = 1) -> int:
        """
        获取API调用需要的积分数

        Args:
            api_name: API接口名称
            batch_count: 调用次数，默认1

        Returns:
            int: 需要的积分数
        """
        return self.estimate_batch_points(api_name, batch_count)

    def record_api_call(self, api_name: str, batch_count: int = 1) -> int:
        """
        记录API调用并扣除积分

        Args:
            api_name: API接口名称
            batch_count: 调用次数，默认1

        Returns:
            int: 实际消耗的积分数
        """
        consumed_points = self.estimate_batch_points(api_name, batch_count)

        # 记录消耗
        for _ in range(batch_count):
            single_points = self.calculate_api_call_points(api_name)
            self.usage_stats.record_consumption(api_name, single_points)

        self.logger.info(
            f"API调用记录 - {api_name}×{batch_count}, 消耗积分: {consumed_points}"
        )
        return consumed_points

    def get_usage_summary(self) -> Dict[str, Any]:
        """
        获取积分使用摘要

        Returns:
            Dict[str, Any]: 使用摘要信息
        """
        summary = {
            "total_budget": self.usage_stats.total_budget,
            "available_points": self.usage_stats.available_points,
            "total_consumed": self.usage_stats.total_consumed,
            "call_count": self.usage_stats.call_count,
            "efficiency_rate": self.usage_stats.get_efficiency_rate(),
            "remaining_percentage": self.usage_stats.get_remaining_budget_percentage(),
            "budget_exhausted": self.usage_stats.is_budget_exhausted(),
            "api_breakdown": dict(self.usage_stats.api_usage),
            "today_usage": self.usage_stats.get_daily_usage_today(),
            "first_call_time": self.usage_stats.first_call_time,
            "last_call_time": self.usage_stats.last_call_time,
        }

        return summary

    def get_most_expensive_apis(self, top_n: int = 5) -> List[Dict[str, Any]]:
        """
        获取积分消耗最高的API列表

        Args:
            top_n: 返回前N个API

        Returns:
            List[Dict[str, Any]]: API消耗排行榜
        """
        api_costs = []
        for api_name, stats in self.usage_stats.api_usage.items():
            api_costs.append(
                {
                    "api_name": api_name,
                    "total_points": stats["points"],
                    "call_count": stats["calls"],
                    "avg_points_per_call": (
                        stats["points"] / stats["calls"] if stats["calls"] > 0 else 0
                    ),
                }
            )

        # 按总积分消耗排序
        api_costs.sort(key=lambda x: x["total_points"], reverse=True)
        return api_costs[:top_n]

    def check_budget_health(self) -> Dict[str, Any]:
        """
        检查积分预算健康状况

        Returns:
            Dict[str, Any]: 预算健康报告
        """
        remaining_percentage = self.usage_stats.get_remaining_budget_percentage()
        efficiency = self.usage_stats.get_efficiency_rate()

        # 健康等级评估
        if remaining_percentage > 80:
            health_level = "excellent"
        elif remaining_percentage > 60:
            health_level = "good"
        elif remaining_percentage > 40:
            health_level = "fair"
        elif remaining_percentage > 20:
            health_level = "poor"
        else:
            health_level = "critical"

        # 效率评估
        if efficiency >= 0.95:
            efficiency_level = "excellent"
        elif efficiency >= 0.8:
            efficiency_level = "good"
        elif efficiency >= 0.6:
            efficiency_level = "fair"
        else:
            efficiency_level = "poor"

        return {
            "health_level": health_level,
            "efficiency_level": efficiency_level,
            "remaining_percentage": remaining_percentage,
            "efficiency_rate": efficiency,
            "recommendations": self._get_budget_recommendations(
                health_level, efficiency_level
            ),
        }

    def _get_budget_recommendations(
        self, health_level: str, efficiency_level: str
    ) -> List[str]:
        """生成预算建议"""
        recommendations = []

        if health_level in ["poor", "critical"]:
            recommendations.append("⚠️ 积分预算紧张，建议减少非必要API调用")
            recommendations.append("💡 考虑使用批量API减少调用次数")

        if efficiency_level == "poor":
            recommendations.append("📊 积分使用效率偏低，建议优化API调用策略")
            recommendations.append("🔍 检查是否有重复或无效的API调用")

        if health_level == "excellent" and efficiency_level == "excellent":
            recommendations.append("✅ 积分使用状况良好，继续保持")

        return recommendations
