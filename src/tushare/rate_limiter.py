#!/usr/bin/env python3
"""
TUSHARE频率限制控制器 - Feature 2.2.2

实现严格的API调用频率控制，防止超出TUSHARE Pro的限制。
采用滑动窗口算法和并发控制，确保合规使用。

核心功能：
- 滑动窗口频率限制 (200次/分钟)
- 并发请求控制 (≤2个并发)
- 请求队列管理和等待机制
- 实时监控和统计

设计原则：
- 严格合规：绝不超出TUSHARE限制
- 高效利用：在限制内最大化吞吐量
- 平滑控制：避免突发请求导致限制
- 监控透明：提供详细的使用统计
"""

import time
import asyncio
import logging
import threading
import uuid
from typing import Dict, List, Optional, Any
from collections import deque

from ..utils.time_utils import get_beijing_time_now


class RequestWindow:
    """
    滑动窗口请求跟踪器

    使用滑动窗口算法跟踪指定时间窗口内的API请求次数。
    自动清理过期请求，保持窗口大小。
    """

    def __init__(self, window_size_seconds: int = 60):
        """
        初始化请求窗口

        Args:
            window_size_seconds: 窗口大小（秒），默认60秒
        """
        self.window_size_seconds = window_size_seconds
        self.requests: deque = deque()  # 存储请求时间戳
        self._lock = threading.Lock()

    def add_request(self) -> None:
        """添加新请求到窗口"""
        with self._lock:
            current_time = time.time()
            self.requests.append(current_time)
            self.cleanup_old_requests()

    def cleanup_old_requests(self) -> None:
        """清理超出窗口的旧请求"""
        current_time = time.time()
        window_start = current_time - self.window_size_seconds

        # 移除窗口外的旧请求
        while self.requests and self.requests[0] < window_start:
            self.requests.popleft()

    def get_request_count(self) -> int:
        """获取当前窗口内的请求数量"""
        with self._lock:
            self.cleanup_old_requests()
            return len(self.requests)

    def get_oldest_request_time(self) -> Optional[float]:
        """获取窗口内最旧请求的时间"""
        with self._lock:
            self.cleanup_old_requests()
            return self.requests[0] if self.requests else None


class ConcurrencyController:
    """
    并发请求控制器

    限制同时进行的API请求数量，防止并发过多导致限制。
    使用token机制管理并发槽位。
    """

    def __init__(self, max_concurrent: int = 2):
        """
        初始化并发控制器

        Args:
            max_concurrent: 最大并发请求数，默认2
        """
        self.max_concurrent = max_concurrent
        self.current_concurrent = 0
        self.active_tokens: Dict[str, float] = {}  # token -> 开始时间
        self._lock = threading.Lock()

    def acquire_slot(self) -> Optional[str]:
        """
        获取并发槽位

        Returns:
            Optional[str]: 成功返回token，失败返回None
        """
        with self._lock:
            if self.current_concurrent >= self.max_concurrent:
                return None

            # 生成唯一token
            token = str(uuid.uuid4())
            self.active_tokens[token] = time.time()
            self.current_concurrent += 1

            return token

    def release_slot(self, token: str) -> bool:
        """
        释放并发槽位

        Args:
            token: 槽位token

        Returns:
            bool: 释放是否成功
        """
        with self._lock:
            if token in self.active_tokens:
                del self.active_tokens[token]
                self.current_concurrent -= 1
                return True
            return False

    def get_available_slots(self) -> int:
        """获取可用并发槽位数"""
        with self._lock:
            return self.max_concurrent - self.current_concurrent

    def get_active_durations(self) -> List[float]:
        """获取活跃请求的持续时间"""
        current_time = time.time()
        with self._lock:
            return [
                current_time - start_time for start_time in self.active_tokens.values()
            ]


class RateLimiter:
    """
    TUSHARE频率限制控制器 - Feature 2.2.2

    综合管理API调用频率和并发控制，确保严格遵守TUSHARE限制。
    提供同步和异步接口，支持等待和队列管理。
    """

    def __init__(
        self,
        max_requests_per_minute: int = 200,
        max_concurrent_requests: int = 2,
        window_size_seconds: int = 60,
    ):
        """
        初始化频率限制控制器

        Args:
            max_requests_per_minute: 每分钟最大请求数，默认200
            max_concurrent_requests: 最大并发请求数，默认2
            window_size_seconds: 滑动窗口大小（秒），默认60
        """
        self.max_requests_per_minute = max_requests_per_minute
        self.max_concurrent_requests = max_concurrent_requests
        self.window_size_seconds = window_size_seconds

        # 初始化组件
        self.request_window = RequestWindow(window_size_seconds)
        self.concurrency_controller = ConcurrencyController(max_concurrent_requests)

        # 日志和统计
        self.logger = logging.getLogger(__name__)
        self.total_requests = 0
        self.total_waits = 0
        self.total_wait_time = 0.0

        self.logger.info(
            f"频率限制器初始化: {max_requests_per_minute}次/分钟, "
            f"{max_concurrent_requests}并发, {window_size_seconds}秒窗口"
        )

    def can_make_request(self) -> bool:
        """
        检查是否可以发起新请求

        Returns:
            bool: 是否可以发起请求
        """
        # 检查频率限制
        current_requests = self.request_window.get_request_count()
        if current_requests >= self.max_requests_per_minute:
            return False

        # 检查并发限制（这里只检查，不占用槽位）
        available_slots = self.concurrency_controller.get_available_slots()
        return available_slots > 0

    def record_request(self) -> None:
        """记录新的API请求"""
        self.request_window.add_request()
        self.total_requests += 1
        self.logger.debug(f"记录API请求，总计: {self.total_requests}")

    def acquire_concurrent_slot(self) -> Optional[str]:
        """
        获取并发槽位

        Returns:
            Optional[str]: 成功返回token，失败返回None
        """
        return self.concurrency_controller.acquire_slot()

    def release_concurrent_slot(self, token: str) -> bool:
        """
        释放并发槽位

        Args:
            token: 槽位token

        Returns:
            bool: 释放是否成功
        """
        return self.concurrency_controller.release_slot(token)

    def get_wait_time(self) -> float:
        """
        计算需要等待的时间（秒）

        Returns:
            float: 需要等待的秒数，0表示可以立即执行
        """
        if self.can_make_request():
            return 0.0

        # 找到最旧的请求，计算它何时超出窗口
        oldest_request_time = self.request_window.get_oldest_request_time()
        if oldest_request_time is None:
            return 0.0

        current_time = time.time()
        window_expire_time = oldest_request_time + self.window_size_seconds
        wait_time = max(0.0, window_expire_time - current_time)

        return wait_time

    async def wait_for_available_slot(self, max_wait_seconds: float = 300.0) -> bool:
        """
        异步等待可用槽位

        Args:
            max_wait_seconds: 最大等待时间（秒），默认5分钟

        Returns:
            bool: 是否成功获得槽位
        """
        start_time = time.time()
        wait_count = 0

        while time.time() - start_time < max_wait_seconds:
            if self.can_make_request():
                if wait_count > 0:
                    total_wait = time.time() - start_time
                    self.total_waits += 1
                    self.total_wait_time += total_wait
                    self.logger.info(f"等待完成，耗时: {total_wait:.2f}秒")
                return True

            # 计算建议的等待时间
            wait_time = self.get_wait_time()
            sleep_time = min(wait_time + 0.1, 1.0)  # 最多等待1秒

            if wait_count == 0:
                self.logger.info(f"需要等待 {wait_time:.1f}秒，开始等待...")

            await asyncio.sleep(sleep_time)
            wait_count += 1

        self.logger.warning(f"等待超时，已等待 {max_wait_seconds}秒")
        return False

    def wait_for_available_slot_sync(self, max_wait_seconds: float = 300.0) -> bool:
        """
        同步等待可用槽位

        Args:
            max_wait_seconds: 最大等待时间（秒）

        Returns:
            bool: 是否成功获得槽位
        """
        start_time = time.time()
        wait_count = 0

        while time.time() - start_time < max_wait_seconds:
            if self.can_make_request():
                if wait_count > 0:
                    total_wait = time.time() - start_time
                    self.total_waits += 1
                    self.total_wait_time += total_wait
                    self.logger.info(f"同步等待完成，耗时: {total_wait:.2f}秒")
                return True

            wait_time = self.get_wait_time()
            sleep_time = min(wait_time + 0.1, 1.0)

            if wait_count == 0:
                self.logger.info(f"同步等待 {wait_time:.1f}秒...")

            time.sleep(sleep_time)
            wait_count += 1

        self.logger.warning(f"同步等待超时，已等待 {max_wait_seconds}秒")
        return False

    def get_current_stats(self) -> Dict[str, Any]:
        """
        获取当前频率限制统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        current_requests = self.request_window.get_request_count()
        available_slots = self.concurrency_controller.get_available_slots()

        stats = {
            # 频率限制统计
            "requests_in_window": current_requests,
            "max_requests_per_minute": self.max_requests_per_minute,
            "remaining_requests": max(
                0, self.max_requests_per_minute - current_requests
            ),
            "window_utilization": (current_requests / self.max_requests_per_minute)
            * 100,
            # 并发控制统计
            "concurrent_requests": self.concurrency_controller.current_concurrent,
            "max_concurrent_requests": self.max_concurrent_requests,
            "available_concurrent_slots": available_slots,
            "concurrent_utilization": (
                self.concurrency_controller.current_concurrent
                / self.max_concurrent_requests
            )
            * 100,
            # 总体统计
            "can_make_request": self.can_make_request(),
            "wait_time_seconds": self.get_wait_time(),
            "total_requests": self.total_requests,
            "total_waits": self.total_waits,
            "total_wait_time": self.total_wait_time,
            "avg_wait_time": self.total_wait_time / max(1, self.total_waits),
            # 活跃请求统计
            "active_request_durations": self.concurrency_controller.get_active_durations(),
            "oldest_request_time": self.request_window.get_oldest_request_time(),
            # 时间信息
            "current_time": get_beijing_time_now().isoformat(),
            "window_size_seconds": self.window_size_seconds,
        }

        return stats

    def get_health_status(self) -> Dict[str, Any]:
        """
        获取频率限制器健康状态

        Returns:
            Dict[str, Any]: 健康状态报告
        """
        stats = self.get_current_stats()

        # 健康等级评估
        window_util = stats["window_utilization"]
        concurrent_util = stats["concurrent_utilization"]
        avg_wait = stats["avg_wait_time"]

        if window_util < 50 and concurrent_util < 50 and avg_wait < 1:
            health_level = "excellent"
        elif window_util < 80 and concurrent_util < 80 and avg_wait < 5:
            health_level = "good"
        elif window_util < 95 and concurrent_util < 95 and avg_wait < 15:
            health_level = "fair"
        else:
            health_level = "poor"

        recommendations = []
        if window_util > 90:
            recommendations.append("⚠️ 频率使用率过高，建议减缓请求速度")
        if concurrent_util > 90:
            recommendations.append("⚠️ 并发使用率过高，建议控制并发数量")
        if avg_wait > 10:
            recommendations.append("📈 平均等待时间较长，建议优化请求策略")

        if not recommendations and health_level == "excellent":
            recommendations.append("✅ 频率控制状况良好")

        return {
            "health_level": health_level,
            "window_utilization": window_util,
            "concurrent_utilization": concurrent_util,
            "average_wait_time": avg_wait,
            "recommendations": recommendations,
            "last_check_time": get_beijing_time_now().isoformat(),
        }
