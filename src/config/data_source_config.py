"""
数据源配置 - Real Data Only版本
"""
import os
from typing import Optional
from ..utils.exceptions import AquaException


class DataSourceNotConfiguredError(AquaException):
    """数据源未配置异常"""
    pass


class DataSourceUnavailableError(AquaException):
    """数据源不可用异常"""
    pass


class DataCollectionError(AquaException):
    """数据采集错误"""
    pass


class UnsupportedDataSourceError(AquaException):
    """不支持的数据源异常"""
    pass


class DataSourceConfig:
    """统一的数据源配置 - Real Data Only版本（支持多种真实数据源）"""
    
    @property
    def tushare_token(self) -> Optional[str]:
        """获取TUSHARE Token"""
        return os.getenv('TUSHARE_TOKEN')
    
    @property
    def csv_data_path(self) -> str:
        """获取CSV数据路径"""
        return os.getenv('CSV_DATA_PATH', 'data/csv/')
    
    @property
    def mysql_host(self) -> Optional[str]:
        """获取MySQL主机地址"""
        return os.getenv('MYSQL_HOST')
    
    @property
    def mysql_port(self) -> int:
        """获取MySQL端口"""
        return int(os.getenv('MYSQL_PORT', '3306'))
    
    @property
    def mysql_user(self) -> Optional[str]:
        """获取MySQL用户名"""
        return os.getenv('MYSQL_USER')
    
    @property
    def mysql_password(self) -> Optional[str]:
        """获取MySQL密码"""
        return os.getenv('MYSQL_PASSWORD')
    
    @property
    def mysql_database(self) -> Optional[str]:
        """获取MySQL数据库名"""
        return os.getenv('MYSQL_DATABASE')
    
    @property
    def is_configured(self) -> bool:
        """检查TUSHARE数据源是否已配置"""
        return self.tushare_token is not None and len(self.tushare_token.strip()) > 0
    
    def is_source_configured(self, source: str) -> bool:
        """检查特定数据源是否已配置"""
        if source == 'tushare':
            return self.is_configured
        elif source == 'csv':
            # CSV数据源默认配置为可用（使用默认路径）
            return True
        elif source == 'mysql':
            # MySQL需要配置主机、用户名和密码
            return (self.mysql_host and self.mysql_user and 
                   self.mysql_password and self.mysql_database)
        return False
    
    def validate(self, source: str = 'tushare') -> None:
        """验证特定数据源配置"""
        if source == 'tushare':
            if not self.is_configured:
                raise DataSourceNotConfiguredError(
                    "未配置TUSHARE_TOKEN环境变量。\n\n"
                    "AQUA数据采集工具使用真实的TUSHARE数据源，请按以下步骤配置：\n"
                    "1. 访问 https://tushare.pro/ 注册账户\n"
                    "2. 在个人中心获取Token\n"
                    "3. 设置环境变量：\n"
                    "   export TUSHARE_TOKEN='your_token_here'  # macOS/Linux\n"
                    "   setx TUSHARE_TOKEN 'your_token_here'    # Windows\n\n"
                    "然后重新运行命令。"
                )
        elif source == 'csv':
            # CSV数据源通常不需要预配置验证
            pass
        elif source == 'mysql':  
            if not self.is_source_configured('mysql'):
                raise DataSourceNotConfiguredError(
                    "MySQL数据源配置不完整。\n\n"
                    "请设置以下环境变量：\n"
                    "export MYSQL_HOST='localhost'        # MySQL服务器地址\n"
                    "export MYSQL_PORT='3306'             # MySQL端口（可选，默认3306）\n" 
                    "export MYSQL_USER='your_username'    # MySQL用户名\n"
                    "export MYSQL_PASSWORD='your_password'# MySQL密码\n"
                    "export MYSQL_DATABASE='your_database'# MySQL数据库名\n\n"
                    "然后重新运行命令。"
                )
    
    def get_config_dict(self, source: str = 'tushare') -> dict:
        """获取特定数据源的配置字典"""
        if source == 'tushare':
            return {
                'name': 'real_tushare_extractor',
                'data_source': 'tushare',
                'target_table': 'stock_daily',
                'environment': 'real_data_only',
                'token': self.tushare_token
            }
        elif source == 'csv':
            return {
                'name': 'real_csv_importer',
                'data_source': 'csv',
                'data_path': self.csv_data_path,
                'environment': 'real_data_only',
                'format': 'fromc2c'
            }
        elif source == 'mysql':
            return {
                'name': 'real_mysql_importer',
                'data_source': 'mysql',
                'host': self.mysql_host,
                'port': self.mysql_port,
                'user': self.mysql_user,
                'password': self.mysql_password,
                'database': self.mysql_database,
                'environment': 'real_data_only'
            }
        else:
            raise UnsupportedDataSourceError(f"不支持的数据源: {source}")