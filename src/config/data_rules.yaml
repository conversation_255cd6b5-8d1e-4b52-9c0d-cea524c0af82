# AQUA DataProcessor规则配置文件
# 版本: 2.0 (Claude Code优化版)
# 基于数据字典标准设计，支持YAML+数据库混合规则管理

# 全局配置
global:
  version: "2.0"
  rule_source: "hybrid"  # hybrid/yaml/database
  data_source_priority: ["TUSHARE", "FROMC2C", "AKSHARE"]  # B1选择
  error_handling_mode: "multi_level"  # C3选择
  processing_mode: "hybrid"  # D3选择
  performance_thresholds:
    small_data_limit: 100000  # <10万条用Polars
    large_data_limit: 1000000  # ≥10万条用DuckDB

# 期货主力合约K线数据规则（基于DATA_DICTIONARY.md）
futures_main_contract_kline:
  description: "期货主力合约K线数据清洗和去重规则"
  table_mapping: "futures_main_contract_kline"
  
  # 主键定义（去重依据）
  primary_key: ["contract_code", "frequency", "trade_datetime"]
  
  # 必需字段
  required_fields:
    - "contract_code"
    - "trade_datetime" 
    - "open_price"
    - "high_price"
    - "low_price"
    - "close_price"
    - "volume"
    - "data_source"
  
  # 字段验证规则
  field_rules:
    contract_code:
      type: "VARCHAR(20)"
      constraints: ["not_empty", "format_check"]
      format_pattern: "^[A-Z]{1,3}\\d{4}$"  # 如RB2501
      
    trade_datetime:
      type: "TIMESTAMP" 
      constraints: ["not_null", "valid_datetime"]
      timezone: "Asia/Shanghai"
      
    open_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"]
      range: [0.01, 999999.99]
      
    high_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"]
      range: [0.01, 999999.99]
      
    low_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"] 
      range: [0.01, 999999.99]
      
    close_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"]
      range: [0.01, 999999.99]
      
    volume:
      type: "BIGINT"
      constraints: ["non_negative"]
      
    amount:
      type: "DECIMAL(22,4)"
      constraints: ["non_negative"]
      nullable: true
      
    open_interest:
      type: "BIGINT" 
      constraints: ["non_negative"]
      nullable: true
      
    data_source:
      type: "VARCHAR(50)"
      constraints: ["not_empty", "valid_source"]
      valid_values: ["TUSHARE", "FROMC2C", "AKSHARE", "WIND", "THS"]
      
    source_quality:
      type: "VARCHAR(20)"
      constraints: ["valid_quality"]
      valid_values: ["HIGH", "NORMAL", "LOW"]
      default: "NORMAL"
  
  # 跨字段验证规则
  cross_field_rules:
    - name: "ohlc_consistency"
      rule: "high_price >= low_price"
      error_level: "ERROR"
      error_action: "isolate"
      
    - name: "ohlc_range_check" 
      rule: "open_price >= low_price AND open_price <= high_price"
      error_level: "ERROR"
      error_action: "isolate"
      
    - name: "close_range_check"
      rule: "close_price >= low_price AND close_price <= high_price"
      error_level: "ERROR" 
      error_action: "isolate"
      
    - name: "volume_amount_consistency"
      rule: "IF amount IS NOT NULL THEN amount >= volume * low_price * 0.8"
      error_level: "WARNING"
      error_action: "repair"
  
  # 数据源优先级规则
  source_priority:
    TUSHARE:
      priority: 1
      quality: "HIGH"
      trust_score: 0.99
      
    FROMC2C:
      priority: 2  
      quality: "NORMAL"
      trust_score: 0.95
      
    AKSHARE:
      priority: 3
      quality: "LOW"
      trust_score: 0.90
  
  # 错误处理策略（C3：多级处理）
  error_handling:
    auto_repair:
      - error_type: "timezone_conversion"
        repair_action: "convert_to_beijing_time"
      - error_type: "decimal_precision"
        repair_action: "round_to_4_decimals"
      - error_type: "missing_amount"
        repair_action: "calculate_from_volume_price"
        
    isolate:
      - error_type: "negative_price"
        isolation_table: "dirty_data_futures_negative_price"
      - error_type: "ohlc_inconsistent"
        isolation_table: "dirty_data_futures_ohlc_error"
      - error_type: "invalid_contract_format"
        isolation_table: "dirty_data_futures_format_error"
        
    discard:
      - error_type: "missing_required_field"
        discard_reason: "insufficient_data"
      - error_type: "system_corruption"
        discard_reason: "data_corruption"

# 股票数据规则
stocks_daily_kline:
  description: "股票日线行情数据清洗和去重规则"
  table_mapping: "stocks_daily_kline"
  
  primary_key: ["stock_code", "trade_date"]
  
  required_fields:
    - "stock_code"
    - "trade_date"
    - "open_price"
    - "high_price" 
    - "low_price"
    - "close_price"
    - "volume"
    - "data_source"
  
  field_rules:
    stock_code:
      type: "VARCHAR(20)"
      constraints: ["not_empty", "format_check"]
      format_pattern: "^\\d{6}\\.(SZ|SH)$"  # 如000001.SZ
      
    trade_date:
      type: "DATE"
      constraints: ["not_null", "valid_date"]
      
    open_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"]
      range: [0.01, 99999.99]
      
    high_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"]
      range: [0.01, 99999.99]
      
    low_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"]
      range: [0.01, 99999.99]
      
    close_price:
      type: "DECIMAL(18,4)"
      constraints: ["non_negative", "reasonable_range"]
      range: [0.01, 99999.99]
      
    volume:
      type: "BIGINT"
      constraints: ["non_negative"]
      
    data_source:
      type: "VARCHAR(50)"
      constraints: ["not_empty", "valid_source"]
      valid_values: ["TUSHARE", "AKSHARE", "WIND", "THS"]
  
  cross_field_rules:
    - name: "ohlc_consistency"
      rule: "high_price >= low_price"
      error_level: "ERROR"
      error_action: "isolate"
      
    - name: "ohlc_range_check"
      rule: "open_price >= low_price AND open_price <= high_price AND close_price >= low_price AND close_price <= high_price"
      error_level: "ERROR"
      error_action: "isolate"
  
  source_priority:
    TUSHARE:
      priority: 1
      quality: "HIGH"
      trust_score: 0.99
      
    AKSHARE:
      priority: 2
      quality: "NORMAL"
      trust_score: 0.90
      
    WIND:
      priority: 3
      quality: "HIGH"
      trust_score: 0.98

# 动态规则扩展配置
dynamic_rules:
  enabled: true
  database_table: "data_processor_dynamic_rules"
  refresh_interval: 300  # 5分钟刷新一次
  priority: "database_overrides_yaml"  # 数据库规则优先级高于YAML

# 性能配置（D3：混合处理模式）
performance:
  processing_engine:
    small_data: "polars"  # <10万条
    large_data: "duckdb"  # ≥10万条
    auto_switch: true
    
  batch_processing:
    enabled: true
    batch_size: 50000
    parallel_workers: 4
    
  memory_management:
    max_memory_usage: "2GB"
    gc_threshold: 0.8
    
  caching:
    rule_cache_ttl: 3600  # 1小时
    validation_cache_ttl: 1800  # 30分钟

# 监控和可观测性
monitoring:
  metrics_collection: true
  performance_tracking: true
  error_rate_alerting: true
  
  alert_thresholds:
    error_rate: 0.05  # 5%错误率告警
    processing_latency: 10  # 10秒延迟告警
    memory_usage: 0.9  # 90%内存使用告警
    
  log_levels:
    data_quality: "INFO"
    performance: "DEBUG"
    errors: "ERROR"