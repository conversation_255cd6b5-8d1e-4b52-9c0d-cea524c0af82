#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA智能环境检测系统

自动检测开发环境、系统配置和依赖状态
版本: 1.0.0
创建时间: 2025-07-31
"""

import os
import sys
import platform
import shutil
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

# 导入AQUA工具
from .paths import Paths
from .simple_config import get_config_manager
from .simple_error import safe_call, handle_error, ErrorLevel
from .cli_ui import get_ui


class EnvironmentType(Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"
    CI_CD = "ci_cd"


class SystemType(Enum):
    """系统类型"""
    WINDOWS = "windows"
    MACOS = "macos"
    LINUX = "linux"
    UNKNOWN = "unknown"


@dataclass
class SystemInfo:
    """系统信息"""
    os_type: SystemType
    os_version: str
    python_version: str
    architecture: str
    cpu_count: int
    memory_gb: float
    available_space_gb: float


@dataclass
class EnvironmentStatus:
    """环境状态"""
    env_type: EnvironmentType
    is_virtual_env: bool
    venv_path: Optional[Path] = None
    package_manager: Optional[str] = None
    node_version: Optional[str] = None
    npm_version: Optional[str] = None


@dataclass
class DependencyStatus:
    """依赖状态"""
    name: str
    required: bool = True
    installed: bool = False
    version: Optional[str] = None
    install_command: Optional[str] = None
    issues: List[str] = field(default_factory=list)


@dataclass
class EnvironmentReport:
    """环境检测报告"""
    system_info: SystemInfo
    env_status: EnvironmentStatus
    python_deps: List[DependencyStatus] = field(default_factory=list)
    system_deps: List[DependencyStatus] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)


class EnvDetector:
    """
    智能环境检测器
    
    核心功能：
    1. 自动检测系统环境和配置
    2. 验证Python和Node.js依赖
    3. 检测虚拟环境状态
    4. 生成改进建议
    """
    
    def __init__(self):
        """初始化环境检测器"""
        self.config = get_config_manager()
        self.ui = get_ui()
        self.paths = Paths()
        
        # 核心依赖定义
        self.python_requirements = [
            ("fastapi", True, "pip install fastapi"),
            ("uvicorn", True, "pip install uvicorn[standard]"),
            ("polars", True, "pip install polars"),
            ("duckdb", True, "pip install duckdb"),
            ("pydantic", True, "pip install pydantic"),
            ("python-multipart", True, "pip install python-multipart"),
            ("pytest", False, "pip install pytest"),
            ("black", False, "pip install black"),
            ("mypy", False, "pip install mypy"),
            ("ruff", False, "pip install ruff"),
        ]
        
        self.system_requirements = [
            ("node", True, "https://nodejs.org/"),
            ("npm", True, "comes with Node.js"),
            ("git", True, "https://git-scm.com/"),
        ]
    
    def detect_system_info(self) -> SystemInfo:
        """检测系统信息"""
        self.ui.print_step("检测系统信息", "loading")
        
        # 操作系统类型
        system = platform.system().lower()
        if system == "windows":
            os_type = SystemType.WINDOWS
        elif system == "darwin":
            os_type = SystemType.MACOS
        elif system == "linux":
            os_type = SystemType.LINUX
        else:
            os_type = SystemType.UNKNOWN
        
        # 系统版本
        os_version = platform.platform()
        
        # Python版本
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        
        # 架构
        architecture = platform.machine()
        
        # CPU核心数
        cpu_count = os.cpu_count() or 1
        
        # 内存信息
        memory_gb = self._get_memory_info()
        
        # 可用空间
        available_space_gb = self._get_disk_space()
        
        return SystemInfo(
            os_type=os_type,
            os_version=os_version,
            python_version=python_version,
            architecture=architecture,
            cpu_count=cpu_count,
            memory_gb=memory_gb,
            available_space_gb=available_space_gb
        )
    
    def detect_environment_status(self) -> EnvironmentStatus:
        """检测环境状态"""
        self.ui.print_step("检测Python环境", "loading")
        
        # 环境类型检测
        env_type = self._detect_environment_type()
        
        # 虚拟环境检测
        is_virtual_env = self._is_in_virtual_env()
        venv_path = self._get_virtual_env_path() if is_virtual_env else None
        
        # Node.js环境检测
        package_manager = self._detect_package_manager()
        node_version = self._get_node_version()
        npm_version = self._get_npm_version()
        
        return EnvironmentStatus(
            env_type=env_type,
            is_virtual_env=is_virtual_env,
            venv_path=venv_path,
            package_manager=package_manager,
            node_version=node_version,
            npm_version=npm_version
        )
    
    def check_python_dependencies(self) -> List[DependencyStatus]:
        """检查Python依赖"""
        self.ui.print_step("检查Python依赖", "loading")
        
        dependencies = []
        
        for name, required, install_cmd in self.python_requirements:
            dep_status = DependencyStatus(
                name=name,
                required=required,
                install_command=install_cmd
            )
            
            try:
                # 尝试导入模块
                __import__(name)
                dep_status.installed = True
                
                # 获取版本信息
                version = self._get_package_version(name)
                dep_status.version = version
                
            except ImportError:
                dep_status.installed = False
                if required:
                    dep_status.issues.append(f"必需依赖 {name} 未安装")
            except Exception as e:
                dep_status.issues.append(f"检查 {name} 时出错: {str(e)}")
            
            dependencies.append(dep_status)
        
        return dependencies
    
    def check_system_dependencies(self) -> List[DependencyStatus]:
        """检查系统依赖"""
        self.ui.print_step("检查系统依赖", "loading")
        
        dependencies = []
        
        for name, required, install_info in self.system_requirements:
            dep_status = DependencyStatus(
                name=name,
                required=required,
                install_command=install_info
            )
            
            # 检查命令是否存在
            if shutil.which(name):
                dep_status.installed = True
                
                # 获取版本信息
                version = self._get_command_version(name)
                dep_status.version = version
                
            else:
                dep_status.installed = False
                if required:
                    dep_status.issues.append(f"必需工具 {name} 未安装")
            
            dependencies.append(dep_status)
        
        return dependencies
    
    def generate_report(self) -> EnvironmentReport:
        """生成完整环境检测报告"""
        self.ui.print_header("环境检测", "正在分析开发环境...")
        
        # 收集所有信息
        system_info = self.detect_system_info()
        env_status = self.detect_environment_status()
        python_deps = self.check_python_dependencies()
        system_deps = self.check_system_dependencies()
        
        # 创建报告
        report = EnvironmentReport(
            system_info=system_info,
            env_status=env_status,
            python_deps=python_deps,
            system_deps=system_deps
        )
        
        # 生成建议和警告
        self._analyze_report(report)
        
        return report
    
    def print_report(self, report: EnvironmentReport):
        """打印环境检测报告"""
        self.ui.print_section("系统信息")
        self.ui.print_key_value("操作系统", f"{report.system_info.os_type.value} ({report.system_info.os_version})")
        self.ui.print_key_value("Python版本", report.system_info.python_version)
        self.ui.print_key_value("架构", report.system_info.architecture)
        self.ui.print_key_value("CPU核心", report.system_info.cpu_count)
        self.ui.print_key_value("内存", f"{report.system_info.memory_gb:.1f} GB")
        self.ui.print_key_value("可用空间", f"{report.system_info.available_space_gb:.1f} GB")
        
        print()
        self.ui.print_section("环境状态")
        self.ui.print_key_value("环境类型", report.env_status.env_type.value)
        self.ui.print_key_value("虚拟环境", "是" if report.env_status.is_virtual_env else "否")
        if report.env_status.venv_path:
            self.ui.print_key_value("虚拟环境路径", str(report.env_status.venv_path))
        if report.env_status.package_manager:
            self.ui.print_key_value("包管理器", report.env_status.package_manager)
        if report.env_status.node_version:
            self.ui.print_key_value("Node.js版本", report.env_status.node_version)
        if report.env_status.npm_version:
            self.ui.print_key_value("NPM版本", report.env_status.npm_version)
        
        # Python依赖表格
        print()
        self.ui.print_section("Python依赖状态")
        self._print_dependency_table(report.python_deps)
        
        # 系统依赖表格
        print()
        self.ui.print_section("系统工具状态")
        self._print_dependency_table(report.system_deps)
        
        # 问题和建议
        if report.errors:
            print()
            self.ui.print_section("错误")
            for error in report.errors:
                self.ui.print_step(error, "error")
        
        if report.warnings:
            print()
            self.ui.print_section("警告")
            for warning in report.warnings:
                self.ui.print_step(warning, "warning")
        
        if report.recommendations:
            print()
            self.ui.print_section("改进建议")
            for rec in report.recommendations:
                self.ui.print_step(rec, "info")
    
    def _detect_environment_type(self) -> EnvironmentType:
        """检测环境类型"""
        # CI/CD环境检测
        ci_indicators = ["CI", "CONTINUOUS_INTEGRATION", "GITHUB_ACTIONS", "GITLAB_CI", "JENKINS_URL"]
        if any(os.getenv(var) for var in ci_indicators):
            return EnvironmentType.CI_CD
        
        # 测试环境检测
        if os.getenv("PYTEST_CURRENT_TEST") or "pytest" in sys.modules:
            return EnvironmentType.TESTING
        
        # 生产环境检测
        if self.config.get_environment() == "production":
            return EnvironmentType.PRODUCTION
        
        return EnvironmentType.DEVELOPMENT
    
    def _is_in_virtual_env(self) -> bool:
        """检查是否在虚拟环境中"""
        return (
            hasattr(sys, 'real_prefix') or
            (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) or
            os.getenv('VIRTUAL_ENV') is not None
        )
    
    def _get_virtual_env_path(self) -> Optional[Path]:
        """获取虚拟环境路径"""
        venv_path = os.getenv('VIRTUAL_ENV')
        if venv_path:
            return Path(venv_path)
        
        # 尝试从sys.prefix推断
        if hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix:
            return Path(sys.prefix)
        
        return None
    
    def _detect_package_manager(self) -> Optional[str]:
        """检测前端包管理器"""
        frontend_path = self.paths.ROOT / "frontend"
        if not frontend_path.exists():
            return None
        
        if (frontend_path / "pnpm-lock.yaml").exists():
            return "pnpm"
        elif (frontend_path / "yarn.lock").exists():
            return "yarn"
        elif (frontend_path / "package-lock.json").exists():
            return "npm"
        elif (frontend_path / "package.json").exists():
            return "npm"  # 默认
        
        return None
    
    def _get_node_version(self) -> Optional[str]:
        """获取Node.js版本"""
        return self._get_command_version("node")
    
    def _get_npm_version(self) -> Optional[str]:
        """获取NPM版本"""
        return self._get_command_version("npm")
    
    def _get_command_version(self, command: str) -> Optional[str]:
        """获取命令版本"""
        try:
            if command == "node":
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            elif command == "npm":
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            elif command == "git":
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            else:
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                # 清理版本字符串
                if command == "git":
                    # git version 2.39.0 -> 2.39.0
                    version = version.split()[-1] if " " in version else version
                return version.replace("v", "")  # 移除v前缀
            
        except (subprocess.TimeoutExpired, subprocess.SubprocessError, FileNotFoundError):
            pass
        
        return None
    
    def _get_package_version(self, package_name: str) -> Optional[str]:
        """获取Python包版本"""
        try:
            if package_name == "fastapi":
                import fastapi
                return fastapi.__version__
            elif package_name == "uvicorn":
                import uvicorn
                return uvicorn.__version__
            elif package_name == "polars":
                import polars as pl
                return pl.__version__
            elif package_name == "duckdb":
                import duckdb
                return duckdb.__version__
            elif package_name == "pydantic":
                import pydantic
                return pydantic.__version__
            elif package_name == "pytest":
                import pytest
                return pytest.__version__
            elif package_name == "black":
                import black
                return black.__version__
            elif package_name == "mypy":
                import mypy
                return mypy.__version__
            elif package_name == "ruff":
                import ruff
                return ruff.__version__
            else:
                # 通用方法
                module = __import__(package_name)
                return getattr(module, '__version__', None)
        except Exception:
            return None
    
    def _get_memory_info(self) -> float:
        """获取内存信息(GB)"""
        try:
            if hasattr(os, 'sysconf') and hasattr(os, 'SC_PAGE_SIZE') and hasattr(os, 'SC_PHYS_PAGES'):
                # Unix系统
                page_size = os.sysconf(os.SC_PAGE_SIZE)
                total_pages = os.sysconf(os.SC_PHYS_PAGES)
                return (page_size * total_pages) / (1024 ** 3)
            else:
                # Windows或其他系统，使用psutil如果可用
                try:
                    import psutil
                    return psutil.virtual_memory().total / (1024 ** 3)
                except ImportError:
                    return 0.0
        except Exception:
            return 0.0
    
    def _get_disk_space(self) -> float:
        """获取可用磁盘空间(GB)"""
        try:
            statvfs = os.statvfs(self.paths.ROOT)
            free_bytes = statvfs.f_frsize * statvfs.f_available
            return free_bytes / (1024 ** 3)
        except (OSError, AttributeError):
            try:
                # Windows
                import shutil
                total, used, free = shutil.disk_usage(self.paths.ROOT)
                return free / (1024 ** 3)
            except Exception:
                return 0.0
    
    def _print_dependency_table(self, dependencies: List[DependencyStatus]):
        """打印依赖状态表格"""
        if not dependencies:
            return
        
        headers = ["依赖", "状态", "版本", "必需"]
        rows = []
        
        for dep in dependencies:
            status = "✅ 已安装" if dep.installed else "❌ 未安装"
            version = dep.version or "-"
            required = "是" if dep.required else "否"
            
            rows.append([dep.name, status, version, required])
        
        self.ui.print_table(headers, rows)
        
        # 显示安装命令
        missing_deps = [dep for dep in dependencies if not dep.installed and dep.required]
        if missing_deps:
            print()
            self.ui.print_step("安装缺少的依赖:", "info")
            for dep in missing_deps:
                if dep.install_command:
                    self.ui.print_key_value(dep.name, dep.install_command)
    
    def _analyze_report(self, report: EnvironmentReport):
        """分析报告并生成建议"""
        # 检查Python版本
        python_version = tuple(map(int, report.system_info.python_version.split(".")))
        if python_version < (3, 9):
            report.warnings.append(f"Python版本 {report.system_info.python_version} 较旧，建议升级到3.9+")
        
        # 检查虚拟环境
        if not report.env_status.is_virtual_env:
            report.warnings.append("未检测到虚拟环境，建议使用虚拟环境隔离依赖")
            report.recommendations.append("创建虚拟环境: python -m venv .venv && source .venv/bin/activate")
        
        # 检查必需依赖
        missing_python_deps = [dep for dep in report.python_deps if dep.required and not dep.installed]
        if missing_python_deps:
            report.errors.append(f"缺少必需的Python依赖: {', '.join(dep.name for dep in missing_python_deps)}")
            report.recommendations.append("安装Python依赖: pip install -r requirements.txt")
        
        missing_system_deps = [dep for dep in report.system_deps if dep.required and not dep.installed]
        if missing_system_deps:
            report.errors.append(f"缺少必需的系统工具: {', '.join(dep.name for dep in missing_system_deps)}")
        
        # 检查资源
        if report.system_info.memory_gb < 4.0:
            report.warnings.append(f"系统内存 {report.system_info.memory_gb:.1f}GB 可能不足，建议4GB+")
        
        if report.system_info.available_space_gb < 1.0:
            report.warnings.append(f"可用磁盘空间 {report.system_info.available_space_gb:.1f}GB 不足")
        
        # 检查Node.js环境
        if not report.env_status.node_version:
            report.warnings.append("未检测到Node.js，前端开发需要Node.js")
            report.recommendations.append("安装Node.js: https://nodejs.org/")
        
        # 性能建议
        if report.system_info.cpu_count >= 4:
            report.recommendations.append(f"检测到{report.system_info.cpu_count}核CPU，可在配置中启用并行处理")


def main():
    """测试和演示函数"""
    detector = EnvDetector()
    
    # 生成并显示报告
    report = detector.generate_report()
    detector.print_report(report)
    
    # 显示总结
    ui = get_ui()
    print()
    
    if report.errors:
        ui.print_box(
            "检测到严重问题，请先解决错误后再运行AQUA",
            "环境检查失败",
            "error"
        )
        return False
    elif report.warnings:
        ui.print_box(
            "检测到一些问题，建议按照建议进行优化",
            "环境检查完成",
            "warning"
        )
    else:
        ui.print_box(
            "环境检查完成，所有依赖和配置正常",
            "环境就绪",
            "success"
        )
    
    return True


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.paths import Paths
    from utils.simple_config import get_config_manager
    from utils.simple_error import safe_call, handle_error, ErrorLevel
    from utils.cli_ui import get_ui
    
    success = main()
    sys.exit(0 if success else 1)