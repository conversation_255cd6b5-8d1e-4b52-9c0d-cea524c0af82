#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA依赖管理优化系统

智能依赖安装、版本管理和冲突解决
版本: 1.0.0
创建时间: 2025-08-01
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import importlib.util
import pkg_resources
from packaging import version
import tempfile
import shutil

# 导入AQUA工具
from .paths import Paths
from .simple_config import get_config_manager
from .simple_error import safe_call, handle_error, ErrorLevel
from .cli_ui import get_ui


class DependencyType(Enum):
    """依赖类型"""
    PYTHON = "python"
    SYSTEM = "system"
    FRONTEND = "frontend"


class InstallMethod(Enum):
    """安装方法"""
    PIP = "pip"
    CONDA = "conda"
    UV = "uv"
    NPM = "npm"
    PNPM = "pnpm"
    YARN = "yarn"
    SYSTEM = "system"


class DependencyStatus(Enum):
    """依赖状态"""
    INSTALLED = "installed"
    MISSING = "missing"
    OUTDATED = "outdated"
    CONFLICT = "conflict"
    BROKEN = "broken"


@dataclass
class DependencyInfo:
    """依赖信息"""
    name: str
    type: DependencyType
    required_version: Optional[str] = None
    installed_version: Optional[str] = None
    status: DependencyStatus = DependencyStatus.MISSING
    install_method: Optional[InstallMethod] = None
    install_command: Optional[str] = None
    description: str = ""
    conflicts: List[str] = field(default_factory=list)
    alternatives: List[str] = field(default_factory=list)
    issues: List[str] = field(default_factory=list)


@dataclass
class InstallationPlan:
    """安装计划"""
    to_install: List[DependencyInfo] = field(default_factory=list)
    to_upgrade: List[DependencyInfo] = field(default_factory=list)
    to_remove: List[DependencyInfo] = field(default_factory=list)
    conflicts: List[Tuple[str, str]] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    estimated_time: int = 0  # 预计安装时间（秒）
    total_size: float = 0.0  # 预计下载大小（MB）


class DependencyManager:
    """
    智能依赖管理器
    
    核心功能：
    1. 依赖检测和状态分析
    2. 智能安装方法选择
    3. 版本冲突解决
    4. 批量安装和回滚
    """
    
    def __init__(self):
        """初始化依赖管理器"""
        self.config = get_config_manager()
        self.ui = get_ui()
        self.paths = Paths()
        
        # 依赖定义文件
        self.deps_config_file = self.paths.ROOT / "config" / "dependencies.json"
        
        # 依赖缓存
        self._dependency_cache = {}
        self._installation_cache = {}
        
        # 加载依赖配置
        self.dependency_specs = self._load_dependency_specs()
        
        # 检测可用的安装工具
        self.available_installers = self._detect_installers()
    
    def _load_dependency_specs(self) -> Dict[str, Any]:
        """加载依赖规格"""
        if self.deps_config_file.exists():
            try:
                with open(self.deps_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                handle_error(e, ErrorLevel.WARNING, "加载依赖配置")
        
        # 默认依赖配置
        return {
            "python": {
                "core": {
                    "fastapi": {"version": ">=0.100.0", "description": "高性能Web框架"},
                    "uvicorn": {"version": ">=0.20.0", "description": "ASGI服务器"},
                    "polars": {"version": ">=0.20.0", "description": "高性能数据处理"},
                    "duckdb": {"version": ">=0.9.0", "description": "嵌入式分析数据库"},
                    "pydantic": {"version": ">=2.0.0", "description": "数据验证框架"}
                },
                "dev": {
                    "pytest": {"version": ">=7.0.0", "description": "测试框架"},
                    "black": {"version": ">=23.0.0", "description": "代码格式化"},
                    "mypy": {"version": ">=1.0.0", "description": "类型检查"},
                    "ruff": {"version": ">=0.1.0", "description": "快速代码检查"}
                },
                "optional": {
                    "jupyter": {"version": ">=1.0.0", "description": "交互式开发环境"},
                    "matplotlib": {"version": ">=3.5.0", "description": "数据可视化"},
                    "plotly": {"version": ">=5.0.0", "description": "交互式图表"}
                }
            },
            "system": {
                "required": {
                    "node": {"version": ">=18.0.0", "description": "JavaScript运行时"},
                    "npm": {"version": ">=9.0.0", "description": "Node包管理器"},
                    "git": {"version": ">=2.30.0", "description": "版本控制系统"}
                },
                "optional": {
                    "docker": {"version": ">=20.0.0", "description": "容器化平台"},
                    "redis": {"version": ">=6.0.0", "description": "内存数据库"}
                }
            },
            "frontend": {
                "core": {
                    "vue": {"version": "^3.3.0", "description": "前端框架"},
                    "typescript": {"version": "^5.0.0", "description": "类型系统"},
                    "vite": {"version": "^5.0.0", "description": "构建工具"},
                    "naive-ui": {"version": "^2.34.0", "description": "UI组件库"}
                },
                "dev": {
                    "vitest": {"version": "^1.0.0", "description": "测试框架"},
                    "eslint": {"version": "^8.0.0", "description": "代码检查"},
                    "prettier": {"version": "^3.0.0", "description": "代码格式化"}
                }
            }
        }
    
    def _detect_installers(self) -> Dict[InstallMethod, bool]:
        """检测可用的安装工具"""
        installers = {}
        
        # Python包管理器
        installers[InstallMethod.PIP] = shutil.which("pip") is not None
        installers[InstallMethod.UV] = shutil.which("uv") is not None
        installers[InstallMethod.CONDA] = shutil.which("conda") is not None
        
        # Node.js包管理器
        installers[InstallMethod.NPM] = shutil.which("npm") is not None
        installers[InstallMethod.PNPM] = shutil.which("pnpm") is not None
        installers[InstallMethod.YARN] = shutil.which("yarn") is not None
        
        return installers
    
    def scan_dependencies(self, dependency_type: Optional[DependencyType] = None) -> List[DependencyInfo]:
        """扫描依赖状态"""
        self.ui.print_step("扫描依赖状态", "loading")
        
        all_dependencies = []
        
        if not dependency_type or dependency_type == DependencyType.PYTHON:
            all_dependencies.extend(self._scan_python_dependencies())
        
        if not dependency_type or dependency_type == DependencyType.SYSTEM:
            all_dependencies.extend(self._scan_system_dependencies())
        
        if not dependency_type or dependency_type == DependencyType.FRONTEND:
            all_dependencies.extend(self._scan_frontend_dependencies())
        
        # 检测冲突
        self._detect_conflicts(all_dependencies)
        
        return all_dependencies
    
    def _scan_python_dependencies(self) -> List[DependencyInfo]:
        """扫描Python依赖"""
        dependencies = []
        python_specs = self.dependency_specs.get("python", {})
        
        for category, deps in python_specs.items():
            for name, spec in deps.items():
                dep_info = DependencyInfo(
                    name=name,
                    type=DependencyType.PYTHON,
                    required_version=spec.get("version"),
                    description=spec.get("description", ""),
                    install_method=self._get_best_python_installer()
                )
                
                # 检查安装状态
                self._check_python_package(dep_info)
                
                # 生成安装命令
                dep_info.install_command = self._generate_install_command(dep_info)
                
                dependencies.append(dep_info)
        
        return dependencies
    
    def _scan_system_dependencies(self) -> List[DependencyInfo]:
        """扫描系统依赖"""
        dependencies = []
        system_specs = self.dependency_specs.get("system", {})
        
        for category, deps in system_specs.items():
            for name, spec in deps.items():
                dep_info = DependencyInfo(
                    name=name,
                    type=DependencyType.SYSTEM,
                    required_version=spec.get("version"),
                    description=spec.get("description", ""),
                    install_method=InstallMethod.SYSTEM
                )
                
                # 检查安装状态
                self._check_system_tool(dep_info)
                
                # 生成安装说明
                dep_info.install_command = self._get_system_install_info(name)
                
                dependencies.append(dep_info)
        
        return dependencies
    
    def _scan_frontend_dependencies(self) -> List[DependencyInfo]:
        """扫描前端依赖"""
        dependencies = []
        
        # 检查是否有前端项目
        frontend_path = self.paths.ROOT / "frontend"
        if not frontend_path.exists():
            return dependencies
        
        package_json = frontend_path / "package.json"
        if not package_json.exists():
            return dependencies
        
        try:
            with open(package_json, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
            
            frontend_specs = self.dependency_specs.get("frontend", {})
            all_frontend_deps = {}
            for category, deps in frontend_specs.items():
                all_frontend_deps.update(deps)
            
            # 检查已定义的依赖
            for name, spec in all_frontend_deps.items():
                dep_info = DependencyInfo(
                    name=name,
                    type=DependencyType.FRONTEND,
                    required_version=spec.get("version"),
                    description=spec.get("description", ""),
                    install_method=self._get_best_frontend_installer()
                )
                
                # 检查package.json中的版本
                installed_version = None
                for dep_type in ["dependencies", "devDependencies"]:
                    if name in package_data.get(dep_type, {}):
                        installed_version = package_data[dep_type][name]
                        break
                
                if installed_version:
                    dep_info.installed_version = installed_version
                    dep_info.status = DependencyStatus.INSTALLED
                    
                    # 检查版本是否满足要求
                    if not self._check_version_compatibility(installed_version, dep_info.required_version):
                        dep_info.status = DependencyStatus.OUTDATED
                        dep_info.issues.append(f"版本过旧: {installed_version} < {dep_info.required_version}")
                else:
                    dep_info.status = DependencyStatus.MISSING
                
                # 生成安装命令
                dep_info.install_command = self._generate_install_command(dep_info)
                
                dependencies.append(dep_info)
                
        except Exception as e:
            handle_error(e, ErrorLevel.WARNING, "扫描前端依赖")
        
        return dependencies
    
    def _check_python_package(self, dep_info: DependencyInfo):
        """检查Python包状态"""
        try:
            # 尝试导入
            spec = importlib.util.find_spec(dep_info.name)
            if spec is None:
                dep_info.status = DependencyStatus.MISSING
                return
            
            # 获取已安装版本
            try:
                distribution = pkg_resources.get_distribution(dep_info.name)
                dep_info.installed_version = distribution.version
                dep_info.status = DependencyStatus.INSTALLED
                
                # 检查版本兼容性
                if dep_info.required_version:
                    if not self._check_version_compatibility(dep_info.installed_version, dep_info.required_version):
                        dep_info.status = DependencyStatus.OUTDATED
                        dep_info.issues.append(f"版本不兼容: {dep_info.installed_version} 不满足 {dep_info.required_version}")
                        
            except pkg_resources.DistributionNotFound:
                # 包存在但版本信息缺失
                dep_info.status = DependencyStatus.BROKEN
                dep_info.issues.append("包损坏或版本信息缺失")
                
        except Exception as e:
            dep_info.status = DependencyStatus.BROKEN
            dep_info.issues.append(f"检查失败: {str(e)}")
    
    def _check_system_tool(self, dep_info: DependencyInfo):
        """检查系统工具状态"""
        if shutil.which(dep_info.name):
            dep_info.status = DependencyStatus.INSTALLED
            
            # 尝试获取版本
            version_info = self._get_command_version(dep_info.name)
            if version_info:
                dep_info.installed_version = version_info
                
                # 检查版本兼容性
                if dep_info.required_version:
                    if not self._check_version_compatibility(dep_info.installed_version, dep_info.required_version):
                        dep_info.status = DependencyStatus.OUTDATED
                        dep_info.issues.append(f"版本过旧: {dep_info.installed_version} < {dep_info.required_version}")
        else:
            dep_info.status = DependencyStatus.MISSING
    
    def _get_command_version(self, command: str) -> Optional[str]:
        """获取命令版本"""
        try:
            if command == "node":
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            elif command == "npm":
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            elif command == "git":
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            else:
                result = subprocess.run([command, "--version"], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                version_output = result.stdout.strip()
                # 提取版本号
                if command == "git":
                    version_output = version_output.split()[-1] if " " in version_output else version_output
                return version_output.replace("v", "")
            
        except (subprocess.TimeoutExpired, subprocess.SubprocessError, FileNotFoundError):
            pass
        
        return None
    
    def _check_version_compatibility(self, installed: str, required: str) -> bool:
        """检查版本兼容性"""
        if not required or not installed:
            return True
        
        try:
            # 处理npm风格的版本要求
            if required.startswith("^"):
                # ^1.0.0 表示 >=1.0.0 且 <2.0.0
                base_version = required[1:]
                installed_ver = version.parse(installed.replace("v", ""))
                base_ver = version.parse(base_version)
                major_limit = version.parse(f"{base_ver.major + 1}.0.0")
                return installed_ver >= base_ver and installed_ver < major_limit
            
            elif required.startswith("~"):
                # ~1.0.0 表示 >=1.0.0 且 <1.1.0
                base_version = required[1:]
                installed_ver = version.parse(installed.replace("v", ""))
                base_ver = version.parse(base_version)
                minor_limit = version.parse(f"{base_ver.major}.{base_ver.minor + 1}.0")
                return installed_ver >= base_ver and installed_ver < minor_limit
            
            elif ">=" in required:
                min_version = required.replace(">=", "").strip()
                return version.parse(installed.replace("v", "")) >= version.parse(min_version)
            
            elif ">" in required:
                min_version = required.replace(">", "").strip()
                return version.parse(installed.replace("v", "")) > version.parse(min_version)
            
            elif "==" in required or "=" in required:
                exact_version = required.replace("==", "").replace("=", "").strip()
                return version.parse(installed.replace("v", "")) == version.parse(exact_version)
            
            else:
                # 默认精确匹配
                return version.parse(installed.replace("v", "")) >= version.parse(required)
                
        except Exception:
            # 版本解析失败，保守返回True
            return True
    
    def _detect_conflicts(self, dependencies: List[DependencyInfo]):
        """检测依赖冲突"""
        name_to_deps = {}
        
        # 按名称分组
        for dep in dependencies:
            if dep.name not in name_to_deps:
                name_to_deps[dep.name] = []
            name_to_deps[dep.name].append(dep)
        
        # 检查同名依赖的版本冲突
        for name, deps in name_to_deps.items():
            if len(deps) > 1:
                versions = [dep.required_version for dep in deps if dep.required_version]
                if len(set(versions)) > 1:
                    for dep in deps:
                        dep.status = DependencyStatus.CONFLICT
                        dep.conflicts = [f"版本冲突: {versions}"]
    
    def _get_best_python_installer(self) -> InstallMethod:
        """获取最佳Python安装器"""
        if self.available_installers.get(InstallMethod.UV):
            return InstallMethod.UV
        elif self.available_installers.get(InstallMethod.CONDA):
            return InstallMethod.CONDA
        else:
            return InstallMethod.PIP
    
    def _get_best_frontend_installer(self) -> InstallMethod:
        """获取最佳前端安装器"""
        frontend_path = self.paths.ROOT / "frontend"
        
        if (frontend_path / "pnpm-lock.yaml").exists():
            return InstallMethod.PNPM
        elif (frontend_path / "yarn.lock").exists():
            return InstallMethod.YARN
        else:
            return InstallMethod.NPM
    
    def _generate_install_command(self, dep_info: DependencyInfo) -> str:
        """生成安装命令"""
        if dep_info.type == DependencyType.PYTHON:
            if dep_info.install_method == InstallMethod.UV:
                return f"uv add {dep_info.name}"
            elif dep_info.install_method == InstallMethod.CONDA:
                return f"conda install {dep_info.name}"
            else:
                version_spec = dep_info.required_version if dep_info.required_version else ""
                return f"pip install {dep_info.name}{version_spec}"
        
        elif dep_info.type == DependencyType.FRONTEND:
            if dep_info.install_method == InstallMethod.PNPM:
                return f"pnpm add {dep_info.name}"
            elif dep_info.install_method == InstallMethod.YARN:
                return f"yarn add {dep_info.name}"
            else:
                return f"npm install {dep_info.name}"
        
        elif dep_info.type == DependencyType.SYSTEM:
            return self._get_system_install_info(dep_info.name)
        
        return ""
    
    def _get_system_install_info(self, tool_name: str) -> str:
        """获取系统工具安装信息"""
        install_info = {
            "node": "访问 https://nodejs.org/ 下载安装",
            "npm": "随Node.js自动安装",
            "git": "访问 https://git-scm.com/ 下载安装",
            "docker": "访问 https://docker.com/ 下载Docker Desktop",
            "redis": "使用包管理器安装: brew install redis (macOS) 或 apt install redis (Ubuntu)"
        }
        
        return install_info.get(tool_name, f"请手动安装 {tool_name}")
    
    def create_installation_plan(self, dependencies: List[DependencyInfo], 
                               install_missing: bool = True,
                               upgrade_outdated: bool = False) -> InstallationPlan:
        """创建安装计划"""
        plan = InstallationPlan()
        
        for dep in dependencies:
            if dep.status == DependencyStatus.MISSING and install_missing:
                plan.to_install.append(dep)
            elif dep.status == DependencyStatus.OUTDATED and upgrade_outdated:
                plan.to_upgrade.append(dep)
            elif dep.status == DependencyStatus.CONFLICT:
                plan.conflicts.append((dep.name, f"版本冲突: {dep.conflicts}"))
        
        # 估算安装时间和大小
        plan.estimated_time = len(plan.to_install) * 30 + len(plan.to_upgrade) * 20  # 秒
        plan.total_size = len(plan.to_install) * 10.0 + len(plan.to_upgrade) * 5.0  # MB（估算）
        
        # 生成警告
        if plan.conflicts:
            plan.warnings.append(f"检测到 {len(plan.conflicts)} 个版本冲突")
        
        system_deps = [dep for dep in plan.to_install if dep.type == DependencyType.SYSTEM]
        if system_deps:
            plan.warnings.append(f"有 {len(system_deps)} 个系统工具需要手动安装")
        
        return plan
    
    def execute_installation_plan(self, plan: InstallationPlan, dry_run: bool = False) -> bool:
        """执行安装计划"""
        if dry_run:
            self._preview_installation_plan(plan)
            return True
        
        self.ui.print_header("依赖安装", f"将安装 {len(plan.to_install)} 个依赖")
        
        success_count = 0
        total_count = len(plan.to_install) + len(plan.to_upgrade)
        
        # 安装新依赖
        for dep in plan.to_install:
            self.ui.print_step(f"安装 {dep.name}", "loading")
            
            if self._install_dependency(dep):
                success_count += 1
                self.ui.print_step(f"✅ {dep.name} 安装成功", "success")
            else:
                self.ui.print_step(f"❌ {dep.name} 安装失败", "error")
        
        # 升级过时依赖
        for dep in plan.to_upgrade:
            self.ui.print_step(f"升级 {dep.name}", "loading")
            
            if self._install_dependency(dep):
                success_count += 1
                self.ui.print_step(f"✅ {dep.name} 升级成功", "success")
            else:
                self.ui.print_step(f"❌ {dep.name} 升级失败", "error")
        
        # 显示结果
        if success_count == total_count:
            self.ui.print_box(f"所有依赖安装完成 ({success_count}/{total_count})", "安装成功", "success")
            return True
        else:
            self.ui.print_box(f"部分依赖安装失败 ({success_count}/{total_count})", "安装完成", "warning")
            return False
    
    def _install_dependency(self, dep_info: DependencyInfo) -> bool:
        """安装单个依赖"""
        if dep_info.type == DependencyType.SYSTEM:
            self.ui.print_step(f"系统依赖需要手动安装: {dep_info.install_command}", "info")
            return True  # 系统依赖不算失败
        
        try:
            if dep_info.type == DependencyType.PYTHON:
                return self._install_python_dependency(dep_info)
            elif dep_info.type == DependencyType.FRONTEND:
                return self._install_frontend_dependency(dep_info)
        except Exception as e:
            handle_error(e, ErrorLevel.ERROR, f"安装依赖 {dep_info.name}")
            return False
        
        return False
    
    def _install_python_dependency(self, dep_info: DependencyInfo) -> bool:
        """安装Python依赖"""
        if dep_info.install_method == InstallMethod.UV:
            cmd = ["uv", "add", dep_info.name]
        elif dep_info.install_method == InstallMethod.CONDA:
            cmd = ["conda", "install", "-y", dep_info.name]
        else:
            version_spec = dep_info.required_version if dep_info.required_version else ""
            cmd = ["pip", "install", f"{dep_info.name}{version_spec}"]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    
    def _install_frontend_dependency(self, dep_info: DependencyInfo) -> bool:
        """安装前端依赖"""
        frontend_path = self.paths.ROOT / "frontend"
        
        if dep_info.install_method == InstallMethod.PNPM:
            cmd = ["pnpm", "add", dep_info.name]
        elif dep_info.install_method == InstallMethod.YARN:
            cmd = ["yarn", "add", dep_info.name]
        else:
            cmd = ["npm", "install", dep_info.name]
        
        result = subprocess.run(cmd, cwd=frontend_path, capture_output=True, text=True)
        return result.returncode == 0
    
    def _preview_installation_plan(self, plan: InstallationPlan):
        """预览安装计划"""
        self.ui.print_section("安装计划预览")
        
        if plan.to_install:
            self.ui.print_step(f"将安装 {len(plan.to_install)} 个依赖:", "info")
            for dep in plan.to_install:
                self.ui.print_key_value(f"  {dep.name}", dep.description)
        
        if plan.to_upgrade:
            self.ui.print_step(f"将升级 {len(plan.to_upgrade)} 个依赖:", "info")
            for dep in plan.to_upgrade:
                self.ui.print_key_value(f"  {dep.name}", f"{dep.installed_version} → {dep.required_version}")
        
        if plan.conflicts:
            self.ui.print_step("版本冲突:", "warning")
            for name, conflict in plan.conflicts:
                self.ui.print_key_value(f"  {name}", conflict)
        
        print()
        self.ui.print_key_value("预计安装时间", f"{plan.estimated_time // 60}分{plan.estimated_time % 60}秒")
        self.ui.print_key_value("预计下载大小", f"{plan.total_size:.1f} MB")
    
    def print_dependency_report(self, dependencies: List[DependencyInfo]):
        """打印依赖报告"""
        # 按类型分组
        python_deps = [d for d in dependencies if d.type == DependencyType.PYTHON]
        system_deps = [d for d in dependencies if d.type == DependencyType.SYSTEM]
        frontend_deps = [d for d in dependencies if d.type == DependencyType.FRONTEND]
        
        # Python依赖
        if python_deps:
            self.ui.print_section("Python依赖")
            self._print_dependency_table(python_deps)
        
        # 系统依赖
        if system_deps:
            print()
            self.ui.print_section("系统工具")
            self._print_dependency_table(system_deps)
        
        # 前端依赖  
        if frontend_deps:
            print()
            self.ui.print_section("前端依赖")
            self._print_dependency_table(frontend_deps)
        
        # 统计信息
        print()
        self._print_dependency_statistics(dependencies)
    
    def _print_dependency_table(self, dependencies: List[DependencyInfo]):
        """打印依赖表格"""
        headers = ["名称", "状态", "已安装", "要求版本", "描述"]
        rows = []
        
        for dep in dependencies:
            status_icon = {
                DependencyStatus.INSTALLED: "✅ 已安装",
                DependencyStatus.MISSING: "❌ 缺失",
                DependencyStatus.OUTDATED: "⚠️ 过时",
                DependencyStatus.CONFLICT: "🔥 冲突",
                DependencyStatus.BROKEN: "💥 损坏"
            }.get(dep.status, "❓ 未知")
            
            installed_ver = dep.installed_version or "-"
            required_ver = dep.required_version or "-"
            description = dep.description[:40] + "..." if len(dep.description) > 40 else dep.description
            
            rows.append([dep.name, status_icon, installed_ver, required_ver, description])
        
        self.ui.print_table(headers, rows)
    
    def _print_dependency_statistics(self, dependencies: List[DependencyInfo]):
        """打印依赖统计"""
        total = len(dependencies)
        installed = len([d for d in dependencies if d.status == DependencyStatus.INSTALLED])
        missing = len([d for d in dependencies if d.status == DependencyStatus.MISSING])
        outdated = len([d for d in dependencies if d.status == DependencyStatus.OUTDATED])
        conflicts = len([d for d in dependencies if d.status == DependencyStatus.CONFLICT])
        
        self.ui.print_section("依赖统计")
        self.ui.print_key_value("总计", total)
        self.ui.print_key_value("已安装", f"{installed} ({installed/total*100:.1f}%)" if total > 0 else "0")
        self.ui.print_key_value("缺失", missing)
        self.ui.print_key_value("过时", outdated)
        self.ui.print_key_value("冲突", conflicts)
    
    def auto_fix_dependencies(self) -> bool:
        """自动修复依赖问题"""
        self.ui.print_header("依赖自动修复", "分析并修复依赖问题")
        
        # 扫描依赖
        dependencies = self.scan_dependencies()
        
        # 创建修复计划
        plan = self.create_installation_plan(dependencies, install_missing=True, upgrade_outdated=True)
        
        if not plan.to_install and not plan.to_upgrade:
            self.ui.print_box("所有依赖都已正确安装，无需修复", "检查完成", "success")
            return True
        
        # 显示修复计划
        self._preview_installation_plan(plan)
        
        # 确认执行
        if self.ui.confirm("是否执行依赖修复?"):
            return self.execute_installation_plan(plan)
        
        return False


def main():
    """测试和演示函数"""
    manager = DependencyManager()
    
    # 扫描依赖
    dependencies = manager.scan_dependencies()
    
    # 显示报告
    manager.print_dependency_report(dependencies)
    
    # 创建安装计划
    plan = manager.create_installation_plan(dependencies)
    
    # 预览计划
    if plan.to_install or plan.to_upgrade:
        print()
        manager._preview_installation_plan(plan)


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.paths import Paths
    from utils.simple_config import get_config_manager
    from utils.simple_error import safe_call, handle_error, ErrorLevel
    from utils.cli_ui import get_ui
    
    main()