"""
MySQL配置管理服务

提供MySQL配置的读取、验证、更新和测试功能
解决前端重复配置MySQL连接参数的用户体验问题
"""

import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path
import toml

from .config_loader import <PERSON>fig<PERSON><PERSON><PERSON>
from .exceptions import (
    BusinessLogicException,
)
from ..data_import.mysql_importer_logic import test_mysql_connection_logic

logger = logging.getLogger(__name__)


@dataclass
class MySQLConfig:
    """MySQL配置数据模型"""

    host: str
    port: int
    user: str
    password: str
    database: str
    charset: str = "utf8mb4"
    connect_timeout: int = 30
    read_timeout: int = 30

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "host": self.host,
            "port": self.port,
            "username": self.user,  # 前端使用username字段
            "password": self.password,
            "database": self.database,
            "charset": self.charset,
            "connect_timeout": self.connect_timeout,
            "read_timeout": self.read_timeout,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MySQLConfig":
        """从字典创建配置对象"""
        return cls(
            host=data.get("host", "localhost"),
            port=int(data.get("port", 3306)),
            user=data.get("user") or data.get("username", "root"),
            password=data.get("password", ""),
            database=data.get("database", ""),
            charset=data.get("charset", "utf8mb4"),
            connect_timeout=int(data.get("connect_timeout", 30)),
            read_timeout=int(data.get("read_timeout", 30)),
        )


class MySQLConfigManager:
    """MySQL配置管理器"""

    def __init__(self):
        self.config_loader = ConfigLoader()
        self.config_file_path = Path("config/settings.toml")
        self._config_cache = {}

    def get_environment_mysql_config(self, environment: str) -> Optional[MySQLConfig]:
        """
        获取指定环境的MySQL配置

        Args:
            environment: 环境名称 (dev/test/prod)

        Returns:
            MySQLConfig对象，如果配置不存在则返回None
        """
        try:
            # 检查缓存
            cache_key = f"{environment}_mysql"
            if cache_key in self._config_cache:
                return self._config_cache[cache_key]

            # 从配置文件读取
            mysql_config_dict = self.config_loader.get_mysql_config(environment)

            if not mysql_config_dict:
                logger.warning(f"未找到环境 {environment} 的MySQL配置")
                return None

            mysql_config = MySQLConfig.from_dict(mysql_config_dict)

            # 缓存配置
            self._config_cache[cache_key] = mysql_config

            logger.info(f"成功加载环境 {environment} 的MySQL配置")
            return mysql_config

        except Exception as e:
            logger.error(f"获取MySQL配置失败: {e}")
            raise BusinessLogicException(f"获取MySQL配置失败: {str(e)}")

    def get_all_environments_mysql_config(self) -> Dict[str, MySQLConfig]:
        """
        获取所有环境的MySQL配置

        Returns:
            环境名称到配置对象的映射
        """
        environments = ["dev", "test", "prod"]
        configs = {}

        for env in environments:
            config = self.get_environment_mysql_config(env)
            if config:
                configs[env] = config

        return configs

    async def test_mysql_connection(
        self, config: MySQLConfig, environment: str = "dev"
    ) -> Dict[str, Any]:
        """
        测试MySQL连接

        Args:
            config: MySQL配置对象
            environment: 环境名称

        Returns:
            测试结果字典
        """
        try:
            # 使用现有的MySQL连接测试逻辑
            result = test_mysql_connection_logic(
                host=config.host,
                port=config.port,
                username=config.user,
                password=config.password,
                database=config.database,
                environment=environment,
            )

            return result

        except Exception as e:
            logger.error(f"MySQL连接测试失败: {e}")
            return {"success": False, "error": str(e), "message": "MySQL连接测试失败"}

    async def test_environment_mysql_connection(
        self, environment: str
    ) -> Dict[str, Any]:
        """
        测试指定环境的MySQL连接

        Args:
            environment: 环境名称

        Returns:
            测试结果字典
        """
        config = self.get_environment_mysql_config(environment)
        if not config:
            return {
                "success": False,
                "error": f"未找到环境 {environment} 的MySQL配置",
                "message": "配置不存在",
            }

        return await self.test_mysql_connection(config, environment)

    def update_environment_mysql_config(
        self, environment: str, config_data: Dict[str, Any]
    ) -> bool:
        """
        更新指定环境的MySQL配置

        Args:
            environment: 环境名称
            config_data: 新的配置数据

        Returns:
            更新是否成功
        """
        try:
            # 验证配置数据
            new_config = MySQLConfig.from_dict(config_data)

            # 读取现有配置文件
            if not self.config_file_path.exists():
                raise BusinessLogicException("配置文件不存在")

            with open(self.config_file_path, "r", encoding="utf-8") as f:
                config_dict = toml.load(f)

            # 更新配置
            config_section = f"{environment}.mysql"
            section_parts = config_section.split(".")

            # 创建嵌套字典结构
            current_section = config_dict
            for part in section_parts[:-1]:
                if part not in current_section:
                    current_section[part] = {}
                current_section = current_section[part]

            # 更新MySQL配置
            current_section[section_parts[-1]] = {
                "host": new_config.host,
                "port": new_config.port,
                "user": new_config.user,
                "password": new_config.password,
                "database": new_config.database,
                "charset": new_config.charset,
                "connect_timeout": new_config.connect_timeout,
                "read_timeout": new_config.read_timeout,
            }

            # 写回配置文件
            with open(self.config_file_path, "w", encoding="utf-8") as f:
                toml.dump(config_dict, f)

            # 清除缓存
            cache_key = f"{environment}_mysql"
            if cache_key in self._config_cache:
                del self._config_cache[cache_key]

            logger.info(f"成功更新环境 {environment} 的MySQL配置")
            return True

        except Exception as e:
            logger.error(f"更新MySQL配置失败: {e}")
            raise BusinessLogicException(f"更新MySQL配置失败: {str(e)}")

    def get_mysql_config_template(self) -> Dict[str, Any]:
        """
        获取MySQL配置模板

        Returns:
            配置模板字典
        """
        return {
            "host": "localhost",
            "port": 3306,
            "username": "root",
            "password": "",
            "database": "",
            "charset": "utf8mb4",
            "connect_timeout": 30,
            "read_timeout": 30,
        }

    def get_mysql_config_presets(self) -> Dict[str, Dict[str, Any]]:
        """
        获取MySQL配置预设

        Returns:
            预设配置字典
        """
        return {
            "localhost": {
                "name": "本地开发环境",
                "host": "localhost",
                "port": 3306,
                "username": "root",
                "password": "",
                "database": "aqua_dev",
                "charset": "utf8mb4",
                "connect_timeout": 30,
                "read_timeout": 30,
            },
            "docker": {
                "name": "Docker环境",
                "host": "mysql",
                "port": 3306,
                "username": "aqua_user",
                "password": "aqua_password",
                "database": "aqua_db",
                "charset": "utf8mb4",
                "connect_timeout": 30,
                "read_timeout": 30,
            },
            "remote": {
                "name": "远程服务器",
                "host": "*************",
                "port": 3306,
                "username": "aqua_user",
                "password": "",
                "database": "aqua_prod",
                "charset": "utf8mb4",
                "connect_timeout": 30,
                "read_timeout": 30,
            },
        }

    def validate_mysql_config(self, config_data: Dict[str, Any]) -> List[str]:
        """
        验证MySQL配置数据

        Args:
            config_data: 配置数据字典

        Returns:
            验证错误列表，空列表表示验证通过
        """
        errors = []

        # 必填字段检查
        required_fields = ["host", "port", "database"]
        for field in required_fields:
            if not config_data.get(field):
                errors.append(f"缺少必填字段: {field}")

        # 端口范围检查
        port = config_data.get("port")
        if port:
            try:
                port_int = int(port)
                if port_int < 1 or port_int > 65535:
                    errors.append("端口号必须在1-65535范围内")
            except ValueError:
                errors.append("端口号必须是有效的整数")

        # 用户名检查
        username = config_data.get("username") or config_data.get("user")
        if not username:
            errors.append("用户名不能为空")

        return errors

    def clear_config_cache(self):
        """清除配置缓存"""
        self._config_cache.clear()
        logger.info("已清除MySQL配置缓存")


# 全局单例实例
_mysql_config_manager = None


def get_mysql_config_manager() -> MySQLConfigManager:
    """获取MySQL配置管理器单例实例"""
    global _mysql_config_manager
    if _mysql_config_manager is None:
        _mysql_config_manager = MySQLConfigManager()
    return _mysql_config_manager
