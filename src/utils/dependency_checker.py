#!/usr/bin/env python3
"""
依赖检查器
检查和验证Python、Node.js和系统依赖的安装状态
"""

import os
import sys
import subprocess
import platform
import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging


class DependencyType(Enum):
    """依赖类型枚举"""
    PYTHON = "python"
    NODEJS = "nodejs"
    SYSTEM = "system"


class DependencyStatus(Enum):
    """依赖状态枚举"""
    INSTALLED = "installed"
    MISSING = "missing"
    OUTDATED = "outdated"
    ERROR = "error"


@dataclass
class DependencyInfo:
    """依赖信息"""
    name: str
    type: DependencyType
    required_version: Optional[str] = None
    installed_version: Optional[str] = None
    status: DependencyStatus = DependencyStatus.MISSING
    description: Optional[str] = None
    install_command: Optional[str] = None
    error_message: Optional[str] = None


class PythonDependencyChecker:
    """Python依赖检查器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
        
        # 查找requirements文件
        self.requirements_files = self._find_requirements_files()
    
    def _find_requirements_files(self) -> List[Path]:
        """查找requirements文件"""
        possible_files = [
            "requirements.txt",
            "requirements-dev.txt",
            "requirements/base.txt",
            "requirements/development.txt",
            "pyproject.toml",
            "setup.py"
        ]
        
        found_files = []
        for file_name in possible_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                found_files.append(file_path)
        
        return found_files
    
    def check_python_version(self) -> DependencyInfo:
        """检查Python版本"""
        try:
            current_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            
            # 检查最低版本要求（Python 3.8+）
            if sys.version_info >= (3, 8):
                status = DependencyStatus.INSTALLED
                error_msg = None
            else:
                status = DependencyStatus.OUTDATED
                error_msg = f"需要Python 3.8+，当前版本: {current_version}"
            
            return DependencyInfo(
                name="python",
                type=DependencyType.PYTHON,
                required_version=">=3.8",
                installed_version=current_version,
                status=status,
                description="Python解释器",
                error_message=error_msg
            )
            
        except Exception as e:
            return DependencyInfo(
                name="python",
                type=DependencyType.PYTHON,
                status=DependencyStatus.ERROR,
                error_message=f"检查Python版本失败: {e}"
            )
    
    def check_pip(self) -> DependencyInfo:
        """检查pip包管理器"""
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # 解析pip版本
                version_match = re.search(r'pip (\d+\.\d+\.\d+)', result.stdout)
                version = version_match.group(1) if version_match else "unknown"
                
                return DependencyInfo(
                    name="pip",
                    type=DependencyType.PYTHON,
                    installed_version=version,
                    status=DependencyStatus.INSTALLED,
                    description="Python包管理器"
                )
            else:
                return DependencyInfo(
                    name="pip",
                    type=DependencyType.PYTHON,
                    status=DependencyStatus.MISSING,
                    description="Python包管理器",
                    install_command="python -m ensurepip --upgrade",
                    error_message="pip未安装或不可用"
                )
                
        except Exception as e:
            return DependencyInfo(
                name="pip",
                type=DependencyType.PYTHON,
                status=DependencyStatus.ERROR,
                error_message=f"检查pip失败: {e}"
            )
    
    def parse_requirements_txt(self, file_path: Path) -> List[Dict[str, str]]:
        """解析requirements.txt文件"""
        requirements = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    
                    # 跳过注释和空行
                    if not line or line.startswith('#'):
                        continue
                    
                    # 跳过-r引用和其他选项
                    if line.startswith('-'):
                        continue
                    
                    # 解析包名和版本
                    if '==' in line:
                        name, version = line.split('==', 1)
                        requirements.append({
                            'name': name.strip(),
                            'version': version.strip(),
                            'operator': '=='
                        })
                    elif '>=' in line:
                        name, version = line.split('>=', 1)
                        requirements.append({
                            'name': name.strip(),
                            'version': version.strip(),
                            'operator': '>='
                        })
                    elif '>' in line:
                        name, version = line.split('>', 1)
                        requirements.append({
                            'name': name.strip(),
                            'version': version.strip(),
                            'operator': '>'
                        })
                    else:
                        requirements.append({
                            'name': line.strip(),
                            'version': None,
                            'operator': None
                        })
        
        except Exception as e:
            self.logger.error(f"解析requirements文件失败: {file_path} - {e}")
        
        return requirements
    
    def check_package_installed(self, package_name: str, required_version: Optional[str] = None) -> DependencyInfo:
        """检查Python包是否已安装"""
        try:
            # 使用pip show检查包信息
            result = subprocess.run([sys.executable, "-m", "pip", "show", package_name], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # 解析包信息
                version_match = re.search(r'Version: (.+)', result.stdout)
                installed_version = version_match.group(1) if version_match else "unknown"
                
                # 检查版本要求
                status = DependencyStatus.INSTALLED
                error_msg = None
                
                if required_version and required_version != installed_version:
                    # 这里可以添加更复杂的版本比较逻辑
                    if required_version.startswith('>='):
                        # 简单的版本比较（实际项目中应使用packaging库）
                        pass
                    else:
                        status = DependencyStatus.OUTDATED
                        error_msg = f"版本不匹配，需要: {required_version}，已安装: {installed_version}"
                
                return DependencyInfo(
                    name=package_name,
                    type=DependencyType.PYTHON,
                    required_version=required_version,
                    installed_version=installed_version,
                    status=status,
                    description=f"Python包: {package_name}",
                    install_command=f"pip install {package_name}" + (f"=={required_version}" if required_version and not required_version.startswith(('>=', '>')) else ""),
                    error_message=error_msg
                )
            else:
                return DependencyInfo(
                    name=package_name,
                    type=DependencyType.PYTHON,
                    required_version=required_version,
                    status=DependencyStatus.MISSING,
                    description=f"Python包: {package_name}",
                    install_command=f"pip install {package_name}" + (f"=={required_version}" if required_version and not required_version.startswith(('>=', '>')) else ""),
                    error_message="包未安装"
                )
                
        except Exception as e:
            return DependencyInfo(
                name=package_name,
                type=DependencyType.PYTHON,
                status=DependencyStatus.ERROR,
                error_message=f"检查包失败: {e}"
            )
    
    def check_all_python_dependencies(self) -> List[DependencyInfo]:
        """检查所有Python依赖"""
        dependencies = []
        
        # 检查Python版本
        dependencies.append(self.check_python_version())
        
        # 检查pip
        dependencies.append(self.check_pip())
        
        # 检查requirements文件中的包
        for req_file in self.requirements_files:
            if req_file.name == "requirements.txt":
                requirements = self.parse_requirements_txt(req_file)
                
                for req in requirements:
                    dep_info = self.check_package_installed(
                        req['name'], 
                        req['version'] if req['operator'] == '==' else None
                    )
                    dependencies.append(dep_info)
        
        return dependencies
    
    def get_missing_dependencies(self) -> List[DependencyInfo]:
        """获取缺失的依赖"""
        all_deps = self.check_all_python_dependencies()
        return [dep for dep in all_deps if dep.status in [DependencyStatus.MISSING, DependencyStatus.OUTDATED]]
    
    def generate_install_commands(self) -> List[str]:
        """生成安装命令"""
        missing_deps = self.get_missing_dependencies()
        commands = []
        
        for dep in missing_deps:
            if dep.install_command and dep.type == DependencyType.PYTHON:
                commands.append(dep.install_command)
        
        return commands


class NodeJSDependencyChecker:
    """Node.js依赖检查器"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)

        # 查找package.json文件
        self.package_json_files = self._find_package_json_files()

    def _find_package_json_files(self) -> List[Path]:
        """查找package.json文件"""
        possible_locations = [
            self.project_root / "package.json",
            self.project_root / "frontend" / "package.json",
            self.project_root / "client" / "package.json",
            self.project_root / "web" / "package.json"
        ]

        found_files = []
        for file_path in possible_locations:
            if file_path.exists():
                found_files.append(file_path)

        return found_files

    def check_nodejs_version(self) -> DependencyInfo:
        """检查Node.js版本"""
        try:
            result = subprocess.run(["node", "--version"],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                version = result.stdout.strip().lstrip('v')

                # 检查最低版本要求（Node.js 16+）
                version_parts = version.split('.')
                major_version = int(version_parts[0])

                if major_version >= 16:
                    status = DependencyStatus.INSTALLED
                    error_msg = None
                else:
                    status = DependencyStatus.OUTDATED
                    error_msg = f"需要Node.js 16+，当前版本: {version}"

                return DependencyInfo(
                    name="nodejs",
                    type=DependencyType.NODEJS,
                    required_version=">=16.0.0",
                    installed_version=version,
                    status=status,
                    description="Node.js运行时",
                    error_message=error_msg
                )
            else:
                return DependencyInfo(
                    name="nodejs",
                    type=DependencyType.NODEJS,
                    status=DependencyStatus.MISSING,
                    description="Node.js运行时",
                    install_command="请从 https://nodejs.org 下载安装Node.js",
                    error_message="Node.js未安装"
                )

        except FileNotFoundError:
            return DependencyInfo(
                name="nodejs",
                type=DependencyType.NODEJS,
                status=DependencyStatus.MISSING,
                description="Node.js运行时",
                install_command="请从 https://nodejs.org 下载安装Node.js",
                error_message="Node.js未安装"
            )
        except Exception as e:
            return DependencyInfo(
                name="nodejs",
                type=DependencyType.NODEJS,
                status=DependencyStatus.ERROR,
                error_message=f"检查Node.js版本失败: {e}"
            )

    def check_npm(self) -> DependencyInfo:
        """检查npm包管理器"""
        try:
            result = subprocess.run(["npm", "--version"],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                version = result.stdout.strip()

                return DependencyInfo(
                    name="npm",
                    type=DependencyType.NODEJS,
                    installed_version=version,
                    status=DependencyStatus.INSTALLED,
                    description="Node.js包管理器"
                )
            else:
                return DependencyInfo(
                    name="npm",
                    type=DependencyType.NODEJS,
                    status=DependencyStatus.MISSING,
                    description="Node.js包管理器",
                    install_command="npm通常随Node.js一起安装",
                    error_message="npm未安装或不可用"
                )

        except FileNotFoundError:
            return DependencyInfo(
                name="npm",
                type=DependencyType.NODEJS,
                status=DependencyStatus.MISSING,
                description="Node.js包管理器",
                install_command="npm通常随Node.js一起安装",
                error_message="npm未安装"
            )
        except Exception as e:
            return DependencyInfo(
                name="npm",
                type=DependencyType.NODEJS,
                status=DependencyStatus.ERROR,
                error_message=f"检查npm失败: {e}"
            )

    def parse_package_json(self, file_path: Path) -> Dict[str, Any]:
        """解析package.json文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"解析package.json失败: {file_path} - {e}")
            return {}

    def check_npm_package_installed(self, package_name: str, required_version: Optional[str] = None,
                                   package_json_dir: Optional[Path] = None) -> DependencyInfo:
        """检查npm包是否已安装"""
        try:
            # 确定检查目录
            check_dir = package_json_dir or self.project_root

            # 使用npm list检查包信息
            result = subprocess.run(["npm", "list", package_name, "--depth=0"],
                                  cwd=check_dir, capture_output=True, text=True, timeout=15)

            if result.returncode == 0 and package_name in result.stdout:
                # 解析版本信息
                version_match = re.search(rf'{re.escape(package_name)}@(\S+)', result.stdout)
                installed_version = version_match.group(1) if version_match else "unknown"

                return DependencyInfo(
                    name=package_name,
                    type=DependencyType.NODEJS,
                    required_version=required_version,
                    installed_version=installed_version,
                    status=DependencyStatus.INSTALLED,
                    description=f"Node.js包: {package_name}",
                    install_command=f"npm install {package_name}" + (f"@{required_version}" if required_version else "")
                )
            else:
                return DependencyInfo(
                    name=package_name,
                    type=DependencyType.NODEJS,
                    required_version=required_version,
                    status=DependencyStatus.MISSING,
                    description=f"Node.js包: {package_name}",
                    install_command=f"npm install {package_name}" + (f"@{required_version}" if required_version else ""),
                    error_message="包未安装"
                )

        except Exception as e:
            return DependencyInfo(
                name=package_name,
                type=DependencyType.NODEJS,
                status=DependencyStatus.ERROR,
                error_message=f"检查包失败: {e}"
            )

    def check_all_nodejs_dependencies(self) -> List[DependencyInfo]:
        """检查所有Node.js依赖"""
        dependencies = []

        # 检查Node.js版本
        dependencies.append(self.check_nodejs_version())

        # 检查npm
        dependencies.append(self.check_npm())

        # 检查package.json文件中的依赖
        for package_json_file in self.package_json_files:
            package_data = self.parse_package_json(package_json_file)
            package_dir = package_json_file.parent

            # 检查dependencies
            deps = package_data.get('dependencies', {})
            for package_name, version in deps.items():
                dep_info = self.check_npm_package_installed(
                    package_name,
                    version.lstrip('^~>=<'),
                    package_dir
                )
                dependencies.append(dep_info)

            # 检查devDependencies（开发依赖）
            dev_deps = package_data.get('devDependencies', {})
            for package_name, version in dev_deps.items():
                dep_info = self.check_npm_package_installed(
                    package_name,
                    version.lstrip('^~>=<'),
                    package_dir
                )
                dep_info.description += " (开发依赖)"
                dependencies.append(dep_info)

        return dependencies

    def get_missing_nodejs_dependencies(self) -> List[DependencyInfo]:
        """获取缺失的Node.js依赖"""
        all_deps = self.check_all_nodejs_dependencies()
        return [dep for dep in all_deps if dep.status in [DependencyStatus.MISSING, DependencyStatus.OUTDATED]]

    def generate_nodejs_install_commands(self) -> List[str]:
        """生成Node.js安装命令"""
        missing_deps = self.get_missing_nodejs_dependencies()
        commands = []

        # 按目录分组命令
        dir_commands = {}

        for dep in missing_deps:
            if dep.install_command and dep.type == DependencyType.NODEJS and dep.name not in ['nodejs', 'npm']:
                # 假设在项目根目录或frontend目录执行
                target_dir = "frontend" if (self.project_root / "frontend" / "package.json").exists() else "."

                if target_dir not in dir_commands:
                    dir_commands[target_dir] = []

                dir_commands[target_dir].append(dep.install_command)

        # 生成命令列表
        for directory, cmds in dir_commands.items():
            if directory != ".":
                commands.append(f"cd {directory}")
            commands.extend(cmds)
            if directory != ".":
                commands.append("cd ..")

        return commands


class SystemDependencyChecker:
    """系统依赖检查器"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
        self.system = platform.system()

    def check_git(self) -> DependencyInfo:
        """检查Git版本控制系统"""
        try:
            result = subprocess.run(["git", "--version"],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # 解析Git版本
                version_match = re.search(r'git version (\d+\.\d+\.\d+)', result.stdout)
                version = version_match.group(1) if version_match else "unknown"

                return DependencyInfo(
                    name="git",
                    type=DependencyType.SYSTEM,
                    installed_version=version,
                    status=DependencyStatus.INSTALLED,
                    description="Git版本控制系统"
                )
            else:
                return DependencyInfo(
                    name="git",
                    type=DependencyType.SYSTEM,
                    status=DependencyStatus.MISSING,
                    description="Git版本控制系统",
                    install_command=self._get_git_install_command(),
                    error_message="Git未安装"
                )

        except FileNotFoundError:
            return DependencyInfo(
                name="git",
                type=DependencyType.SYSTEM,
                status=DependencyStatus.MISSING,
                description="Git版本控制系统",
                install_command=self._get_git_install_command(),
                error_message="Git未安装"
            )
        except Exception as e:
            return DependencyInfo(
                name="git",
                type=DependencyType.SYSTEM,
                status=DependencyStatus.ERROR,
                error_message=f"检查Git失败: {e}"
            )

    def _get_git_install_command(self) -> str:
        """获取Git安装命令"""
        if self.system == "Darwin":
            return "brew install git 或从 https://git-scm.com 下载"
        elif self.system == "Linux":
            return "sudo apt-get install git 或 sudo yum install git"
        elif self.system == "Windows":
            return "从 https://git-scm.com 下载Git for Windows"
        else:
            return "请从 https://git-scm.com 下载安装Git"

    def check_database_tools(self) -> List[DependencyInfo]:
        """检查数据库工具"""
        dependencies = []

        # 检查SQLite
        sqlite_info = self._check_command("sqlite3", "SQLite数据库", "sqlite3 --version")
        dependencies.append(sqlite_info)

        # 检查PostgreSQL客户端（可选）
        psql_info = self._check_command("psql", "PostgreSQL客户端", "psql --version", optional=True)
        dependencies.append(psql_info)

        # 检查MySQL客户端（可选）
        mysql_info = self._check_command("mysql", "MySQL客户端", "mysql --version", optional=True)
        dependencies.append(mysql_info)

        return dependencies

    def check_development_tools(self) -> List[DependencyInfo]:
        """检查开发工具"""
        dependencies = []

        # 检查curl
        curl_info = self._check_command("curl", "HTTP客户端工具", "curl --version")
        dependencies.append(curl_info)

        # 检查wget（可选）
        wget_info = self._check_command("wget", "下载工具", "wget --version", optional=True)
        dependencies.append(wget_info)

        # 检查make（可选）
        make_info = self._check_command("make", "构建工具", "make --version", optional=True)
        dependencies.append(make_info)

        return dependencies

    def check_platform_specific_tools(self) -> List[DependencyInfo]:
        """检查平台特定工具"""
        dependencies = []

        if self.system == "Darwin":
            # macOS特定工具
            xcode_info = self._check_xcode_tools()
            dependencies.append(xcode_info)

            homebrew_info = self._check_command("brew", "Homebrew包管理器", "brew --version", optional=True)
            dependencies.append(homebrew_info)

        elif self.system == "Linux":
            # Linux特定工具
            gcc_info = self._check_command("gcc", "GCC编译器", "gcc --version", optional=True)
            dependencies.append(gcc_info)

            # 检查包管理器
            if self._command_exists("apt-get"):
                apt_info = DependencyInfo(
                    name="apt",
                    type=DependencyType.SYSTEM,
                    status=DependencyStatus.INSTALLED,
                    description="APT包管理器"
                )
                dependencies.append(apt_info)
            elif self._command_exists("yum"):
                yum_info = DependencyInfo(
                    name="yum",
                    type=DependencyType.SYSTEM,
                    status=DependencyStatus.INSTALLED,
                    description="YUM包管理器"
                )
                dependencies.append(yum_info)

        elif self.system == "Windows":
            # Windows特定工具
            powershell_info = self._check_command("powershell", "PowerShell", "powershell -Command '$PSVersionTable.PSVersion'")
            dependencies.append(powershell_info)

            # 检查Windows包管理器
            choco_info = self._check_command("choco", "Chocolatey包管理器", "choco --version", optional=True)
            dependencies.append(choco_info)

        return dependencies

    def _check_command(self, command: str, description: str, version_cmd: str, optional: bool = False) -> DependencyInfo:
        """检查命令是否可用"""
        try:
            result = subprocess.run(version_cmd.split(),
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # 尝试解析版本
                version = self._extract_version(result.stdout)

                return DependencyInfo(
                    name=command,
                    type=DependencyType.SYSTEM,
                    installed_version=version,
                    status=DependencyStatus.INSTALLED,
                    description=description + (" (可选)" if optional else "")
                )
            else:
                status = DependencyStatus.MISSING if not optional else DependencyStatus.MISSING
                return DependencyInfo(
                    name=command,
                    type=DependencyType.SYSTEM,
                    status=status,
                    description=description + (" (可选)" if optional else ""),
                    install_command=self._get_install_command(command),
                    error_message=f"{command}未安装" + (" (可选依赖)" if optional else "")
                )

        except FileNotFoundError:
            status = DependencyStatus.MISSING if not optional else DependencyStatus.MISSING
            return DependencyInfo(
                name=command,
                type=DependencyType.SYSTEM,
                status=status,
                description=description + (" (可选)" if optional else ""),
                install_command=self._get_install_command(command),
                error_message=f"{command}未安装" + (" (可选依赖)" if optional else "")
            )
        except Exception as e:
            return DependencyInfo(
                name=command,
                type=DependencyType.SYSTEM,
                status=DependencyStatus.ERROR,
                error_message=f"检查{command}失败: {e}"
            )

    def _check_xcode_tools(self) -> DependencyInfo:
        """检查Xcode命令行工具（macOS）"""
        try:
            result = subprocess.run(["xcode-select", "-p"],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                return DependencyInfo(
                    name="xcode-tools",
                    type=DependencyType.SYSTEM,
                    status=DependencyStatus.INSTALLED,
                    description="Xcode命令行工具"
                )
            else:
                return DependencyInfo(
                    name="xcode-tools",
                    type=DependencyType.SYSTEM,
                    status=DependencyStatus.MISSING,
                    description="Xcode命令行工具",
                    install_command="xcode-select --install",
                    error_message="Xcode命令行工具未安装"
                )

        except Exception as e:
            return DependencyInfo(
                name="xcode-tools",
                type=DependencyType.SYSTEM,
                status=DependencyStatus.ERROR,
                error_message=f"检查Xcode工具失败: {e}"
            )

    def _command_exists(self, command: str) -> bool:
        """检查命令是否存在"""
        try:
            subprocess.run([command, "--version"],
                         capture_output=True, timeout=5)
            return True
        except:
            return False

    def _extract_version(self, output: str) -> str:
        """从输出中提取版本号"""
        # 常见的版本号模式
        patterns = [
            r'(\d+\.\d+\.\d+)',
            r'version (\d+\.\d+\.\d+)',
            r'v(\d+\.\d+\.\d+)',
            r'(\d+\.\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, output)
            if match:
                return match.group(1)

        return "unknown"

    def _get_install_command(self, command: str) -> str:
        """获取安装命令"""
        install_commands = {
            "Darwin": {
                "sqlite3": "brew install sqlite3",
                "psql": "brew install postgresql",
                "mysql": "brew install mysql",
                "curl": "brew install curl",
                "wget": "brew install wget",
                "make": "brew install make"
            },
            "Linux": {
                "sqlite3": "sudo apt-get install sqlite3 或 sudo yum install sqlite",
                "psql": "sudo apt-get install postgresql-client 或 sudo yum install postgresql",
                "mysql": "sudo apt-get install mysql-client 或 sudo yum install mysql",
                "curl": "sudo apt-get install curl 或 sudo yum install curl",
                "wget": "sudo apt-get install wget 或 sudo yum install wget",
                "make": "sudo apt-get install make 或 sudo yum install make"
            },
            "Windows": {
                "sqlite3": "choco install sqlite 或从官网下载",
                "psql": "choco install postgresql 或从官网下载",
                "mysql": "choco install mysql 或从官网下载",
                "curl": "curl通常已内置在Windows 10+",
                "wget": "choco install wget",
                "make": "choco install make"
            }
        }

        return install_commands.get(self.system, {}).get(command, f"请安装 {command}")

    def check_all_system_dependencies(self) -> List[DependencyInfo]:
        """检查所有系统依赖"""
        dependencies = []

        # 检查Git
        dependencies.append(self.check_git())

        # 检查数据库工具
        dependencies.extend(self.check_database_tools())

        # 检查开发工具
        dependencies.extend(self.check_development_tools())

        # 检查平台特定工具
        dependencies.extend(self.check_platform_specific_tools())

        return dependencies

    def get_missing_system_dependencies(self) -> List[DependencyInfo]:
        """获取缺失的系统依赖"""
        all_deps = self.check_all_system_dependencies()
        return [dep for dep in all_deps if dep.status in [DependencyStatus.MISSING, DependencyStatus.OUTDATED]]

    def generate_system_install_commands(self) -> List[str]:
        """生成系统安装命令"""
        missing_deps = self.get_missing_system_dependencies()
        commands = []

        for dep in missing_deps:
            if dep.install_command and dep.type == DependencyType.SYSTEM:
                commands.append(dep.install_command)

        return commands


def main():
    """主函数 - 测试依赖检查器"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)

    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent

    print("🔧 依赖检查器测试")
    print("=" * 50)

    # 测试Python依赖检查器
    print("\n🐍 Python依赖检查:")
    print("-" * 30)

    python_checker = PythonDependencyChecker(project_root)
    python_dependencies = python_checker.check_all_python_dependencies()

    print(f"发现 {len(python_dependencies)} 个Python依赖:")
    for dep in python_dependencies:
        status_icon = {
            DependencyStatus.INSTALLED: "✅",
            DependencyStatus.MISSING: "❌",
            DependencyStatus.OUTDATED: "⚠️",
            DependencyStatus.ERROR: "💥"
        }.get(dep.status, "❓")

        print(f"  {status_icon} {dep.name}: {dep.status.value}")
        if dep.installed_version:
            print(f"    已安装版本: {dep.installed_version}")
        if dep.error_message:
            print(f"    错误: {dep.error_message}")

    # 测试Node.js依赖检查器
    print("\n📦 Node.js依赖检查:")
    print("-" * 30)

    nodejs_checker = NodeJSDependencyChecker(project_root)
    nodejs_dependencies = nodejs_checker.check_all_nodejs_dependencies()

    print(f"发现 {len(nodejs_dependencies)} 个Node.js依赖:")
    for dep in nodejs_dependencies:
        status_icon = {
            DependencyStatus.INSTALLED: "✅",
            DependencyStatus.MISSING: "❌",
            DependencyStatus.OUTDATED: "⚠️",
            DependencyStatus.ERROR: "💥"
        }.get(dep.status, "❓")

        print(f"  {status_icon} {dep.name}: {dep.status.value}")
        if dep.installed_version:
            print(f"    已安装版本: {dep.installed_version}")
        if dep.error_message:
            print(f"    错误: {dep.error_message}")

    # 测试系统依赖检查器
    print("\n🖥️ 系统依赖检查:")
    print("-" * 30)

    system_checker = SystemDependencyChecker(project_root)
    system_dependencies = system_checker.check_all_system_dependencies()

    print(f"发现 {len(system_dependencies)} 个系统依赖:")
    for dep in system_dependencies:
        status_icon = {
            DependencyStatus.INSTALLED: "✅",
            DependencyStatus.MISSING: "❌",
            DependencyStatus.OUTDATED: "⚠️",
            DependencyStatus.ERROR: "💥"
        }.get(dep.status, "❓")

        print(f"  {status_icon} {dep.name}: {dep.status.value}")
        if dep.installed_version:
            print(f"    已安装版本: {dep.installed_version}")
        if dep.error_message:
            print(f"    错误: {dep.error_message}")

    # 显示缺失的依赖汇总
    python_missing = python_checker.get_missing_dependencies()
    nodejs_missing = nodejs_checker.get_missing_nodejs_dependencies()
    system_missing = system_checker.get_missing_system_dependencies()

    if python_missing or nodejs_missing or system_missing:
        print(f"\n❌ 缺失依赖汇总:")
        if python_missing:
            print(f"  Python: {len(python_missing)} 个缺失")
        if nodejs_missing:
            print(f"  Node.js: {len(nodejs_missing)} 个缺失")
        if system_missing:
            print(f"  系统: {len(system_missing)} 个缺失")

        print(f"\n🔧 建议的安装命令:")
        python_commands = python_checker.generate_install_commands()
        nodejs_commands = nodejs_checker.generate_nodejs_install_commands()
        system_commands = system_checker.generate_system_install_commands()

        if python_commands:
            print("  Python:")
            for cmd in python_commands:
                print(f"    {cmd}")

        if nodejs_commands:
            print("  Node.js:")
            for cmd in nodejs_commands:
                print(f"    {cmd}")

        if system_commands:
            print("  系统:")
            for cmd in system_commands:
                print(f"    {cmd}")
    else:
        print("\n✅ 所有依赖都已正确安装!")

    print("\n✅ 依赖检查器测试完成")


if __name__ == "__main__":
    main()
