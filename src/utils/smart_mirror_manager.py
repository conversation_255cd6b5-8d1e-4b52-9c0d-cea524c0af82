#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能镜像源管理器
实现镜像源的智能选择、健康检查和故障转移功能
"""

import asyncio
import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import aiohttp
import requests
from concurrent.futures import ThreadPoolExecutor

from utils.config_loader import ConfigLoader

logger = logging.getLogger(__name__)

@dataclass
class MirrorInfo:
    """镜像源信息"""
    name: str
    host: str
    url: str
    priority: int
    region: str
    features: List[str]
    # 性能指标
    response_time: float = 0.0
    success_rate: float = 1.0
    last_check: Optional[datetime] = None
    is_healthy: bool = True
    failure_count: int = 0
    
    def to_dict(self):
        """转换为字典，便于序列化"""
        data = asdict(self)
        if self.last_check:
            data['last_check'] = self.last_check.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: dict):
        """从字典创建实例"""
        if 'last_check' in data and data['last_check']:
            data['last_check'] = datetime.fromisoformat(data['last_check'])
        return cls(**data)

class SmartMirrorManager:
    """智能镜像源管理器"""
    
    def __init__(self, config_loader: Optional[ConfigLoader] = None):
        self.config_loader = config_loader or ConfigLoader()
        self.network_config = self._load_network_config()
        self.mirrors: List[MirrorInfo] = []
        self.cache_file = Path("cache/mirror_performance.json")
        self.selected_mirror: Optional[MirrorInfo] = None
        
        # 确保缓存目录存在
        self.cache_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化镜像源
        self._initialize_mirrors()
        
        # 加载缓存的性能数据
        self._load_cached_performance()
    
    def _load_network_config(self) -> Dict:
        """加载网络配置"""
        try:
            full_config = self.config_loader.get_config("dev")  # 使用dev环境配置
            network_config = full_config.get("network", {})
            
            # 如果dev环境没有网络配置，尝试从全局配置加载
            if not network_config:
                # 加载全局网络配置
                global_config = self.config_loader._load_config()
                network_config = global_config.get("network", {})
                logger.info(f"从全局配置加载网络配置: {len(network_config)} 项")
            else:
                logger.info(f"从dev环境配置加载网络配置: {len(network_config)} 项")
            
            return network_config
        except Exception as e:
            logger.warning(f"加载网络配置失败: {e}, 使用默认配置")
            return {}
    
    def _initialize_mirrors(self):
        """初始化镜像源列表"""
        # 加载中国镜像源
        china_mirrors = self.network_config.get("china_mirrors", [])
        for mirror_data in china_mirrors:
            mirror = MirrorInfo(**mirror_data)
            self.mirrors.append(mirror)
        
        # 添加默认PyPI源
        default_pypi = self.network_config.get("default_pypi", {
            "name": "PyPI官方",
            "host": "pypi.org", 
            "url": "https://pypi.org/simple/",
            "priority": 0,
            "region": "海外",
            "features": ["官方源"]
        })
        self.mirrors.append(MirrorInfo(**default_pypi))
        
        logger.info(f"初始化了 {len(self.mirrors)} 个镜像源")
    
    def _load_cached_performance(self):
        """加载缓存的性能数据"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                
                # 检查缓存是否过期
                cache_time = datetime.fromisoformat(cached_data.get('timestamp', '1970-01-01'))
                cache_duration = self.network_config.get('mirror_selection', {}).get('cache_duration', 24)
                
                if datetime.now() - cache_time < timedelta(hours=cache_duration):
                    # 更新镜像源性能数据
                    for mirror in self.mirrors:
                        mirror_cache = cached_data.get('mirrors', {}).get(mirror.host, {})
                        if mirror_cache:
                            mirror.response_time = mirror_cache.get('response_time', 0.0)
                            mirror.success_rate = mirror_cache.get('success_rate', 1.0)
                            mirror.is_healthy = mirror_cache.get('is_healthy', True)
                            mirror.failure_count = mirror_cache.get('failure_count', 0)
                            if 'last_check' in mirror_cache:
                                mirror.last_check = datetime.fromisoformat(mirror_cache['last_check'])
                    
                    logger.info("已加载缓存的镜像源性能数据")
                else:
                    logger.info("缓存已过期，将重新测试镜像源性能")
        except Exception as e:
            logger.warning(f"加载缓存性能数据失败: {e}")
    
    def _save_performance_cache(self):
        """保存性能数据到缓存"""
        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'mirrors': {}
            }
            
            for mirror in self.mirrors:
                cache_data['mirrors'][mirror.host] = {
                    'response_time': mirror.response_time,
                    'success_rate': mirror.success_rate,
                    'is_healthy': mirror.is_healthy,
                    'failure_count': mirror.failure_count,
                    'last_check': mirror.last_check.isoformat() if mirror.last_check else None
                }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            
            logger.debug("已保存镜像源性能数据到缓存")
        except Exception as e:
            logger.warning(f"保存性能缓存失败: {e}")
    
    async def test_mirror_performance(self, mirror: MirrorInfo) -> Tuple[float, bool]:
        """测试单个镜像源的性能"""
        mirror_config = self.network_config.get('mirror_selection', {})
        timeout = mirror_config.get('connection_timeout', 5)
        
        try:
            start_time = time.time()
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                # 测试基本连接
                async with session.get(mirror.url) as response:
                    if response.status == 200:
                        response_time = time.time() - start_time
                        mirror.response_time = response_time
                        mirror.is_healthy = True
                        mirror.failure_count = 0
                        mirror.last_check = datetime.now()
                        
                        logger.debug(f"镜像源 {mirror.name} 测试成功: {response_time:.2f}s")
                        return response_time, True
                    else:
                        logger.warning(f"镜像源 {mirror.name} 返回状态码: {response.status}")
                        return float('inf'), False
                        
        except Exception as e:
            logger.warning(f"镜像源 {mirror.name} 测试失败: {e}")
            mirror.failure_count += 1
            mirror.is_healthy = False
            mirror.last_check = datetime.now()
            return float('inf'), False
    
    async def test_all_mirrors_async(self) -> Dict[str, Tuple[float, bool]]:
        """异步测试所有镜像源性能"""
        mirror_config = self.network_config.get('mirror_selection', {})
        concurrent_tests = mirror_config.get('concurrent_tests', 3)
        
        logger.info(f"开始测试 {len(self.mirrors)} 个镜像源的性能...")
        
        # 使用信号量限制并发数
        semaphore = asyncio.Semaphore(concurrent_tests)
        
        async def test_with_semaphore(mirror):
            async with semaphore:
                return mirror.host, await self.test_mirror_performance(mirror)
        
        # 并发测试所有镜像源
        tasks = [test_with_semaphore(mirror) for mirror in self.mirrors]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        performance_results = {}
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"镜像源测试异常: {result}")
                continue
            host, (response_time, is_healthy) = result
            performance_results[host] = (response_time, is_healthy)
        
        logger.info(f"镜像源性能测试完成，共测试 {len(performance_results)} 个")
        return performance_results
    
    def test_all_mirrors_sync(self) -> Dict[str, Tuple[float, bool]]:
        """同步方式测试所有镜像源（用于同步调用）"""
        try:
            # 在新的事件循环中运行异步测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.test_all_mirrors_async())
        except Exception as e:
            logger.error(f"同步测试镜像源失败: {e}")
            return {}
        finally:
            loop.close()
    
    def select_best_mirror(self, strategy: Optional[str] = None) -> MirrorInfo:
        """选择最佳镜像源"""
        if not self.mirrors:
            raise ValueError("没有可用的镜像源")
        
        strategy = strategy or self.network_config.get('mirror_selection', {}).get('strategy', 'auto')
        
        # 过滤出健康的镜像源
        healthy_mirrors = [m for m in self.mirrors if m.is_healthy]
        if not healthy_mirrors:
            logger.warning("没有健康的镜像源，使用所有镜像源")
            healthy_mirrors = self.mirrors
        
        if strategy == 'speed':
            # 速度优先：选择响应时间最短的
            selected = min(healthy_mirrors, key=lambda m: m.response_time)
        elif strategy == 'stability':
            # 稳定性优先：选择成功率最高且优先级最高的
            selected = max(healthy_mirrors, key=lambda m: (m.success_rate, -m.priority, -m.response_time))
        elif strategy == 'region':
            # 地区优先：优先选择中国地区的镜像源
            china_mirrors = [m for m in healthy_mirrors if m.region != '海外']
            if china_mirrors:
                selected = min(china_mirrors, key=lambda m: (m.priority, m.response_time))
            else:
                selected = min(healthy_mirrors, key=lambda m: m.response_time)
        else:  # auto
            # 自动选择：优先选择国内镜像源，然后综合考虑性能
            china_mirrors = [m for m in healthy_mirrors if m.region != '海外']
            
            if china_mirrors:
                # 有国内镜像源时，从国内镜像源中选择最佳的
                def china_score(mirror):
                    # 优先级权重 (越小越好)
                    priority_score = mirror.priority * 2
                    # 响应时间权重 (越小越好)  
                    time_score = min(mirror.response_time, 10) * 3
                    # 成功率权重 (越大越好)
                    success_score = (1 - mirror.success_rate) * 5
                    return priority_score + time_score + success_score
                
                selected = min(china_mirrors, key=china_score)
            else:
                # 没有国内镜像源时，选择最佳的海外镜像源
                def overseas_score(mirror):
                    # 响应时间和成功率为主要考虑因素
                    time_score = min(mirror.response_time, 10) * 4
                    success_score = (1 - mirror.success_rate) * 6
                    return time_score + success_score
                
                selected = min(healthy_mirrors, key=overseas_score)
        
        self.selected_mirror = selected
        logger.info(f"选择镜像源: {selected.name} ({selected.url}) - 响应时间: {selected.response_time:.2f}s")
        return selected
    
    def get_best_mirror_url(self, test_performance: bool = True) -> str:
        """获取最佳镜像源URL"""
        # 如果需要测试性能且缓存过期，则重新测试
        if test_performance:
            cache_duration = self.network_config.get('mirror_selection', {}).get('cache_duration', 24)
            need_test = True
            
            if self.mirrors and self.mirrors[0].last_check:
                time_since_check = datetime.now() - self.mirrors[0].last_check
                need_test = time_since_check > timedelta(hours=cache_duration)
            
            if need_test:
                logger.info("缓存已过期，重新测试镜像源性能...")
                self.test_all_mirrors_sync()
                self._save_performance_cache()
        
        # 选择最佳镜像源
        best_mirror = self.select_best_mirror()
        return best_mirror.url
    
    def get_mirror_with_failover(self, package_name: Optional[str] = None) -> List[str]:
        """获取带故障转移的镜像源列表"""
        # 检测网络环境，中国网络优先使用国内镜像源
        healthy_mirrors = [m for m in self.mirrors if m.is_healthy]
        if not healthy_mirrors:
            healthy_mirrors = self.mirrors
        
        # 分离国内和海外镜像源
        china_mirrors = [m for m in healthy_mirrors if m.region != '海外']
        overseas_mirrors = [m for m in healthy_mirrors if m.region == '海外']
        
        # 国内镜像源按优先级排序（数字越小优先级越高）
        china_mirrors.sort(key=lambda m: (m.priority, m.response_time))
        # 海外镜像源按响应时间排序
        overseas_mirrors.sort(key=lambda m: m.response_time)
        
        # 中国网络环境：国内镜像源优先，海外镜像源作为备选
        # 故障转移顺序：清华 -> 阿里云 -> 华为云 -> 腾讯云 -> 中科大 -> 豆瓣 -> PyPI官方
        sorted_mirrors = china_mirrors + overseas_mirrors
        
        # 返回URL列表用于故障转移
        return [mirror.url for mirror in sorted_mirrors]
    
    def report_mirror_failure(self, mirror_url: str):
        """报告镜像源故障"""
        for mirror in self.mirrors:
            if mirror.url == mirror_url:
                mirror.failure_count += 1
                
                # 检查是否需要标记为不健康
                health_config = self.network_config.get('health_check', {})
                failure_threshold = health_config.get('failure_threshold', 3)
                
                if mirror.failure_count >= failure_threshold:
                    mirror.is_healthy = False
                    logger.warning(f"镜像源 {mirror.name} 已标记为不健康 (失败次数: {mirror.failure_count})")
                
                break
    
    def get_mirror_status(self) -> Dict:
        """获取所有镜像源的状态信息"""
        status = {
            'total_mirrors': len(self.mirrors),
            'healthy_mirrors': len([m for m in self.mirrors if m.is_healthy]),
            'selected_mirror': self.selected_mirror.name if self.selected_mirror else None,
            'mirrors': []
        }
        
        for mirror in sorted(self.mirrors, key=lambda m: (m.priority, m.response_time)):
            mirror_status = {
                'name': mirror.name,
                'url': mirror.url,
                'region': mirror.region,
                'priority': mirror.priority,
                'response_time': f"{mirror.response_time:.2f}s" if mirror.response_time > 0 else "未测试",
                'success_rate': f"{mirror.success_rate:.1%}",
                'is_healthy': "✅" if mirror.is_healthy else "❌",
                'failure_count': mirror.failure_count,
                'last_check': mirror.last_check.strftime("%Y-%m-%d %H:%M:%S") if mirror.last_check else "从未",
                'features': mirror.features
            }
            status['mirrors'].append(mirror_status)
        
        return status

# 便捷函数
def get_best_mirror_url(test_performance: bool = True) -> str:
    """获取最佳镜像源URL的便捷函数"""
    manager = SmartMirrorManager()
    return manager.get_best_mirror_url(test_performance)

def get_mirrors_with_failover() -> List[str]:
    """获取带故障转移的镜像源列表的便捷函数"""
    manager = SmartMirrorManager()
    return manager.get_mirror_with_failover()