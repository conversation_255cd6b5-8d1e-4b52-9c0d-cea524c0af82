#!/usr/bin/env python3
"""
时间工具模块
提供标准化的时间处理功能

特性：
- 北京时间标准化处理
- 时间格式转换
- 时间戳生成
- 时区转换
"""

import pytz
from datetime import datetime, timezone
from typing import Optional, Union


# 北京时区
BEIJING_TZ = pytz.timezone("Asia/Shanghai")


def get_beijing_time_now() -> datetime:
    """
    获取当前北京时间

    Returns:
        datetime: 当前北京时间（带时区信息）
    """
    return datetime.now(BEIJING_TZ)


def get_utc_time_now() -> datetime:
    """
    获取当前UTC时间

    Returns:
        datetime: 当前UTC时间（带时区信息）
    """
    return datetime.now(timezone.utc)


def beijing_to_utc(beijing_time: datetime) -> datetime:
    """
    将北京时间转换为UTC时间

    Args:
        beijing_time: 北京时间

    Returns:
        datetime: UTC时间
    """
    if beijing_time.tzinfo is None:
        # 如果没有时区信息，假设为北京时间
        beijing_time = BEIJING_TZ.localize(beijing_time)

    return beijing_time.astimezone(timezone.utc)


def utc_to_beijing(utc_time: datetime) -> datetime:
    """
    将UTC时间转换为北京时间

    Args:
        utc_time: UTC时间

    Returns:
        datetime: 北京时间
    """
    if utc_time.tzinfo is None:
        # 如果没有时区信息，假设为UTC时间
        utc_time = utc_time.replace(tzinfo=timezone.utc)

    return utc_time.astimezone(BEIJING_TZ)


def format_datetime(dt: datetime, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化日期时间

    Args:
        dt: 日期时间对象
        fmt: 格式字符串，默认为 "%Y-%m-%d %H:%M:%S"

    Returns:
        str: 格式化后的日期时间字符串
    """
    return dt.strftime(fmt)


def parse_datetime(datetime_str: str, fmt: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """
    解析日期时间字符串

    Args:
        datetime_str: 日期时间字符串
        fmt: 格式字符串，默认为 "%Y-%m-%d %H:%M:%S"

    Returns:
        datetime: 解析后的日期时间对象（北京时间）
    """
    dt = datetime.strptime(datetime_str, fmt)
    return BEIJING_TZ.localize(dt)


def get_date_string(dt: Optional[datetime] = None) -> str:
    """
    获取日期字符串

    Args:
        dt: 日期时间对象，默认为当前北京时间

    Returns:
        str: 日期字符串 (YYYY-MM-DD)
    """
    if dt is None:
        dt = get_beijing_time_now()
    return dt.strftime("%Y-%m-%d")


def get_time_string(dt: Optional[datetime] = None) -> str:
    """
    获取时间字符串

    Args:
        dt: 日期时间对象，默认为当前北京时间

    Returns:
        str: 时间字符串 (HH:MM:SS)
    """
    if dt is None:
        dt = get_beijing_time_now()
    return dt.strftime("%H:%M:%S")


def get_datetime_string(dt: Optional[datetime] = None) -> str:
    """
    获取日期时间字符串

    Args:
        dt: 日期时间对象，默认为当前北京时间

    Returns:
        str: 日期时间字符串 (YYYY-MM-DD HH:MM:SS)
    """
    if dt is None:
        dt = get_beijing_time_now()
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def get_timestamp_string(dt: Optional[datetime] = None) -> str:
    """
    获取时间戳字符串

    Args:
        dt: 日期时间对象，默认为当前北京时间

    Returns:
        str: 时间戳字符串 (YYYYMMDD_HHMMSS)
    """
    if dt is None:
        dt = get_beijing_time_now()
    return dt.strftime("%Y%m%d_%H%M%S")


def get_iso_string(dt: Optional[datetime] = None) -> str:
    """
    获取ISO格式时间字符串

    Args:
        dt: 日期时间对象，默认为当前北京时间

    Returns:
        str: ISO格式时间字符串
    """
    if dt is None:
        dt = get_beijing_time_now()
    return dt.isoformat()


def is_trading_day(dt: datetime) -> bool:
    """
    判断是否为交易日（简单版本：周一到周五）

    Args:
        dt: 日期时间对象

    Returns:
        bool: 是否为交易日
    """
    # 周一到周五为交易日 (0=周一, 6=周日)
    return dt.weekday() < 5


def is_trading_time(dt: datetime) -> bool:
    """
    判断是否为交易时间（简单版本：9:00-15:00）

    Args:
        dt: 日期时间对象

    Returns:
        bool: 是否为交易时间
    """
    if not is_trading_day(dt):
        return False

    # 简单判断：9:00-15:00为交易时间
    hour = dt.hour
    return 9 <= hour < 15


def get_next_trading_day(dt: datetime) -> datetime:
    """
    获取下一个交易日

    Args:
        dt: 基准日期时间

    Returns:
        datetime: 下一个交易日的日期时间
    """
    from datetime import timedelta

    next_day = dt + timedelta(days=1)

    # 如果是周末，继续往后推
    while not is_trading_day(next_day):
        next_day += timedelta(days=1)

    return next_day


def get_previous_trading_day(dt: datetime) -> datetime:
    """
    获取前一个交易日

    Args:
        dt: 基准日期时间

    Returns:
        datetime: 前一个交易日的日期时间
    """
    from datetime import timedelta

    prev_day = dt - timedelta(days=1)

    # 如果是周末，继续往前推
    while not is_trading_day(prev_day):
        prev_day -= timedelta(days=1)

    return prev_day


def calculate_time_difference(start_time: datetime, end_time: datetime) -> dict:
    """
    计算时间差

    Args:
        start_time: 开始时间
        end_time: 结束时间

    Returns:
        dict: 时间差信息
    """
    delta = end_time - start_time

    days = delta.days
    hours, remainder = divmod(delta.seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    return {
        "days": days,
        "hours": hours,
        "minutes": minutes,
        "seconds": seconds,
        "total_seconds": delta.total_seconds(),
        "total_minutes": delta.total_seconds() / 60,
        "total_hours": delta.total_seconds() / 3600,
        "total_days": delta.total_seconds() / 86400,
    }


def format_time_difference(time_diff: dict) -> str:
    """
    格式化时间差显示

    Args:
        time_diff: 时间差信息字典

    Returns:
        str: 格式化后的时间差字符串
    """
    parts = []

    if time_diff["days"] > 0:
        parts.append(f"{time_diff['days']}天")

    if time_diff["hours"] > 0:
        parts.append(f"{time_diff['hours']}小时")

    if time_diff["minutes"] > 0:
        parts.append(f"{time_diff['minutes']}分钟")

    if time_diff["seconds"] > 0 or not parts:
        parts.append(f"{time_diff['seconds']}秒")

    return " ".join(parts)


def format_duration(seconds: float) -> str:
    """
    格式化持续时间为可读字符串

    Args:
        seconds: 持续时间（秒）

    Returns:
        str: 格式化后的持续时间字符串
    """
    if seconds < 0.001:
        return f"{seconds * 1000000:.2f}μs"
    elif seconds < 1:
        return f"{seconds * 1000:.2f}ms"
    elif seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m{remaining_seconds:.1f}s"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        remaining_seconds = seconds % 60
        return f"{hours}h{remaining_minutes}m{remaining_seconds:.1f}s"


def get_market_session_times():
    """
    获取交易时段时间

    Returns:
        dict: 交易时段时间信息
    """
    return {
        "morning_session": {"start": "09:30", "end": "11:30"},
        "afternoon_session": {"start": "13:00", "end": "15:00"},
        "night_session": {"start": "21:00", "end": "23:00"},
    }


def normalize_datetime_to_beijing(dt: Union[datetime, str]) -> datetime:
    """
    将日期时间标准化为北京时间

    Args:
        dt: 日期时间对象或字符串

    Returns:
        datetime: 标准化的北京时间
    """
    if isinstance(dt, str):
        # 尝试解析常见的时间格式
        formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d %H:%M:%S.%f",
            "%Y-%m-%d",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d",
        ]

        for fmt in formats:
            try:
                dt = datetime.strptime(dt, fmt)
                break
            except ValueError:
                continue
        else:
            raise ValueError(f"无法解析时间字符串: {dt}")

    # 如果没有时区信息，假设为北京时间
    if dt.tzinfo is None:
        dt = BEIJING_TZ.localize(dt)
    else:
        # 转换为北京时间
        dt = dt.astimezone(BEIJING_TZ)

    return dt
