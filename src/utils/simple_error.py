#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA简化错误处理系统

简单优于复杂的错误处理方式，适合个人开发者
版本: 1.0.0
创建时间: 2025-07-31
"""

import sys
import traceback
import logging
from typing import Dict, Any, Optional, Callable, Type
from enum import Enum
from functools import wraps
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime

# 导入简化配置系统
from .simple_config import get_config_manager


class ErrorLevel(Enum):
    """错误级别 - 简化的错误分类"""
    INFO = "info"           # 信息性错误，可忽略
    WARNING = "warning"     # 警告，需要注意但不影响运行
    ERROR = "error"         # 错误，影响功能但可恢复
    CRITICAL = "critical"   # 严重错误，系统无法继续运行


@dataclass
class ErrorInfo:
    """错误信息数据类 - 简单清晰的错误描述"""
    level: ErrorLevel
    message: str
    location: str = ""                    # 错误发生位置
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    suggestion: str = ""                  # 解决建议
    
    def __str__(self) -> str:
        """友好的错误信息显示"""
        location_info = f" [{self.location}]" if self.location else ""
        suggestion_info = f"\n💡 建议: {self.suggestion}" if self.suggestion else ""
        
        level_icons = {
            ErrorLevel.INFO: "ℹ️",
            ErrorLevel.WARNING: "⚠️", 
            ErrorLevel.ERROR: "❌",
            ErrorLevel.CRITICAL: "🚨"
        }
        
        icon = level_icons.get(self.level, "❓")
        return f"{icon} {self.level.value.upper()}{location_info}: {self.message}{suggestion_info}"


class SimpleErrorHandler:
    """
    简化错误处理器 - 专注于开发者体验
    
    核心理念：
    1. 错误信息要清晰易懂
    2. 提供实用的解决建议
    3. 自动恢复策略
    4. 开发者友好的日志
    """
    
    def __init__(self):
        """初始化错误处理器"""
        self.config = get_config_manager()
        self.logger = logging.getLogger("SimpleError")
        self.error_count = 0
        self.error_history = []
        self.max_history = 100
        
        # 常见错误的解决建议映射
        self.suggestion_map = {
            FileNotFoundError: "检查文件路径是否正确，确保文件存在",
            PermissionError: "检查文件/目录权限，可能需要管理员权限",
            ConnectionError: "检查网络连接，确认服务是否可用", 
            ImportError: "检查依赖是否安装：pip install 缺失的包",
            KeyError: "检查配置文件或数据结构中是否存在该键",
            ValueError: "检查输入参数的格式和取值范围",
            TypeError: "检查参数类型是否匹配函数要求",
            AttributeError: "检查对象是否有该属性或方法",
        }
    
    def handle_error(
        self, 
        error: Exception, 
        level: ErrorLevel = ErrorLevel.ERROR,
        context: Optional[str] = None,
        suggestion: Optional[str] = None
    ) -> ErrorInfo:
        """
        处理错误并返回结构化信息
        
        Args:
            error: 异常对象
            level: 错误级别
            context: 错误上下文信息
            suggestion: 自定义解决建议
        
        Returns:
            ErrorInfo: 结构化错误信息
        """
        # 获取错误位置信息
        location = self._get_error_location(error)
        
        # 获取或生成解决建议
        if not suggestion:
            suggestion = self.suggestion_map.get(type(error), "")
        
        # 创建错误信息
        error_info = ErrorInfo(
            level=level,
            message=str(error),
            location=location,
            details={
                "error_type": type(error).__name__,
                "context": context,
                "traceback": traceback.format_exc() if self.config.is_debug() else None
            },
            suggestion=suggestion
        )
        
        # 记录错误
        self._log_error(error_info)
        self._add_to_history(error_info)
        self.error_count += 1
        
        return error_info
    
    def _get_error_location(self, error: Exception) -> str:
        """获取错误发生位置"""
        try:
            tb = error.__traceback__
            if tb:
                # 找到最后一个用户代码位置（跳过框架代码）
                while tb.tb_next:
                    tb = tb.tb_next
                
                filename = Path(tb.tb_frame.f_code.co_filename).name
                lineno = tb.tb_lineno
                func_name = tb.tb_frame.f_code.co_name
                
                return f"{filename}:{lineno} in {func_name}()"
        except:
            pass
        return "unknown"
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        log_methods = {
            ErrorLevel.INFO: self.logger.info,
            ErrorLevel.WARNING: self.logger.warning,
            ErrorLevel.ERROR: self.logger.error,
            ErrorLevel.CRITICAL: self.logger.critical
        }
        
        log_method = log_methods.get(error_info.level, self.logger.error)
        log_message = f"{error_info.message} [{error_info.location}]"
        
        if self.config.is_debug() and error_info.details.get("traceback"):
            log_message += f"\n{error_info.details['traceback']}"
        
        log_method(log_message)
    
    def _add_to_history(self, error_info: ErrorInfo):
        """添加到错误历史"""
        self.error_history.append(error_info)
        
        # 保持历史记录在限制范围内
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        if not self.error_history:
            return {"total_errors": 0, "recent_errors": []}
        
        # 按级别统计
        level_counts = {}
        for error_info in self.error_history:
            level = error_info.level.value
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # 最近的错误
        recent_errors = [
            {
                "level": err.level.value,
                "message": err.message,
                "location": err.location,
                "timestamp": err.timestamp.isoformat()
            }
            for err in self.error_history[-5:]  # 最近5个错误
        ]
        
        return {
            "total_errors": self.error_count,
            "level_counts": level_counts,
            "recent_errors": recent_errors,
            "suggestions_available": sum(1 for err in self.error_history if err.suggestion)
        }
    
    def clear_history(self):
        """清空错误历史"""
        self.error_history.clear()
        self.error_count = 0


def safe_call(
    func: Callable, 
    *args,
    error_level: ErrorLevel = ErrorLevel.ERROR,
    default_return: Any = None,
    context: Optional[str] = None,
    **kwargs
) -> Any:
    """
    安全调用函数，自动处理异常
    
    Args:
        func: 要调用的函数
        *args: 函数位置参数
        error_level: 错误级别
        default_return: 出错时的默认返回值
        context: 错误上下文信息
        **kwargs: 函数关键字参数
    
    Returns:
        函数返回值或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_handler = get_error_handler()
        error_info = error_handler.handle_error(e, error_level, context)
        
        # 在开发模式下显示错误信息
        if get_config_manager().is_debug():
            print(f"\n{error_info}")
        
        return default_return


def error_handler_decorator(
    error_level: ErrorLevel = ErrorLevel.ERROR,
    default_return: Any = None,
    reraise: bool = False
):
    """
    错误处理装饰器
    
    Args:
        error_level: 错误级别
        default_return: 出错时的默认返回值
        reraise: 是否重新抛出异常
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler = get_error_handler()
                context = f"调用 {func.__name__}"
                error_info = error_handler.handle_error(e, error_level, context)
                
                # 在开发模式下显示错误信息
                if get_config_manager().is_debug():
                    print(f"\n{error_info}")
                
                if reraise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator


def validate_input(
    value: Any, 
    validator: Callable[[Any], bool],
    error_message: str,
    suggestion: str = ""
) -> Any:
    """
    输入验证器
    
    Args:
        value: 要验证的值
        validator: 验证函数，返回True表示验证通过
        error_message: 验证失败时的错误信息
        suggestion: 解决建议
    
    Returns:
        验证通过的值
    
    Raises:
        ValueError: 验证失败时抛出
    """
    try:
        if not validator(value):
            error = ValueError(error_message)
            error_handler = get_error_handler()
            error_info = error_handler.handle_error(
                error, 
                ErrorLevel.ERROR, 
                "输入验证", 
                suggestion
            )
            raise error
        return value
    except Exception as e:
        if isinstance(e, ValueError):
            raise
        # 验证器本身出错
        error_handler = get_error_handler()
        error_handler.handle_error(
            e, 
            ErrorLevel.WARNING, 
            "验证器执行", 
            "检查验证函数的实现"
        )
        return value  # 验证器出错时返回原值


def critical_error(message: str, suggestion: str = "", exit_code: int = 1):
    """
    处理严重错误并退出程序
    
    Args:
        message: 错误信息
        suggestion: 解决建议
        exit_code: 退出码
    """
    error = RuntimeError(message)
    error_handler = get_error_handler()
    error_info = error_handler.handle_error(
        error, 
        ErrorLevel.CRITICAL, 
        "系统关键错误",
        suggestion
    )
    
    print(f"\n🚨 严重错误导致程序终止:")
    print(f"   {error_info}")
    print(f"\n💡 程序即将退出，请根据建议解决問題后重试。")
    
    sys.exit(exit_code)


# 全局错误处理器实例
_error_handler: Optional[SimpleErrorHandler] = None


def get_error_handler() -> SimpleErrorHandler:
    """获取全局错误处理器"""
    global _error_handler
    if _error_handler is None:
        _error_handler = SimpleErrorHandler()
    return _error_handler


# 便捷函数
def handle_error(
    error: Exception,
    level: ErrorLevel = ErrorLevel.ERROR,
    context: Optional[str] = None,
    suggestion: Optional[str] = None
) -> ErrorInfo:
    """处理错误的便捷函数"""
    return get_error_handler().handle_error(error, level, context, suggestion)


def get_error_summary() -> Dict[str, Any]:
    """获取错误摘要的便捷函数"""
    return get_error_handler().get_error_summary()


def clear_error_history():
    """清空错误历史的便捷函数"""
    get_error_handler().clear_history()


def main():
    """测试和演示函数"""
    print("🔧 AQUA简化错误处理系统测试")
    
    # 测试不同级别的错误
    print("\n📋 测试不同错误级别:")
    
    try:
        raise FileNotFoundError("测试文件未找到")
    except Exception as e:
        error_info = handle_error(e, ErrorLevel.WARNING, "文件操作测试")
        print(f"  {error_info}")
    
    try:
        raise ValueError("测试无效值")
    except Exception as e:
        error_info = handle_error(e, ErrorLevel.ERROR, "数据验证测试")
        print(f"  {error_info}")
    
    # 测试安全调用
    print("\n🛡️ 测试安全调用:")
    
    def risky_function(x):
        if x < 0:
            raise ValueError("负数不被支持")
        return x * 2
    
    result1 = safe_call(risky_function, 5, context="正常调用")
    print(f"  正常调用结果: {result1}")
    
    result2 = safe_call(risky_function, -1, default_return=0, context="异常调用")
    print(f"  异常调用结果: {result2}")
    
    # 测试装饰器
    print("\n🎭 测试错误处理装饰器:")
    
    @error_handler_decorator(default_return="fallback")
    def decorated_function(value):
        if value == "error":
            raise RuntimeError("装饰器测试错误")
        return f"处理完成: {value}"
    
    result3 = decorated_function("success")
    print(f"  正常调用: {result3}")
    
    result4 = decorated_function("error")
    print(f"  异常调用: {result4}")
    
    # 测试输入验证
    print("\n✅ 测试输入验证:")
    
    try:
        valid_value = validate_input(
            10, 
            lambda x: isinstance(x, int) and x > 0,
            "值必须是正整数",
            "请提供大于0的整数"
        )
        print(f"  验证通过: {valid_value}")
    except ValueError as e:
        print(f"  验证失败: {e}")
    
    try:
        validate_input(
            -5,
            lambda x: isinstance(x, int) and x > 0, 
            "值必须是正整数",
            "请提供大于0的整数"
        )
    except ValueError as e:
        print(f"  验证失败: {e}")
    
    # 显示错误摘要
    print("\n📊 错误摘要:")
    summary = get_error_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.simple_config import get_config_manager
    main()