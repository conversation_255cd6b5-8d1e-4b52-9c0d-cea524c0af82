#!/usr/bin/env python3
"""
环境变量管理器
统一管理AQUA项目中的所有环境变量，提供标准化的访问接口
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.paths import Paths


class EnvVarType(Enum):
    """环境变量类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    PATH = "path"
    URL = "url"
    LIST = "list"


@dataclass
class EnvVarDefinition:
    """环境变量定义"""
    name: str
    var_type: EnvVarType
    default: Any = None
    description: str = ""
    required: bool = False
    sensitive: bool = False
    validation_pattern: Optional[str] = None
    choices: Optional[List[str]] = None


class EnvironmentVariableManager:
    """环境变量管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._env_definitions: Dict[str, EnvVarDefinition] = {}
        self._cached_values: Dict[str, Any] = {}
        
        # 初始化标准环境变量定义
        self._init_standard_definitions()
    
    def _init_standard_definitions(self):
        """初始化标准环境变量定义"""
        # AQUA核心环境变量
        self.register_env_var(EnvVarDefinition(
            name="AQUA_ENV",
            var_type=EnvVarType.STRING,
            default="dev",
            description="AQUA运行环境",
            choices=["dev", "test", "prod"]
        ))
        
        self.register_env_var(EnvVarDefinition(
            name="AQUA_DEBUG",
            var_type=EnvVarType.BOOLEAN,
            default=False,
            description="是否启用调试模式"
        ))
        
        self.register_env_var(EnvVarDefinition(
            name="AQUA_LOG_LEVEL",
            var_type=EnvVarType.STRING,
            default="INFO",
            description="日志级别",
            choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        ))
        
        # 数据源相关
        self.register_env_var(EnvVarDefinition(
            name="AQUA_TUSHARE_TOKEN",
            var_type=EnvVarType.STRING,
            default="",
            description="Tushare API Token",
            required=False,
            sensitive=True
        ))
        
        # 数据库相关
        self.register_env_var(EnvVarDefinition(
            name="AQUA_DB_PATH",
            var_type=EnvVarType.PATH,
            default="{datacenter_dir}/aqua_dev.duckdb",
            description="数据库文件路径"
        ))
        
        # 前端相关
        self.register_env_var(EnvVarDefinition(
            name="VITE_API_BASE_URL",
            var_type=EnvVarType.URL,
            default="http://localhost:8000",
            description="前端API基础URL"
        ))
        
        self.register_env_var(EnvVarDefinition(
            name="VITE_APP_TITLE",
            var_type=EnvVarType.STRING,
            default="AQUA数据中心",
            description="应用标题"
        ))
        
        # 系统相关
        self.register_env_var(EnvVarDefinition(
            name="AQUA_MAX_WORKERS",
            var_type=EnvVarType.INTEGER,
            default=4,
            description="最大工作线程数"
        ))
        
        self.register_env_var(EnvVarDefinition(
            name="AQUA_MEMORY_LIMIT_GB",
            var_type=EnvVarType.FLOAT,
            default=4.0,
            description="内存限制(GB)"
        ))
    
    def register_env_var(self, definition: EnvVarDefinition):
        """注册环境变量定义"""
        self._env_definitions[definition.name] = definition
        self.logger.debug(f"注册环境变量: {definition.name}")
    
    def get(self, name: str, default: Any = None) -> Any:
        """
        获取环境变量值
        
        Args:
            name: 环境变量名
            default: 默认值（覆盖定义中的默认值）
            
        Returns:
            环境变量值
        """
        # 检查缓存
        if name in self._cached_values:
            return self._cached_values[name]
        
        # 获取定义
        definition = self._env_definitions.get(name)
        if not definition:
            # 未定义的环境变量，使用标准方式获取
            value = os.environ.get(name, default)
            self._cached_values[name] = value
            return value
        
        # 使用定义获取值
        raw_value = os.environ.get(name)
        
        if raw_value is None:
            if definition.required:
                raise ValueError(f"必需的环境变量 {name} 未设置")
            
            # 使用默认值
            final_default = default if default is not None else definition.default
            value = self._convert_value(final_default, definition.var_type)
        else:
            # 转换类型
            value = self._convert_value(raw_value, definition.var_type)
            
            # 验证值
            self._validate_value(name, value, definition)
        
        # 缓存结果
        self._cached_values[name] = value
        return value
    
    def set(self, name: str, value: Any, persist: bool = False):
        """
        设置环境变量值
        
        Args:
            name: 环境变量名
            value: 值
            persist: 是否持久化到.env文件
        """
        # 转换为字符串
        str_value = str(value)
        
        # 设置到环境
        os.environ[name] = str_value
        
        # 更新缓存
        self._cached_values[name] = value
        
        if persist:
            self._persist_to_env_file(name, str_value)
        
        self.logger.debug(f"设置环境变量: {name}={str_value}")
    
    def _convert_value(self, value: Any, var_type: EnvVarType) -> Any:
        """转换环境变量值类型"""
        if value is None:
            return None
        
        try:
            if var_type == EnvVarType.STRING:
                return str(value)
            elif var_type == EnvVarType.INTEGER:
                return int(value)
            elif var_type == EnvVarType.FLOAT:
                return float(value)
            elif var_type == EnvVarType.BOOLEAN:
                if isinstance(value, bool):
                    return value
                return str(value).lower() in ('true', '1', 'yes', 'on')
            elif var_type == EnvVarType.PATH:
                # 处理路径占位符
                path_str = str(value)
                if '{' in path_str:
                    from src.utils.paths import Paths
                    path_str = Paths.resolve_placeholder(path_str)
                return Path(path_str).expanduser()
            elif var_type == EnvVarType.URL:
                return str(value)
            elif var_type == EnvVarType.LIST:
                if isinstance(value, list):
                    return value
                return str(value).split(',')
            else:
                return str(value)
        except (ValueError, TypeError) as e:
            raise ValueError(f"无法将 '{value}' 转换为 {var_type.value}: {e}")
    
    def _validate_value(self, name: str, value: Any, definition: EnvVarDefinition):
        """验证环境变量值"""
        # 检查选择项
        if definition.choices and value not in definition.choices:
            raise ValueError(f"环境变量 {name} 的值 '{value}' 不在允许的选择中: {definition.choices}")
        
        # 检查验证模式
        if definition.validation_pattern:
            import re
            if not re.match(definition.validation_pattern, str(value)):
                raise ValueError(f"环境变量 {name} 的值 '{value}' 不匹配验证模式: {definition.validation_pattern}")
    
    def _persist_to_env_file(self, name: str, value: str):
        """持久化环境变量到.env文件"""
        env_file = Paths.ROOT / ".env"
        
        # 读取现有内容
        lines = []
        if env_file.exists():
            lines = env_file.read_text().splitlines()
        
        # 查找并更新或添加变量
        found = False
        for i, line in enumerate(lines):
            if line.startswith(f"{name}="):
                lines[i] = f"{name}={value}"
                found = True
                break
        
        if not found:
            lines.append(f"{name}={value}")
        
        # 写回文件
        env_file.write_text('\n'.join(lines) + '\n')
    
    def get_all_definitions(self) -> Dict[str, EnvVarDefinition]:
        """获取所有环境变量定义"""
        return self._env_definitions.copy()
    
    def validate_all(self) -> List[str]:
        """验证所有环境变量，返回错误列表"""
        errors = []
        
        for name, definition in self._env_definitions.items():
            try:
                self.get(name)
            except Exception as e:
                errors.append(f"{name}: {str(e)}")
        
        return errors
    
    def clear_cache(self):
        """清除缓存"""
        self._cached_values.clear()
    
    def get_env_info(self) -> Dict[str, Any]:
        """获取环境信息摘要"""
        info = {
            'total_definitions': len(self._env_definitions),
            'cached_values': len(self._cached_values),
            'required_vars': [name for name, def_ in self._env_definitions.items() if def_.required],
            'sensitive_vars': [name for name, def_ in self._env_definitions.items() if def_.sensitive],
            'missing_required': []
        }
        
        # 检查缺失的必需变量
        for name in info['required_vars']:
            if not os.environ.get(name):
                info['missing_required'].append(name)
        
        return info


# 全局环境变量管理器实例
env_manager = EnvironmentVariableManager()


# 便捷函数
def get_env(name: str, default: Any = None) -> Any:
    """获取环境变量值的便捷函数"""
    return env_manager.get(name, default)


def set_env(name: str, value: Any, persist: bool = False):
    """设置环境变量值的便捷函数"""
    env_manager.set(name, value, persist)


def is_debug() -> bool:
    """检查是否为调试模式"""
    return get_env("AQUA_DEBUG", False)


def get_environment() -> str:
    """获取当前环境"""
    return get_env("AQUA_ENV", "dev")


def get_log_level() -> str:
    """获取日志级别"""
    return get_env("AQUA_LOG_LEVEL", "INFO")


if __name__ == "__main__":
    # 测试环境变量管理器
    print("🔧 AQUA环境变量管理器测试")
    print("=" * 40)
    
    # 显示环境信息
    info = env_manager.get_env_info()
    print(f"已定义变量: {info['total_definitions']}")
    print(f"必需变量: {len(info['required_vars'])}")
    print(f"敏感变量: {len(info['sensitive_vars'])}")
    
    # 测试获取变量
    print(f"\n当前环境: {get_environment()}")
    print(f"调试模式: {is_debug()}")
    print(f"日志级别: {get_log_level()}")
    
    # 验证所有变量
    errors = env_manager.validate_all()
    if errors:
        print(f"\n⚠️ 发现 {len(errors)} 个验证错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n✅ 所有环境变量验证通过")
