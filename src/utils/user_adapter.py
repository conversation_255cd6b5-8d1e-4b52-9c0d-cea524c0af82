#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA用户级别适配系统

根据用户技能水平和偏好自动调整界面和功能复杂度
版本: 1.0.0
创建时间: 2025-08-01
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime, timedelta

# 导入AQUA工具
from .paths import Paths
from .simple_config import get_config_manager
from .simple_error import safe_call, handle_error, ErrorLevel
from .cli_ui import get_ui


class UserLevel(Enum):
    """用户级别"""
    BEGINNER = "beginner"           # 初学者
    INTERMEDIATE = "intermediate"   # 中级用户
    ADVANCED = "advanced"          # 高级用户
    EXPERT = "expert"              # 专家级用户


class AdaptationMode(Enum):
    """适配模式"""
    SIMPLE = "simple"        # 简化模式
    STANDARD = "standard"    # 标准模式
    ADVANCED = "advanced"    # 高级模式
    CUSTOM = "custom"        # 自定义模式


@dataclass
class UserProfile:
    """用户档案"""
    user_id: str
    level: UserLevel = UserLevel.BEGINNER
    mode: AdaptationMode = AdaptationMode.SIMPLE
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())
    
    # 使用统计
    login_count: int = 0
    command_count: int = 0
    error_count: int = 0
    success_count: int = 0
    
    # 偏好设置
    preferences: Dict[str, Any] = field(default_factory=dict)
    
    # 技能评估
    skills: Dict[str, float] = field(default_factory=lambda: {
        "python": 0.0,
        "data_analysis": 0.0,
        "quantitative": 0.0,
        "cli_usage": 0.0,
        "configuration": 0.0
    })
    
    # 特性使用情况
    feature_usage: Dict[str, int] = field(default_factory=dict)
    
    # 学习进度
    learning_progress: Dict[str, float] = field(default_factory=dict)


@dataclass
class AdaptationConfig:
    """适配配置"""
    # UI适配
    show_advanced_options: bool = False
    show_debug_info: bool = False
    show_performance_metrics: bool = False
    use_colors: bool = True
    verbose_output: bool = False
    
    # 功能适配
    enable_shortcuts: bool = False
    enable_auto_complete: bool = True
    enable_suggestions: bool = True
    enable_warnings: bool = True
    
    # 复杂度控制
    max_concurrent_operations: int = 1
    show_config_details: bool = False
    enable_batch_operations: bool = False
    show_internal_logs: bool = False
    
    # 帮助和指导
    show_tooltips: bool = True
    show_examples: bool = True
    auto_explain: bool = True
    step_by_step_guide: bool = True


class UserAdapter:
    """
    用户级别适配器
    
    核心功能：
    1. 自动检测用户技能水平
    2. 根据用户级别调整界面复杂度
    3. 个性化功能推荐
    4. 学习进度跟踪
    """
    
    def __init__(self):
        """初始化用户适配器"""
        self.config = get_config_manager()
        self.ui = get_ui()
        self.paths = Paths()
        
        # 用户数据文件路径
        self.user_data_dir = self.paths.ROOT / ".aqua" / "user"
        self.user_data_dir.mkdir(parents=True, exist_ok=True)
        self.profile_file = self.user_data_dir / "profile.json"
        
        # 当前用户档案
        self.current_profile: Optional[UserProfile] = None
        
        # 默认适配配置
        self.adaptation_configs = {
            UserLevel.BEGINNER: AdaptationConfig(
                show_advanced_options=False,
                show_debug_info=False,
                show_performance_metrics=False,
                verbose_output=True,
                enable_shortcuts=False,
                max_concurrent_operations=1,
                show_config_details=False,
                enable_batch_operations=False,
                show_internal_logs=False,
                show_tooltips=True,
                show_examples=True,
                auto_explain=True,
                step_by_step_guide=True
            ),
            UserLevel.INTERMEDIATE: AdaptationConfig(
                show_advanced_options=False,
                show_debug_info=False,
                show_performance_metrics=True,
                verbose_output=False,
                enable_shortcuts=True,
                max_concurrent_operations=2,
                show_config_details=True,
                enable_batch_operations=False,
                show_internal_logs=False,
                show_tooltips=True,
                show_examples=True,
                auto_explain=False,
                step_by_step_guide=False
            ),
            UserLevel.ADVANCED: AdaptationConfig(
                show_advanced_options=True,
                show_debug_info=True,
                show_performance_metrics=True,
                verbose_output=False,
                enable_shortcuts=True,
                max_concurrent_operations=4,
                show_config_details=True,
                enable_batch_operations=True,
                show_internal_logs=True,
                show_tooltips=False,
                show_examples=False,
                auto_explain=False,
                step_by_step_guide=False
            ),
            UserLevel.EXPERT: AdaptationConfig(
                show_advanced_options=True,
                show_debug_info=True,
                show_performance_metrics=True,
                verbose_output=False,
                enable_shortcuts=True,
                max_concurrent_operations=8,
                show_config_details=True,
                enable_batch_operations=True,
                show_internal_logs=True,
                show_tooltips=False,
                show_examples=False,
                auto_explain=False,
                step_by_step_guide=False
            )
        }
    
    def load_user_profile(self, user_id: Optional[str] = None) -> UserProfile:
        """加载用户档案"""
        if user_id is None:
            user_id = self._get_default_user_id()
        
        try:
            if self.profile_file.exists():
                with open(self.profile_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 验证用户ID
                if data.get('user_id') == user_id:
                    # 转换枚举字段
                    if 'level' in data and isinstance(data['level'], str):
                        data['level'] = UserLevel(data['level'])
                    if 'mode' in data and isinstance(data['mode'], str):
                        data['mode'] = AdaptationMode(data['mode'])
                    
                    # 更新技能评估
                    profile = UserProfile(**data)
                    profile.last_updated = datetime.now().isoformat()
                    self.current_profile = profile
                    return profile
            
            # 创建新的用户档案
            profile = UserProfile(user_id=user_id)
            self.current_profile = profile
            self._save_profile()
            return profile
            
        except Exception as e:
            handle_error(e, ErrorLevel.WARNING, "加载用户档案")
            # 创建默认档案
            profile = UserProfile(user_id=user_id)
            self.current_profile = profile
            return profile
    
    def _save_profile(self):
        """保存用户档案"""
        if not self.current_profile:
            return
        
        try:
            with open(self.profile_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.current_profile), f, indent=2, ensure_ascii=False)
        except Exception as e:
            handle_error(e, ErrorLevel.WARNING, "保存用户档案")
    
    def _get_default_user_id(self) -> str:
        """获取默认用户ID"""
        # 使用系统用户名作为默认ID
        import getpass
        try:
            return getpass.getuser()
        except Exception:
            return "default_user"
    
    def assess_user_level(self, profile: UserProfile) -> UserLevel:
        """评估用户级别"""
        if not profile:
            return UserLevel.BEGINNER
        
        # 计算综合评分
        score = 0.0
        
        # 使用频率评分 (40%)
        if profile.command_count > 0:
            success_rate = profile.success_count / profile.command_count
            frequency_score = min(profile.command_count / 100, 1.0) * success_rate
            score += frequency_score * 0.4
        
        # 技能评分 (40%)
        avg_skill = sum(profile.skills.values()) / len(profile.skills) if profile.skills else 0.0
        score += avg_skill * 0.4
        
        # 特性使用评分 (20%)
        advanced_features = ['batch_operations', 'custom_config', 'api_access', 'scripting']
        advanced_usage = sum(profile.feature_usage.get(f, 0) for f in advanced_features)
        feature_score = min(advanced_usage / 50, 1.0)
        score += feature_score * 0.2
        
        # 根据评分确定级别
        if score >= 0.8:
            return UserLevel.EXPERT
        elif score >= 0.6:
            return UserLevel.ADVANCED
        elif score >= 0.3:
            return UserLevel.INTERMEDIATE
        else:
            return UserLevel.BEGINNER
    
    def get_adaptation_config(self, level: Optional[UserLevel] = None) -> AdaptationConfig:
        """获取适配配置"""
        if level is None and self.current_profile:
            level = self.current_profile.level
        
        if level is None:
            level = UserLevel.BEGINNER
        
        return self.adaptation_configs.get(level, self.adaptation_configs[UserLevel.BEGINNER])
    
    def adapt_ui_display(self, content: Dict[str, Any], context: str = "general") -> Dict[str, Any]:
        """适配UI显示内容"""
        if not self.current_profile:
            return content
        
        config = self.get_adaptation_config()
        adapted_content = content.copy()
        
        # 根据配置调整显示内容
        if not config.show_advanced_options:
            # 隐藏高级选项
            adapted_content = self._filter_advanced_options(adapted_content)
        
        if not config.show_debug_info:
            # 隐藏调试信息
            adapted_content = self._filter_debug_info(adapted_content)
        
        if config.show_tooltips and context in ["commands", "config"]:
            # 添加工具提示
            adapted_content = self._add_tooltips(adapted_content, context)
        
        if config.show_examples and context in ["commands", "config"]:
            # 添加示例
            adapted_content = self._add_examples(adapted_content, context)
        
        return adapted_content
    
    def get_personalized_suggestions(self) -> List[str]:
        """获取个性化建议"""
        if not self.current_profile:
            return []
        
        suggestions = []
        config = self.get_adaptation_config()
        
        # 基于用户级别的建议
        if self.current_profile.level == UserLevel.BEGINNER:
            suggestions.extend([
                "建议从基础数据导入功能开始学习",
                "可以查看内置示例和教程",
                "遇到问题时可以使用 --help 查看帮助"
            ])
        elif self.current_profile.level == UserLevel.INTERMEDIATE:
            suggestions.extend([
                "尝试使用批量操作提高效率",
                "可以自定义配置文件满足特定需求",
                "建议学习使用API接口进行自动化"
            ])
        elif self.current_profile.level in [UserLevel.ADVANCED, UserLevel.EXPERT]:
            suggestions.extend([
                "可以使用脚本模式进行复杂操作",
                "建议配置性能监控获得更好体验",
                "可以参与社区贡献自定义插件"
            ])
        
        # 基于技能评估的建议
        low_skills = [skill for skill, score in self.current_profile.skills.items() if score < 0.3]
        if low_skills:
            suggestions.append(f"建议加强以下技能学习: {', '.join(low_skills)}")
        
        # 基于使用模式的建议
        if self.current_profile.error_count > self.current_profile.success_count * 0.5:
            suggestions.append("检测到较高错误率，建议查看常见问题解决方案")
        
        return suggestions[:5]  # 最多返回5个建议
    
    def record_user_action(self, action: str, success: bool = True, **kwargs):
        """记录用户操作"""
        if not self.current_profile:
            return
        
        # 更新计数器
        self.current_profile.command_count += 1
        if success:
            self.current_profile.success_count += 1
        else:
            self.current_profile.error_count += 1
        
        # 更新特性使用统计
        if action in self.current_profile.feature_usage:
            self.current_profile.feature_usage[action] += 1
        else:
            self.current_profile.feature_usage[action] = 1
        
        # 更新技能评估
        self._update_skills_based_on_action(action, success, **kwargs)
        
        # 重新评估用户级别
        new_level = self.assess_user_level(self.current_profile)
        if new_level != self.current_profile.level:
            self.current_profile.level = new_level
            self.ui.print_step(f"恭喜！您的用户级别已提升至: {new_level.value}", "success")
        
        # 更新时间戳
        self.current_profile.last_updated = datetime.now().isoformat()
        
        # 保存档案
        self._save_profile()
    
    def _update_skills_based_on_action(self, action: str, success: bool, **kwargs):
        """根据操作更新技能评估"""
        if not self.current_profile:
            return
        
        skill_mapping = {
            "data_import": "data_analysis",
            "config_update": "configuration",
            "api_call": "python",
            "batch_operation": "quantitative",
            "command_execution": "cli_usage"
        }
        
        skill = skill_mapping.get(action, "cli_usage")
        current_score = self.current_profile.skills.get(skill, 0.0)
        
        # 成功操作增加技能分数，失败操作轻微减少
        if success:
            increment = 0.05 * (1.0 - current_score)  # 越高越难提升
            self.current_profile.skills[skill] = min(current_score + increment, 1.0)
        else:
            decrement = 0.01
            self.current_profile.skills[skill] = max(current_score - decrement, 0.0)
    
    def _filter_advanced_options(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """过滤高级选项"""
        filtered = content.copy()
        
        # 移除标记为高级的选项
        advanced_keys = ['debug', 'performance', 'internal', 'expert']
        for key in list(filtered.keys()):
            if any(adv in key.lower() for adv in advanced_keys):
                filtered.pop(key, None)
        
        return filtered
    
    def _filter_debug_info(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """过滤调试信息"""
        filtered = content.copy()
        
        # 移除调试相关信息
        debug_keys = ['debug', 'trace', 'verbose', 'internal']
        for key in list(filtered.keys()):
            if any(debug in key.lower() for debug in debug_keys):
                filtered.pop(key, None)
        
        return filtered
    
    def _add_tooltips(self, content: Dict[str, Any], context: str) -> Dict[str, Any]:
        """添加工具提示"""
        enhanced = content.copy()
        
        tooltips = {
            "data_import": "数据导入功能支持CSV、JSON、Excel等多种格式",
            "config": "配置文件用于存储系统参数和用户偏好",
            "api": "API接口提供程序化访问AQUA功能的能力",
            "batch": "批量操作可以同时处理多个文件或任务"
        }
        
        for key, tooltip in tooltips.items():
            if key in enhanced:
                enhanced[f"{key}_tooltip"] = tooltip
        
        return enhanced
    
    def _add_examples(self, content: Dict[str, Any], context: str) -> Dict[str, Any]:
        """添加示例"""
        enhanced = content.copy()
        
        examples = {
            "data_import": "示例: aqua import data.csv --format csv",
            "config": "示例: aqua config set debug true",
            "api": "示例: curl http://localhost:8000/api/data",
            "batch": "示例: aqua batch process *.csv"
        }
        
        for key, example in examples.items():
            if key in enhanced:
                enhanced[f"{key}_example"] = example
        
        return enhanced
    
    def get_user_dashboard(self) -> Dict[str, Any]:
        """获取用户仪表板信息"""
        if not self.current_profile:
            return {}
        
        # 计算使用统计
        total_actions = self.current_profile.command_count
        success_rate = (self.current_profile.success_count / total_actions * 100) if total_actions > 0 else 0
        
        # 获取最常用功能
        top_features = sorted(
            self.current_profile.feature_usage.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # 技能进度
        skill_progress = {
            skill: f"{score*100:.1f}%" 
            for skill, score in self.current_profile.skills.items()
        }
        
        dashboard = {
            "user_info": {
                "user_id": self.current_profile.user_id,
                "level": self.current_profile.level.value,
                "created_at": self.current_profile.created_at,
                "last_updated": self.current_profile.last_updated
            },
            "usage_stats": {
                "total_commands": total_actions,
                "success_rate": f"{success_rate:.1f}%",
                "error_count": self.current_profile.error_count,
                "login_count": self.current_profile.login_count
            },
            "skill_progress": skill_progress,
            "top_features": dict(top_features),
            "suggestions": self.get_personalized_suggestions(),
            "next_level_progress": self._calculate_next_level_progress()
        }
        
        return dashboard
    
    def _calculate_next_level_progress(self) -> Dict[str, Any]:
        """计算升级进度"""
        if not self.current_profile:
            return {}
        
        current_level = self.current_profile.level
        level_order = [UserLevel.BEGINNER, UserLevel.INTERMEDIATE, UserLevel.ADVANCED, UserLevel.EXPERT]
        
        try:
            current_index = level_order.index(current_level)
            if current_index < len(level_order) - 1:
                next_level = level_order[current_index + 1]
                
                # 简化的进度计算
                current_score = sum(self.current_profile.skills.values()) / len(self.current_profile.skills)
                
                progress_thresholds = {
                    UserLevel.INTERMEDIATE: 0.3,
                    UserLevel.ADVANCED: 0.6,
                    UserLevel.EXPERT: 0.8
                }
                
                next_threshold = progress_thresholds[next_level]
                progress = min((current_score / next_threshold) * 100, 100)
                
                return {
                    "next_level": next_level.value,
                    "progress": f"{progress:.1f}%",
                    "requirements": f"需要更多使用经验和技能提升"
                }
        except (ValueError, KeyError):
            pass
        
        return {"message": "您已达到最高级别！"}
    
    def print_user_dashboard(self):
        """打印用户仪表板"""
        dashboard = self.get_user_dashboard()
        if not dashboard:
            self.ui.print_step("无法加载用户信息", "error")
            return
        
        self.ui.print_header("用户仪表板", f"欢迎 {dashboard['user_info']['user_id']}")
        
        # 用户信息
        self.ui.print_section("用户信息")
        self.ui.print_key_value("用户级别", dashboard['user_info']['level'])
        self.ui.print_key_value("注册时间", dashboard['user_info']['created_at'][:10])
        self.ui.print_key_value("最后更新", dashboard['user_info']['last_updated'][:10])
        
        # 使用统计
        print()
        self.ui.print_section("使用统计")
        self.ui.print_key_value("总命令数", dashboard['usage_stats']['total_commands'])
        self.ui.print_key_value("成功率", dashboard['usage_stats']['success_rate'])
        self.ui.print_key_value("错误次数", dashboard['usage_stats']['error_count'])
        
        # 技能进度
        print()
        self.ui.print_section("技能进度")
        for skill, progress in dashboard['skill_progress'].items():
            self.ui.print_key_value(skill, progress)
        
        # 常用功能
        if dashboard['top_features']:
            print()
            self.ui.print_section("常用功能")
            for feature, count in dashboard['top_features'].items():
                self.ui.print_key_value(feature, f"{count} 次")
        
        # 升级进度
        next_level = dashboard.get('next_level_progress', {})
        if next_level and 'next_level' in next_level:
            print()
            self.ui.print_section("升级进度")
            self.ui.print_key_value("下一级别", next_level['next_level'])
            self.ui.print_key_value("完成度", next_level['progress'])
            self.ui.print_key_value("升级要求", next_level['requirements'])
        
        # 个性化建议
        suggestions = dashboard.get('suggestions', [])
        if suggestions:
            print()
            self.ui.print_section("个性化建议")
            for suggestion in suggestions:
                self.ui.print_step(suggestion, "info")


def main():
    """测试和演示函数"""
    adapter = UserAdapter()
    
    # 加载用户档案
    profile = adapter.load_user_profile()
    
    # 显示用户仪表板
    adapter.print_user_dashboard()
    
    # 模拟一些用户操作
    ui = get_ui()
    print()
    ui.print_section("模拟用户操作")
    
    actions = [
        ("data_import", True),
        ("config_update", True),
        ("api_call", False),
        ("batch_operation", True),
        ("command_execution", True)
    ]
    
    for action, success in actions:
        adapter.record_user_action(action, success)
        status = "成功" if success else "失败"
        ui.print_step(f"记录操作: {action} - {status}", "success" if success else "warning")
    
    # 显示更新后的仪表板
    print()
    ui.print_section("更新后的用户信息")
    adapter.print_user_dashboard()


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.paths import Paths
    from utils.simple_config import get_config_manager
    from utils.simple_error import safe_call, handle_error, ErrorLevel
    from utils.cli_ui import get_ui
    
    main()