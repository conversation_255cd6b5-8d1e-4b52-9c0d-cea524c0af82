#!/usr/bin/env python3
"""
统一日志管理模块

提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from .config_loader import ConfigLoader
from .time_utils import get_beijing_time_now


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    # 颜色代码
    COLORS = {
        "DEBUG": "\033[36m",  # 青色
        "INFO": "\033[32m",  # 绿色
        "WARNING": "\033[33m",  # 黄色
        "ERROR": "\033[31m",  # 红色
        "CRITICAL": "\033[35m",  # 紫色
        "RESET": "\033[0m",  # 重置
    }

    def format(self, record):
        # 获取原始格式化结果
        formatted = super().format(record)

        # 如果是控制台输出且支持颜色
        if hasattr(record, "stream") and record.stream == sys.stdout:
            color = self.COLORS.get(record.levelname, "")
            reset = self.COLORS["RESET"]
            return f"{color}{formatted}{reset}"

        return formatted


class AquaLoggerManager:
    """AQUA日志管理器"""

    def __init__(
        self, config_path: str = "config/settings.toml", environment: str = "dev"
    ):
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.get_config(environment)
        self.environment = environment
        self.loggers: Dict[str, logging.Logger] = {}
        self._setup_logging()

    def _setup_logging(self):
        """设置日志配置"""
        # 获取日志配置
        log_config = self.config.get("logging", {})

        # 基础配置
        self.log_level = getattr(logging, log_config.get("level", "INFO").upper())
        self.log_format = log_config.get(
            "format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        self.log_file_path = log_config.get("file_path", "logs/aqua_{date}.log")
        self.max_file_size = log_config.get("max_file_size", "10MB")
        self.backup_count = log_config.get("backup_count", 5)

        # 确保日志目录存在
        log_dir = Path(self.log_file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # 设置根日志级别
        logging.getLogger().setLevel(self.log_level)

    def get_logger(self, name: str) -> logging.Logger:
        """获取或创建logger"""
        if name in self.loggers:
            return self.loggers[name]

        logger = logging.getLogger(name)
        logger.setLevel(self.log_level)

        # 避免重复添加handler
        if not logger.handlers:
            self._add_handlers(logger)

        self.loggers[name] = logger
        return logger

    def _add_handlers(self, logger: logging.Logger):
        """添加日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)

        # 如果是开发环境，使用彩色格式
        if self.environment == "dev":
            console_formatter = ColoredFormatter(self.log_format)
            console_handler.stream = sys.stdout  # 标记为控制台输出
        else:
            console_formatter = logging.Formatter(self.log_format)

        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)

        # 文件处理器
        if self.log_file_path:
            # 使用统一路径管理系统处理占位符
            from .paths import Paths, resolve_path
            
            # 确保核心目录存在
            Paths.ensure_dirs()
            
            # 解析路径占位符
            file_path = resolve_path(self.log_file_path)
            
            # 确保具体的日志文件目录存在
            log_file_dir = Path(file_path).parent
            log_file_dir.mkdir(parents=True, exist_ok=True)

            # 解析文件大小
            max_bytes = self._parse_file_size(self.max_file_size)

            # 创建轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=max_bytes,
                backupCount=self.backup_count,
                encoding="utf-8",
            )
            file_handler.setLevel(self.log_level)

            # 文件格式（不使用颜色）
            file_formatter = logging.Formatter(self.log_format)
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

    def _parse_file_size(self, size_str: str) -> int:
        """解析文件大小字符串"""
        size_str = size_str.upper()

        if size_str.endswith("KB"):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith("MB"):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith("GB"):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)

    def create_request_logger(self, request_id: str) -> logging.Logger:
        """创建请求特定的logger"""
        logger_name = f"request_{request_id}"
        logger = self.get_logger(logger_name)

        # 添加请求ID到日志记录
        class RequestFilter(logging.Filter):
            def filter(self, record):
                record.request_id = request_id
                return True

        logger.addFilter(RequestFilter())
        return logger

    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            "total_loggers": len(self.loggers),
            "log_level": logging.getLevelName(self.log_level),
            "log_file_path": self.log_file_path,
            "environment": self.environment,
            "loggers": list(self.loggers.keys()),
            "timestamp": get_beijing_time_now().isoformat(),
        }

        # 获取日志文件大小
        if self.log_file_path:
            file_path = Path(
                self.log_file_path.format(
                    date=datetime.now().strftime("%Y%m%d"), env=self.environment
                )
            )
            if file_path.exists():
                stats["log_file_size"] = file_path.stat().st_size

        return stats

    def set_log_level(self, level: str):
        """动态设置日志级别"""
        self.log_level = getattr(logging, level.upper())

        # 更新所有logger的级别
        for logger in self.loggers.values():
            logger.setLevel(self.log_level)
            for handler in logger.handlers:
                handler.setLevel(self.log_level)

    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        if not self.log_file_path:
            return

        log_dir = Path(self.log_file_path).parent
        if not log_dir.exists():
            return

        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        deleted_files = []

        for log_file in log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    deleted_files.append(str(log_file))
                except Exception as e:
                    print(f"删除日志文件失败: {log_file}, {e}")

        return deleted_files


# 全局日志管理器实例
_logger_manager: Optional[AquaLoggerManager] = None


def get_logger_manager() -> AquaLoggerManager:
    """获取全局日志管理器"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = AquaLoggerManager()
    return _logger_manager


def get_logger(name: str) -> logging.Logger:
    """获取logger的便利函数"""
    return get_logger_manager().get_logger(name)


def setup_logging(config_path: str = "config/settings.toml", environment: str = "dev"):
    """设置日志配置的便利函数"""
    global _logger_manager
    _logger_manager = AquaLoggerManager(config_path, environment)
    return _logger_manager


def log_function_call(logger: logging.Logger):
    """函数调用日志装饰器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            logger.debug(f"调用函数 {func.__name__} 开始: args={args}, kwargs={kwargs}")

            try:
                result = func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.debug(f"调用函数 {func.__name__} 成功: 耗时 {duration:.3f}秒")
                return result
            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.error(
                    f"调用函数 {func.__name__} 失败: {e}, 耗时 {duration:.3f}秒"
                )
                raise

        return wrapper

    return decorator


def log_performance(logger: logging.Logger, operation: str):
    """性能日志上下文管理器"""

    class PerformanceLogger:
        def __init__(self, logger: logging.Logger, operation: str):
            self.logger = logger
            self.operation = operation
            self.start_time = None

        def __enter__(self):
            self.start_time = datetime.now()
            self.logger.info(f"开始执行 {self.operation}")
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            end_time = datetime.now()
            duration = (end_time - self.start_time).total_seconds()

            if exc_type is None:
                self.logger.info(f"完成执行 {self.operation}: 耗时 {duration:.3f}秒")
            else:
                self.logger.error(
                    f"执行失败 {self.operation}: {exc_val}, 耗时 {duration:.3f}秒"
                )

    return PerformanceLogger(logger, operation)


def exception_logger(func):
    """
    异常日志装饰器
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger = logging.getLogger(func.__module__)
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            raise

    return wrapper


def log_error(message: str, exc_info=None):
    """
    记录错误日志的便利函数
    :param message: 错误消息
    :param exc_info: 异常信息(可选)
    """
    logger = logging.getLogger(__name__)
    logger.error(message, exc_info=exc_info)


def log_info(message: str):
    """
    记录信息日志的便利函数
    :param message: 信息消息
    """
    logger = logging.getLogger(__name__)
    logger.info(message)


def log_debug(message: str):
    """
    记录调试日志的便利函数
    :param message: 调试消息
    """
    logger = logging.getLogger(__name__)
    logger.debug(message)


def log_warning(message: str):
    """
    记录警告日志的便利函数
    :param message: 警告消息
    """
    logger = logging.getLogger(__name__)
    logger.warning(message)


def log_success(message: str):
    """
    记录成功日志的便利函数
    :param message: 成功消息
    """
    logger = logging.getLogger(__name__)
    logger.info(message)  # 通常将成功消息记录为INFO级别


def set_log_file(file_path: str):
    """
    动态设置日志文件路径的便利函数
    :param file_path: 新的日志文件路径
    """
    manager = get_logger_manager()
    manager.log_file_path = file_path
    # 重新配置所有logger的处理器以应用新的文件路径
    for logger_name, logger_obj in manager.loggers.items():
        # 移除旧的文件处理器
        for handler in logger_obj.handlers[:]:
            if isinstance(handler, logging.handlers.RotatingFileHandler):
                logger_obj.removeHandler(handler)
        # 添加新的文件处理器
        manager._add_handlers(logger_obj)


def set_sensitive_keys(keys: list):
    """
    设置需要脱敏的敏感信息键
    :param keys: 敏感信息键列表
    """
    # 可以在这里实现敏感信息脱敏的逻辑，例如将这些键存储在AquaLoggerManager中
    # 或者在日志格式化器中处理
    manager = get_logger_manager()
    if not hasattr(manager, "sensitive_keys"):
        manager.sensitive_keys = []
    manager.sensitive_keys.extend(keys)
    # 可以在这里更新日志格式化器，使其能够处理敏感信息


def main():
    """测试函数"""
    # 创建日志管理器
    manager = setup_logging()

    # 测试不同级别的日志
    logger = get_logger("test_logger")

    logger.debug("这是一个调试信息")
    logger.info("这是一个信息")
    logger.warning("这是一个警告")
    logger.error("这是一个错误")
    logger.critical("这是一个严重错误")

    # 测试性能日志
    with log_performance(logger, "测试操作"):
        import time

        time.sleep(0.1)

    # 测试函数装饰器
    @log_function_call(logger)
    def test_function(x, y):
        return x + y

    result = test_function(1, 2)
    print(f"函数结果: {result}")

    # 显示统计信息
    stats = manager.get_log_statistics()
    print(f"日志统计: {stats}")


if __name__ == "__main__":
    main()
