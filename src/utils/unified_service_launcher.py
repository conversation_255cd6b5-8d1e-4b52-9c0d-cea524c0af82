#!/usr/bin/env python3
"""
统一服务启动接口
提供跨平台的统一服务启动、停止、状态检查接口
"""

import os
import sys
import time
import platform
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from enum import Enum
from dataclasses import dataclass
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.paths import Paths
from src.utils.permission_checker import CrossPlatformPermissionChecker
from src.utils.permission_error_handler import PermissionErrorHandler


class ServiceType(Enum):
    """服务类型枚举"""
    BACKEND = "backend"
    FRONTEND = "frontend"
    DATABASE = "database"
    WORKER = "worker"
    SCHEDULER = "scheduler"


class ServiceStatus(Enum):
    """服务状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    UNKNOWN = "unknown"


class LaunchMethod(Enum):
    """启动方法枚举"""
    DIRECT = "direct"          # 直接启动
    HONCHO = "honcho"          # 使用honcho
    SYSTEMD = "systemd"        # 使用systemd (Linux)
    LAUNCHD = "launchd"        # 使用launchd (macOS)
    WINDOWS_SERVICE = "windows_service"  # Windows服务
    POWERSHELL = "powershell"  # PowerShell脚本


@dataclass
class ServiceConfig:
    """服务配置"""
    name: str
    service_type: ServiceType
    command: Union[str, List[str]]
    working_directory: Optional[Path] = None
    environment: Optional[Dict[str, str]] = None
    port: Optional[int] = None
    health_check_url: Optional[str] = None
    health_check_command: Optional[str] = None
    startup_timeout: int = 30
    shutdown_timeout: int = 10
    required: bool = True
    auto_restart: bool = False
    dependencies: Optional[List[str]] = None


@dataclass
class ServiceInstance:
    """服务实例"""
    config: ServiceConfig
    status: ServiceStatus = ServiceStatus.STOPPED
    process: Optional[subprocess.Popen] = None
    pid: Optional[int] = None
    start_time: Optional[float] = None
    last_health_check: Optional[float] = None
    error_message: Optional[str] = None


class UnifiedServiceLauncher:
    """统一服务启动器"""
    
    def __init__(self, project_root: Optional[Path] = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.platform = platform.system()
        self.logger = logging.getLogger(__name__)
        
        # 初始化工具
        self.permission_checker = CrossPlatformPermissionChecker()
        self.error_handler = PermissionErrorHandler()
        
        # 服务实例
        self.services: Dict[str, ServiceInstance] = {}
        
        # 平台特定配置
        self.launch_method = self._detect_launch_method()
        
        # 初始化默认服务配置
        self._initialize_default_services()
    
    def _detect_launch_method(self) -> LaunchMethod:
        """检测最佳启动方法"""
        if self.platform == "Windows":
            # 检查是否有PowerShell脚本
            ps_script = self.project_root / "Start-AQUA.ps1"
            if ps_script.exists():
                return LaunchMethod.POWERSHELL
            return LaunchMethod.DIRECT
        
        elif self.platform == "Darwin":  # macOS
            # 检查是否支持launchd
            if Path("/bin/launchctl").exists():
                return LaunchMethod.LAUNCHD
            return LaunchMethod.DIRECT
        
        elif self.platform == "Linux":
            # 检查是否支持systemd
            if Path("/bin/systemctl").exists():
                return LaunchMethod.SYSTEMD
            return LaunchMethod.DIRECT
        
        # 检查是否有honcho
        try:
            subprocess.run(["honcho", "--version"], 
                         capture_output=True, check=True)
            return LaunchMethod.HONCHO
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        return LaunchMethod.DIRECT
    
    def _initialize_default_services(self):
        """初始化默认服务配置"""
        # 检测虚拟环境
        venv_path = self.project_root / ".venv"
        if self.platform == "Windows":
            python_exe = venv_path / "Scripts" / "python.exe"
            uvicorn_exe = venv_path / "Scripts" / "uvicorn.exe"
        else:
            python_exe = venv_path / "bin" / "python"
            uvicorn_exe = venv_path / "bin" / "uvicorn"
        
        # 后端服务配置
        backend_config = ServiceConfig(
            name="backend",
            service_type=ServiceType.BACKEND,
            command=[
                str(uvicorn_exe),
                "src.api.main:app",
                "--host", "127.0.0.1",
                "--port", "8000",
                "--reload"
            ],
            working_directory=self.project_root,
            port=8000,
            health_check_url="http://127.0.0.1:8000/",
            startup_timeout=30,
            required=True
        )
        
        # 前端服务配置
        frontend_dir = self.project_root / "frontend"
        if frontend_dir.exists():
            # 检测包管理器
            package_manager = "npm"
            if (frontend_dir / "pnpm-lock.yaml").exists():
                package_manager = "pnpm"
            elif (frontend_dir / "yarn.lock").exists():
                package_manager = "yarn"
            
            frontend_config = ServiceConfig(
                name="frontend",
                service_type=ServiceType.FRONTEND,
                command=[package_manager, "run", "dev"],
                working_directory=frontend_dir,
                port=5173,
                health_check_url="http://localhost:5173/",
                startup_timeout=45,
                required=False,
                dependencies=["backend"]
            )
            
            self.services["frontend"] = ServiceInstance(config=frontend_config)
        
        self.services["backend"] = ServiceInstance(config=backend_config)
    
    def add_service(self, config: ServiceConfig) -> bool:
        """添加服务配置"""
        try:
            self.services[config.name] = ServiceInstance(config=config)
            self.logger.info(f"添加服务配置: {config.name}")
            return True
        except Exception as e:
            self.logger.error(f"添加服务配置失败: {config.name} - {e}")
            return False
    
    def remove_service(self, service_name: str) -> bool:
        """移除服务配置"""
        if service_name in self.services:
            # 如果服务正在运行，先停止
            if self.services[service_name].status == ServiceStatus.RUNNING:
                self.stop_service(service_name)
            
            del self.services[service_name]
            self.logger.info(f"移除服务配置: {service_name}")
            return True
        return False
    
    def start_service(self, service_name: str) -> bool:
        """启动单个服务"""
        if service_name not in self.services:
            self.logger.error(f"服务不存在: {service_name}")
            return False
        
        service = self.services[service_name]
        config = service.config
        
        # 检查服务状态
        if service.status == ServiceStatus.RUNNING:
            self.logger.info(f"服务已在运行: {service_name}")
            return True
        
        # 检查依赖服务
        if config.dependencies:
            for dep in config.dependencies:
                if not self._is_service_running(dep):
                    self.logger.warning(f"依赖服务未运行: {dep}")
                    if not self.start_service(dep):
                        self.logger.error(f"启动依赖服务失败: {dep}")
                        return False
        
        # 检查权限
        if not self._check_service_permissions(config):
            self.logger.error(f"服务权限检查失败: {service_name}")
            return False
        
        try:
            service.status = ServiceStatus.STARTING
            service.start_time = time.time()
            
            # 根据启动方法启动服务
            if self.launch_method == LaunchMethod.POWERSHELL and self.platform == "Windows":
                success = self._start_with_powershell(service)
            elif self.launch_method == LaunchMethod.HONCHO:
                success = self._start_with_honcho(service)
            else:
                success = self._start_direct(service)
            
            if success:
                service.status = ServiceStatus.RUNNING
                self.logger.info(f"服务启动成功: {service_name}")
                
                # 等待服务初始化
                if not self._wait_for_service_ready(service):
                    self.logger.warning(f"服务启动超时: {service_name}")
                
                return True
            else:
                service.status = ServiceStatus.ERROR
                self.logger.error(f"服务启动失败: {service_name}")
                return False
                
        except Exception as e:
            service.status = ServiceStatus.ERROR
            service.error_message = str(e)
            self.logger.error(f"启动服务异常: {service_name} - {e}")
            
            # 尝试权限错误处理
            context = {
                'service_name': service_name,
                'command': config.command,
                'working_directory': str(config.working_directory),
                'operation_type': 'service_start'
            }
            
            error_result = self.error_handler.handle_permission_error(e, context)
            if error_result.get('auto_recovery_success'):
                self.logger.info(f"权限错误自动恢复成功: {service_name}")
                return self.start_service(service_name)  # 重试启动
            
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """停止单个服务"""
        if service_name not in self.services:
            self.logger.error(f"服务不存在: {service_name}")
            return False
        
        service = self.services[service_name]
        
        if service.status != ServiceStatus.RUNNING:
            self.logger.info(f"服务未在运行: {service_name}")
            return True
        
        try:
            service.status = ServiceStatus.STOPPING
            
            if service.process:
                # 优雅关闭
                service.process.terminate()
                
                # 等待进程结束
                try:
                    service.process.wait(timeout=service.config.shutdown_timeout)
                except subprocess.TimeoutExpired:
                    # 强制关闭
                    service.process.kill()
                    service.process.wait()
                
                service.process = None
                service.pid = None
            
            service.status = ServiceStatus.STOPPED
            service.start_time = None
            service.error_message = None
            
            self.logger.info(f"服务停止成功: {service_name}")
            return True
            
        except Exception as e:
            service.status = ServiceStatus.ERROR
            service.error_message = str(e)
            self.logger.error(f"停止服务异常: {service_name} - {e}")
            return False
    
    def restart_service(self, service_name: str) -> bool:
        """重启服务"""
        self.logger.info(f"重启服务: {service_name}")
        return self.stop_service(service_name) and self.start_service(service_name)
    
    def start_all_services(self, required_only: bool = False) -> bool:
        """启动所有服务"""
        self.logger.info("开始启动所有服务")
        
        success_count = 0
        total_count = 0
        
        # 按依赖关系排序服务
        sorted_services = self._sort_services_by_dependencies()
        
        for service_name in sorted_services:
            service = self.services[service_name]
            
            # 如果只启动必需服务，跳过非必需服务
            if required_only and not service.config.required:
                continue
            
            total_count += 1
            
            if self.start_service(service_name):
                success_count += 1
            elif service.config.required:
                self.logger.error(f"必需服务启动失败，终止启动过程: {service_name}")
                return False
        
        self.logger.info(f"服务启动完成: {success_count}/{total_count}")
        return success_count == total_count
    
    def stop_all_services(self) -> bool:
        """停止所有服务"""
        self.logger.info("开始停止所有服务")
        
        success_count = 0
        total_count = 0
        
        # 按依赖关系逆序停止服务
        sorted_services = list(reversed(self._sort_services_by_dependencies()))
        
        for service_name in sorted_services:
            if self.services[service_name].status == ServiceStatus.RUNNING:
                total_count += 1
                if self.stop_service(service_name):
                    success_count += 1
        
        self.logger.info(f"服务停止完成: {success_count}/{total_count}")
        return success_count == total_count
    
    def get_service_status(self, service_name: str) -> Optional[ServiceStatus]:
        """获取服务状态"""
        if service_name not in self.services:
            return None
        
        service = self.services[service_name]
        
        # 如果服务应该在运行，检查进程是否还存在
        if service.status == ServiceStatus.RUNNING and service.process:
            if service.process.poll() is not None:
                # 进程已结束
                service.status = ServiceStatus.STOPPED
                service.process = None
                service.pid = None
        
        return service.status
    
    def get_all_services_status(self) -> Dict[str, Dict]:
        """获取所有服务状态"""
        status_dict = {}
        
        for service_name, service in self.services.items():
            status_dict[service_name] = {
                'status': self.get_service_status(service_name),
                'type': service.config.service_type,
                'port': service.config.port,
                'pid': service.pid,
                'start_time': service.start_time,
                'error_message': service.error_message,
                'required': service.config.required
            }
        
        return status_dict
    
    def health_check(self, service_name: str) -> bool:
        """健康检查"""
        if service_name not in self.services:
            return False
        
        service = self.services[service_name]
        config = service.config
        
        if service.status != ServiceStatus.RUNNING:
            return False
        
        try:
            # URL健康检查
            if config.health_check_url:
                import requests
                response = requests.get(config.health_check_url, timeout=5)
                return response.status_code == 200
            
            # 命令健康检查
            if config.health_check_command:
                result = subprocess.run(
                    config.health_check_command,
                    shell=True,
                    capture_output=True,
                    timeout=5
                )
                return result.returncode == 0
            
            # 端口检查
            if config.port:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', config.port))
                sock.close()
                return result == 0
            
            # 进程检查
            if service.process:
                return service.process.poll() is None
            
            return True
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {service_name} - {e}")
            return False
    
    def health_check_all(self) -> Dict[str, bool]:
        """所有服务健康检查"""
        results = {}

        for service_name in self.services:
            results[service_name] = self.health_check(service_name)

        return results

    def get_service_metrics(self, service_name: str) -> Dict[str, Any]:
        """获取服务性能指标"""
        if service_name not in self.services:
            raise ValueError(f"服务不存在: {service_name}")

        service = self.services[service_name]
        metrics = {
            "name": service_name,
            "status": self.get_service_status(service_name),
            "uptime": 0,
            "memory_usage": 0,
            "cpu_usage": 0,
            "port": service.config.port,
            "pid": service.pid
        }

        # 如果服务正在运行，获取详细指标
        if service.process and service.process.poll() is None:
            try:
                import psutil
                if service.pid:
                    proc = psutil.Process(service.pid)
                    metrics["uptime"] = time.time() - proc.create_time()
                    metrics["memory_usage"] = proc.memory_info().rss / 1024 / 1024  # MB
                    metrics["cpu_usage"] = proc.cpu_percent()
            except (ImportError, psutil.NoSuchProcess, psutil.AccessDenied):
                # psutil不可用或进程不存在时，使用默认值
                pass

        return metrics

    def get_all_service_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务的性能指标"""
        metrics = {}
        for service_name in self.services:
            try:
                metrics[service_name] = self.get_service_metrics(service_name)
            except Exception as e:
                metrics[service_name] = {
                    "name": service_name,
                    "status": ServiceStatus.ERROR,
                    "error": str(e)
                }
        return metrics

    def monitor_services(self, interval: int = 30, duration: int = 300) -> List[Dict[str, Any]]:
        """监控服务状态和性能指标

        Args:
            interval: 监控间隔（秒）
            duration: 监控持续时间（秒）

        Returns:
            监控数据列表
        """
        monitoring_data = []
        start_time = time.time()

        while time.time() - start_time < duration:
            timestamp = time.time()
            metrics = self.get_all_service_metrics()

            monitoring_data.append({
                "timestamp": timestamp,
                "metrics": metrics
            })

            self.logger.info(f"服务监控数据收集完成: {len(metrics)}个服务")
            time.sleep(interval)

        return monitoring_data

    def check_service_dependencies(self, service_name: str) -> Dict[str, bool]:
        """检查服务依赖状态"""
        if service_name not in self.services:
            raise ValueError(f"服务不存在: {service_name}")

        service = self.services[service_name]
        dependency_status = {}

        for dep_name in service.config.dependencies:
            if dep_name in self.services:
                dependency_status[dep_name] = self.get_service_status(dep_name) == ServiceStatus.RUNNING
            else:
                dependency_status[dep_name] = False
                self.logger.warning(f"依赖服务不存在: {dep_name}")

        return dependency_status

    def validate_service_configuration(self, service_name: str) -> Dict[str, Any]:
        """验证服务配置"""
        if service_name not in self.services:
            raise ValueError(f"服务不存在: {service_name}")

        service = self.services[service_name]
        config = service.config
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        # 检查必要字段
        if not config.name:
            validation_result["errors"].append("服务名称不能为空")
            validation_result["valid"] = False

        if not config.command:
            validation_result["errors"].append("启动命令不能为空")
            validation_result["valid"] = False

        # 检查端口
        if config.port:
            if not (1 <= config.port <= 65535):
                validation_result["errors"].append(f"端口号无效: {config.port}")
                validation_result["valid"] = False

            # 检查端口是否被占用
            if self._is_port_in_use(config.port):
                validation_result["warnings"].append(f"端口可能被占用: {config.port}")

        # 检查工作目录
        if config.working_directory:
            if not Path(config.working_directory).exists():
                validation_result["errors"].append(f"工作目录不存在: {config.working_directory}")
                validation_result["valid"] = False

        # 检查依赖
        for dep_name in config.dependencies:
            if dep_name not in self.services:
                validation_result["warnings"].append(f"依赖服务未注册: {dep_name}")

        return validation_result

    def _is_startup_method_available(self, method: str) -> bool:
        """检查启动方法是否可用"""
        system = platform.system()

        method_availability = {
            "direct": True,  # 直接启动总是可用
            "honcho": self._check_command_available("honcho"),
            "systemd": system == "Linux" and self._check_command_available("systemctl"),
            "launchd": system == "Darwin" and self._check_command_available("launchctl"),
            "windows_service": system == "Windows" and self._check_command_available("sc"),
            "powershell": system == "Windows" and self._check_command_available("powershell")
        }

        return method_availability.get(method, False)

    def _check_command_available(self, command: str) -> bool:
        """检查命令是否可用"""
        try:
            subprocess.run([command, "--version"],
                         capture_output=True,
                         check=False,
                         timeout=5)
            return True
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False

    def _check_service_permissions(self, config: ServiceConfig) -> bool:
        """检查服务权限"""
        try:
            # 检查工作目录权限
            if config.working_directory:
                result = self.permission_checker.check_path_permissions(
                    str(config.working_directory)
                )
                if not result.success:
                    return False

            # 检查端口权限
            if config.port:
                port_result = self.permission_checker.check_network_permissions(config.port)
                if not port_result.get('can_bind', True):
                    return False

            return True

        except Exception as e:
            self.logger.error(f"权限检查异常: {e}")
            return False

    def _is_service_running(self, service_name: str) -> bool:
        """检查服务是否在运行"""
        status = self.get_service_status(service_name)
        return status == ServiceStatus.RUNNING

    def _wait_for_service_ready(self, service: ServiceInstance) -> bool:
        """等待服务就绪"""
        config = service.config
        timeout = config.startup_timeout
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.health_check(config.name):
                return True
            time.sleep(1)

        return False

    def _sort_services_by_dependencies(self) -> List[str]:
        """按依赖关系排序服务"""
        sorted_services = []
        visited = set()
        visiting = set()

        def visit(service_name: str):
            if service_name in visiting:
                # 循环依赖
                self.logger.warning(f"检测到循环依赖: {service_name}")
                return

            if service_name in visited:
                return

            visiting.add(service_name)

            if service_name in self.services:
                config = self.services[service_name].config
                if config.dependencies:
                    for dep in config.dependencies:
                        visit(dep)

            visiting.remove(service_name)
            visited.add(service_name)
            sorted_services.append(service_name)

        for service_name in self.services:
            visit(service_name)

        return sorted_services

    def _start_direct(self, service: ServiceInstance) -> bool:
        """直接启动服务"""
        config = service.config

        try:
            # 准备命令
            if isinstance(config.command, str):
                cmd = config.command
                shell = True
            else:
                cmd = config.command
                shell = False

            # 准备环境变量
            env = os.environ.copy()
            if config.environment:
                env.update(config.environment)

            # 启动进程
            process = subprocess.Popen(
                cmd,
                shell=shell,
                cwd=config.working_directory,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            service.process = process
            service.pid = process.pid

            self.logger.info(f"直接启动服务: {config.name} (PID: {process.pid})")
            return True

        except Exception as e:
            self.logger.error(f"直接启动服务失败: {config.name} - {e}")
            return False

    def _start_with_powershell(self, service: ServiceInstance) -> bool:
        """使用PowerShell脚本启动服务"""
        config = service.config

        try:
            # 查找PowerShell脚本
            ps_script = self.project_root / "Start-AQUA.ps1"
            if not ps_script.exists():
                self.logger.error("PowerShell启动脚本不存在")
                return False

            # 启动PowerShell脚本
            cmd = ["powershell.exe", "-ExecutionPolicy", "Bypass", "-File", str(ps_script)]

            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            service.process = process
            service.pid = process.pid

            self.logger.info(f"PowerShell启动服务: {config.name} (PID: {process.pid})")
            return True

        except Exception as e:
            self.logger.error(f"PowerShell启动服务失败: {config.name} - {e}")
            return False

    def _start_with_honcho(self, service: ServiceInstance) -> bool:
        """使用honcho启动服务"""
        config = service.config

        try:
            # 检查Procfile
            procfile = self.project_root / "Procfile.dev"
            if not procfile.exists():
                self.logger.error("Procfile.dev不存在")
                return False

            # 启动honcho
            cmd = ["honcho", "start", config.name]

            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            service.process = process
            service.pid = process.pid

            self.logger.info(f"Honcho启动服务: {config.name} (PID: {process.pid})")
            return True

        except Exception as e:
            self.logger.error(f"Honcho启动服务失败: {config.name} - {e}")
            return False

    def get_launch_method(self) -> LaunchMethod:
        """获取当前启动方法"""
        return self.launch_method

    def set_launch_method(self, method: LaunchMethod) -> bool:
        """设置启动方法"""
        try:
            self.launch_method = method
            self.logger.info(f"设置启动方法: {method}")
            return True
        except Exception as e:
            self.logger.error(f"设置启动方法失败: {e}")
            return False


# 便捷函数
def create_service_launcher(project_root: Optional[Path] = None) -> UnifiedServiceLauncher:
    """创建服务启动器的便捷函数"""
    return UnifiedServiceLauncher(project_root)


def quick_start_services(required_only: bool = False) -> bool:
    """快速启动服务的便捷函数"""
    launcher = create_service_launcher()
    return launcher.start_all_services(required_only)


def quick_stop_services() -> bool:
    """快速停止服务的便捷函数"""
    launcher = create_service_launcher()
    return launcher.stop_all_services()


if __name__ == "__main__":
    # 测试统一服务启动器
    print("🚀 统一服务启动器测试")
    print("=" * 40)

    launcher = UnifiedServiceLauncher()

    print(f"检测到的启动方法: {launcher.get_launch_method()}")
    print(f"配置的服务数量: {len(launcher.services)}")

    # 显示服务状态
    status = launcher.get_all_services_status()
    for service_name, info in status.items():
        print(f"服务 {service_name}: {info['status']} (端口: {info['port']})")

    print("✅ 统一服务启动器测试完成")
