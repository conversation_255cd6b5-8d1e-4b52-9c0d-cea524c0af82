#!/usr/bin/env python3
"""
配置加载器V2模块 - 兼容性桥接
基于现有ConfigLoader提供兼容性接口
"""

from .config_loader import ConfigLoader


class ConfigLoaderV2(ConfigLoader):
    """配置加载器V2 - 兼容性实现"""
    
    def __init__(self, config_path: str = "config/settings.toml"):
        """初始化配置加载器V2"""
        try:
            super().__init__(config_path)
        except FileNotFoundError:
            # 如果配置文件不存在，创建一个最小配置
            self._create_minimal_config()
            super().__init__(config_path)
    
    def _create_minimal_config(self):
        """创建最小配置文件"""
        from pathlib import Path
        import toml
        
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        minimal_config = {
            "environment": {
                "current": "dev",
                "log_level": "INFO"
            },
            "database": {
                "default_path": "data/aqua_real_data.duckdb"
            }
        }
        
        config_path = config_dir / "settings.toml"
        with open(config_path, "w", encoding="utf-8") as f:
            toml.dump(minimal_config, f)