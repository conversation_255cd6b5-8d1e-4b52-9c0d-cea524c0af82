#!/usr/bin/env python3
"""
统一异常处理模块

提供标准化的异常类和错误处理机制
"""

from typing import Optional, Dict, Any, List
from enum import Enum

from .time_utils import get_beijing_time_now
from .logger import get_logger


class ErrorCode(Enum):
    """错误代码枚举"""

    # 通用错误 1000-1999
    UNKNOWN_ERROR = 1000
    INVALID_PARAMETER = 1001
    MISSING_PARAMETER = 1002
    PERMISSION_DENIED = 1003
    RATE_LIMIT_EXCEEDED = 1004

    # 数据库错误 2000-2999
    DATABASE_CONNECTION_ERROR = 2000
    DATABASE_QUERY_ERROR = 2001
    DATABASE_TRANSACTION_ERROR = 2002
    TABLE_NOT_FOUND = 2003
    COLUMN_NOT_FOUND = 2004
    CONSTRAINT_VIOLATION = 2005

    # 缓存错误 3000-3999
    CACHE_CONNECTION_ERROR = 3000
    CACHE_OPERATION_ERROR = 3001
    CACHE_TIMEOUT = 3002

    # 数据处理错误 4000-4999
    DATA_VALIDATION_ERROR = 4000
    DATA_PARSING_ERROR = 4001
    DATA_TRANSFORMATION_ERROR = 4002
    FILE_FORMAT_ERROR = 4003
    FILE_NOT_FOUND = 4004

    # 网络错误 5000-5999
    NETWORK_ERROR = 5000
    TIMEOUT_ERROR = 5001
    SERVICE_UNAVAILABLE = 5002

    # 业务逻辑错误 6000-6999
    BUSINESS_LOGIC_ERROR = 6000
    INSUFFICIENT_DATA = 6001
    CALCULATION_ERROR = 6002


class AquaException(Exception):
    """AQUA系统基础异常类"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause
        self.timestamp = get_beijing_time_now()

        # 记录异常日志
        logger = get_logger("AquaException")
        logger.error(
            f"[{error_code.name}] {message}",
            extra={
                "error_code": error_code.value,
                "details": details,
                "cause": str(cause) if cause else None,
            },
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code.value,
            "error_name": self.error_code.name,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "cause": str(self.cause) if self.cause else None,
        }


class DatabaseException(AquaException):
    """数据库相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.DATABASE_CONNECTION_ERROR,
        table_name: Optional[str] = None,
        query: Optional[str] = None,
        cause: Optional[Exception] = None,
    ):
        details = {}
        if table_name:
            details["table_name"] = table_name
        if query:
            details["query"] = query[:500]  # 限制查询长度

        super().__init__(message, error_code, details, cause)


class CacheException(AquaException):
    """缓存相关异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.CACHE_CONNECTION_ERROR,
        cache_key: Optional[str] = None,
        cache_level: Optional[str] = None,
        cause: Optional[Exception] = None,
    ):
        details = {}
        if cache_key:
            details["cache_key"] = cache_key
        if cache_level:
            details["cache_level"] = cache_level

        super().__init__(message, error_code, details, cause)


class DataValidationException(AquaException):
    """数据验证异常"""

    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        validation_rules: Optional[List[str]] = None,
        cause: Optional[Exception] = None,
    ):
        details = {}
        if field_name:
            details["field_name"] = field_name
        if field_value is not None:
            details["field_value"] = str(field_value)
        if validation_rules:
            details["validation_rules"] = validation_rules

        super().__init__(message, ErrorCode.DATA_VALIDATION_ERROR, details, cause)


class BusinessLogicException(AquaException):
    """业务逻辑异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.BUSINESS_LOGIC_ERROR,
        operation: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        details = {}
        if operation:
            details["operation"] = operation
        if context:
            details["context"] = context

        super().__init__(message, error_code, details, cause)


class ExceptionHandler:
    """异常处理器"""

    def __init__(self):
        self.logger = get_logger("ExceptionHandler")
        self.error_counters = {}

    def handle_exception(
        self, exc: Exception, context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理异常并返回标准格式的错误信息"""

        # 增加错误计数
        error_type = type(exc).__name__
        self.error_counters[error_type] = self.error_counters.get(error_type, 0) + 1

        # 如果是 AquaException，直接返回格式化信息
        if isinstance(exc, AquaException):
            error_info = exc.to_dict()
            if context:
                error_info["context"] = context
            return error_info

        # 处理其他异常
        error_code = self._map_exception_to_code(exc)

        error_info = {
            "error_code": error_code.value,
            "error_name": error_code.name,
            "message": str(exc),
            "details": {"exception_type": type(exc).__name__, "context": context},
            "timestamp": get_beijing_time_now().isoformat(),
            "cause": None,
        }

        # 记录日志
        self.logger.error(f"未处理的异常: {error_info}", exc_info=True)

        return error_info

    def _map_exception_to_code(self, exc: Exception) -> ErrorCode:
        """将异常映射到错误代码"""
        exception_mapping = {
            ConnectionError: ErrorCode.DATABASE_CONNECTION_ERROR,
            TimeoutError: ErrorCode.TIMEOUT_ERROR,
            ValueError: ErrorCode.DATA_VALIDATION_ERROR,
            TypeError: ErrorCode.DATA_VALIDATION_ERROR,
            FileNotFoundError: ErrorCode.FILE_NOT_FOUND,
            PermissionError: ErrorCode.PERMISSION_DENIED,
        }

        return exception_mapping.get(type(exc), ErrorCode.UNKNOWN_ERROR)

    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        total_errors = sum(self.error_counters.values())

        return {
            "total_errors": total_errors,
            "error_types": self.error_counters.copy(),
            "timestamp": get_beijing_time_now().isoformat(),
        }

    def reset_statistics(self):
        """重置错误统计"""
        self.error_counters.clear()


# 全局异常处理器实例
_exception_handler = None


def get_exception_handler() -> ExceptionHandler:
    """获取全局异常处理器"""
    global _exception_handler
    if _exception_handler is None:
        _exception_handler = ExceptionHandler()
    return _exception_handler


def handle_exception(
    exc: Exception, context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """处理异常的便利函数"""
    return get_exception_handler().handle_exception(exc, context)


def raise_database_error(
    message: str,
    table_name: Optional[str] = None,
    query: Optional[str] = None,
    cause: Optional[Exception] = None,
):
    """抛出数据库错误"""
    raise DatabaseException(message, table_name=table_name, query=query, cause=cause)


def raise_cache_error(
    message: str,
    cache_key: Optional[str] = None,
    cache_level: Optional[str] = None,
    cause: Optional[Exception] = None,
):
    """抛出缓存错误"""
    raise CacheException(
        message, cache_key=cache_key, cache_level=cache_level, cause=cause
    )


def raise_validation_error(
    message: str,
    field_name: Optional[str] = None,
    field_value: Optional[Any] = None,
    validation_rules: Optional[List[str]] = None,
    cause: Optional[Exception] = None,
):
    """抛出数据验证错误"""
    raise DataValidationException(
        message,
        field_name=field_name,
        field_value=field_value,
        validation_rules=validation_rules,
        cause=cause,
    )


def raise_business_error(
    message: str,
    operation: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    cause: Optional[Exception] = None,
):
    """抛出业务逻辑错误"""
    raise BusinessLogicException(
        message, operation=operation, context=context, cause=cause
    )


def safe_execute(func, *args, **kwargs):
    """安全执行函数，捕获并处理异常"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_info = handle_exception(
            e, {"function": func.__name__, "args": args, "kwargs": kwargs}
        )
        raise AquaException(
            f"执行 {func.__name__} 时发生错误: {error_info['message']}",
            ErrorCode(error_info["error_code"]),
            error_info["details"],
            e,
        )


def validate_not_none(value: Any, field_name: str):
    """验证值不为空"""
    if value is None:
        raise_validation_error(
            f"字段 '{field_name}' 不能为空",
            field_name=field_name,
            field_value=value,
            validation_rules=["not_none"],
        )


def validate_type(value: Any, expected_type: type, field_name: str):
    """验证值类型"""
    if not isinstance(value, expected_type):
        raise_validation_error(
            f"字段 '{field_name}' 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}",
            field_name=field_name,
            field_value=value,
            validation_rules=[f"type_{expected_type.__name__}"],
        )


def validate_range(value: Any, min_val: Any, max_val: Any, field_name: str):
    """验证值范围"""
    if value < min_val or value > max_val:
        raise_validation_error(
            f"字段 '{field_name}' 值超出范围 [{min_val}, {max_val}]",
            field_name=field_name,
            field_value=value,
            validation_rules=[f"range_{min_val}_{max_val}"],
        )


def main():
    """测试函数"""
    # 测试异常处理
    try:
        raise_database_error("测试数据库错误", table_name="test_table")
    except AquaException as e:
        print("数据库异常:", e.to_dict())

    try:
        raise_validation_error(
            "测试验证错误", field_name="test_field", field_value=None
        )
    except AquaException as e:
        print("验证异常:", e.to_dict())

    # 测试异常处理器
    handler = get_exception_handler()
    try:
        raise ValueError("测试普通异常")
    except Exception as e:
        error_info = handler.handle_exception(e)
        print("处理后的异常:", error_info)

    # 显示统计信息
    print("错误统计:", handler.get_error_statistics())


if __name__ == "__main__":
    main()
