#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA简化配置管理系统

简单优于复杂的配置管理方式，适合个人开发者
版本: 1.0.0
创建时间: 2025-07-31
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from functools import lru_cache

# 导入统一路径管理系统
from .paths import Paths, resolve_path


@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "{datacenter_dir}/aqua_{env}.duckdb"
    auto_create: bool = True
    memory_limit: str = "2GB"
    threads: int = 4
    
    def get_resolved_path(self, environment: str = "dev") -> str:
        """获取解析后的数据库路径"""
        # 设置临时环境变量以确保正确解析
        original_env = os.getenv('AQUA_ENV')
        os.environ['AQUA_ENV'] = environment
        try:
            resolved = resolve_path(self.path)
            return resolved
        finally:
            if original_env is not None:
                os.environ['AQUA_ENV'] = original_env
            elif 'AQUA_ENV' in os.environ:
                del os.environ['AQUA_ENV']


@dataclass 
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    file_path: str = "{logs_root}/aqua_{env}_{date}.log"
    max_file_size: str = "10MB"
    backup_count: int = 5
    console_output: bool = True
    
    def get_resolved_path(self, environment: str = "dev") -> str:
        """获取解析后的日志路径"""
        # 设置临时环境变量以确保正确解析
        original_env = os.getenv('AQUA_ENV')
        os.environ['AQUA_ENV'] = environment
        try:
            resolved = resolve_path(self.file_path)
            return resolved
        finally:
            if original_env is not None:
                os.environ['AQUA_ENV'] = original_env
            elif 'AQUA_ENV' in os.environ:
                del os.environ['AQUA_ENV']


@dataclass
class CacheConfig:
    """缓存配置"""
    enabled: bool = True
    l1_max_size: int = 1000
    l1_ttl: int = 1800
    l2_cache_dir: str = "{cache_root}"
    l2_max_size_mb: int = 100
    default_ttl: int = 3600
    
    def get_resolved_cache_dir(self) -> str:
        """获取解析后的缓存目录"""
        return resolve_path(self.l2_cache_dir)


@dataclass
class PerformanceConfig:
    """性能配置"""
    memory_limit_mb: int = 1536  # 个人开发者友好
    max_workers: int = 2
    chunk_size: int = 500
    startup_target_seconds: float = 2.0
    enable_monitoring: bool = True


@dataclass
class AppConfig:
    """应用配置"""
    name: str = "AQUA"
    version: str = "2.0.0"
    debug: bool = True
    environment: str = "dev"
    
    # 子配置
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)


class SimpleConfig:
    """
    简化配置管理器 - 简单优于复杂的原则
    
    核心理念：
    1. 约定优于配置
    2. 智能默认值
    3. 环境变量优先
    4. 统一路径管理
    """
    
    def __init__(self, environment: Optional[str] = None):
        """
        初始化简化配置管理器
        
        Args:
            environment: 环境名称，如果不指定则自动检测
        """
        self.environment = environment or self._detect_environment()
        self.logger = logging.getLogger(__name__)
        self._config: Optional[AppConfig] = None
        
        # 确保核心目录存在
        Paths.ensure_dirs()
    
    def _detect_environment(self) -> str:
        """智能检测运行环境"""
        # 按优先级检查环境变量
        env_vars = ["AQUA_ENV", "ENV", "ENVIRONMENT"]
        for env_var in env_vars:
            env = os.getenv(env_var)
            if env:
                return env.lower()
        
        # 根据运行条件推断环境
        if os.getenv("CI"):
            return "test"
        elif hasattr(sys, 'ps1') or os.getenv("JUPYTER_RUNTIME_DIR"):
            return "dev"
        elif Path("/proc/version").exists():  # Linux生产环境检测
            with open("/proc/version") as f:
                if "microsoft" in f.read().lower():
                    return "dev"  # WSL开发环境
                return "prod"
        else:
            return "dev"  # 默认开发环境
    
    @lru_cache(maxsize=1)
    def get_config(self) -> AppConfig:
        """
        获取应用配置 - 使用缓存提高性能
        
        Returns:
            AppConfig: 应用配置对象
        """
        if self._config is None:
            self._config = self._load_config()
        return self._config
    
    def _load_config(self) -> AppConfig:
        """加载配置"""
        # 基础配置
        config = AppConfig(environment=self.environment)
        
        # 根据环境调整配置
        if self.environment == "dev":
            config.debug = True
            config.logging.level = "DEBUG"
            config.performance.memory_limit_mb = 1536
            config.performance.enable_monitoring = True
        elif self.environment == "test":
            config.debug = False
            config.logging.level = "INFO"
            config.performance.memory_limit_mb = 2048
            config.performance.max_workers = 4
        elif self.environment == "prod":
            config.debug = False
            config.logging.level = "WARNING"
            config.logging.console_output = False
            config.performance.memory_limit_mb = 4096
            config.performance.max_workers = 8
        
        # 应用环境变量覆盖
        self._apply_env_overrides(config)
        
        self.logger.info(f"已加载 {self.environment} 环境配置")
        return config
    
    def _apply_env_overrides(self, config: AppConfig) -> None:
        """应用环境变量覆盖"""
        # 日志级别覆盖
        log_level = os.getenv("AQUA_LOG_LEVEL") or os.getenv("LOG_LEVEL")
        if log_level:
            config.logging.level = log_level.upper()
        
        # 调试模式覆盖
        debug = os.getenv("AQUA_DEBUG") or os.getenv("DEBUG")
        if debug:
            config.debug = debug.lower() in ("true", "1", "yes", "on")
        
        # 数据库路径覆盖
        db_path = os.getenv(f"AQUA_{self.environment.upper()}_DB_PATH")
        if db_path:
            config.database.path = db_path
        
        # 内存限制覆盖
        memory_limit = os.getenv("AQUA_MEMORY_LIMIT_MB")
        if memory_limit:
            try:
                config.performance.memory_limit_mb = int(memory_limit)
            except ValueError:
                self.logger.warning(f"无效的内存限制值: {memory_limit}")
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        return self.get_config().database
    
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        return self.get_config().logging
    
    def get_cache_config(self) -> CacheConfig:
        """获取缓存配置"""
        return self.get_config().cache
    
    def get_performance_config(self) -> PerformanceConfig:
        """获取性能配置"""
        return self.get_config().performance
    
    def get_database_path(self) -> str:
        """获取数据库路径"""
        return self.get_database_config().get_resolved_path(self.environment)
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return self.get_logging_config().get_resolved_path(self.environment)
    
    def get_cache_dir(self) -> str:
        """获取缓存目录路径"""
        return self.get_cache_config().get_resolved_cache_dir()
    
    def is_debug(self) -> bool:
        """是否为调试模式"""
        return self.get_config().debug
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息摘要"""
        config = self.get_config()
        platform_info = Paths.get_platform_info()
        
        return {
            "environment": self.environment,
            "debug": config.debug,
            "platform": platform_info["system"],  
            "architecture": platform_info["architecture"],
            "database_path": self.get_database_path(),
            "log_level": config.logging.level,
            "memory_limit_mb": config.performance.memory_limit_mb,
            "cache_enabled": config.cache.enabled,
            "startup_target": f"{config.performance.startup_target_seconds}s",
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性"""
        config = self.get_config()
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "info": []
        }
        
        try:
            # 验证数据库路径
            db_path = Path(self.get_database_path())
            db_dir = db_path.parent
            if not db_dir.exists():
                validation_result["warnings"].append(f"数据库目录不存在，将自动创建: {db_dir}")
                db_dir.mkdir(parents=True, exist_ok=True)
                validation_result["info"].append(f"已创建数据库目录: {db_dir}")
            
            # 验证日志路径
            log_path = Path(self.get_log_file_path())
            log_dir = log_path.parent
            if not log_dir.exists():
                validation_result["warnings"].append(f"日志目录不存在，将自动创建: {log_dir}")
                log_dir.mkdir(parents=True, exist_ok=True)
                validation_result["info"].append(f"已创建日志目录: {log_dir}")
            
            # 验证缓存路径
            cache_dir = Path(self.get_cache_dir())
            if not cache_dir.exists():
                validation_result["warnings"].append(f"缓存目录不存在，将自动创建: {cache_dir}")
                cache_dir.mkdir(parents=True, exist_ok=True)
                validation_result["info"].append(f"已创建缓存目录: {cache_dir}")
            
            # 验证内存限制合理性
            memory_limit = config.performance.memory_limit_mb
            if memory_limit < 512:
                validation_result["warnings"].append(f"内存限制过低: {memory_limit}MB，建议至少1GB")
            elif memory_limit > 8192:
                validation_result["warnings"].append(f"内存限制过高: {memory_limit}MB，注意系统资源")
            
            validation_result["info"].append(f"配置验证完成，环境: {self.environment}")
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"配置验证异常: {e}")
        
        return validation_result
    
    def reload_config(self) -> None:
        """重新加载配置"""
        self._config = None
        self.get_config.cache_clear()
        self.logger.info("配置已重新加载")


# 全局配置管理器实例
_config_manager: Optional[SimpleConfig] = None


def get_config_manager(environment: Optional[str] = None) -> SimpleConfig:
    """
    获取全局配置管理器 - 单例模式
    
    Args:
        environment: 环境名称，仅在首次调用时有效
        
    Returns:
        SimpleConfig: 配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = SimpleConfig(environment)
    return _config_manager


def get_database_path() -> str:
    """获取数据库路径的便捷函数"""
    return get_config_manager().get_database_path()


def get_log_file_path() -> str:
    """获取日志文件路径的便捷函数"""
    return get_config_manager().get_log_file_path()


def get_cache_dir() -> str:
    """获取缓存目录的便捷函数"""
    return get_config_manager().get_cache_dir()


def is_debug_mode() -> bool:
    """检查是否为调试模式的便捷函数"""
    return get_config_manager().is_debug()


def get_environment() -> str:
    """获取当前环境的便捷函数"""
    return get_config_manager().environment


def main():
    """测试和演示函数"""
    print("🔧 AQUA简化配置管理系统测试")
    
    # 创建配置管理器
    config_manager = SimpleConfig("dev")
    
    # 显示环境信息
    env_info = config_manager.get_environment_info()
    print(f"📋 环境信息:")
    for key, value in env_info.items():
        print(f"  {key}: {value}")
    
    # 验证配置
    validation = config_manager.validate_config()
    print(f"\n✅ 配置验证:")
    print(f"  有效: {validation['valid']}")
    
    if validation["errors"]:
        print(f"  错误: {validation['errors']}")
    if validation["warnings"]:
        print(f"  警告: {validation['warnings']}")
    if validation["info"]:
        print(f"  信息: {validation['info']}")
    
    # 测试便捷函数
    print(f"\n🛠️ 便捷函数测试:")
    print(f"  数据库路径: {get_database_path()}")
    print(f"  日志文件路径: {get_log_file_path()}")
    print(f"  缓存目录: {get_cache_dir()}")
    print(f"  调试模式: {is_debug_mode()}")
    print(f"  当前环境: {get_environment()}")


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.paths import Paths, resolve_path
    main()