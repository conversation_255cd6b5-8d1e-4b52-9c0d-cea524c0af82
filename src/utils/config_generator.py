#!/usr/bin/env python3
"""
配置文件生成器
自动生成和管理项目配置文件
"""

import os
import sys
import json
import yaml
import platform
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from jinja2 import Template, Environment, FileSystemLoader, select_autoescape


class ConfigFormat(Enum):
    """配置文件格式枚举"""
    JSON = "json"
    YAML = "yaml"
    INI = "ini"
    ENV = "env"
    TOML = "toml"


class ConfigType(Enum):
    """配置类型枚举"""
    DATABASE = "database"
    SERVER = "server"
    LOGGING = "logging"
    ENVIRONMENT = "environment"
    FRONTEND = "frontend"
    BACKEND = "backend"


@dataclass
class ConfigTemplate:
    """配置模板"""
    name: str
    type: ConfigType
    format: ConfigFormat
    template_path: str
    output_path: str
    description: str
    required_variables: List[str]
    optional_variables: Dict[str, Any]
    platform_specific: bool = False


class ConfigTemplateSystem:
    """配置模板系统"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
        self.system = platform.system()
        
        # 模板目录
        self.templates_dir = project_root / "config" / "templates"
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 输出目录
        self.config_dir = project_root / "config"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            autoescape=select_autoescape(['html', 'xml'])
        )
        
        # 注册的模板
        self.templates = {}
        
        # 初始化默认模板
        self._initialize_default_templates()
    
    def _initialize_default_templates(self):
        """初始化默认配置模板"""
        # 数据库配置模板
        self.register_template(ConfigTemplate(
            name="database_config",
            type=ConfigType.DATABASE,
            format=ConfigFormat.JSON,
            template_path="database.json.j2",
            output_path="config/database.json",
            description="数据库连接配置",
            required_variables=["db_type", "db_host", "db_port", "db_name"],
            optional_variables={
                "db_user": "aqua_user",
                "db_password": "aqua_password",
                "db_pool_size": 10,
                "db_timeout": 30
            }
        ))
        
        # 服务器配置模板
        self.register_template(ConfigTemplate(
            name="server_config",
            type=ConfigType.SERVER,
            format=ConfigFormat.YAML,
            template_path="server.yaml.j2",
            output_path="config/server.yaml",
            description="服务器运行配置",
            required_variables=["server_host", "server_port"],
            optional_variables={
                "debug": False,
                "workers": 4,
                "timeout": 60,
                "max_connections": 1000
            }
        ))
        
        # 日志配置模板
        self.register_template(ConfigTemplate(
            name="logging_config",
            type=ConfigType.LOGGING,
            format=ConfigFormat.YAML,
            template_path="logging.yaml.j2",
            output_path="config/logging.yaml",
            description="日志系统配置",
            required_variables=["log_level"],
            optional_variables={
                "log_file": "logs/aqua.log",
                "max_file_size": "10MB",
                "backup_count": 5,
                "console_output": True
            }
        ))
        
        # 环境变量模板
        self.register_template(ConfigTemplate(
            name="env_config",
            type=ConfigType.ENVIRONMENT,
            format=ConfigFormat.ENV,
            template_path="environment.env.j2",
            output_path=".env",
            description="环境变量配置",
            required_variables=["environment"],
            optional_variables={
                "debug": "false",
                "secret_key": "your-secret-key-here",
                "api_base_url": "http://localhost:8000"
            }
        ))
        
        # 前端配置模板
        self.register_template(ConfigTemplate(
            name="frontend_config",
            type=ConfigType.FRONTEND,
            format=ConfigFormat.JSON,
            template_path="frontend.json.j2",
            output_path="frontend/config/app.json",
            description="前端应用配置",
            required_variables=["api_url"],
            optional_variables={
                "app_name": "AQUA",
                "version": "1.0.0",
                "theme": "default",
                "language": "zh-CN"
            }
        ))
    
    def register_template(self, template: ConfigTemplate):
        """注册配置模板"""
        self.templates[template.name] = template
        self.logger.info(f"注册配置模板: {template.name}")
    
    def create_template_file(self, template: ConfigTemplate, content: str):
        """创建模板文件"""
        template_file = self.templates_dir / template.template_path
        template_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.logger.info(f"创建模板文件: {template_file}")
    
    def generate_config(self, template_name: str, variables: Dict[str, Any], 
                       output_path: Optional[str] = None) -> bool:
        """生成配置文件"""
        if template_name not in self.templates:
            self.logger.error(f"模板不存在: {template_name}")
            return False
        
        template = self.templates[template_name]
        
        # 检查必需变量
        missing_vars = [var for var in template.required_variables if var not in variables]
        if missing_vars:
            self.logger.error(f"缺少必需变量: {missing_vars}")
            return False
        
        # 合并可选变量
        config_vars = template.optional_variables.copy()
        config_vars.update(variables)
        
        # 添加系统信息
        config_vars.update({
            "system": self.system,
            "platform": platform.platform(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "project_root": str(self.project_root)
        })
        
        try:
            # 加载模板
            jinja_template = self.jinja_env.get_template(template.template_path)
            
            # 渲染配置
            rendered_content = jinja_template.render(**config_vars)
            
            # 确定输出路径
            final_output_path = output_path or template.output_path
            output_file = self.project_root / final_output_path
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(rendered_content)
            
            self.logger.info(f"生成配置文件: {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成配置文件失败: {e}")
            return False
    
    def generate_all_configs(self, global_variables: Dict[str, Any]) -> Dict[str, bool]:
        """生成所有配置文件"""
        results = {}
        
        for template_name, template in self.templates.items():
            # 跳过平台特定模板（如果不匹配）
            if template.platform_specific and not self._is_platform_compatible(template):
                continue
            
            result = self.generate_config(template_name, global_variables)
            results[template_name] = result
        
        return results
    
    def _is_platform_compatible(self, template: ConfigTemplate) -> bool:
        """检查模板是否与当前平台兼容"""
        # 这里可以添加更复杂的平台兼容性逻辑
        return True
    
    def validate_config_file(self, file_path: Path, format: ConfigFormat) -> bool:
        """验证配置文件格式"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if format == ConfigFormat.JSON:
                json.loads(content)
            elif format == ConfigFormat.YAML:
                yaml.safe_load(content)
            elif format == ConfigFormat.ENV:
                # 简单验证.env文件格式
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#') and '=' not in line:
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置文件验证失败: {file_path} - {e}")
            return False
    
    def backup_config(self, config_path: str) -> Optional[str]:
        """备份配置文件"""
        config_file = self.project_root / config_path
        
        if not config_file.exists():
            return None
        
        # 生成备份文件名
        timestamp = int(time.time())
        backup_file = config_file.with_suffix(f".backup.{timestamp}{config_file.suffix}")
        
        try:
            shutil.copy2(config_file, backup_file)
            self.logger.info(f"备份配置文件: {backup_file}")
            return str(backup_file)
        except Exception as e:
            self.logger.error(f"备份配置文件失败: {e}")
            return None
    
    def restore_config(self, backup_path: str, target_path: str) -> bool:
        """恢复配置文件"""
        backup_file = Path(backup_path)
        target_file = self.project_root / target_path
        
        if not backup_file.exists():
            self.logger.error(f"备份文件不存在: {backup_file}")
            return False
        
        try:
            shutil.copy2(backup_file, target_file)
            self.logger.info(f"恢复配置文件: {target_file}")
            return True
        except Exception as e:
            self.logger.error(f"恢复配置文件失败: {e}")
            return False
    
    def list_templates(self) -> List[Dict[str, Any]]:
        """列出所有模板"""
        templates_info = []
        
        for name, template in self.templates.items():
            templates_info.append({
                "name": name,
                "type": template.type.value,
                "format": template.format.value,
                "description": template.description,
                "required_variables": template.required_variables,
                "optional_variables": list(template.optional_variables.keys())
            })
        
        return templates_info

    def generate_environment_specific_configs(self, environment: str) -> Dict[str, bool]:
        """生成环境特定配置"""
        env_configs = {
            "development": {
                "debug": True,
                "log_level": "DEBUG",
                "db_pool_size": 5,
                "server_workers": 1,
                "api_base_url": "http://localhost:8000"
            },
            "testing": {
                "debug": True,
                "log_level": "INFO",
                "db_pool_size": 3,
                "server_workers": 1,
                "api_base_url": "http://localhost:8001"
            },
            "production": {
                "debug": False,
                "log_level": "WARNING",
                "db_pool_size": 20,
                "server_workers": 4,
                "api_base_url": "https://api.aqua.com"
            }
        }

        if environment not in env_configs:
            self.logger.error(f"不支持的环境: {environment}")
            return {}

        config_vars = env_configs[environment]
        config_vars["environment"] = environment

        return self.generate_all_configs(config_vars)


def main():
    """主函数 - 测试配置模板系统"""
    import time
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    
    print("⚙️ 配置模板系统测试")
    print("=" * 40)
    
    # 创建配置模板系统
    config_system = ConfigTemplateSystem(project_root)
    
    # 创建默认模板文件
    print("\n📝 创建默认模板文件...")
    
    # 数据库配置模板
    db_template = """
{
  "database": {
    "type": "{{ db_type }}",
    "host": "{{ db_host }}",
    "port": {{ db_port }},
    "name": "{{ db_name }}",
    "user": "{{ db_user }}",
    "password": "{{ db_password }}",
    "pool_size": {{ db_pool_size }},
    "timeout": {{ db_timeout }}
  },
  "generated_at": "{{ ansible_date_time.iso8601 if ansible_date_time else 'unknown' }}",
  "platform": "{{ system }}"
}
""".strip()
    
    config_system.create_template_file(
        config_system.templates["database_config"], 
        db_template
    )
    
    # 环境变量模板
    env_template = """
# AQUA项目环境变量配置
# 生成时间: {{ ansible_date_time.iso8601 if ansible_date_time else 'unknown' }}
# 平台: {{ system }}

# 基本配置
ENVIRONMENT={{ environment }}
DEBUG={{ debug }}
SECRET_KEY={{ secret_key }}

# API配置
API_BASE_URL={{ api_base_url }}

# 项目路径
PROJECT_ROOT={{ project_root }}
""".strip()
    
    config_system.create_template_file(
        config_system.templates["env_config"], 
        env_template
    )
    
    # 列出所有模板
    print(f"\n📋 已注册的模板:")
    templates = config_system.list_templates()
    for template in templates:
        print(f"  - {template['name']}: {template['description']}")
        print(f"    格式: {template['format']}, 类型: {template['type']}")
        print(f"    必需变量: {template['required_variables']}")
    
    # 测试生成配置文件
    print(f"\n🔧 测试生成配置文件...")
    
    test_variables = {
        "db_type": "sqlite",
        "db_host": "localhost",
        "db_port": 5432,
        "db_name": "aqua_db",
        "environment": "development",
        "api_url": "http://localhost:8000/api"
    }
    
    results = config_system.generate_all_configs(test_variables)
    
    for template_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {template_name}")
    
    print("\n✅ 配置模板系统测试完成")


if __name__ == "__main__":
    main()
