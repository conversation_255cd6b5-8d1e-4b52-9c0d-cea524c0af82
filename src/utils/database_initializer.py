#!/usr/bin/env python3
"""
数据库初始化优化工具
优化数据库连接、表结构初始化和数据导入
"""

import os
import sys
import sqlite3
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging


class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
        self.db_path = project_root / "data" / "aqua.db"
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=10)
            conn.execute("SELECT 1")
            conn.close()
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def initialize_tables(self) -> bool:
        """初始化表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 创建示例表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS stocks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    name TEXT NOT NULL,
                    price REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            self.logger.error(f"表结构初始化失败: {e}")
            return False
    
    def import_initial_data(self) -> bool:
        """导入初始数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 插入示例数据
            sample_data = [
                ("AAPL", "Apple Inc.", 150.0),
                ("GOOGL", "Alphabet Inc.", 2500.0),
                ("MSFT", "Microsoft Corporation", 300.0)
            ]
            
            conn.executemany(
                "INSERT OR IGNORE INTO stocks (symbol, name, price) VALUES (?, ?, ?)",
                sample_data
            )
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            self.logger.error(f"初始数据导入失败: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """数据库健康检查"""
        health_info = {
            "connection": False,
            "tables_exist": False,
            "data_count": 0,
            "db_size": 0
        }
        
        try:
            # 检查连接
            health_info["connection"] = self.check_connection()
            
            if health_info["connection"]:
                conn = sqlite3.connect(self.db_path)
                
                # 检查表是否存在
                cursor = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='stocks'"
                )
                health_info["tables_exist"] = cursor.fetchone() is not None
                
                # 检查数据数量
                if health_info["tables_exist"]:
                    cursor = conn.execute("SELECT COUNT(*) FROM stocks")
                    health_info["data_count"] = cursor.fetchone()[0]
                
                conn.close()
            
            # 检查数据库文件大小
            if self.db_path.exists():
                health_info["db_size"] = self.db_path.stat().st_size
                
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
        
        return health_info


class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
    
    def detect_initialization_failure(self) -> List[str]:
        """检测初始化失败"""
        failures = []
        
        # 检查关键文件是否存在
        critical_files = [
            "data/aqua.db",
            "config/database.json",
            ".env"
        ]
        
        for file_path in critical_files:
            if not (self.project_root / file_path).exists():
                failures.append(f"关键文件缺失: {file_path}")
        
        return failures
    
    def auto_recovery(self) -> bool:
        """自动恢复"""
        try:
            # 重新初始化数据库
            db_init = DatabaseInitializer(self.project_root)
            
            if not db_init.check_connection():
                db_init.initialize_tables()
                db_init.import_initial_data()
            
            return True
        except Exception as e:
            self.logger.error(f"自动恢复失败: {e}")
            return False
    
    def manual_recovery_guide(self) -> List[str]:
        """手动恢复指导"""
        return [
            "1. 检查数据库文件权限",
            "2. 重新运行初始化脚本",
            "3. 检查配置文件格式",
            "4. 清理临时文件后重试"
        ]


def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    project_root = Path(__file__).parent.parent.parent
    
    print("🗄️ 数据库初始化优化测试")
    print("=" * 40)
    
    # 初始化数据库
    db_init = DatabaseInitializer(project_root)
    
    print("检查数据库连接...")
    if db_init.check_connection():
        print("✅ 数据库连接正常")
    else:
        print("❌ 数据库连接失败，开始初始化...")
        
        if db_init.initialize_tables():
            print("✅ 表结构初始化成功")
        else:
            print("❌ 表结构初始化失败")
        
        if db_init.import_initial_data():
            print("✅ 初始数据导入成功")
        else:
            print("❌ 初始数据导入失败")
    
    # 健康检查
    health = db_init.health_check()
    print(f"\n📊 数据库健康状态:")
    print(f"  连接状态: {'✅' if health['connection'] else '❌'}")
    print(f"  表结构: {'✅' if health['tables_exist'] else '❌'}")
    print(f"  数据数量: {health['data_count']}")
    print(f"  数据库大小: {health['db_size']} bytes")
    
    # 错误恢复测试
    recovery = ErrorRecoveryManager(project_root)
    failures = recovery.detect_initialization_failure()
    
    if failures:
        print(f"\n⚠️ 检测到初始化问题:")
        for failure in failures:
            print(f"  - {failure}")
        
        print(f"\n🔧 手动恢复指导:")
        for step in recovery.manual_recovery_guide():
            print(f"  {step}")
    else:
        print(f"\n✅ 初始化状态正常")
    
    print("\n✅ 数据库初始化优化测试完成")


if __name__ == "__main__":
    main()
