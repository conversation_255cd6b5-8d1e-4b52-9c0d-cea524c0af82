#!/usr/bin/env python3
"""
平台特定服务适配器
为不同平台提供专门的服务启动、管理和监控逻辑
"""

import os
import sys
import platform
import subprocess
import json
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.unified_service_launcher import ServiceConfig, ServiceInstance, ServiceStatus


@dataclass
class PlatformServiceResult:
    """平台服务操作结果"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error_code: Optional[int] = None


class PlatformServiceAdapter(ABC):
    """平台服务适配器基类"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def start_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """启动服务"""
        pass
    
    @abstractmethod
    def stop_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """停止服务"""
        pass
    
    @abstractmethod
    def get_service_status(self, service: ServiceInstance) -> PlatformServiceResult:
        """获取服务状态"""
        pass
    
    @abstractmethod
    def install_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """安装服务（系统服务）"""
        pass
    
    @abstractmethod
    def uninstall_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """卸载服务（系统服务）"""
        pass


class WindowsServiceAdapter(PlatformServiceAdapter):
    """Windows平台服务适配器"""
    
    def __init__(self, project_root: Path):
        super().__init__(project_root)
        self.powershell_script = project_root / "Start-AQUA.ps1"
    
    def start_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """启动Windows服务"""
        config = service.config
        
        try:
            # 优先使用PowerShell脚本
            if self.powershell_script.exists() and config.name in ["backend", "frontend"]:
                return self._start_with_powershell(service)
            
            # 检查是否是Windows服务
            if self._is_windows_service(config.name):
                return self._start_windows_service(service)
            
            # 使用直接启动
            return self._start_direct(service)
            
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Windows服务启动失败: {e}",
                error_code=1
            )
    
    def stop_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """停止Windows服务"""
        config = service.config
        
        try:
            # 检查是否是Windows服务
            if self._is_windows_service(config.name):
                return self._stop_windows_service(service)
            
            # 使用进程终止
            if service.process:
                service.process.terminate()
                service.process.wait(timeout=10)
                
                return PlatformServiceResult(
                    success=True,
                    message=f"Windows服务已停止: {config.name}"
                )
            
            return PlatformServiceResult(
                success=True,
                message=f"服务未在运行: {config.name}"
            )
            
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Windows服务停止失败: {e}",
                error_code=1
            )
    
    def get_service_status(self, service: ServiceInstance) -> PlatformServiceResult:
        """获取Windows服务状态"""
        config = service.config
        
        try:
            # 检查是否是Windows服务
            if self._is_windows_service(config.name):
                return self._get_windows_service_status(service)
            
            # 检查进程状态
            if service.process:
                if service.process.poll() is None:
                    status = ServiceStatus.RUNNING
                else:
                    status = ServiceStatus.STOPPED
            else:
                status = ServiceStatus.STOPPED
            
            return PlatformServiceResult(
                success=True,
                message=f"服务状态: {status}",
                data={"status": status}
            )
            
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"获取Windows服务状态失败: {e}",
                error_code=1
            )
    
    def install_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """安装Windows服务"""
        config = service.config
        
        try:
            # 生成Windows服务配置
            service_config = self._generate_windows_service_config(config)
            
            # 使用sc命令安装服务
            cmd = [
                "sc", "create", config.name,
                "binPath=", service_config["binPath"],
                "DisplayName=", service_config["displayName"],
                "start=", "auto"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"Windows服务安装成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"Windows服务安装失败: {result.stderr}",
                    error_code=result.returncode
                )
                
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Windows服务安装异常: {e}",
                error_code=1
            )
    
    def uninstall_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """卸载Windows服务"""
        config = service.config
        
        try:
            # 先停止服务
            self.stop_service(service)
            
            # 使用sc命令删除服务
            cmd = ["sc", "delete", config.name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"Windows服务卸载成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"Windows服务卸载失败: {result.stderr}",
                    error_code=result.returncode
                )
                
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Windows服务卸载异常: {e}",
                error_code=1
            )
    
    def _start_with_powershell(self, service: ServiceInstance) -> PlatformServiceResult:
        """使用PowerShell脚本启动服务"""
        try:
            cmd = [
                "powershell.exe",
                "-ExecutionPolicy", "Bypass",
                "-File", str(self.powershell_script)
            ]
            
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            service.process = process
            service.pid = process.pid
            
            return PlatformServiceResult(
                success=True,
                message=f"PowerShell启动成功: {service.config.name}",
                data={"pid": process.pid}
            )
            
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"PowerShell启动失败: {e}",
                error_code=1
            )
    
    def _start_direct(self, service: ServiceInstance) -> PlatformServiceResult:
        """直接启动服务"""
        config = service.config
        
        try:
            # 准备命令
            if isinstance(config.command, str):
                cmd = config.command
                shell = True
            else:
                cmd = config.command
                shell = False
            
            # 准备环境变量
            env = os.environ.copy()
            if config.environment:
                env.update(config.environment)
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                shell=shell,
                cwd=config.working_directory or self.project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            service.process = process
            service.pid = process.pid
            
            return PlatformServiceResult(
                success=True,
                message=f"直接启动成功: {config.name}",
                data={"pid": process.pid}
            )
            
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"直接启动失败: {e}",
                error_code=1
            )
    
    def _is_windows_service(self, service_name: str) -> bool:
        """检查是否是Windows系统服务"""
        try:
            cmd = ["sc", "query", service_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def _start_windows_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """启动Windows系统服务"""
        config = service.config
        
        try:
            cmd = ["sc", "start", config.name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"Windows系统服务启动成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"Windows系统服务启动失败: {result.stderr}",
                    error_code=result.returncode
                )
                
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Windows系统服务启动异常: {e}",
                error_code=1
            )
    
    def _stop_windows_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """停止Windows系统服务"""
        config = service.config
        
        try:
            cmd = ["sc", "stop", config.name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"Windows系统服务停止成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"Windows系统服务停止失败: {result.stderr}",
                    error_code=result.returncode
                )
                
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Windows系统服务停止异常: {e}",
                error_code=1
            )
    
    def _get_windows_service_status(self, service: ServiceInstance) -> PlatformServiceResult:
        """获取Windows系统服务状态"""
        config = service.config
        
        try:
            cmd = ["sc", "query", config.name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # 解析服务状态
                output = result.stdout
                if "RUNNING" in output:
                    status = ServiceStatus.RUNNING
                elif "STOPPED" in output:
                    status = ServiceStatus.STOPPED
                elif "START_PENDING" in output:
                    status = ServiceStatus.STARTING
                elif "STOP_PENDING" in output:
                    status = ServiceStatus.STOPPING
                else:
                    status = ServiceStatus.UNKNOWN
                
                return PlatformServiceResult(
                    success=True,
                    message=f"Windows系统服务状态: {status}",
                    data={"status": status, "output": output}
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"获取Windows系统服务状态失败: {result.stderr}",
                    error_code=result.returncode
                )
                
        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"获取Windows系统服务状态异常: {e}",
                error_code=1
            )
    
    def _generate_windows_service_config(self, config: ServiceConfig) -> Dict[str, str]:
        """生成Windows服务配置"""
        # 构建服务可执行路径
        if isinstance(config.command, list):
            bin_path = " ".join(config.command)
        else:
            bin_path = config.command
        
        return {
            "binPath": bin_path,
            "displayName": f"AQUA {config.name.title()} Service",
            "description": f"AQUA项目的{config.name}服务"
        }


class MacOSServiceAdapter(PlatformServiceAdapter):
    """macOS平台服务适配器"""

    def __init__(self, project_root: Path):
        super().__init__(project_root)
        self.launchd_dir = Path.home() / "Library" / "LaunchAgents"
        self.launchd_dir.mkdir(parents=True, exist_ok=True)

    def start_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """启动macOS服务"""
        config = service.config

        try:
            # 检查是否是launchd服务
            if self._is_launchd_service(config.name):
                return self._start_launchd_service(service)

            # 使用直接启动
            return self._start_direct(service)

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"macOS服务启动失败: {e}",
                error_code=1
            )

    def stop_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """停止macOS服务"""
        config = service.config

        try:
            # 检查是否是launchd服务
            if self._is_launchd_service(config.name):
                return self._stop_launchd_service(service)

            # 使用进程终止
            if service.process:
                service.process.terminate()
                service.process.wait(timeout=10)

                return PlatformServiceResult(
                    success=True,
                    message=f"macOS服务已停止: {config.name}"
                )

            return PlatformServiceResult(
                success=True,
                message=f"服务未在运行: {config.name}"
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"macOS服务停止失败: {e}",
                error_code=1
            )

    def get_service_status(self, service: ServiceInstance) -> PlatformServiceResult:
        """获取macOS服务状态"""
        config = service.config

        try:
            # 检查是否是launchd服务
            if self._is_launchd_service(config.name):
                return self._get_launchd_service_status(service)

            # 检查进程状态
            if service.process:
                if service.process.poll() is None:
                    status = ServiceStatus.RUNNING
                else:
                    status = ServiceStatus.STOPPED
            else:
                status = ServiceStatus.STOPPED

            return PlatformServiceResult(
                success=True,
                message=f"服务状态: {status}",
                data={"status": status}
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"获取macOS服务状态失败: {e}",
                error_code=1
            )

    def install_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """安装macOS launchd服务"""
        config = service.config

        try:
            # 生成launchd plist文件
            plist_content = self._generate_launchd_plist(config)
            plist_file = self.launchd_dir / f"com.aqua.{config.name}.plist"

            # 写入plist文件
            with open(plist_file, 'w') as f:
                f.write(plist_content)

            # 加载服务
            cmd = ["launchctl", "load", str(plist_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"macOS launchd服务安装成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"macOS launchd服务安装失败: {result.stderr}",
                    error_code=result.returncode
                )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"macOS launchd服务安装异常: {e}",
                error_code=1
            )

    def uninstall_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """卸载macOS launchd服务"""
        config = service.config

        try:
            plist_file = self.launchd_dir / f"com.aqua.{config.name}.plist"

            # 卸载服务
            if plist_file.exists():
                cmd = ["launchctl", "unload", str(plist_file)]
                subprocess.run(cmd, capture_output=True, text=True)

                # 删除plist文件
                plist_file.unlink()

            return PlatformServiceResult(
                success=True,
                message=f"macOS launchd服务卸载成功: {config.name}"
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"macOS launchd服务卸载异常: {e}",
                error_code=1
            )

    def _start_direct(self, service: ServiceInstance) -> PlatformServiceResult:
        """直接启动服务"""
        config = service.config

        try:
            # 准备命令
            if isinstance(config.command, str):
                cmd = config.command
                shell = True
            else:
                cmd = config.command
                shell = False

            # 准备环境变量
            env = os.environ.copy()
            if config.environment:
                env.update(config.environment)

            # 启动进程
            process = subprocess.Popen(
                cmd,
                shell=shell,
                cwd=config.working_directory or self.project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            service.process = process
            service.pid = process.pid

            return PlatformServiceResult(
                success=True,
                message=f"直接启动成功: {config.name}",
                data={"pid": process.pid}
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"直接启动失败: {e}",
                error_code=1
            )

    def _is_launchd_service(self, service_name: str) -> bool:
        """检查是否是launchd服务"""
        plist_file = self.launchd_dir / f"com.aqua.{service_name}.plist"
        return plist_file.exists()

    def _start_launchd_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """启动launchd服务"""
        config = service.config

        try:
            cmd = ["launchctl", "start", f"com.aqua.{config.name}"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"launchd服务启动成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"launchd服务启动失败: {result.stderr}",
                    error_code=result.returncode
                )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"launchd服务启动异常: {e}",
                error_code=1
            )

    def _stop_launchd_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """停止launchd服务"""
        config = service.config

        try:
            cmd = ["launchctl", "stop", f"com.aqua.{config.name}"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"launchd服务停止成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"launchd服务停止失败: {result.stderr}",
                    error_code=result.returncode
                )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"launchd服务停止异常: {e}",
                error_code=1
            )

    def _get_launchd_service_status(self, service: ServiceInstance) -> PlatformServiceResult:
        """获取launchd服务状态"""
        config = service.config

        try:
            cmd = ["launchctl", "list", f"com.aqua.{config.name}"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 解析服务状态
                output = result.stdout
                if "PID" in output and output.split()[0] != "-":
                    status = ServiceStatus.RUNNING
                else:
                    status = ServiceStatus.STOPPED

                return PlatformServiceResult(
                    success=True,
                    message=f"launchd服务状态: {status}",
                    data={"status": status, "output": output}
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"获取launchd服务状态失败: {result.stderr}",
                    error_code=result.returncode
                )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"获取launchd服务状态异常: {e}",
                error_code=1
            )

    def _generate_launchd_plist(self, config: ServiceConfig) -> str:
        """生成launchd plist文件内容"""
        # 构建程序参数
        if isinstance(config.command, list):
            program_arguments = config.command
        else:
            program_arguments = config.command.split()

        # 构建环境变量
        environment_variables = config.environment or {}

        plist_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.aqua.{config.name}</string>
    <key>ProgramArguments</key>
    <array>
"""

        for arg in program_arguments:
            plist_content += f"        <string>{arg}</string>\n"

        plist_content += """    </array>
    <key>WorkingDirectory</key>
    <string>{}</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
""".format(config.working_directory or self.project_root)

        if environment_variables:
            plist_content += "    <key>EnvironmentVariables</key>\n    <dict>\n"
            for key, value in environment_variables.items():
                plist_content += f"        <key>{key}</key>\n        <string>{value}</string>\n"
            plist_content += "    </dict>\n"

        plist_content += """    <key>StandardOutPath</key>
    <string>{}/logs/{}.log</string>
    <key>StandardErrorPath</key>
    <string>{}/logs/{}.error.log</string>
</dict>
</plist>""".format(self.project_root, config.name, self.project_root, config.name)

        return plist_content


class LinuxServiceAdapter(PlatformServiceAdapter):
    """Linux平台服务适配器"""

    def __init__(self, project_root: Path):
        super().__init__(project_root)
        self.systemd_user_dir = Path.home() / ".config" / "systemd" / "user"
        self.systemd_user_dir.mkdir(parents=True, exist_ok=True)

    def start_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """启动Linux服务"""
        config = service.config

        try:
            # 检查是否是systemd服务
            if self._is_systemd_service(config.name):
                return self._start_systemd_service(service)

            # 使用直接启动
            return self._start_direct(service)

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Linux服务启动失败: {e}",
                error_code=1
            )

    def stop_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """停止Linux服务"""
        config = service.config

        try:
            # 检查是否是systemd服务
            if self._is_systemd_service(config.name):
                return self._stop_systemd_service(service)

            # 使用进程终止
            if service.process:
                service.process.terminate()
                service.process.wait(timeout=10)

                return PlatformServiceResult(
                    success=True,
                    message=f"Linux服务已停止: {config.name}"
                )

            return PlatformServiceResult(
                success=True,
                message=f"服务未在运行: {config.name}"
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Linux服务停止失败: {e}",
                error_code=1
            )

    def get_service_status(self, service: ServiceInstance) -> PlatformServiceResult:
        """获取Linux服务状态"""
        config = service.config

        try:
            # 检查是否是systemd服务
            if self._is_systemd_service(config.name):
                return self._get_systemd_service_status(service)

            # 检查进程状态
            if service.process:
                if service.process.poll() is None:
                    status = ServiceStatus.RUNNING
                else:
                    status = ServiceStatus.STOPPED
            else:
                status = ServiceStatus.STOPPED

            return PlatformServiceResult(
                success=True,
                message=f"服务状态: {status}",
                data={"status": status}
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"获取Linux服务状态失败: {e}",
                error_code=1
            )

    def install_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """安装Linux systemd服务"""
        config = service.config

        try:
            # 生成systemd service文件
            service_content = self._generate_systemd_service(config)
            service_file = self.systemd_user_dir / f"aqua-{config.name}.service"

            # 写入service文件
            with open(service_file, 'w') as f:
                f.write(service_content)

            # 重新加载systemd配置
            cmd = ["systemctl", "--user", "daemon-reload"]
            subprocess.run(cmd, capture_output=True, text=True)

            # 启用服务
            cmd = ["systemctl", "--user", "enable", f"aqua-{config.name}.service"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"Linux systemd服务安装成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"Linux systemd服务安装失败: {result.stderr}",
                    error_code=result.returncode
                )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Linux systemd服务安装异常: {e}",
                error_code=1
            )

    def uninstall_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """卸载Linux systemd服务"""
        config = service.config

        try:
            service_file = self.systemd_user_dir / f"aqua-{config.name}.service"

            # 停止并禁用服务
            cmd = ["systemctl", "--user", "stop", f"aqua-{config.name}.service"]
            subprocess.run(cmd, capture_output=True, text=True)

            cmd = ["systemctl", "--user", "disable", f"aqua-{config.name}.service"]
            subprocess.run(cmd, capture_output=True, text=True)

            # 删除service文件
            if service_file.exists():
                service_file.unlink()

            # 重新加载systemd配置
            cmd = ["systemctl", "--user", "daemon-reload"]
            subprocess.run(cmd, capture_output=True, text=True)

            return PlatformServiceResult(
                success=True,
                message=f"Linux systemd服务卸载成功: {config.name}"
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"Linux systemd服务卸载异常: {e}",
                error_code=1
            )

    def _start_direct(self, service: ServiceInstance) -> PlatformServiceResult:
        """直接启动服务"""
        config = service.config

        try:
            # 准备命令
            if isinstance(config.command, str):
                cmd = config.command
                shell = True
            else:
                cmd = config.command
                shell = False

            # 准备环境变量
            env = os.environ.copy()
            if config.environment:
                env.update(config.environment)

            # 启动进程
            process = subprocess.Popen(
                cmd,
                shell=shell,
                cwd=config.working_directory or self.project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            service.process = process
            service.pid = process.pid

            return PlatformServiceResult(
                success=True,
                message=f"直接启动成功: {config.name}",
                data={"pid": process.pid}
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"直接启动失败: {e}",
                error_code=1
            )

    def _is_systemd_service(self, service_name: str) -> bool:
        """检查是否是systemd服务"""
        service_file = self.systemd_user_dir / f"aqua-{service_name}.service"
        return service_file.exists()

    def _start_systemd_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """启动systemd服务"""
        config = service.config

        try:
            cmd = ["systemctl", "--user", "start", f"aqua-{config.name}.service"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"systemd服务启动成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"systemd服务启动失败: {result.stderr}",
                    error_code=result.returncode
                )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"systemd服务启动异常: {e}",
                error_code=1
            )

    def _stop_systemd_service(self, service: ServiceInstance) -> PlatformServiceResult:
        """停止systemd服务"""
        config = service.config

        try:
            cmd = ["systemctl", "--user", "stop", f"aqua-{config.name}.service"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return PlatformServiceResult(
                    success=True,
                    message=f"systemd服务停止成功: {config.name}"
                )
            else:
                return PlatformServiceResult(
                    success=False,
                    message=f"systemd服务停止失败: {result.stderr}",
                    error_code=result.returncode
                )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"systemd服务停止异常: {e}",
                error_code=1
            )

    def _get_systemd_service_status(self, service: ServiceInstance) -> PlatformServiceResult:
        """获取systemd服务状态"""
        config = service.config

        try:
            cmd = ["systemctl", "--user", "is-active", f"aqua-{config.name}.service"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            output = result.stdout.strip()
            if output == "active":
                status = ServiceStatus.RUNNING
            elif output == "inactive":
                status = ServiceStatus.STOPPED
            elif output == "activating":
                status = ServiceStatus.STARTING
            elif output == "deactivating":
                status = ServiceStatus.STOPPING
            else:
                status = ServiceStatus.UNKNOWN

            return PlatformServiceResult(
                success=True,
                message=f"systemd服务状态: {status}",
                data={"status": status, "output": output}
            )

        except Exception as e:
            return PlatformServiceResult(
                success=False,
                message=f"获取systemd服务状态异常: {e}",
                error_code=1
            )

    def _generate_systemd_service(self, config: ServiceConfig) -> str:
        """生成systemd service文件内容"""
        # 构建执行命令
        if isinstance(config.command, list):
            exec_start = " ".join(config.command)
        else:
            exec_start = config.command

        # 构建环境变量
        environment_lines = ""
        if config.environment:
            for key, value in config.environment.items():
                environment_lines += f"Environment={key}={value}\n"

        service_content = f"""[Unit]
Description=AQUA {config.name.title()} Service
After=network.target

[Service]
Type=simple
ExecStart={exec_start}
WorkingDirectory={config.working_directory or self.project_root}
{environment_lines}Restart=always
RestartSec=10
User={os.getenv('USER', 'aqua')}

[Install]
WantedBy=default.target
"""

        return service_content


class PlatformServiceAdapterFactory:
    """平台服务适配器工厂"""

    @staticmethod
    def create_adapter(project_root: Path) -> PlatformServiceAdapter:
        """根据当前平台创建适配器"""
        system = platform.system()

        if system == "Windows":
            return WindowsServiceAdapter(project_root)
        elif system == "Darwin":
            return MacOSServiceAdapter(project_root)
        elif system == "Linux":
            return LinuxServiceAdapter(project_root)
        else:
            raise ValueError(f"不支持的平台: {system}")

    @staticmethod
    def get_supported_platforms() -> List[str]:
        """获取支持的平台列表"""
        return ["Windows", "Darwin", "Linux"]


# 便捷函数
def create_platform_adapter(project_root: Optional[Path] = None) -> PlatformServiceAdapter:
    """创建平台适配器的便捷函数"""
    if project_root is None:
        project_root = Path(__file__).parent.parent.parent

    return PlatformServiceAdapterFactory.create_adapter(project_root)


if __name__ == "__main__":
    # 测试平台服务适配器
    print("🔧 平台服务适配器测试")
    print("=" * 40)

    try:
        adapter = create_platform_adapter()
        print(f"当前平台: {platform.system()}")
        print(f"适配器类型: {type(adapter).__name__}")
        print("✅ 平台服务适配器创建成功")
    except Exception as e:
        print(f"❌ 平台服务适配器创建失败: {e}")

    print("✅ 平台服务适配器测试完成")
