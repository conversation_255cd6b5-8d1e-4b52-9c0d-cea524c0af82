#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA简化服务管理系统

简单优于复杂的服务管理，一键启动所有服务
版本: 1.0.0
创建时间: 2025-07-31
"""

import os
import sys
import subprocess
import threading
import time
import signal
from pathlib import Path
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum

# 导入AQUA工具
from .simple_config import get_config_manager
from .simple_error import safe_call, handle_error, ErrorLevel
from .cli_ui import get_ui


class ServiceStatus(Enum):
    """服务状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    ERROR = "error"


@dataclass
class Service:
    """服务定义"""
    name: str
    command: str
    description: str = ""
    port: Optional[int] = None
    health_check: Optional[str] = None
    startup_time: int = 3
    required: bool = True
    
    # 运行时状态
    status: ServiceStatus = ServiceStatus.STOPPED
    process: Optional[subprocess.Popen] = None
    start_time: Optional[float] = None
    logs: List[str] = field(default_factory=list)


class ServiceManager:
    """
    简化服务管理器
    
    核心理念：
    1. 一键启动所有服务
    2. 智能健康检查
    3. 优雅关闭机制
    4. 实时状态监控
    """
    
    def __init__(self):
        """初始化服务管理器"""
        self.config = get_config_manager()
        self.ui = get_ui()
        self.services: Dict[str, Service] = {}
        self.running = False
        self.shutdown_event = threading.Event()

        # Windows平台特定初始化
        if os.name == 'nt':
            self._windows_init()

        # 定义服务
        self._define_services()

        # 设置信号处理
        self._setup_signal_handlers()

    def _windows_init(self):
        """Windows平台特定初始化"""
        try:
            # 检查关键依赖
            self._check_windows_dependencies()

            # 设置Windows特定的环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUNBUFFERED'] = '1'

        except Exception as e:
            self.ui.print_step(f"Windows初始化警告: {e}", "warning")

    def _check_windows_dependencies(self):
        """检查Windows平台依赖"""
        missing_deps = []

        # 检查Python依赖
        try:
            import h11, pydantic_core
        except ImportError as e:
            missing_deps.append(f"Python依赖: {e}")

        # 检查前端依赖
        frontend_path = Path(__file__).parent.parent.parent / "frontend"
        if frontend_path.exists() and not (frontend_path / "node_modules").exists():
            missing_deps.append("前端依赖: node_modules不存在")

        if missing_deps:
            self.ui.print_step("检测到缺失依赖，尝试自动修复...", "warning")
            self._auto_fix_dependencies(missing_deps)

    def _auto_fix_dependencies(self, missing_deps):
        """自动修复依赖问题"""
        for dep in missing_deps:
            if "Python依赖" in dep:
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "install",
                        "h11", "pydantic-core", "pydantic"
                    ], check=True, capture_output=True)
                    self.ui.print_step("Python依赖修复成功", "success")
                except subprocess.CalledProcessError:
                    self.ui.print_step("Python依赖修复失败，请手动安装", "error")

            elif "前端依赖" in dep:
                try:
                    frontend_path = Path(__file__).parent.parent.parent / "frontend"
                    subprocess.run(["pnpm", "install"], cwd=frontend_path, check=True, capture_output=True)
                    self.ui.print_step("前端依赖修复成功", "success")
                except (subprocess.CalledProcessError, FileNotFoundError):
                    self.ui.print_step("前端依赖修复失败，请手动运行: cd frontend && pnpm install", "error")

    def _define_services(self):
        """定义AQUA服务"""
        root_path = Path(__file__).parent.parent.parent
        
        # 检测虚拟环境路径
        venv_path = root_path / ".venv"
        if sys.platform == "win32":
            python_path = venv_path / "Scripts" / "python.exe"
            uvicorn_path = venv_path / "Scripts" / "uvicorn.exe"
        else:
            python_path = venv_path / "bin" / "python"
            uvicorn_path = venv_path / "bin" / "uvicorn"
        
        # 后端API服务
        backend_cmd = f"{uvicorn_path} src.api.main:app --host 127.0.0.1 --port 8000 --reload"
        if self.config.is_debug():
            backend_cmd += " --log-level debug"
        
        self.services["backend"] = Service(
            name="backend",
            command=backend_cmd,
            description="AQUA后端API服务",
            port=8000,
            health_check="http://127.0.0.1:8000/",
            startup_time=5,
            required=True
        )
        
        # 前端开发服务
        frontend_path = root_path / "frontend"
        if frontend_path.exists():
            # 检测包管理器
            if (frontend_path / "pnpm-lock.yaml").exists():
                npm_cmd = "pnpm"
            elif (frontend_path / "yarn.lock").exists():
                npm_cmd = "yarn"
            else:
                npm_cmd = "npm"
            
            # 构建跨平台兼容的前端启动命令
            if os.name == 'nt':  # Windows
                # Windows: 使用绝对路径和更稳定的命令格式
                frontend_abs_path = root_path / "frontend"
                # 检查npm_cmd是否在PATH中，如果不在则使用npx
                try:
                    subprocess.run([npm_cmd, "--version"], check=True, capture_output=True)
                    frontend_cmd = f'cmd /c "cd /d "{frontend_abs_path}" && {npm_cmd} dev"'
                except (subprocess.CalledProcessError, FileNotFoundError):
                    # 回退到npx方式
                    frontend_cmd = f'cmd /c "cd /d "{frontend_abs_path}" && npx {npm_cmd} dev"'
            else:  # macOS/Linux - 保持原有逻辑不变
                frontend_cmd = f"cd frontend && {npm_cmd} dev"

            self.services["frontend"] = Service(
                name="frontend",
                command=frontend_cmd,
                description="Vue.js前端开发服务",
                port=5173,
                health_check="http://localhost:5173/",
                startup_time=10,
                required=False
            )
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.ui.print_step("接收到终止信号，正在关闭服务...", "warning")
            self.stop_all_services()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start_service(self, service_name: str) -> bool:
        """启动单个服务"""
        if service_name not in self.services:
            self.ui.print_step(f"服务 {service_name} 不存在", "error")
            return False
        
        service = self.services[service_name]
        
        if service.status == ServiceStatus.RUNNING:
            self.ui.print_step(f"{service.name} 已在运行", "info")
            return True
        
        self.ui.print_step(f"启动 {service.description}", "loading")
        service.status = ServiceStatus.STARTING
        service.start_time = time.time()
        
        try:
            # 跨平台进程启动配置
            if os.name == 'nt':  # Windows
                # Windows特定配置：使用CREATE_NEW_PROCESS_GROUP避免继承问题
                creation_flags = subprocess.CREATE_NEW_PROCESS_GROUP
                # Windows下使用detached模式
                service.process = subprocess.Popen(
                    service.command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1,
                    creationflags=creation_flags,
                    start_new_session=False  # Windows不支持此参数
                )
            else:  # macOS/Linux - 保持原有逻辑不变
                service.process = subprocess.Popen(
                    service.command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1,
                    start_new_session=True  # Unix系统使用新会话
                )
            
            # 等待启动 - Windows需要更长时间
            wait_time = 3 if os.name == 'nt' else 1
            time.sleep(wait_time)

            # 检查进程是否还在运行
            if service.process.poll() is None:
                service.status = ServiceStatus.RUNNING

                # Windows额外验证：检查端口是否真的在监听
                if os.name == 'nt' and service.port:
                    self._verify_windows_service(service)
                
                # 启动日志监控线程
                self._start_log_monitor(service)
                
                # 启动健康检查
                if service.health_check:
                    self._start_health_check(service)
                
                self.ui.print_step(f"{service.description} 启动成功", "success")
                return True
            else:
                service.status = ServiceStatus.ERROR
                self.ui.print_step(f"{service.description} 启动失败", "error")
                return False
                
        except Exception as e:
            service.status = ServiceStatus.ERROR
            handle_error(e, ErrorLevel.ERROR, f"启动服务 {service_name}")
            return False

    def _verify_windows_service(self, service: Service):
        """Windows平台服务验证"""
        def delayed_check():
            time.sleep(5)  # 给服务更多启动时间
            try:
                import socket
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(2)
                    result = sock.connect_ex(('127.0.0.1', service.port))
                    if result == 0:
                        self.ui.print_step(f"{service.description} 端口验证成功", "success")
                    else:
                        self.ui.print_step(f"{service.description} 端口验证失败，但进程仍在运行", "warning")
            except Exception as e:
                self.ui.print_step(f"{service.description} 验证出错: {e}", "warning")

        # 在后台线程中进行验证，不阻塞主流程
        threading.Thread(target=delayed_check, daemon=True).start()
    
    def _start_log_monitor(self, service: Service):
        """启动日志监控线程"""
        def monitor_logs():
            if not service.process:
                return
            
            try:
                for line in iter(service.process.stdout.readline, ''):
                    if self.shutdown_event.is_set():
                        break
                    
                    line = line.strip()
                    if line:
                        service.logs.append(line)
                        
                        # 保持最近100行日志
                        if len(service.logs) > 100:
                            service.logs.pop(0)
                        
                        # 检查错误信息
                        if any(keyword in line.lower() for keyword in ['error', 'failed', 'exception']):
                            if self.config.is_debug():
                                self.ui.print_step(f"{service.name}: {line}", "warning")
                        
            except Exception as e:
                if not self.shutdown_event.is_set():
                    handle_error(e, ErrorLevel.WARNING, f"监控 {service.name} 日志")
        
        thread = threading.Thread(target=monitor_logs, daemon=True)
        thread.start()
    
    def _start_health_check(self, service: Service):
        """启动健康检查"""
        def check_health():
            time.sleep(service.startup_time)  # 等待服务完全启动
            
            try:
                import requests
                response = requests.get(service.health_check, timeout=5)
                if response.status_code == 200:
                    self.ui.print_step(f"{service.description} 健康检查通过", "success")
                else:
                    self.ui.print_step(f"{service.description} 健康检查失败: {response.status_code}", "warning")
            except ImportError:
                # 如果没有requests，使用简单的socket检查
                if service.port:
                    self._check_port(service)
            except Exception as e:
                if service.status == ServiceStatus.RUNNING:
                    self.ui.print_step(f"{service.description} 健康检查失败", "warning")
        
        thread = threading.Thread(target=check_health, daemon=True)
        thread.start()
    
    def _check_port(self, service: Service):
        """检查端口是否开放"""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(5)
                result = sock.connect_ex(('127.0.0.1', service.port))
                if result == 0:
                    self.ui.print_step(f"{service.description} 端口 {service.port} 可访问", "success")
                else:
                    self.ui.print_step(f"{service.description} 端口 {service.port} 不可访问", "warning")
        except Exception as e:
            handle_error(e, ErrorLevel.WARNING, f"检查 {service.name} 端口")
    
    def stop_service(self, service_name: str) -> bool:
        """停止单个服务"""
        if service_name not in self.services:
            return False
        
        service = self.services[service_name]
        
        if service.status != ServiceStatus.RUNNING or not service.process:
            return True
        
        self.ui.print_step(f"正在停止 {service.description}", "info")
        
        try:
            # 优雅关闭
            service.process.terminate()
            
            # 等待进程结束
            try:
                service.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # 强制关闭
                service.process.kill()
                service.process.wait()
            
            service.status = ServiceStatus.STOPPED
            service.process = None
            
            self.ui.print_step(f"{service.description} 已停止", "success")
            return True
            
        except Exception as e:
            handle_error(e, ErrorLevel.ERROR, f"停止服务 {service_name}")
            return False
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        self.ui.print_header("AQUA服务管理器", "正在启动所有服务")
        
        self.running = True
        success_count = 0
        total_count = len(self.services)
        
        # 按优先级启动服务（后端优先）
        service_order = ["backend", "frontend"]
        
        for service_name in service_order:
            if service_name in self.services:
                if self.start_service(service_name):
                    success_count += 1
                elif self.services[service_name].required:
                    self.ui.print_step("必需服务启动失败，终止启动过程", "error")
                    return False
        
        # 启动其他服务
        for service_name in self.services:
            if service_name not in service_order:
                if self.start_service(service_name):
                    success_count += 1
        
        # 显示启动结果
        self.ui.print_section("服务启动完成")
        
        if success_count == total_count:
            self.ui.print_step(f"所有 {total_count} 个服务启动成功", "success")
            self._show_service_status()
            return True
        else:
            self.ui.print_step(f"{success_count}/{total_count} 个服务启动成功", "warning")
            return False
    
    def stop_all_services(self):
        """停止所有服务"""
        self.ui.print_section("正在停止所有服务")
        self.shutdown_event.set()
        
        for service_name in reversed(list(self.services.keys())):
            self.stop_service(service_name)
        
        self.running = False
        self.ui.print_step("所有服务已停止", "success")
    
    def _show_service_status(self):
        """显示服务状态表格"""
        headers = ["服务", "状态", "端口", "描述"]
        rows = []
        
        for service in self.services.values():
            status_text = "运行中" if service.status == ServiceStatus.RUNNING else service.status.value
            port_text = str(service.port) if service.port else "-"
            
            rows.append([
                service.name,
                status_text,
                port_text,
                service.description
            ])
        
        self.ui.print_table(headers, rows)
        
        # 显示访问信息
        running_services = [s for s in self.services.values() if s.status == ServiceStatus.RUNNING and s.port]
        if running_services:
            self.ui.print_section("访问地址")
            for service in running_services:
                if service.name == "backend":
                    self.ui.print_key_value("API文档", f"http://127.0.0.1:{service.port}/docs")
                    self.ui.print_key_value("API根路径", f"http://127.0.0.1:{service.port}/")
                elif service.name == "frontend":
                    self.ui.print_key_value("前端界面", f"http://localhost:{service.port}/")
    
    def restart_service(self, service_name: str) -> bool:
        """重启单个服务"""
        self.ui.print_step(f"重启 {service_name}", "info")
        
        if self.stop_service(service_name):
            time.sleep(1)
            return self.start_service(service_name)
        return False
    
    def get_service_logs(self, service_name: str, lines: int = 20) -> List[str]:
        """获取服务日志"""
        if service_name not in self.services:
            return []
        
        service = self.services[service_name]
        return service.logs[-lines:] if service.logs else []
    
    def monitor_services(self):
        """监控服务状态"""
        self.ui.print_section("服务监控")
        self.ui.print_step("按 Ctrl+C 停止监控和所有服务", "info")
        
        try:
            while self.running:
                time.sleep(10)  # 每10秒检查一次
                
                # 检查服务状态
                for service in self.services.values():
                    if service.status == ServiceStatus.RUNNING and service.process:
                        if service.process.poll() is not None:
                            # 服务意外退出
                            service.status = ServiceStatus.ERROR
                            self.ui.print_step(f"{service.description} 意外退出", "error")
                            
                            if service.required:
                                self.ui.print_step("必需服务退出，正在尝试重启", "warning")
                                if not self.restart_service(service.name):
                                    self.ui.print_step("重启失败，停止所有服务", "error")
                                    break
                
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all_services()


def create_startup_script():
    """创建启动脚本"""
    root_path = Path(__file__).parent.parent.parent
    
    # Python启动脚本
    startup_script = root_path / "start_aqua.py"
    
    script_content = '''#!/usr/bin/env python3
"""
AQUA一键启动脚本
"""

import sys
from pathlib import Path

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.service_manager import ServiceManager
from utils.cli_ui import get_ui

def main():
    """主函数"""
    ui = get_ui()
    
    try:
        ui.print_banner("AQUA量化分析平台", "primary")
        
        service_manager = ServiceManager()
        
        if service_manager.start_all_services():
            ui.print_step("所有服务启动成功，开始监控模式", "success")
            service_manager.monitor_services()
        else:
            ui.print_step("服务启动失败", "error")
            return 1
            
    except Exception as e:
        ui.print_step(f"启动过程中发生错误: {e}", "error")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
    
    with open(startup_script, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置可执行权限
    try:
        if hasattr(os, 'chmod'):
            os.chmod(startup_script, 0o755)
    except (OSError, AttributeError):
        # 在某些系统上可能不支持chmod
        pass
    
    return startup_script


def main():
    """测试和演示函数"""
    ui = get_ui()
    ui.print_banner("AQUA服务管理器测试", "primary")
    
    service_manager = ServiceManager()
    
    # 显示定义的服务
    ui.print_section("已定义的服务")
    for service in service_manager.services.values():
        ui.print_key_value(service.name, service.description)
    
    # 询问是否启动服务
    if ui.confirm("是否启动所有服务进行测试"):
        if service_manager.start_all_services():
            ui.print_step("启动成功，监控5秒后自动停止", "success")
            time.sleep(5)
            service_manager.stop_all_services()
        else:
            ui.print_step("启动失败", "error")
    
    # 创建启动脚本
    ui.print_section("创建启动脚本")
    script_path = create_startup_script()
    ui.print_step(f"启动脚本已创建: {script_path}", "success")
    ui.print_step("使用方法: python start_aqua.py", "info")


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.simple_config import get_config_manager
    from utils.simple_error import safe_call, handle_error, ErrorLevel
    from utils.cli_ui import get_ui
    main()