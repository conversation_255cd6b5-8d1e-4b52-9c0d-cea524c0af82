#!/usr/bin/env python3
"""
权限错误处理机制
提供统一的权限错误处理、恢复和用户指导功能
"""

import os
import sys
import stat
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple, Callable
from enum import Enum
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.paths import Paths
from src.utils.permission_checker import (
    CrossPlatformPermission<PERSON>hecker, 
    PermissionLevel, 
    PermissionCheckResult
)


class PermissionErrorType(Enum):
    """权限错误类型枚举"""
    ACCESS_DENIED = "access_denied"
    INSUFFICIENT_PRIVILEGES = "insufficient_privileges"
    FILE_NOT_FOUND = "file_not_found"
    DIRECTORY_NOT_WRITABLE = "directory_not_writable"
    EXECUTABLE_NOT_FOUND = "executable_not_found"
    NETWORK_PORT_BLOCKED = "network_port_blocked"
    SYSTEM_PROTECTION = "system_protection"
    UNKNOWN_ERROR = "unknown_error"


class RecoveryAction(Enum):
    """恢复动作枚举"""
    RETRY = "retry"
    ELEVATE_PRIVILEGES = "elevate_privileges"
    CREATE_DIRECTORY = "create_directory"
    CHANGE_PERMISSIONS = "change_permissions"
    USE_ALTERNATIVE_PATH = "use_alternative_path"
    SKIP_OPERATION = "skip_operation"
    MANUAL_INTERVENTION = "manual_intervention"
    ABORT_OPERATION = "abort_operation"


class PermissionErrorHandler:
    """权限错误处理器"""
    
    def __init__(self):
        self.platform = platform.system()
        self.logger = logging.getLogger(__name__)
        self.permission_checker = CrossPlatformPermissionChecker()
        
        # 错误处理策略映射
        self.error_strategies = {
            PermissionErrorType.ACCESS_DENIED: self._handle_access_denied,
            PermissionErrorType.INSUFFICIENT_PRIVILEGES: self._handle_insufficient_privileges,
            PermissionErrorType.FILE_NOT_FOUND: self._handle_file_not_found,
            PermissionErrorType.DIRECTORY_NOT_WRITABLE: self._handle_directory_not_writable,
            PermissionErrorType.EXECUTABLE_NOT_FOUND: self._handle_executable_not_found,
            PermissionErrorType.NETWORK_PORT_BLOCKED: self._handle_network_port_blocked,
            PermissionErrorType.SYSTEM_PROTECTION: self._handle_system_protection,
            PermissionErrorType.UNKNOWN_ERROR: self._handle_unknown_error,
        }
        
        # 平台特定的恢复策略
        self.platform_recovery_strategies = {
            "Windows": self._get_windows_recovery_strategies(),
            "Darwin": self._get_macos_recovery_strategies(),
            "Linux": self._get_linux_recovery_strategies(),
        }
    
    def handle_permission_error(self, error: Exception, context: Dict) -> Dict:
        """
        处理权限错误
        
        Args:
            error: 权限错误异常
            context: 错误上下文信息
            
        Returns:
            处理结果字典
        """
        error_type = self._classify_error(error, context)
        
        result = {
            'error_type': error_type,
            'original_error': str(error),
            'context': context,
            'recovery_actions': [],
            'user_guidance': [],
            'auto_recovery_attempted': False,
            'auto_recovery_success': False,
            'requires_manual_intervention': False
        }
        
        # 获取处理策略
        strategy = self.error_strategies.get(error_type, self._handle_unknown_error)
        
        try:
            # 执行错误处理策略
            strategy_result = strategy(error, context)
            result.update(strategy_result)
            
            # 尝试自动恢复
            if result['recovery_actions']:
                auto_recovery_result = self._attempt_auto_recovery(result['recovery_actions'], context)
                result['auto_recovery_attempted'] = True
                result['auto_recovery_success'] = auto_recovery_result['success']
                result['auto_recovery_details'] = auto_recovery_result
                
        except Exception as e:
            self.logger.error(f"权限错误处理失败: {e}")
            result['handler_error'] = str(e)
        
        return result
    
    def _classify_error(self, error: Exception, context: Dict) -> PermissionErrorType:
        """分类权限错误"""
        if error is None:
            return PermissionErrorType.UNKNOWN_ERROR

        error_str = str(error).lower()

        if isinstance(error, PermissionError):
            if "access is denied" in error_str or "permission denied" in error_str or "access denied" in error_str:
                return PermissionErrorType.ACCESS_DENIED
            elif "operation not permitted" in error_str:
                return PermissionErrorType.INSUFFICIENT_PRIVILEGES
        elif isinstance(error, FileNotFoundError):
            return PermissionErrorType.FILE_NOT_FOUND
        elif isinstance(error, OSError):
            if "address already in use" in error_str:
                return PermissionErrorType.NETWORK_PORT_BLOCKED
            elif "no such file or directory" in error_str:
                return PermissionErrorType.FILE_NOT_FOUND

        # 根据上下文进一步分类
        if context.get('operation_type') == 'directory_write':
            return PermissionErrorType.DIRECTORY_NOT_WRITABLE
        elif context.get('operation_type') == 'execute':
            return PermissionErrorType.EXECUTABLE_NOT_FOUND

        return PermissionErrorType.UNKNOWN_ERROR
    
    def _handle_access_denied(self, error: Exception, context: Dict) -> Dict:
        """处理访问拒绝错误"""
        path = context.get('path')
        
        recovery_actions = []
        user_guidance = []
        
        if path:
            # 检查路径权限
            check_result = self.permission_checker.check_path_permissions(path)
            
            if not check_result.permissions.get('readable', False):
                recovery_actions.append({
                    'action': RecoveryAction.CHANGE_PERMISSIONS,
                    'target': str(path),
                    'new_permissions': '644' if Path(path).is_file() else '755'
                })
                user_guidance.append(f"尝试修改 {path} 的权限以允许读取")
            
            if not check_result.permissions.get('writable', False) and context.get('requires_write'):
                recovery_actions.append({
                    'action': RecoveryAction.CHANGE_PERMISSIONS,
                    'target': str(path),
                    'new_permissions': '644' if Path(path).is_file() else '755'
                })
                user_guidance.append(f"尝试修改 {path} 的权限以允许写入")
        
        # 平台特定的处理
        if self.platform == "Windows":
            user_guidance.append("考虑以管理员身份运行程序")
            recovery_actions.append({
                'action': RecoveryAction.ELEVATE_PRIVILEGES,
                'method': 'run_as_administrator'
            })
        else:
            user_guidance.append("考虑使用sudo运行程序或修改文件权限")
            recovery_actions.append({
                'action': RecoveryAction.ELEVATE_PRIVILEGES,
                'method': 'sudo'
            })
        
        return {
            'recovery_actions': recovery_actions,
            'user_guidance': user_guidance,
            'requires_manual_intervention': True
        }
    
    def _handle_insufficient_privileges(self, error: Exception, context: Dict) -> Dict:
        """处理权限不足错误"""
        recovery_actions = [{
            'action': RecoveryAction.ELEVATE_PRIVILEGES,
            'method': 'run_as_administrator' if self.platform == "Windows" else 'sudo'
        }]
        
        user_guidance = []
        if self.platform == "Windows":
            user_guidance.append("需要管理员权限，请右键选择'以管理员身份运行'")
        else:
            user_guidance.append("需要超级用户权限，请使用sudo运行命令")
        
        return {
            'recovery_actions': recovery_actions,
            'user_guidance': user_guidance,
            'requires_manual_intervention': True
        }
    
    def _handle_file_not_found(self, error: Exception, context: Dict) -> Dict:
        """处理文件未找到错误"""
        path = context.get('path')
        recovery_actions = []
        user_guidance = []
        
        if path:
            path_obj = Path(path)
            
            # 尝试创建父目录
            if not path_obj.parent.exists():
                recovery_actions.append({
                    'action': RecoveryAction.CREATE_DIRECTORY,
                    'target': str(path_obj.parent)
                })
                user_guidance.append(f"创建缺失的目录: {path_obj.parent}")
            
            # 如果是可执行文件，提供替代路径
            if context.get('operation_type') == 'execute':
                recovery_actions.append({
                    'action': RecoveryAction.USE_ALTERNATIVE_PATH,
                    'alternatives': self._find_executable_alternatives(path_obj.name)
                })
                user_guidance.append(f"查找 {path_obj.name} 的替代路径")
        
        return {
            'recovery_actions': recovery_actions,
            'user_guidance': user_guidance,
            'requires_manual_intervention': len(recovery_actions) == 0
        }
    
    def _handle_directory_not_writable(self, error: Exception, context: Dict) -> Dict:
        """处理目录不可写错误"""
        path = context.get('path')
        recovery_actions = []
        user_guidance = []
        
        if path:
            # 尝试修改目录权限
            recovery_actions.append({
                'action': RecoveryAction.CHANGE_PERMISSIONS,
                'target': str(path),
                'new_permissions': '755'
            })
            user_guidance.append(f"修改目录 {path} 的权限以允许写入")
            
            # 提供替代目录
            alternative_path = self._get_alternative_writable_directory(path)
            if alternative_path:
                recovery_actions.append({
                    'action': RecoveryAction.USE_ALTERNATIVE_PATH,
                    'alternative': str(alternative_path)
                })
                user_guidance.append(f"使用替代目录: {alternative_path}")
        
        return {
            'recovery_actions': recovery_actions,
            'user_guidance': user_guidance,
            'requires_manual_intervention': True
        }
    
    def _handle_executable_not_found(self, error: Exception, context: Dict) -> Dict:
        """处理可执行文件未找到错误"""
        executable = context.get('executable')
        recovery_actions = []
        user_guidance = []
        
        if executable:
            # 查找替代路径
            alternatives = self._find_executable_alternatives(executable)
            if alternatives:
                recovery_actions.append({
                    'action': RecoveryAction.USE_ALTERNATIVE_PATH,
                    'alternatives': alternatives
                })
                user_guidance.append(f"找到 {executable} 的替代路径: {alternatives}")
            else:
                user_guidance.append(f"请安装 {executable} 或确保它在PATH中")
                recovery_actions.append({
                    'action': RecoveryAction.MANUAL_INTERVENTION,
                    'required_action': f'install_{executable}'
                })
        
        return {
            'recovery_actions': recovery_actions,
            'user_guidance': user_guidance,
            'requires_manual_intervention': True
        }
    
    def _handle_network_port_blocked(self, error: Exception, context: Dict) -> Dict:
        """处理网络端口被阻塞错误"""
        port = context.get('port')
        recovery_actions = []
        user_guidance = []
        
        if port:
            # 查找替代端口
            alternative_ports = self._find_alternative_ports(port)
            if alternative_ports:
                recovery_actions.append({
                    'action': RecoveryAction.USE_ALTERNATIVE_PATH,
                    'alternatives': alternative_ports
                })
                user_guidance.append(f"尝试使用替代端口: {alternative_ports}")
            
            user_guidance.append(f"检查端口 {port} 是否被其他程序占用")
            user_guidance.append("检查防火墙设置是否阻止了端口访问")
        
        return {
            'recovery_actions': recovery_actions,
            'user_guidance': user_guidance,
            'requires_manual_intervention': True
        }
    
    def _handle_system_protection(self, error: Exception, context: Dict) -> Dict:
        """处理系统保护错误"""
        user_guidance = []
        recovery_actions = []
        
        if self.platform == "Darwin":
            user_guidance.append("系统完整性保护(SIP)可能阻止了操作")
            user_guidance.append("考虑使用用户目录而非系统目录")
        elif self.platform == "Linux":
            user_guidance.append("SELinux或AppArmor可能阻止了操作")
            user_guidance.append("检查安全策略配置")
        elif self.platform == "Windows":
            user_guidance.append("Windows Defender或UAC可能阻止了操作")
            user_guidance.append("考虑添加程序到信任列表")
        
        recovery_actions.append({
            'action': RecoveryAction.USE_ALTERNATIVE_PATH,
            'suggestion': 'use_user_directory'
        })
        
        return {
            'recovery_actions': recovery_actions,
            'user_guidance': user_guidance,
            'requires_manual_intervention': True
        }
    
    def _handle_unknown_error(self, error: Exception, context: Dict) -> Dict:
        """处理未知错误"""
        return {
            'recovery_actions': [{
                'action': RecoveryAction.MANUAL_INTERVENTION,
                'error_details': str(error)
            }],
            'user_guidance': [
                "遇到未知的权限错误",
                f"错误详情: {str(error)}",
                "请检查系统日志或联系技术支持"
            ],
            'requires_manual_intervention': True
        }
    
    def _attempt_auto_recovery(self, recovery_actions: List[Dict], context: Dict) -> Dict:
        """尝试自动恢复"""
        recovery_result = {
            'success': False,
            'attempted_actions': [],
            'successful_actions': [],
            'failed_actions': []
        }
        
        for action in recovery_actions:
            action_type = action.get('action')
            
            try:
                if action_type == RecoveryAction.CREATE_DIRECTORY:
                    success = self._auto_create_directory(action.get('target'))
                elif action_type == RecoveryAction.CHANGE_PERMISSIONS:
                    success = self._auto_change_permissions(
                        action.get('target'), 
                        action.get('new_permissions')
                    )
                else:
                    # 其他动作需要手动干预
                    success = False
                
                recovery_result['attempted_actions'].append(action)
                
                if success:
                    recovery_result['successful_actions'].append(action)
                else:
                    recovery_result['failed_actions'].append(action)
                    
            except Exception as e:
                self.logger.error(f"自动恢复失败: {action_type} - {e}")
                recovery_result['failed_actions'].append({**action, 'error': str(e)})
        
        recovery_result['success'] = len(recovery_result['successful_actions']) > 0
        return recovery_result
    
    def _auto_create_directory(self, directory: str) -> bool:
        """自动创建目录"""
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"创建目录失败: {directory} - {e}")
            return False
    
    def _auto_change_permissions(self, path: str, permissions: str) -> bool:
        """自动修改权限"""
        try:
            # 检查实际的平台，而不是初始化时的平台
            import platform
            current_platform = platform.system()
            if current_platform != "Windows":
                mode = int(permissions, 8)
                Path(path).chmod(mode)
                return True
            return False  # Windows权限修改比较复杂
        except Exception as e:
            self.logger.error(f"修改权限失败: {path} - {e}")
            return False
    
    def _find_executable_alternatives(self, executable: str) -> List[str]:
        """查找可执行文件的替代路径"""
        alternatives = []
        
        # 常见的可执行文件路径
        common_paths = [
            "/usr/bin", "/usr/local/bin", "/opt/bin",
            "C:/Program Files", "C:/Windows/System32"
        ]
        
        for path in common_paths:
            full_path = Path(path) / executable
            if full_path.exists():
                alternatives.append(str(full_path))
        
        return alternatives
    
    def _find_alternative_ports(self, port: int) -> List[int]:
        """查找替代端口"""
        alternatives = []
        
        # 尝试相邻端口
        for offset in [1, -1, 10, -10, 100, -100]:
            alt_port = port + offset
            if 1024 <= alt_port <= 65535:
                # 简单检查端口是否可用
                try:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                    sock.bind(('localhost', alt_port))
                    sock.close()
                    alternatives.append(alt_port)
                    if len(alternatives) >= 3:  # 最多返回3个替代端口
                        break
                except:
                    continue
        
        return alternatives
    
    def _get_alternative_writable_directory(self, original_path: str) -> Optional[Path]:
        """获取替代的可写目录"""
        original = Path(original_path)
        
        # 尝试用户目录
        user_dir = Path.home() / "AQUA" / original.name
        if self._is_directory_writable(user_dir.parent):
            return user_dir
        
        # 尝试临时目录
        import tempfile
        temp_dir = Path(tempfile.gettempdir()) / "AQUA" / original.name
        if self._is_directory_writable(temp_dir.parent):
            return temp_dir
        
        return None
    
    def _is_directory_writable(self, directory: Path) -> bool:
        """检查目录是否可写"""
        try:
            directory.mkdir(parents=True, exist_ok=True)
            test_file = directory / ".write_test"
            test_file.write_text("test")
            test_file.unlink()
            return True
        except:
            return False
    
    def _get_windows_recovery_strategies(self) -> Dict:
        """获取Windows恢复策略"""
        return {
            'elevate_privileges': "右键选择'以管理员身份运行'",
            'check_antivirus': "检查Windows Defender或其他杀毒软件设置",
            'check_uac': "检查用户账户控制(UAC)设置"
        }
    
    def _get_macos_recovery_strategies(self) -> Dict:
        """获取macOS恢复策略"""
        return {
            'check_sip': "检查系统完整性保护(SIP)状态",
            'check_gatekeeper': "检查Gatekeeper设置",
            'use_sudo': "使用sudo命令提升权限"
        }
    
    def _get_linux_recovery_strategies(self) -> Dict:
        """获取Linux恢复策略"""
        return {
            'check_selinux': "检查SELinux策略",
            'check_apparmor': "检查AppArmor配置",
            'use_sudo': "使用sudo命令提升权限"
        }


# 便捷函数
def handle_permission_error(error: Exception, context: Dict = None) -> Dict:
    """处理权限错误的便捷函数"""
    handler = PermissionErrorHandler()
    return handler.handle_permission_error(error, context or {})


if __name__ == "__main__":
    # 测试权限错误处理器
    print("🔐 权限错误处理器测试")
    print("=" * 40)
    
    handler = PermissionErrorHandler()
    
    # 测试文件不存在错误
    try:
        with open("/nonexistent/path/file.txt", 'r') as f:
            pass
    except Exception as e:
        result = handler.handle_permission_error(e, {
            'path': '/nonexistent/path/file.txt',
            'operation_type': 'file_read'
        })
        print(f"错误类型: {result['error_type']}")
        print(f"恢复动作: {len(result['recovery_actions'])} 个")
        print(f"用户指导: {len(result['user_guidance'])} 条")
        print(f"需要手动干预: {result['requires_manual_intervention']}")
    
    print("✅ 权限错误处理器测试完成")
