#!/usr/bin/env python3
"""
跨平台权限检查工具
提供统一的权限检查接口，支持Windows、macOS、Linux
"""

import os
import sys
import stat
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
from enum import Enum
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.paths import Paths


class PermissionLevel(Enum):
    """权限级别枚举"""
    NONE = "none"
    READ = "read"
    WRITE = "write"
    EXECUTE = "execute"
    FULL = "full"


class PermissionCheckResult:
    """权限检查结果"""
    
    def __init__(self, path: Union[str, Path], success: bool, 
                 permissions: Dict, issues: List[str] = None, 
                 recommendations: List[str] = None):
        self.path = Path(path)
        self.success = success
        self.permissions = permissions
        self.issues = issues or []
        self.recommendations = recommendations or []
    
    def __str__(self):
        status = "✅" if self.success else "❌"
        return f"{status} {self.path}: {len(self.issues)} issues"
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'path': str(self.path),
            'success': self.success,
            'permissions': self.permissions,
            'issues': self.issues,
            'recommendations': self.recommendations
        }


class CrossPlatformPermissionChecker:
    """跨平台权限检查器"""
    
    def __init__(self):
        self.platform = platform.system()
        self.logger = logging.getLogger(__name__)
        
        # 平台特定的权限检查器
        if self.platform == "Windows":
            self._init_windows_checker()
        else:
            self._init_unix_checker()
    
    def _init_windows_checker(self):
        """初始化Windows权限检查器"""
        try:
            import ctypes
            self.has_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        except:
            self.has_admin = False
    
    def _init_unix_checker(self):
        """初始化Unix权限检查器"""
        self.current_uid = os.getuid()
        self.current_gid = os.getgid()
        self.current_groups = os.getgroups()
        self.is_root = self.current_uid == 0
    
    def check_path_permissions(self, path: Union[str, Path], 
                             required_permissions: List[PermissionLevel] = None) -> PermissionCheckResult:
        """
        检查路径权限
        
        Args:
            path: 要检查的路径
            required_permissions: 需要的权限级别列表
            
        Returns:
            权限检查结果
        """
        path = Path(path)
        required_permissions = required_permissions or [PermissionLevel.READ]
        
        if not path.exists():
            return PermissionCheckResult(
                path=path,
                success=False,
                permissions={},
                issues=["路径不存在"],
                recommendations=["创建路径或检查路径是否正确"]
            )
        
        # 获取基本权限信息
        permissions = self._get_basic_permissions(path)
        
        # 检查特定权限需求
        issues = []
        recommendations = []
        
        for perm_level in required_permissions:
            if not self._has_permission(path, perm_level, permissions):
                issues.append(f"缺少{perm_level.value}权限")
                recommendations.append(f"授予{perm_level.value}权限")
        
        # 平台特定检查
        platform_issues, platform_recs = self._platform_specific_checks(path, permissions)
        issues.extend(platform_issues)
        recommendations.extend(platform_recs)
        
        success = len(issues) == 0
        
        return PermissionCheckResult(
            path=path,
            success=success,
            permissions=permissions,
            issues=issues,
            recommendations=recommendations
        )
    
    def _get_basic_permissions(self, path: Path) -> Dict:
        """获取基本权限信息"""
        try:
            stat_info = path.stat()
            mode = stat_info.st_mode
            
            permissions = {
                'exists': True,
                'is_file': path.is_file(),
                'is_directory': path.is_dir(),
                'is_symlink': path.is_symlink(),
                'readable': os.access(path, os.R_OK),
                'writable': os.access(path, os.W_OK),
                'executable': os.access(path, os.X_OK),
                'size': stat_info.st_size,
                'modified_time': stat_info.st_mtime,
                'owner_uid': stat_info.st_uid,
                'owner_gid': stat_info.st_gid,
            }
            
            if self.platform != "Windows":
                # Unix权限详细信息
                permissions.update({
                    'mode_octal': oct(stat.S_IMODE(mode)),
                    'owner_read': bool(mode & stat.S_IRUSR),
                    'owner_write': bool(mode & stat.S_IWUSR),
                    'owner_execute': bool(mode & stat.S_IXUSR),
                    'group_read': bool(mode & stat.S_IRGRP),
                    'group_write': bool(mode & stat.S_IWGRP),
                    'group_execute': bool(mode & stat.S_IXGRP),
                    'other_read': bool(mode & stat.S_IROTH),
                    'other_write': bool(mode & stat.S_IWOTH),
                    'other_execute': bool(mode & stat.S_IXOTH),
                    'is_setuid': bool(mode & stat.S_ISUID),
                    'is_setgid': bool(mode & stat.S_ISGID),
                    'is_sticky': bool(mode & stat.S_ISVTX),
                })
                
                # 获取所有者信息
                try:
                    import pwd
                    import grp
                    permissions['owner_name'] = pwd.getpwuid(stat_info.st_uid).pw_name
                    permissions['group_name'] = grp.getgrgid(stat_info.st_gid).gr_name
                except:
                    pass
            
            return permissions
            
        except Exception as e:
            self.logger.error(f"获取权限信息失败: {path} - {e}")
            return {'exists': False, 'error': str(e)}
    
    def _has_permission(self, path: Path, perm_level: PermissionLevel, permissions: Dict) -> bool:
        """检查是否具有特定权限"""
        if perm_level == PermissionLevel.NONE:
            return True
        elif perm_level == PermissionLevel.READ:
            return permissions.get('readable', False)
        elif perm_level == PermissionLevel.WRITE:
            return permissions.get('writable', False)
        elif perm_level == PermissionLevel.EXECUTE:
            return permissions.get('executable', False)
        elif perm_level == PermissionLevel.FULL:
            return (permissions.get('readable', False) and 
                   permissions.get('writable', False) and 
                   permissions.get('executable', False))
        return False
    
    def _platform_specific_checks(self, path: Path, permissions: Dict) -> Tuple[List[str], List[str]]:
        """平台特定的权限检查"""
        issues = []
        recommendations = []
        
        if self.platform == "Windows":
            issues_win, recs_win = self._windows_specific_checks(path, permissions)
            issues.extend(issues_win)
            recommendations.extend(recs_win)
        elif self.platform == "Darwin":
            issues_mac, recs_mac = self._macos_specific_checks(path, permissions)
            issues.extend(issues_mac)
            recommendations.extend(recs_mac)
        else:  # Linux
            issues_linux, recs_linux = self._linux_specific_checks(path, permissions)
            issues.extend(issues_linux)
            recommendations.extend(recs_linux)
        
        return issues, recommendations
    
    def _windows_specific_checks(self, path: Path, permissions: Dict) -> Tuple[List[str], List[str]]:
        """Windows特定检查"""
        issues = []
        recommendations = []
        
        # 检查是否需要管理员权限
        if path.is_relative_to(Path("C:/Program Files")) or path.is_relative_to(Path("C:/Windows")):
            if not self.has_admin:
                issues.append("访问系统目录需要管理员权限")
                recommendations.append("以管理员身份运行程序")
        
        return issues, recommendations
    
    def _macos_specific_checks(self, path: Path, permissions: Dict) -> Tuple[List[str], List[str]]:
        """macOS特定检查"""
        issues = []
        recommendations = []
        
        # 检查SIP保护的目录
        sip_protected_paths = [
            "/System", "/usr", "/bin", "/sbin"
        ]
        
        for protected_path in sip_protected_paths:
            if path.is_relative_to(Path(protected_path)):
                issues.append("路径受SIP(系统完整性保护)限制")
                recommendations.append("避免修改系统保护的目录")
                break
        
        # 检查Gatekeeper相关
        if path.suffix in ['.app', '.pkg', '.dmg']:
            issues.append("可执行文件可能受Gatekeeper限制")
            recommendations.append("考虑代码签名或用户授权")
        
        return issues, recommendations
    
    def _linux_specific_checks(self, path: Path, permissions: Dict) -> Tuple[List[str], List[str]]:
        """Linux特定检查"""
        issues = []
        recommendations = []
        
        # 检查SELinux
        if self._has_selinux():
            issues.append("系统启用了SELinux，可能影响文件访问")
            recommendations.append("检查SELinux策略和上下文")
        
        # 检查AppArmor
        if self._has_apparmor():
            issues.append("系统启用了AppArmor，可能影响程序执行")
            recommendations.append("检查AppArmor配置文件")
        
        return issues, recommendations
    
    def _has_selinux(self) -> bool:
        """检查是否启用SELinux"""
        try:
            result = subprocess.run(['getenforce'], capture_output=True, text=True, timeout=5)
            return result.returncode == 0 and 'Enforcing' in result.stdout
        except:
            return False
    
    def _has_apparmor(self) -> bool:
        """检查是否启用AppArmor"""
        try:
            result = subprocess.run(['aa-status'], capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def check_directory_permissions(self, directory: Union[str, Path], 
                                  recursive: bool = False) -> List[PermissionCheckResult]:
        """
        检查目录权限
        
        Args:
            directory: 目录路径
            recursive: 是否递归检查子目录
            
        Returns:
            权限检查结果列表
        """
        directory = Path(directory)
        results = []
        
        # 检查目录本身
        dir_result = self.check_path_permissions(directory, [PermissionLevel.READ, PermissionLevel.EXECUTE])
        results.append(dir_result)
        
        if recursive and directory.exists() and directory.is_dir():
            try:
                for item in directory.rglob('*'):
                    if item.is_file():
                        file_result = self.check_path_permissions(item, [PermissionLevel.READ])
                        results.append(file_result)
                    elif item.is_dir():
                        subdir_result = self.check_path_permissions(item, [PermissionLevel.READ, PermissionLevel.EXECUTE])
                        results.append(subdir_result)
            except PermissionError as e:
                self.logger.warning(f"递归检查权限失败: {directory} - {e}")
        
        return results
    
    def check_executable_permissions(self, file_path: Union[str, Path]) -> PermissionCheckResult:
        """检查可执行文件权限"""
        return self.check_path_permissions(file_path, [PermissionLevel.READ, PermissionLevel.EXECUTE])
    
    def check_network_permissions(self, port: int) -> Dict:
        """检查网络端口权限"""
        result = {
            'port': port,
            'can_bind': False,
            'issues': [],
            'recommendations': []
        }
        
        try:
            import socket
            
            # 尝试绑定端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            try:
                sock.bind(('localhost', port))
                result['can_bind'] = True
            except PermissionError:
                result['issues'].append(f"没有权限绑定端口 {port}")
                if port < 1024:
                    result['recommendations'].append("绑定特权端口需要管理员权限")
                else:
                    result['recommendations'].append("检查端口是否被其他程序占用")
            except OSError as e:
                result['issues'].append(f"端口 {port} 绑定失败: {e}")
                result['recommendations'].append("检查端口是否已被占用")
            finally:
                sock.close()
                
        except Exception as e:
            result['issues'].append(f"网络权限检查失败: {e}")
        
        return result
    
    def fix_permissions(self, path: Union[str, Path], 
                       target_permissions: PermissionLevel) -> bool:
        """
        修复权限问题
        
        Args:
            path: 路径
            target_permissions: 目标权限级别
            
        Returns:
            是否修复成功
        """
        path = Path(path)
        
        if not path.exists():
            self.logger.error(f"路径不存在: {path}")
            return False
        
        try:
            if self.platform == "Windows":
                return self._fix_windows_permissions(path, target_permissions)
            else:
                return self._fix_unix_permissions(path, target_permissions)
        except Exception as e:
            self.logger.error(f"修复权限失败: {path} - {e}")
            return False
    
    def _fix_unix_permissions(self, path: Path, target_permissions: PermissionLevel) -> bool:
        """修复Unix权限"""
        try:
            if target_permissions == PermissionLevel.READ:
                mode = 0o644 if path.is_file() else 0o755
            elif target_permissions == PermissionLevel.WRITE:
                mode = 0o644 if path.is_file() else 0o755
            elif target_permissions == PermissionLevel.EXECUTE:
                mode = 0o755
            elif target_permissions == PermissionLevel.FULL:
                mode = 0o755
            else:
                return True
            
            path.chmod(mode)
            return True
        except Exception as e:
            self.logger.error(f"修复Unix权限失败: {path} - {e}")
            return False
    
    def _fix_windows_permissions(self, path: Path, target_permissions: PermissionLevel) -> bool:
        """修复Windows权限"""
        # Windows权限修复比较复杂，这里提供基本实现
        try:
            if target_permissions == PermissionLevel.EXECUTE:
                # 确保文件可执行
                if path.suffix not in ['.exe', '.bat', '.cmd']:
                    self.logger.warning(f"文件可能不是可执行文件: {path}")
            
            return True
        except Exception as e:
            self.logger.error(f"修复Windows权限失败: {path} - {e}")
            return False


# 便捷函数
def check_path(path: Union[str, Path], 
               required_permissions: List[PermissionLevel] = None) -> PermissionCheckResult:
    """检查路径权限的便捷函数"""
    checker = CrossPlatformPermissionChecker()
    return checker.check_path_permissions(path, required_permissions)


def check_directory(directory: Union[str, Path], recursive: bool = False) -> List[PermissionCheckResult]:
    """检查目录权限的便捷函数"""
    checker = CrossPlatformPermissionChecker()
    return checker.check_directory_permissions(directory, recursive)


def check_executable(file_path: Union[str, Path]) -> PermissionCheckResult:
    """检查可执行文件权限的便捷函数"""
    checker = CrossPlatformPermissionChecker()
    return checker.check_executable_permissions(file_path)


def check_network_port(port: int) -> Dict:
    """检查网络端口权限的便捷函数"""
    checker = CrossPlatformPermissionChecker()
    return checker.check_network_permissions(port)


if __name__ == "__main__":
    # 测试权限检查器
    print("🔐 跨平台权限检查器测试")
    print("=" * 40)
    
    checker = CrossPlatformPermissionChecker()
    
    # 测试路径权限检查
    test_paths = [
        Paths.ROOT,
        Paths.SRC,
        Paths.CONFIG,
        Paths.DATA
    ]
    
    for path in test_paths:
        result = checker.check_path_permissions(path, [PermissionLevel.READ, PermissionLevel.WRITE])
        print(f"{result}")
        if result.issues:
            for issue in result.issues:
                print(f"  ⚠️ {issue}")
    
    # 测试网络权限
    test_ports = [8000, 3000, 80]
    for port in test_ports:
        net_result = checker.check_network_permissions(port)
        status = "✅" if net_result['can_bind'] else "❌"
        print(f"{status} 端口 {port}: {'可绑定' if net_result['can_bind'] else '不可绑定'}")
    
    print("✅ 权限检查器测试完成")
