#!/usr/bin/env python3
"""
配置验证工具
提供配置文件的验证和环境变量解析功能

特性：
- 配置文件完整性验证
- 环境变量替换支持
- 配置值类型验证
- 路径存在性检查
"""

import os
import re
import platform
import toml
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum


class ValidationLevel(Enum):
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationResult:
    level: ValidationLevel
    message: str
    path: str
    suggestion: Optional[str] = None


class ConfigValidator:
    """配置验证器"""

    def __init__(self, config_path: str = "config/settings.toml"):
        self.config_path = Path(config_path)
        self.config = None
        self.validation_results: List[ValidationResult] = []

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")

        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                self.config = toml.load(f)
                return self.config
        except Exception as e:
            raise ValueError(f"配置文件格式错误: {e}")

    def substitute_env_vars(self, value: str) -> str:
        """替换环境变量"""
        if not isinstance(value, str):
            return value

        # 支持 ${VAR} 和 $VAR 格式
        def replace_var(match):
            var_name = match.group(1) or match.group(2)
            return os.environ.get(var_name, match.group(0))

        # 替换 ${VAR} 格式
        value = re.sub(r"\$\{([^}]+)\}", replace_var, value)
        # 替换 $VAR 格式
        value = re.sub(r"\$([A-Za-z_][A-Za-z0-9_]*)", replace_var, value)

        return value

    def validate_paths(self, config: Dict[str, Any], env_name: str) -> None:
        """验证路径配置"""
        env_config = config.get(env_name, {})

        # 验证数据库路径
        if "database" in env_config:
            db_path = env_config["database"].get("path", "")
            if db_path:
                db_path = self.substitute_env_vars(db_path)
                db_dir = Path(db_path).parent
                if not db_dir.exists():
                    self.validation_results.append(
                        ValidationResult(
                            level=ValidationLevel.WARNING,
                            message=f"数据库目录不存在: {db_dir}",
                            path=f"{env_name}.database.path",
                            suggestion=f"创建目录: mkdir -p {db_dir}",
                        )
                    )

        # 验证CSV数据目录
        if "csv" in env_config:
            csv_dir = env_config["csv"].get("data_dir", "")
            if csv_dir:
                csv_dir = self.substitute_env_vars(csv_dir)
                if not Path(csv_dir).exists():
                    self.validation_results.append(
                        ValidationResult(
                            level=ValidationLevel.WARNING,
                            message=f"CSV数据目录不存在: {csv_dir}",
                            path=f"{env_name}.csv.data_dir",
                            suggestion=f"检查路径或创建目录: mkdir -p {csv_dir}",
                        )
                    )

        # 验证备份目录
        if "database" in env_config:
            backup_dir = env_config["database"].get("backup_dir", "")
            if backup_dir:
                backup_dir = self.substitute_env_vars(backup_dir)
                if not Path(backup_dir).exists():
                    self.validation_results.append(
                        ValidationResult(
                            level=ValidationLevel.INFO,
                            message=f"备份目录不存在，将自动创建: {backup_dir}",
                            path=f"{env_name}.database.backup_dir",
                        )
                    )

    def validate_mysql_config(self, config: Dict[str, Any], env_name: str) -> None:
        """验证MySQL配置"""
        env_config = config.get(env_name, {})
        mysql_config = env_config.get("mysql", {})

        if not mysql_config:
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.WARNING,
                    message="MySQL配置缺失",
                    path=f"{env_name}.mysql",
                    suggestion="如果需要MySQL导入功能，请添加MySQL配置",
                )
            )
            return

        required_fields = ["host", "port", "user", "password", "database"]
        for field in required_fields:
            if field not in mysql_config:
                self.validation_results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"MySQL配置缺少必需字段: {field}",
                        path=f"{env_name}.mysql.{field}",
                        suggestion=f"添加 {field} 配置项",
                    )
                )

    def validate_performance_config(
        self, config: Dict[str, Any], env_name: str
    ) -> None:
        """验证性能配置"""
        env_config = config.get(env_name, {})
        perf_config = env_config.get("performance", {})

        if not perf_config:
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.INFO,
                    message="性能配置使用默认值",
                    path=f"{env_name}.performance",
                )
            )
            return

        # 验证数值配置
        numeric_fields = {
            "max_workers": (1, 16),
            "chunk_size": (100, 10000),
            "memory_limit_mb": (128, 8192),
            "cache_size_mb": (64, 2048),
        }

        for field, (min_val, max_val) in numeric_fields.items():
            if field in perf_config:
                value = perf_config[field]
                if not isinstance(value, int) or value < min_val or value > max_val:
                    self.validation_results.append(
                        ValidationResult(
                            level=ValidationLevel.WARNING,
                            message=f"性能配置 {field} 值不在建议范围内: {value}",
                            path=f"{env_name}.performance.{field}",
                            suggestion=f"建议设置在 {min_val}-{max_val} 范围内",
                        )
                    )

        # 个人开发者特殊检查
        if perf_config.get("personal_dev_mode"):
            memory_limit = perf_config.get("memory_limit_mb", 0)
            if memory_limit > 1536:
                self.validation_results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"内存限制{memory_limit}MB超过个人开发者建议值1536MB",
                        path=f"{env_name}.performance.memory_limit_mb",
                        suggestion="建议调整为≤1536MB以优化个人开发者体验",
                    )
                )

    def validate_environment(self, env_name: str) -> None:
        """验证特定环境配置"""
        if not self.config:
            raise ValueError("配置文件未加载")

        if env_name not in self.config:
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"环境配置不存在: {env_name}",
                    path=env_name,
                    suggestion=f"在配置文件中添加 [{env_name}] 环境配置",
                )
            )
            return

        # 验证各个配置项
        self.validate_paths(self.config, env_name)
        self.validate_mysql_config(self.config, env_name)
        self.validate_performance_config(self.config, env_name)
        self.validate_tushare_config(self.config, env_name)
        self.validate_cross_platform_config(self.config, env_name)

    def validate_all(self) -> List[ValidationResult]:
        """验证所有配置"""
        self.validation_results = []

        # 加载配置
        try:
            self.load_config()
        except Exception as e:
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"配置文件加载失败: {e}",
                    path="config_file",
                )
            )
            return self.validation_results

        # 验证各个环境
        environments = ["test", "dev", "prod"]
        for env in environments:
            if env in self.config:
                self.validate_environment(env)

        return self.validation_results

    def print_validation_results(self) -> None:
        """打印验证结果"""
        if not self.validation_results:
            print("✅ 配置验证通过，无问题发现")
            return

        # 按级别分组
        errors = [
            r for r in self.validation_results if r.level == ValidationLevel.ERROR
        ]
        warnings = [
            r for r in self.validation_results if r.level == ValidationLevel.WARNING
        ]
        infos = [r for r in self.validation_results if r.level == ValidationLevel.INFO]

        # 打印错误
        if errors:
            print(f"❌ 发现 {len(errors)} 个错误:")
            for result in errors:
                print(f"  - {result.path}: {result.message}")
                if result.suggestion:
                    print(f"    建议: {result.suggestion}")

        # 打印警告
        if warnings:
            print(f"⚠️  发现 {len(warnings)} 个警告:")
            for result in warnings:
                print(f"  - {result.path}: {result.message}")
                if result.suggestion:
                    print(f"    建议: {result.suggestion}")

        # 打印信息
        if infos:
            print(f"ℹ️  发现 {len(infos)} 个信息:")
            for result in infos:
                print(f"  - {result.path}: {result.message}")
                if result.suggestion:
                    print(f"    建议: {result.suggestion}")

    def get_processed_config(self, env_name: str) -> Dict[str, Any]:
        """获取处理后的配置（环境变量替换）"""
        if not self.config:
            self.load_config()

        if env_name not in self.config:
            raise ValueError(f"环境配置不存在: {env_name}")

        config = self.config[env_name].copy()

        # 递归替换环境变量
        def process_dict(d: Dict[str, Any]) -> Dict[str, Any]:
            result = {}
            for k, v in d.items():
                if isinstance(v, dict):
                    result[k] = process_dict(v)
                elif isinstance(v, str):
                    result[k] = self.substitute_env_vars(v)
                else:
                    result[k] = v
            return result

        return process_dict(config)

    def validate_tushare_config(self, config: Dict[str, Any], env_name: str) -> None:
        """验证TUSHARE配置"""
        # 检查TUSHARE API配置
        datasources = config.get("datasources", {})
        api_config = datasources.get("api", {})
        tushare_config = api_config.get("tushare", {})

        if not tushare_config:
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="TUSHARE API配置缺失",
                    path="datasources.api.tushare",
                    suggestion="在配置文件中添加TUSHARE API配置段",
                )
            )
            return

        # 验证TUSHARE是否启用
        if not tushare_config.get("enabled", False):
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.WARNING,
                    message="TUSHARE API未启用",
                    path="datasources.api.tushare.enabled",
                    suggestion="将enabled设置为true以启用TUSHARE数据源",
                )
            )

        # 验证Token配置
        token = tushare_config.get("token", "")
        if not token or token == "":
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="TUSHARE Token未配置",
                    path="datasources.api.tushare.token",
                    suggestion="设置环境变量TUSHARE_TOKEN或在配置中指定token",
                )
            )
        elif token == "${TUSHARE_TOKEN}" and not os.getenv("TUSHARE_TOKEN"):
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="环境变量TUSHARE_TOKEN未设置",
                    path="datasources.api.tushare.token",
                    suggestion="export TUSHARE_TOKEN=your_token_here",
                )
            )

        # 验证积分预算配置
        budget_config = tushare_config.get("budget", {})
        if budget_config:
            total_points = budget_config.get("total_points", 0)
            daily_limit = budget_config.get("daily_limit", 0)

            if total_points <= daily_limit * 30:
                self.validation_results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"总积分{total_points}可能不足一个月使用(日均{daily_limit})",
                        path="datasources.api.tushare.budget",
                        suggestion="考虑调整daily_limit或增加total_points",
                    )
                )

    def validate_cross_platform_config(
        self, config: Dict[str, Any], env_name: str
    ) -> None:
        """验证跨平台配置"""
        platform_config = config.get("platform", {})

        if not platform_config:
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.WARNING,
                    message="跨平台配置缺失",
                    path="platform",
                    suggestion="添加platform配置段以支持跨平台特性",
                )
            )
            return

        # 验证个人开发者配置
        personal_dev_config = platform_config.get("personal_dev", {})
        if personal_dev_config:
            # 验证环境变量配置
            env_vars = personal_dev_config.get("env_vars", [])
            for env_var in env_vars:
                if env_var == "TUSHARE_TOKEN" and not os.getenv(env_var):
                    self.validation_results.append(
                        ValidationResult(
                            level=ValidationLevel.WARNING,
                            message=f"环境变量{env_var}未设置",
                            path=f"platform.personal_dev.env_vars.{env_var}",
                            suggestion=f"export {env_var}=your_value_here",
                        )
                    )

        # 验证当前平台支持
        current_platform = platform.system().lower()
        platform_map = {"windows": "windows", "darwin": "darwin", "linux": "linux"}
        mapped_platform = platform_map.get(current_platform)

        supported_platforms = platform_config.get("supported_platforms", [])

        if mapped_platform is None:
            # 未知平台，记录错误
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"当前平台{current_platform}未被识别或支持",
                    path="platform.supported_platforms",
                    suggestion=f"当前平台{current_platform}可能不受支持，请检查兼容性",
                )
            )
        elif mapped_platform not in supported_platforms:
            # 已知平台但不在支持列表
            self.validation_results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"当前平台{current_platform}不在支持列表中",
                    path="platform.supported_platforms",
                    suggestion=f"将'{mapped_platform}'添加到supported_platforms列表",
                )
            )


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="AQUA配置验证工具")
    parser.add_argument(
        "--config", "-c", default="config/settings.toml", help="配置文件路径"
    )
    parser.add_argument(
        "--env", "-e", choices=["test", "dev", "prod"], help="验证特定环境"
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    args = parser.parse_args()

    validator = ConfigValidator(args.config)

    try:
        if args.env:
            validator.load_config()
            validator.validate_environment(args.env)
        else:
            validator.validate_all()

        validator.print_validation_results()

        # 如果有错误，返回非零退出码
        errors = [
            r for r in validator.validation_results if r.level == ValidationLevel.ERROR
        ]
        if errors:
            exit(1)

    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        exit(1)


if __name__ == "__main__":
    main()
