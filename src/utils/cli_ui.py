#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA CLI输出美化系统

简单优雅的命令行界面输出，提升开发者体验
版本: 1.0.0
创建时间: 2025-07-31
"""

import os
import sys
import time
from typing import Optional, List, Dict, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

# 导入简化配置系统
from .simple_config import get_config_manager


class Color(Enum):
    """颜色定义"""
    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"
    
    # 基础颜色
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"
    
    # 亮色
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"


class Icon(Enum):
    """图标定义"""
    # 状态图标
    SUCCESS = "✅"
    ERROR = "❌"
    WARNING = "⚠️"
    INFO = "ℹ️"
    QUESTION = "❓"
    
    # 操作图标
    ROCKET = "🚀"
    GEAR = "⚙️"
    FOLDER = "📁"
    FILE = "📄"
    DATABASE = "🗄️"
    NETWORK = "🌐"
    
    # 进度图标
    LOADING = "⏳"
    HOURGLASS = "⌛"
    TIMER = "⏱️"
    
    # 箭头和分隔符
    ARROW_RIGHT = "➤"
    BULLET = "•"
    DIAMOND = "◆"


@dataclass
class Theme:
    """CLI主题配置"""
    primary: Color = Color.BRIGHT_BLUE
    success: Color = Color.BRIGHT_GREEN
    warning: Color = Color.BRIGHT_YELLOW
    error: Color = Color.BRIGHT_RED
    info: Color = Color.BRIGHT_CYAN
    muted: Color = Color.DIM
    accent: Color = Color.BRIGHT_MAGENTA


class CLIUI:
    """
    CLI用户界面美化器
    
    提供统一、美观的命令行输出格式
    """
    
    def __init__(self, theme: Optional[Theme] = None):
        """初始化CLI UI"""
        self.theme = theme or Theme()
        self.config = get_config_manager()
        self.supports_color = self._check_color_support()
        self.width = self._get_terminal_width()
    
    def _check_color_support(self) -> bool:
        """检查终端是否支持颜色"""
        # 在调试模式下强制启用颜色
        if self.config.is_debug():
            return True
        
        # 检查环境变量
        if os.getenv("NO_COLOR"):
            return False
        
        if os.getenv("FORCE_COLOR"):
            return True
        
        # 检查终端类型
        term = os.getenv("TERM", "")
        if "color" in term or term in ["xterm", "xterm-256color", "screen"]:
            return True
        
        # 检查是否为tty
        return hasattr(sys.stdout, "isatty") and sys.stdout.isatty()
    
    def _get_terminal_width(self) -> int:
        """获取终端宽度"""
        try:
            return os.get_terminal_size().columns
        except:
            return 80  # 默认宽度
    
    def colorize(self, text: str, color: Color) -> str:
        """给文本添加颜色"""
        if not self.supports_color:
            return text
        return f"{color.value}{text}{Color.RESET.value}"
    
    def print_header(self, title: str, subtitle: Optional[str] = None):
        """打印标题头部"""
        print()
        # 主标题
        header = f"{Icon.ROCKET.value} {title}"
        print(self.colorize(header, self.theme.primary))
        
        # 副标题
        if subtitle:
            print(self.colorize(f"   {subtitle}", self.theme.muted))
        
        # 分隔线
        separator = "═" * min(len(title) + 10, self.width - 4)
        print(self.colorize(separator, self.theme.muted))
        print()
    
    def print_section(self, title: str):
        """打印节标题"""
        section = f"{Icon.DIAMOND.value} {title}"
        print(self.colorize(section, self.theme.accent))
    
    def print_step(self, step: str, status: str = "info"):
        """打印步骤信息"""
        icons = {
            "info": Icon.INFO.value,
            "success": Icon.SUCCESS.value,
            "warning": Icon.WARNING.value,
            "error": Icon.ERROR.value,
            "loading": Icon.LOADING.value
        }
        
        colors = {
            "info": self.theme.info,
            "success": self.theme.success,
            "warning": self.theme.warning,
            "error": self.theme.error,
            "loading": self.theme.primary
        }
        
        icon = icons.get(status, Icon.INFO.value)
        color = colors.get(status, self.theme.info)
        
        message = f"  {icon} {step}"
        print(self.colorize(message, color))
    
    def print_key_value(self, key: str, value: Any, indent: int = 2):
        """打印键值对"""
        spaces = " " * indent
        key_part = self.colorize(f"{key}:", self.theme.muted)
        value_part = self.colorize(str(value), self.theme.primary)
        print(f"{spaces}{key_part} {value_part}")
    
    def print_list(self, items: List[str], bullet: str = None, color: Color = None):
        """打印列表"""
        bullet = bullet or Icon.BULLET.value
        color = color or self.theme.info
        
        for item in items:
            line = f"  {bullet} {item}"
            print(self.colorize(line, color))
    
    def print_table(self, headers: List[str], rows: List[List[str]], max_width: int = None):
        """打印简单表格"""
        if not rows:
            return
        
        max_width = max_width or self.width - 4
        
        # 计算列宽
        col_widths = []
        for i, header in enumerate(headers):
            max_len = len(header)
            for row in rows:
                if i < len(row):
                    max_len = max(max_len, len(str(row[i])))
            col_widths.append(min(max_len + 2, max_width // len(headers)))
        
        # 打印表头
        header_line = ""
        for i, header in enumerate(headers):
            width = col_widths[i]
            header_line += header.ljust(width)
        print(self.colorize(header_line, self.theme.accent))
        
        # 分隔线
        separator = "─" * sum(col_widths)
        print(self.colorize(separator, self.theme.muted))
        
        # 打印行
        for row in rows:
            row_line = ""
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    width = col_widths[i]
                    row_line += str(cell).ljust(width)
            print(row_line)
    
    def print_progress_bar(self, current: int, total: int, prefix: str = "", width: int = 30):
        """打印进度条"""
        if total <= 0:
            return
        
        percent = current / total
        filled = int(width * percent)
        bar = "█" * filled + "░" * (width - filled)
        
        progress_text = f"{prefix} [{bar}] {current}/{total} ({percent:.1%})"
        print(f"\r{self.colorize(progress_text, self.theme.primary)}", end="", flush=True)
        
        if current >= total:
            print()  # 完成时换行
    
    def print_box(self, content: str, title: str = None, style: str = "info"):
        """打印边框框"""
        lines = content.strip().split('\n')
        max_len = max(len(line) for line in lines) if lines else 0
        
        if title:
            max_len = max(max_len, len(title) + 4)
        
        box_width = min(max_len + 4, self.width - 4)
        
        colors = {
            "info": self.theme.info,
            "success": self.theme.success,
            "warning": self.theme.warning,
            "error": self.theme.error
        }
        color = colors.get(style, self.theme.info)
        
        # 顶部边框
        top_line = "┌" + "─" * (box_width - 2) + "┐"
        print(self.colorize(top_line, color))
        
        # 标题行
        if title:
            title_line = f"│ {title.center(box_width - 4)} │"
            print(self.colorize(title_line, color))
            sep_line = "├" + "─" * (box_width - 2) + "┤"
            print(self.colorize(sep_line, color))
        
        # 内容行
        for line in lines:
            content_line = f"│ {line.ljust(box_width - 4)} │"
            print(self.colorize(content_line, color))
        
        # 底部边框
        bottom_line = "└" + "─" * (box_width - 2) + "┘"
        print(self.colorize(bottom_line, color))
    
    def print_banner(self, text: str, style: str = "primary"):
        """打印横幅"""
        colors = {
            "primary": self.theme.primary,
            "success": self.theme.success,
            "warning": self.theme.warning,
            "error": self.theme.error
        }
        color = colors.get(style, self.theme.primary)
        
        banner_width = min(len(text) + 6, self.width)
        padding = (banner_width - len(text)) // 2
        
        border = "=" * banner_width
        content = f"{' ' * padding}{text}{' ' * padding}"
        
        print()
        print(self.colorize(border, color))
        print(self.colorize(content, color))
        print(self.colorize(border, color))
        print()
    
    def print_spinner(self, message: str, duration: float = 2.0):
        """打印旋转加载器"""
        if not self.supports_color:
            print(f"{Icon.LOADING.value} {message}")
            time.sleep(duration)
            return
        
        spinners = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        start_time = time.time()
        
        while time.time() - start_time < duration:
            for spinner in spinners:
                if time.time() - start_time >= duration:
                    break
                
                spinner_text = f"\r{self.colorize(spinner, self.theme.primary)} {message}"
                print(spinner_text, end="", flush=True)
                time.sleep(0.1)
        
        # 完成标记
        done_text = f"\r{self.colorize(Icon.SUCCESS.value, self.theme.success)} {message}"
        print(f"{done_text}{''.ljust(10)}")  # 清除残留字符
    
    def confirm(self, message: str, default: bool = True) -> bool:
        """确认对话框"""
        suffix = "[Y/n]" if default else "[y/N]"
        prompt = f"{Icon.QUESTION.value} {message} {suffix}: "
        
        try:
            response = input(self.colorize(prompt, self.theme.info)).strip().lower()
            
            if not response:
                return default
            
            return response in ['y', 'yes', '是', 'true', '1']
        except KeyboardInterrupt:
            print("\n")
            return False
    
    def clear_line(self):
        """清除当前行"""
        if self.supports_color:
            print(f"\r{' ' * self.width}\r", end="", flush=True)


# 全局UI实例
_ui_instance: Optional[CLIUI] = None


def get_ui() -> CLIUI:
    """获取全局UI实例"""
    global _ui_instance
    if _ui_instance is None:
        _ui_instance = CLIUI()
    return _ui_instance


# 便捷函数
def print_header(title: str, subtitle: Optional[str] = None):
    """打印标题头部的便捷函数"""
    get_ui().print_header(title, subtitle)


def print_step(step: str, status: str = "info"):
    """打印步骤的便捷函数"""
    get_ui().print_step(step, status)


def print_success(message: str):
    """打印成功信息的便捷函数"""
    get_ui().print_step(message, "success")


def print_error(message: str):
    """打印错误信息的便捷函数"""
    get_ui().print_step(message, "error")


def print_warning(message: str):
    """打印警告信息的便捷函数"""
    get_ui().print_step(message, "warning")


def print_info(message: str):
    """打印信息的便捷函数"""
    get_ui().print_step(message, "info")


def confirm(message: str, default: bool = True) -> bool:
    """确认对话框的便捷函数"""
    return get_ui().confirm(message, default)


def main():
    """测试和演示函数"""
    ui = CLIUI()
    
    # 测试标题和横幅
    ui.print_banner("AQUA CLI美化系统演示", "primary")
    ui.print_header("系统功能测试", "展示所有UI组件功能")
    
    # 测试步骤状态
    ui.print_section("状态消息测试")
    ui.print_step("正在初始化系统", "loading")
    time.sleep(0.5)
    ui.print_step("系统初始化完成", "success")
    ui.print_step("发现配置警告", "warning")
    ui.print_step("遇到网络错误", "error")
    ui.print_step("这是一般信息", "info")
    
    print()
    
    # 测试键值对
    ui.print_section("配置信息显示")
    ui.print_key_value("环境", "开发环境")
    ui.print_key_value("版本", "2.0.0")
    ui.print_key_value("调试模式", True)
    ui.print_key_value("内存限制", "1536MB")
    
    print()
    
    # 测试列表
    ui.print_section("模块列表")
    modules = ["数据仓库", "策略构建", "回测引擎", "模拟交易", "AI助手"]
    ui.print_list(modules, bullet="▶")
    
    print()
    
    # 测试表格
    ui.print_section("服务状态")
    headers = ["服务", "状态", "端口", "启动时间"]
    rows = [
        ["Backend API", "Running", "8000", "2.3s"],
        ["Frontend", "Running", "5173", "1.8s"],
        ["Database", "Connected", "5432", "0.5s"]
    ]
    ui.print_table(headers, rows)
    
    print()
    
    # 测试进度条
    ui.print_section("进度条演示")
    for i in range(11):
        ui.print_progress_bar(i, 10, "数据加载中")
        time.sleep(0.1)
    
    print()
    
    # 测试框架
    ui.print_section("信息框演示")
    ui.print_box(
        "AQUA系统启动成功！\n所有服务正常运行\n请访问 http://localhost:5173 查看前端界面",
        "系统状态",
        "success"
    )
    
    # 测试旋转器
    ui.print_section("加载动画演示")
    ui.print_spinner("正在连接数据库", 1.0)
    ui.print_spinner("正在加载配置", 1.0)
    
    # 测试确认对话框
    ui.print_section("交互测试")
    if ui.confirm("是否继续演示高级功能"):
        ui.print_success("用户选择继续")
    else:
        ui.print_info("用户选择跳过")
    
    # 完成
    ui.print_banner("演示完成", "success")


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.simple_config import get_config_manager
    main()