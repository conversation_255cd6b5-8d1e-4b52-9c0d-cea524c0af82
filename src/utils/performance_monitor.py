#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA性能监控和优化系统

提供性能监控、瓶颈检测和自动优化功能
版本: 1.0.0
创建时间: 2025-08-01
"""

import time
import psutil
import functools
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import json
import asyncio
from datetime import datetime, timedelta

# 导入AQUA工具
from .paths import Paths
from .simple_config import get_config_manager
from .simple_error import safe_call, handle_error, ErrorLevel
from .cli_ui import get_ui


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"        # 计数器
    GAUGE = "gauge"           # 仪表
    HISTOGRAM = "histogram"   # 直方图
    TIMER = "timer"          # 计时器


class PerformanceLevel(Enum):
    """性能等级"""
    EXCELLENT = "excellent"   # 优秀
    GOOD = "good"            # 良好
    AVERAGE = "average"      # 一般
    POOR = "poor"           # 较差
    CRITICAL = "critical"   # 严重


@dataclass
class MetricData:
    """指标数据"""
    name: str
    type: MetricType
    value: float
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class PerformanceReport:
    """性能报告"""
    timestamp: datetime
    system_metrics: Dict[str, float]
    application_metrics: Dict[str, float]
    performance_level: PerformanceLevel
    bottlenecks: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class PerformanceMonitor:
    """
    性能监控器
    
    核心功能：
    1. 系统资源监控
    2. 应用性能指标收集
    3. 性能瓶颈检测
    4. 优化建议生成
    """
    
    def __init__(self):
        """初始化性能监控器"""
        self.config = get_config_manager()
        self.ui = get_ui()
        self.paths = Paths()
        
        # 性能数据存储
        self.metrics_history = defaultdict(lambda: deque(maxlen=1000))
        self.performance_thresholds = self._load_performance_thresholds()
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread = None
        self.collected_metrics = []
        
        # 性能基线
        self.baseline_metrics = {}
        self.baseline_file = self.paths.ROOT / ".aqua" / "performance_baseline.json"
    
    def _load_performance_thresholds(self) -> Dict[str, Dict[str, float]]:
        """加载性能阈值"""
        return {
            "cpu": {
                "excellent": 20.0,
                "good": 40.0,
                "average": 60.0,
                "poor": 80.0
            },
            "memory": {
                "excellent": 30.0,
                "good": 50.0,
                "average": 70.0,
                "poor": 85.0
            },
            "disk_io": {
                "excellent": 10.0,
                "good": 30.0,
                "average": 50.0,
                "poor": 80.0
            },
            "response_time": {
                "excellent": 0.1,
                "good": 0.5,
                "average": 1.0,
                "poor": 3.0
            }
        }
    
    def start_monitoring(self, interval: float = 5.0):
        """启动性能监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        self.ui.print_step("性能监控已启动", "success")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        self.ui.print_step("性能监控已停止", "info")
    
    def _monitoring_loop(self, interval: float):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                system_metrics = self.collect_system_metrics()
                
                # 收集应用指标
                app_metrics = self.collect_application_metrics()
                
                # 存储指标
                for name, value in {**system_metrics, **app_metrics}.items():
                    metric = MetricData(
                        name=name,
                        type=MetricType.GAUGE,
                        value=value
                    )
                    self.metrics_history[name].append(metric)
                
                # 检测异常
                self._detect_performance_issues({**system_metrics, **app_metrics})
                
                time.sleep(interval)
                
            except Exception as e:
                handle_error(e, ErrorLevel.WARNING, "性能监控")
                time.sleep(interval)
    
    def collect_system_metrics(self) -> Dict[str, float]:
        """收集系统指标"""
        try:
            return {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "memory_available_gb": psutil.virtual_memory().available / 1024**3,
                "disk_usage_percent": psutil.disk_usage(self.paths.ROOT).percent,
                "disk_io_read_mb": psutil.disk_io_counters().read_bytes / 1024**2 if psutil.disk_io_counters() else 0,
                "disk_io_write_mb": psutil.disk_io_counters().write_bytes / 1024**2 if psutil.disk_io_counters() else 0,
                "network_sent_mb": psutil.net_io_counters().bytes_sent / 1024**2,
                "network_recv_mb": psutil.net_io_counters().bytes_recv / 1024**2,
                "process_count": len(psutil.pids()),
                "boot_time": time.time() - psutil.boot_time()
            }
        except Exception as e:
            handle_error(e, ErrorLevel.WARNING, "收集系统指标")
            return {}
    
    def collect_application_metrics(self) -> Dict[str, float]:
        """收集应用指标"""
        try:
            current_process = psutil.Process()
            return {
                "app_cpu_percent": current_process.cpu_percent(),
                "app_memory_mb": current_process.memory_info().rss / 1024**2,
                "app_memory_percent": current_process.memory_percent(),
                "app_thread_count": current_process.num_threads(),
                "app_file_descriptors": current_process.num_fds() if hasattr(current_process, 'num_fds') else 0,
                "app_uptime": time.time() - current_process.create_time()
            }
        except Exception as e:
            handle_error(e, ErrorLevel.WARNING, "收集应用指标")
            return {}
    
    def _detect_performance_issues(self, metrics: Dict[str, float]):
        """检测性能问题"""
        issues = []
        
        # CPU使用率检查
        cpu_percent = metrics.get("cpu_percent", 0)
        if cpu_percent > self.performance_thresholds["cpu"]["poor"]:
            issues.append(f"CPU使用率过高: {cpu_percent:.1f}%")
        
        # 内存使用率检查
        memory_percent = metrics.get("memory_percent", 0)
        if memory_percent > self.performance_thresholds["memory"]["poor"]:
            issues.append(f"内存使用率过高: {memory_percent:.1f}%")
        
        # 磁盘使用率检查
        disk_percent = metrics.get("disk_usage_percent", 0)
        if disk_percent > 90:
            issues.append(f"磁盘空间不足: {disk_percent:.1f}%")
        
        # 应用内存泄露检查
        app_memory_mb = metrics.get("app_memory_mb", 0)
        if app_memory_mb > 500:  # 超过500MB
            issues.append(f"应用内存使用量较高: {app_memory_mb:.1f}MB")
        
        if issues:
            for issue in issues:
                self.ui.print_step(f"性能警告: {issue}", "warning")
    
    def measure_execution_time(self, func: Callable = None, *, name: str = None):
        """执行时间测量装饰器"""
        def decorator(f):
            @functools.wraps(f)
            def wrapper(*args, **kwargs):
                metric_name = name or f"{f.__module__}.{f.__name__}"
                start_time = time.perf_counter()
                
                try:
                    result = f(*args, **kwargs)
                    success = True
                except Exception as e:
                    success = False
                    raise
                finally:
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time
                    
                    # 记录指标
                    metric = MetricData(
                        name=f"{metric_name}_execution_time",
                        type=MetricType.TIMER,
                        value=execution_time,
                        tags={"success": str(success)},
                        unit="seconds"
                    )
                    self.metrics_history[metric.name].append(metric)
                    
                    # 性能警告
                    if execution_time > 5.0:  # 超过5秒
                        self.ui.print_step(f"性能警告: {metric_name} 执行时间 {execution_time:.2f}s", "warning")
                
                return result
            return wrapper
        
        if func is None:
            return decorator
        else:
            return decorator(func)
    
    def profile_memory_usage(self, func: Callable = None, *, name: str = None):
        """内存使用分析装饰器"""
        def decorator(f):
            @functools.wraps(f)
            def wrapper(*args, **kwargs):
                metric_name = name or f"{f.__module__}.{f.__name__}"
                
                # 记录开始内存
                process = psutil.Process()
                start_memory = process.memory_info().rss / 1024**2
                
                try:
                    result = f(*args, **kwargs)
                    return result
                finally:
                    # 记录结束内存
                    end_memory = process.memory_info().rss / 1024**2
                    memory_delta = end_memory - start_memory
                    
                    # 记录指标
                    metric = MetricData(
                        name=f"{metric_name}_memory_delta",
                        type=MetricType.GAUGE,
                        value=memory_delta,
                        tags={"start_mb": f"{start_memory:.1f}", "end_mb": f"{end_memory:.1f}"},
                        unit="MB"
                    )
                    self.metrics_history[metric.name].append(metric)
                    
                    # 内存泄露警告
                    if memory_delta > 50:  # 增长超过50MB
                        self.ui.print_step(f"内存警告: {metric_name} 内存增长 {memory_delta:.1f}MB", "warning")
            
            return wrapper
        
        if func is None:
            return decorator
        else:
            return decorator(func)
    
    def generate_performance_report(self) -> PerformanceReport:
        """生成性能报告"""
        # 收集当前指标
        system_metrics = self.collect_system_metrics()
        app_metrics = self.collect_application_metrics()
        
        # 计算性能等级
        performance_level = self._calculate_performance_level(system_metrics, app_metrics)
        
        # 检测瓶颈
        bottlenecks = self._identify_bottlenecks(system_metrics, app_metrics)
        
        # 生成建议
        recommendations = self._generate_recommendations(system_metrics, app_metrics, bottlenecks)
        
        # 生成警告
        warnings = self._generate_warnings(system_metrics, app_metrics)
        
        return PerformanceReport(
            timestamp=datetime.now(),
            system_metrics=system_metrics,
            application_metrics=app_metrics,
            performance_level=performance_level,
            bottlenecks=bottlenecks,
            recommendations=recommendations,
            warnings=warnings
        )
    
    def _calculate_performance_level(self, system_metrics: Dict[str, float], 
                                   app_metrics: Dict[str, float]) -> PerformanceLevel:
        """计算整体性能等级"""
        scores = []
        
        # CPU性能评分
        cpu_percent = system_metrics.get("cpu_percent", 0)
        cpu_score = self._get_metric_score("cpu", cpu_percent)
        scores.append(cpu_score)
        
        # 内存性能评分
        memory_percent = system_metrics.get("memory_percent", 0)
        memory_score = self._get_metric_score("memory", memory_percent)
        scores.append(memory_score)
        
        # 计算平均分
        avg_score = sum(scores) / len(scores) if scores else 0
        
        if avg_score >= 4:
            return PerformanceLevel.EXCELLENT
        elif avg_score >= 3:
            return PerformanceLevel.GOOD
        elif avg_score >= 2:
            return PerformanceLevel.AVERAGE
        elif avg_score >= 1:
            return PerformanceLevel.POOR
        else:
            return PerformanceLevel.CRITICAL
    
    def _get_metric_score(self, metric_type: str, value: float) -> int:
        """获取指标评分"""
        thresholds = self.performance_thresholds.get(metric_type, {})
        
        if value <= thresholds.get("excellent", 0):
            return 5
        elif value <= thresholds.get("good", 0):
            return 4
        elif value <= thresholds.get("average", 0):
            return 3
        elif value <= thresholds.get("poor", 0):
            return 2
        else:
            return 1
    
    def _identify_bottlenecks(self, system_metrics: Dict[str, float], 
                            app_metrics: Dict[str, float]) -> List[str]:
        """识别性能瓶颈"""
        bottlenecks = []
        
        # CPU瓶颈
        cpu_percent = system_metrics.get("cpu_percent", 0)
        if cpu_percent > 80:
            bottlenecks.append(f"CPU瓶颈: 使用率 {cpu_percent:.1f}%")
        
        # 内存瓶颈
        memory_percent = system_metrics.get("memory_percent", 0)
        if memory_percent > 85:
            bottlenecks.append(f"内存瓶颈: 使用率 {memory_percent:.1f}%")
        
        # 磁盘空间瓶颈
        disk_percent = system_metrics.get("disk_usage_percent", 0)
        if disk_percent > 90:
            bottlenecks.append(f"磁盘空间瓶颈: 使用率 {disk_percent:.1f}%")
        
        # 应用内存瓶颈
        app_memory_mb = app_metrics.get("app_memory_mb", 0)
        if app_memory_mb > 1000:  # 超过1GB
            bottlenecks.append(f"应用内存瓶颈: 使用量 {app_memory_mb:.1f}MB")
        
        return bottlenecks
    
    def _generate_recommendations(self, system_metrics: Dict[str, float], 
                                app_metrics: Dict[str, float], 
                                bottlenecks: List[str]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # CPU优化建议
        cpu_percent = system_metrics.get("cpu_percent", 0)
        if cpu_percent > 70:
            recommendations.extend([
                "关闭不必要的后台程序",
                "优化代码中的计算密集型操作",
                "考虑使用多进程或异步处理"
            ])
        
        # 内存优化建议
        memory_percent = system_metrics.get("memory_percent", 0)
        if memory_percent > 80:
            recommendations.extend([
                "清理系统内存和缓存",
                "检查是否存在内存泄露",
                "优化数据结构使用"
            ])
        
        # 磁盘优化建议
        disk_percent = system_metrics.get("disk_usage_percent", 0)
        if disk_percent > 85:
            recommendations.extend([
                "清理临时文件和日志",
                "压缩或删除不必要的文件",
                "考虑扩展存储空间"
            ])
        
        # 应用优化建议
        app_memory_mb = app_metrics.get("app_memory_mb", 0)
        if app_memory_mb > 500:
            recommendations.extend([
                "优化数据加载策略",
                "使用对象池减少内存分配",
                "启用缓存压缩功能"
            ])
        
        return recommendations[:5]  # 最多返回5个建议
    
    def _generate_warnings(self, system_metrics: Dict[str, float], 
                         app_metrics: Dict[str, float]) -> List[str]:
        """生成性能警告"""
        warnings = []
        
        # 系统负载警告
        cpu_percent = system_metrics.get("cpu_percent", 0)
        memory_percent = system_metrics.get("memory_percent", 0)
        
        if cpu_percent > 90 or memory_percent > 95:
            warnings.append("系统负载过高，可能影响性能")
        
        # 磁盘空间警告
        disk_percent = system_metrics.get("disk_usage_percent", 0)
        if disk_percent > 95:
            warnings.append("磁盘空间严重不足，请立即清理")
        
        # 应用异常警告
        app_memory_mb = app_metrics.get("app_memory_mb", 0)
        if app_memory_mb > 2000:  # 超过2GB
            warnings.append("应用内存使用量异常，可能存在内存泄露")
        
        return warnings
    
    def print_performance_report(self, report: PerformanceReport):
        """打印性能报告"""
        self.ui.print_header("性能监控报告", f"生成时间: {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 整体性能等级
        level_icons = {
            PerformanceLevel.EXCELLENT: "🌟",
            PerformanceLevel.GOOD: "✅",
            PerformanceLevel.AVERAGE: "⚠️",
            PerformanceLevel.POOR: "❌",
            PerformanceLevel.CRITICAL: "🚨"
        }
        icon = level_icons.get(report.performance_level, "❓")
        self.ui.print_section(f"整体性能等级: {icon} {report.performance_level.value.upper()}")
        
        # 系统指标
        print()
        self.ui.print_section("系统指标")
        self.ui.print_key_value("CPU使用率", f"{report.system_metrics.get('cpu_percent', 0):.1f}%")
        self.ui.print_key_value("内存使用率", f"{report.system_metrics.get('memory_percent', 0):.1f}%")
        self.ui.print_key_value("磁盘使用率", f"{report.system_metrics.get('disk_usage_percent', 0):.1f}%")
        self.ui.print_key_value("进程数量", f"{report.system_metrics.get('process_count', 0):.0f}")
        
        # 应用指标
        print()
        self.ui.print_section("应用指标")
        self.ui.print_key_value("应用CPU", f"{report.application_metrics.get('app_cpu_percent', 0):.1f}%")
        self.ui.print_key_value("应用内存", f"{report.application_metrics.get('app_memory_mb', 0):.1f} MB")
        self.ui.print_key_value("线程数量", f"{report.application_metrics.get('app_thread_count', 0):.0f}")
        self.ui.print_key_value("运行时间", f"{report.application_metrics.get('app_uptime', 0)/3600:.1f} 小时")
        
        # 性能瓶颈
        if report.bottlenecks:
            print()
            self.ui.print_section("性能瓶颈")
            for bottleneck in report.bottlenecks:
                self.ui.print_step(bottleneck, "error")
        
        # 优化建议
        if report.recommendations:
            print()
            self.ui.print_section("优化建议")
            for rec in report.recommendations:
                self.ui.print_step(rec, "info")
        
        # 性能警告
        if report.warnings:
            print()
            self.ui.print_section("性能警告")
            for warning in report.warnings:
                self.ui.print_step(warning, "warning")
    
    def optimize_performance(self):
        """自动性能优化"""
        self.ui.print_step("开始自动性能优化", "info")
        
        optimizations = []
        
        # 垃圾回收优化
        import gc
        before_objects = len(gc.get_objects())
        gc.collect()
        after_objects = len(gc.get_objects())
        freed_objects = before_objects - after_objects
        
        if freed_objects > 0:
            optimizations.append(f"释放了 {freed_objects} 个未使用对象")
        
        # 缓存清理
        cache_dir = self.paths.ROOT / ".aqua" / "cache"
        if cache_dir.exists():
            cache_size = sum(f.stat().st_size for f in cache_dir.rglob('*') if f.is_file())
            if cache_size > 100 * 1024 * 1024:  # 超过100MB
                # 清理缓存逻辑
                optimizations.append("建议清理缓存文件")
        
        # 显示优化结果
        if optimizations:
            self.ui.print_section("优化结果")
            for opt in optimizations:
                self.ui.print_step(opt, "success")
        else:
            self.ui.print_step("系统性能已经很好，无需优化", "success")
    
    def benchmark_system(self) -> Dict[str, float]:
        """系统性能基准测试"""
        self.ui.print_step("开始系统性能基准测试", "info")
        
        benchmark_results = {}
        
        # CPU基准测试
        self.ui.print_step("CPU基准测试", "loading")
        start_time = time.perf_counter()
        
        # 简单的CPU密集计算
        result = sum(i * i for i in range(100000))
        
        cpu_time = time.perf_counter() - start_time
        benchmark_results["cpu_benchmark_time"] = cpu_time
        
        # 内存基准测试
        self.ui.print_step("内存基准测试", "loading")
        start_time = time.perf_counter()
        
        # 创建和删除大量对象
        test_data = [list(range(1000)) for _ in range(1000)]
        del test_data
        
        memory_time = time.perf_counter() - start_time
        benchmark_results["memory_benchmark_time"] = memory_time
        
        # 磁盘IO基准测试
        self.ui.print_step("磁盘IO基准测试", "loading")
        test_file = self.paths.ROOT / ".aqua" / "benchmark_test.tmp"
        test_file.parent.mkdir(exist_ok=True)
        
        start_time = time.perf_counter()
        
        # 写入测试
        with open(test_file, 'w') as f:
            for i in range(10000):
                f.write(f"Test line {i}\n")
        
        # 读取测试
        with open(test_file, 'r') as f:
            lines = f.readlines()
        
        # 清理测试文件
        test_file.unlink(missing_ok=True)
        
        io_time = time.perf_counter() - start_time
        benchmark_results["io_benchmark_time"] = io_time
        
        # 计算综合性能分数
        cpu_score = max(0, 100 - cpu_time * 1000)  # CPU越快分数越高
        memory_score = max(0, 100 - memory_time * 1000)  # 内存越快分数越高
        io_score = max(0, 100 - io_time * 10)  # IO越快分数越高
        
        overall_score = (cpu_score + memory_score + io_score) / 3
        benchmark_results["overall_performance_score"] = overall_score
        
        self.ui.print_step(f"基准测试完成，综合性能分数: {overall_score:.1f}", "success")
        
        return benchmark_results


def performance_monitor(name: str = None):
    """性能监控装饰器"""
    monitor = PerformanceMonitor()
    return monitor.measure_execution_time(name=name)


def memory_profiler(name: str = None):
    """内存分析装饰器"""
    monitor = PerformanceMonitor()
    return monitor.profile_memory_usage(name=name)


def main():
    """测试和演示函数"""
    monitor = PerformanceMonitor()
    
    # 生成性能报告
    report = monitor.generate_performance_report()
    monitor.print_performance_report(report)
    
    # 系统基准测试
    print()
    benchmark_results = monitor.benchmark_system()
    
    # 性能优化
    print()
    monitor.optimize_performance()


if __name__ == "__main__":
    # 支持直接运行时的导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    
    # 重新导入以支持直接运行
    from utils.paths import Paths
    from utils.simple_config import get_config_manager
    from utils.simple_error import safe_call, handle_error, ErrorLevel
    from utils.cli_ui import get_ui
    
    main()