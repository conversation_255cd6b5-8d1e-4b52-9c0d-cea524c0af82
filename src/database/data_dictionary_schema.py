#!/usr/bin/env python3
"""
数据字典表结构定义

基于DATA_DICTIONARY.md权威文档的表结构扩展支持
确保数据库表结构完全符合数据字典规范
"""

import logging
from typing import Dict, List, Optional

from ..utils.config_loader import ConfigLoader


class DataDictionarySchema:
    """数据字典表结构管理器"""

    def __init__(self, environment: str = "development"):
        """
        初始化数据字典表结构管理器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.config_loader = ConfigLoader()
        self.logger = logging.getLogger(__name__)

        # DATA_DICTIONARY.md权威表结构定义
        self.table_definitions = {
            "fut_main_contract_kline_5min": {
                "description": "期货主力合约5分钟K线数据表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS fut_main_contract_kline_5min (
                    contract_code VARCHAR(20) NOT NULL,
                    trade_datetime TIMESTAMP NOT NULL,
                    open DECIMAL(10,2) NOT NULL,
                    high DECIMAL(10,2) NOT NULL,
                    low DECIMAL(10,2) NOT NULL,
                    close DECIMAL(10,2) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    open_interest BIGINT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (contract_code, trade_datetime)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_fut_5min_contract_time ON fut_main_contract_kline_5min(contract_code, trade_datetime);",
                    "CREATE INDEX IF NOT EXISTS idx_fut_5min_time ON fut_main_contract_kline_5min(trade_datetime);",
                ],
                "constraints": [
                    "ALTER TABLE fut_main_contract_kline_5min ADD CONSTRAINT chk_fut_5min_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);",
                    "ALTER TABLE fut_main_contract_kline_5min ADD CONSTRAINT chk_fut_5min_volume CHECK (volume >= 0 AND amount >= 0 AND open_interest >= 0);",
                    "ALTER TABLE fut_main_contract_kline_5min ADD CONSTRAINT chk_fut_5min_ohlc CHECK (high >= open AND high >= close AND high >= low AND low <= open AND low <= close);",
                ],
            },
            "fut_main_contract_kline_15min": {
                "description": "期货主力合约15分钟K线数据表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS fut_main_contract_kline_15min (
                    contract_code VARCHAR(20) NOT NULL,
                    trade_datetime TIMESTAMP NOT NULL,
                    open DECIMAL(10,2) NOT NULL,
                    high DECIMAL(10,2) NOT NULL,
                    low DECIMAL(10,2) NOT NULL,
                    close DECIMAL(10,2) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    open_interest BIGINT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (contract_code, trade_datetime)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_fut_15min_contract_time ON fut_main_contract_kline_15min(contract_code, trade_datetime);",
                    "CREATE INDEX IF NOT EXISTS idx_fut_15min_time ON fut_main_contract_kline_15min(trade_datetime);",
                ],
                "constraints": [
                    "ALTER TABLE fut_main_contract_kline_15min ADD CONSTRAINT chk_fut_15min_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);",
                    "ALTER TABLE fut_main_contract_kline_15min ADD CONSTRAINT chk_fut_15min_volume CHECK (volume >= 0 AND amount >= 0 AND open_interest >= 0);",
                    "ALTER TABLE fut_main_contract_kline_15min ADD CONSTRAINT chk_fut_15min_ohlc CHECK (high >= open AND high >= close AND high >= low AND low <= open AND low <= close);",
                ],
            },
            "fut_main_contract_kline_30min": {
                "description": "期货主力合约30分钟K线数据表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS fut_main_contract_kline_30min (
                    contract_code VARCHAR(20) NOT NULL,
                    trade_datetime TIMESTAMP NOT NULL,
                    open DECIMAL(10,2) NOT NULL,
                    high DECIMAL(10,2) NOT NULL,
                    low DECIMAL(10,2) NOT NULL,
                    close DECIMAL(10,2) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    open_interest BIGINT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (contract_code, trade_datetime)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_fut_30min_contract_time ON fut_main_contract_kline_30min(contract_code, trade_datetime);",
                    "CREATE INDEX IF NOT EXISTS idx_fut_30min_time ON fut_main_contract_kline_30min(trade_datetime);",
                ],
                "constraints": [
                    "ALTER TABLE fut_main_contract_kline_30min ADD CONSTRAINT chk_fut_30min_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);",
                    "ALTER TABLE fut_main_contract_kline_30min ADD CONSTRAINT chk_fut_30min_volume CHECK (volume >= 0 AND amount >= 0 AND open_interest >= 0);",
                    "ALTER TABLE fut_main_contract_kline_30min ADD CONSTRAINT chk_fut_30min_ohlc CHECK (high >= open AND high >= close AND high >= low AND low <= open AND low <= close);",
                ],
            },
            "fut_main_contract_kline_daily": {
                "description": "期货主力合约日K线数据表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS fut_main_contract_kline_daily (
                    contract_code VARCHAR(20) NOT NULL,
                    trade_date DATE NOT NULL,
                    open DECIMAL(10,2) NOT NULL,
                    high DECIMAL(10,2) NOT NULL,
                    low DECIMAL(10,2) NOT NULL,
                    close DECIMAL(10,2) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    open_interest BIGINT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (contract_code, trade_date)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_fut_daily_contract_date ON fut_main_contract_kline_daily(contract_code, trade_date);",
                    "CREATE INDEX IF NOT EXISTS idx_fut_daily_date ON fut_main_contract_kline_daily(trade_date);",
                ],
                "constraints": [
                    "ALTER TABLE fut_main_contract_kline_daily ADD CONSTRAINT chk_fut_daily_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);",
                    "ALTER TABLE fut_main_contract_kline_daily ADD CONSTRAINT chk_fut_daily_volume CHECK (volume >= 0 AND amount >= 0 AND open_interest >= 0);",
                    "ALTER TABLE fut_main_contract_kline_daily ADD CONSTRAINT chk_fut_daily_ohlc CHECK (high >= open AND high >= close AND high >= low AND low <= open AND low <= close);",
                ],
            },
            "stock_kline_daily": {
                "description": "股票日K线数据表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS stock_kline_daily (
                    symbol VARCHAR(10) NOT NULL,
                    trade_date DATE NOT NULL,
                    open DECIMAL(10,2) NOT NULL,
                    high DECIMAL(10,2) NOT NULL,
                    low DECIMAL(10,2) NOT NULL,
                    close DECIMAL(10,2) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    turnover_rate DECIMAL(8,4),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (symbol, trade_date)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_stock_kline_symbol_date ON stock_kline_daily(symbol, trade_date);",
                    "CREATE INDEX IF NOT EXISTS idx_stock_kline_date ON stock_kline_daily(trade_date);",
                ],
                "constraints": [
                    "ALTER TABLE stock_kline_daily ADD CONSTRAINT chk_stock_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);",
                    "ALTER TABLE stock_kline_daily ADD CONSTRAINT chk_stock_volume CHECK (volume >= 0 AND amount >= 0);",
                ],
            },
            "stock_basic_info": {
                "description": "股票基础信息表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS stock_basic_info (
                    symbol VARCHAR(10) NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    market VARCHAR(10) NOT NULL,
                    industry VARCHAR(50),
                    list_date DATE,
                    status VARCHAR(20) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (symbol)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_stock_basic_market ON stock_basic_info(market);",
                    "CREATE INDEX IF NOT EXISTS idx_stock_basic_industry ON stock_basic_info(industry);",
                ],
                "constraints": [],
            },
            "fut_basic_info": {
                "description": "期货基础信息表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS fut_basic_info (
                    product_code VARCHAR(10) NOT NULL,
                    product_name VARCHAR(50) NOT NULL,
                    exchange VARCHAR(10) NOT NULL,
                    contract_size INTEGER,
                    tick_size DECIMAL(10,4),
                    trading_unit VARCHAR(20),
                    delivery_month VARCHAR(20),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (product_code)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_fut_basic_exchange ON fut_basic_info(exchange);"
                ],
                "constraints": [],
            },
            # =================================================================
            # DATA_DICTIONARY.md v3.0 分层架构表结构定义
            # =================================================================
            # CSV层表结构 (历史数据基础层)
            "csv_fut_main_contract_kline_15min": {
                "description": "CSV层-期货主力合约15分钟K线数据 (FromC2C历史数据)",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS csv_fut_main_contract_kline_15min (
                    contract_code VARCHAR(20) NOT NULL,
                    product_code VARCHAR(10) NOT NULL,
                    trade_datetime TIMESTAMP NOT NULL,
                    open DECIMAL(18,4) NOT NULL,
                    high DECIMAL(18,4) NOT NULL,
                    low DECIMAL(18,4) NOT NULL,
                    close DECIMAL(18,4) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(22,4) NOT NULL,
                    open_interest BIGINT NOT NULL,
                    source_file VARCHAR(255) NOT NULL,
                    data_source VARCHAR(50) DEFAULT 'CSV_FROMC2C',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (contract_code, trade_datetime)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_csv_fut_15min_product_time ON csv_fut_main_contract_kline_15min(product_code, trade_datetime);",
                    "CREATE INDEX IF NOT EXISTS idx_csv_fut_15min_time ON csv_fut_main_contract_kline_15min(trade_datetime);",
                    "CREATE INDEX IF NOT EXISTS idx_csv_fut_15min_source ON csv_fut_main_contract_kline_15min(data_source);",
                ],
                "constraints": [
                    "ALTER TABLE csv_fut_main_contract_kline_15min ADD CONSTRAINT chk_csv_fut_15min_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);",
                    "ALTER TABLE csv_fut_main_contract_kline_15min ADD CONSTRAINT chk_csv_fut_15min_volume CHECK (volume >= 0 AND amount >= 0 AND open_interest >= 0);",
                    "ALTER TABLE csv_fut_main_contract_kline_15min ADD CONSTRAINT chk_csv_fut_15min_ohlc CHECK (high >= open AND high >= close AND high >= low AND low <= open AND low <= close);",
                ],
            },
            "csv_fut_main_contract_kline_5min": {
                "description": "CSV层-期货主力合约5分钟K线数据 (FromC2C历史数据)",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS csv_fut_main_contract_kline_5min (
                    contract_code VARCHAR(20) NOT NULL,
                    product_code VARCHAR(10) NOT NULL,
                    trade_datetime TIMESTAMP NOT NULL,
                    open DECIMAL(18,4) NOT NULL,
                    high DECIMAL(18,4) NOT NULL,
                    low DECIMAL(18,4) NOT NULL,
                    close DECIMAL(18,4) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(22,4) NOT NULL,
                    open_interest BIGINT NOT NULL,
                    source_file VARCHAR(255) NOT NULL,
                    data_source VARCHAR(50) DEFAULT 'CSV_FROMC2C',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (contract_code, trade_datetime)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_csv_fut_5min_product_time ON csv_fut_main_contract_kline_5min(product_code, trade_datetime);",
                    "CREATE INDEX IF NOT EXISTS idx_csv_fut_5min_time ON csv_fut_main_contract_kline_5min(trade_datetime);",
                ],
                "constraints": [
                    "ALTER TABLE csv_fut_main_contract_kline_5min ADD CONSTRAINT chk_csv_fut_5min_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);"
                ],
            },
            "csv_fut_main_contract_kline_30min": {
                "description": "CSV层-期货主力合约30分钟K线数据 (FromC2C历史数据)",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS csv_fut_main_contract_kline_30min (
                    contract_code VARCHAR(20) NOT NULL,
                    product_code VARCHAR(10) NOT NULL,
                    trade_datetime TIMESTAMP NOT NULL,
                    open DECIMAL(18,4) NOT NULL,
                    high DECIMAL(18,4) NOT NULL,
                    low DECIMAL(18,4) NOT NULL,
                    close DECIMAL(18,4) NOT NULL,
                    volume BIGINT NOT NULL,
                    amount DECIMAL(22,4) NOT NULL,
                    open_interest BIGINT NOT NULL,
                    source_file VARCHAR(255) NOT NULL,
                    data_source VARCHAR(50) DEFAULT 'CSV_FROMC2C',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (contract_code, trade_datetime)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_csv_fut_30min_product_time ON csv_fut_main_contract_kline_30min(product_code, trade_datetime);",
                    "CREATE INDEX IF NOT EXISTS idx_csv_fut_30min_time ON csv_fut_main_contract_kline_30min(trade_datetime);",
                ],
                "constraints": [
                    "ALTER TABLE csv_fut_main_contract_kline_30min ADD CONSTRAINT chk_csv_fut_30min_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);"
                ],
            },
            # TUSHARE层表结构 (权威数据标准层) - 预留结构
            "tushare_stock_daily": {
                "description": "TUSHARE层-股票日线行情数据 (Tushare Pro权威数据)",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS tushare_stock_daily (
                    ts_code VARCHAR(20) NOT NULL,
                    trade_date DATE NOT NULL,
                    open DECIMAL(18,4) NOT NULL,
                    high DECIMAL(18,4) NOT NULL,
                    low DECIMAL(18,4) NOT NULL,
                    close DECIMAL(18,4) NOT NULL,
                    pre_close DECIMAL(18,4),
                    change DECIMAL(18,4),
                    pct_chg DECIMAL(10,4),
                    vol BIGINT,
                    amount DECIMAL(22,4),
                    data_source VARCHAR(50) DEFAULT 'TUSHARE_PRO',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_tushare_stock_date ON tushare_stock_daily(trade_date);",
                    "CREATE INDEX IF NOT EXISTS idx_tushare_stock_code ON tushare_stock_daily(ts_code);",
                ],
                "constraints": [
                    "ALTER TABLE tushare_stock_daily ADD CONSTRAINT chk_tushare_stock_prices CHECK (open >= 0 AND high >= 0 AND low >= 0 AND close >= 0);"
                ],
            },
            # MySQL层表结构 (补充数据资源层) - 预留结构
            "mysql_akshare_fut_basic": {
                "description": "MySQL层-期货基础信息 (AKShare补充数据)",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS mysql_akshare_fut_basic (
                    symbol VARCHAR(20) NOT NULL,
                    name VARCHAR(100),
                    exchange VARCHAR(20),
                    listed_date DATE,
                    delisted_date DATE,
                    contract_multiplier INTEGER,
                    price_tick DECIMAL(10,4),
                    data_source VARCHAR(50) DEFAULT 'AKSHARE_MYSQL',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (symbol)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_mysql_fut_basic_exchange ON mysql_akshare_fut_basic(exchange);",
                    "CREATE INDEX IF NOT EXISTS idx_mysql_fut_basic_listed ON mysql_akshare_fut_basic(listed_date);",
                ],
                "constraints": [],
            },
            # 系统管理层表结构 - 导入历史和统计
            "data_import_history": {
                "description": "数据导入历史记录表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS data_import_history (
                    import_id VARCHAR(50) NOT NULL,
                    import_type VARCHAR(20) NOT NULL,
                    environment VARCHAR(20) NOT NULL,
                    source_info TEXT NOT NULL,
                    target_tables TEXT,
                    import_status VARCHAR(20) NOT NULL,
                    total_records BIGINT DEFAULT 0,
                    success_records BIGINT DEFAULT 0,
                    error_records BIGINT DEFAULT 0,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    duration_seconds INTEGER,
                    error_message TEXT,
                    import_config TEXT,
                    session_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (import_id)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_import_history_type ON data_import_history(import_type);",
                    "CREATE INDEX IF NOT EXISTS idx_import_history_env ON data_import_history(environment);",
                    "CREATE INDEX IF NOT EXISTS idx_import_history_status ON data_import_history(import_status);",
                    "CREATE INDEX IF NOT EXISTS idx_import_history_start_time ON data_import_history(start_time);",
                    "CREATE INDEX IF NOT EXISTS idx_import_history_type_env ON data_import_history(import_type, environment);",
                ],
                "constraints": [
                    "ALTER TABLE data_import_history ADD CONSTRAINT chk_import_type CHECK (import_type IN ('csv', 'mysql', 'fromc2c', 'tushare'));",
                    "ALTER TABLE data_import_history ADD CONSTRAINT chk_import_status CHECK (import_status IN ('pending', 'running', 'completed', 'failed', 'cancelled', 'paused'));",
                ],
            },
            "data_import_stats": {
                "description": "数据导入统计汇总表",
                "create_sql": """
                CREATE TABLE IF NOT EXISTS data_import_stats (
                    stat_id VARCHAR(50) NOT NULL,
                    environment VARCHAR(20) NOT NULL,
                    import_type VARCHAR(20) NOT NULL,
                    stat_period VARCHAR(20) NOT NULL,
                    stat_date DATE NOT NULL,
                    total_imports INTEGER DEFAULT 0,
                    successful_imports INTEGER DEFAULT 0,
                    failed_imports INTEGER DEFAULT 0,
                    total_records BIGINT DEFAULT 0,
                    total_duration_seconds BIGINT DEFAULT 0,
                    avg_duration_seconds DECIMAL(10,2),
                    success_rate DECIMAL(5,4),
                    error_summary TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (stat_id)
                );
                """,
                "indexes": [
                    "CREATE INDEX IF NOT EXISTS idx_import_stats_date ON data_import_stats(stat_date);",
                    "CREATE INDEX IF NOT EXISTS idx_import_stats_type ON data_import_stats(import_type);",
                    "CREATE INDEX IF NOT EXISTS idx_import_stats_env ON data_import_stats(environment);",
                    "CREATE INDEX IF NOT EXISTS idx_import_stats_period ON data_import_stats(stat_period);",
                    "CREATE INDEX IF NOT EXISTS idx_import_stats_env_type_date ON data_import_stats(environment, import_type, stat_date);",
                ],
                "constraints": [
                    "ALTER TABLE data_import_stats ADD CONSTRAINT chk_stats_period CHECK (stat_period IN ('day', 'week', 'month'));",
                    "ALTER TABLE data_import_stats ADD CONSTRAINT chk_stats_success_rate CHECK (success_rate >= 0 AND success_rate <= 1);",
                ],
            },
        }

    def create_data_dictionary_table(self, table_name: str, connection) -> bool:
        """
        创建符合DATA_DICTIONARY.md规范的表

        Args:
            table_name: 表名
            connection: 数据库连接

        Returns:
            bool: 是否创建成功
        """
        if table_name not in self.table_definitions:
            self.logger.error(f"表 {table_name} 不在DATA_DICTIONARY.md定义中")
            return False

        table_def = self.table_definitions[table_name]

        try:
            # 创建表
            connection.execute(table_def["create_sql"])
            self.logger.info(f"创建表: {table_name}")

            # 创建索引
            for index_sql in table_def["indexes"]:
                try:
                    connection.execute(index_sql)
                except Exception as e:
                    self.logger.warning(f"创建索引失败: {e}")

            # 添加约束（DuckDB可能不支持所有约束）
            for constraint_sql in table_def["constraints"]:
                try:
                    connection.execute(constraint_sql)
                except Exception as e:
                    self.logger.debug(f"约束创建跳过: {e}")

            connection.commit()
            self.logger.info(f"表 {table_name} 创建完成")
            return True

        except Exception as e:
            self.logger.error(f"创建表 {table_name} 失败: {e}")
            return False

    def create_all_data_dictionary_tables(self, connection) -> Dict[str, bool]:
        """
        创建所有DATA_DICTIONARY.md定义的表

        Args:
            connection: 数据库连接

        Returns:
            Dict[str, bool]: 每个表的创建结果
        """
        results = {}

        for table_name in self.table_definitions.keys():
            results[table_name] = self.create_data_dictionary_table(
                table_name, connection
            )

        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        self.logger.info(f"数据字典表创建完成: {success_count}/{total_count} 成功")

        return results

    def validate_table_exists(self, table_name: str, connection) -> bool:
        """
        验证表是否存在且结构正确

        Args:
            table_name: 表名
            connection: 数据库连接

        Returns:
            bool: 表是否存在且结构正确
        """
        try:
            # 检查表是否存在
            result = connection.execute(
                f"SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{table_name}'"
            ).fetchone()
            if not result or result[0] == 0:
                return False

            # 检查表结构（获取列信息）
            columns_result = connection.execute(f"DESCRIBE {table_name}").fetchall()
            existing_columns = {row[0].lower() for row in columns_result}

            if table_name in self.table_definitions:
                # 检查是否有必需的列
                table_def = self.table_definitions[table_name]
                create_sql = table_def["create_sql"]

                # 简单检查：确保必需字段存在
                required_fields = (
                    ["contract_code", "trade_datetime", "open", "high", "low", "close"]
                    if "fut_" in table_name
                    else ["symbol", "trade_date"]
                )
                for field in required_fields:
                    if field.lower() not in existing_columns:
                        self.logger.warning(f"表 {table_name} 缺少必需字段: {field}")
                        return False

            return True

        except Exception as e:
            self.logger.error(f"验证表 {table_name} 失败: {e}")
            return False

    def get_table_definition(self, table_name: str) -> Optional[Dict]:
        """
        获取表的完整定义

        Args:
            table_name: 表名

        Returns:
            Optional[Dict]: 表定义
        """
        return self.table_definitions.get(table_name)

    def get_supported_tables(self) -> List[str]:
        """
        获取所有支持的表名

        Returns:
            List[str]: 支持的表名列表
        """
        return list(self.table_definitions.keys())

    def is_data_dictionary_table(self, table_name: str) -> bool:
        """
        检查是否为DATA_DICTIONARY.md定义的表

        Args:
            table_name: 表名

        Returns:
            bool: 是否为标准表
        """
        return table_name in self.table_definitions

    def get_schema_summary(self) -> Dict:
        """
        获取数据字典架构摘要

        Returns:
            Dict: 架构摘要
        """
        return {
            "total_tables": len(self.table_definitions),
            "futures_tables": [t for t in self.table_definitions.keys() if "fut_" in t],
            "stock_tables": [t for t in self.table_definitions.keys() if "stock_" in t],
            "authority_source": "DATA_DICTIONARY.md v3.0 (分层架构版)",
            "environment": self.environment,
            "schema_version": "v3.0",
            "layered_tables": self.get_layered_tables_summary(),
        }

    def get_layered_tables_summary(self) -> Dict:
        """获取分层表结构摘要"""
        return {
            "csv_layer": [
                t for t in self.table_definitions.keys() if t.startswith("csv_")
            ],
            "tushare_layer": [
                t for t in self.table_definitions.keys() if t.startswith("tushare_")
            ],
            "mysql_layer": [
                t for t in self.table_definitions.keys() if t.startswith("mysql_")
            ],
        }
