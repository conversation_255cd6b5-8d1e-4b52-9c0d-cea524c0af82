"""
DuckDB数据库连接管理器
提供统一的数据库连接和基础操作接口
"""

import duckdb
import logging
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from contextlib import contextmanager

from ..utils.config_loader import ConfigLoader
from ..utils.time_utils import get_beijing_time_now
from ..utils.exceptions import (
    raise_database_error,
)


class DuckDBConnectionManager:
    """DuckDB连接管理器"""

    def __init__(self, environment: str = "test"):
        """
        初始化连接管理器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.config_loader = ConfigLoader()
        self.config = self.config_loader.get_config(environment)

        # 日志配置 - 必须在路径展开之前初始化
        self.logger = logging.getLogger(__name__)

        # 数据库配置 - 使用ConfigLoader的占位符解析功能
        db_config = self.config_loader.get_database_config(environment)
        db_path_str = db_config.get("path", "{datacenter_dir}/aqua_" + environment + ".duckdb")

        # 使用ConfigLoader的路径展开功能，已经自动处理了占位符
        self._db_path = self.config_loader.expand_cross_platform_path(db_path_str, auto_mkdir=True)

        # 日志记录实际使用的数据库路径
        self.logger.debug(f"数据库路径展开: {db_path_str} -> {self._db_path}")

        # 连接池（简单实现）
        self._connection: Optional[duckdb.DuckDBPyConnection] = None

    def _get_database_path(self) -> str:
        """
        Get database file path.

        Returns:
            Path to database file as string
        """
        return str(self._db_path)

    def get_connection(self) -> duckdb.DuckDBPyConnection:
        """
        获取数据库连接

        Returns:
            DuckDB连接对象

        Raises:
            DatabaseException: 数据库连接失败
        """
        if self._connection is None:
            try:
                # 🚩 WINDOWS_VERIFICATION_FLAG: 跨平台数据库连接处理
                self._connection = duckdb.connect(str(self._db_path))
                self.logger.info(f"连接到数据库: {self._db_path}")
            except duckdb.IOException as e:
                if "Conflicting lock" in str(e):
                    # 数据库锁定冲突处理
                    self.logger.warning(
                        f"数据库锁定冲突，尝试强制关闭连接: {self._db_path}"
                    )
                    self._force_close_connections()
                    try:
                        # 重试连接
                        self._connection = duckdb.connect(str(self._db_path))
                        self.logger.info(f"重试连接成功: {self._db_path}")
                    except Exception as retry_e:
                        raise_database_error(
                            f"数据库连接失败，重试后仍无法连接: {str(retry_e)}",
                            cause=retry_e,
                        )
                else:
                    raise_database_error(f"数据库IO错误: {str(e)}", cause=e)
            except Exception as e:
                raise_database_error(f"连接数据库失败: {str(e)}", cause=e)

        return self._connection

    def _force_close_connections(self):
        """
        强制关闭可能的数据库连接
        """
        try:
            if self._connection:
                self._connection.close()
                self._connection = None
        except:
            pass

        # 尝试通过临时连接强制解锁
        try:
            import time

            time.sleep(0.1)  # 短暂等待
        except:
            pass

    def close_connection(self):
        """关闭数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None
            self.logger.info("数据库连接已关闭")

    @contextmanager
    def get_cursor(self):
        """
        获取数据库游标的上下文管理器

        Yields:
            DuckDB游标对象
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
        except Exception as e:
            self.logger.error(f"数据库操作异常: {e}")
            raise
        finally:
            cursor.close()

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Tuple]:
        """
        执行查询语句

        Args:
            query: SQL查询语句
            params: 查询参数

        Returns:
            查询结果列表
        """
        with self.get_cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()

    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """
        批量执行SQL语句

        Args:
            query: SQL语句
            params_list: 参数列表

        Returns:
            影响的行数
        """
        with self.get_cursor() as cursor:
            cursor.executemany(query, params_list)
            return cursor.rowcount

    def execute_script(self, script: str):
        """
        执行SQL脚本

        Args:
            script: SQL脚本内容
        """
        conn = self.get_connection()
        conn.executescript(script)

    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在

        Args:
            table_name: 表名

        Returns:
            表是否存在
        """
        query = """
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_name = ? 
        AND table_schema = 'main'
        """
        result = self.execute_query(query, (table_name,))
        return result[0][0] > 0 if result else False

    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息

        Args:
            table_name: 表名

        Returns:
            表结构信息列表
        """
        if not self.table_exists(table_name):
            return []

        query = f"DESCRIBE {table_name}"
        columns = self.execute_query(query)

        result = []
        for col in columns:
            result.append(
                {
                    "column_name": col[0],
                    "column_type": col[1],
                    "null": col[2],
                    "key": col[3] if len(col) > 3 else None,
                    "default": col[4] if len(col) > 4 else None,
                    "extra": col[5] if len(col) > 5 else None,
                }
            )

        return result

    def get_table_count(self, table_name: str) -> int:
        """
        获取表的记录数

        Args:
            table_name: 表名

        Returns:
            记录数
        """
        if not self.table_exists(table_name):
            return 0

        query = f"SELECT COUNT(*) FROM {table_name}"
        result = self.execute_query(query)
        return result[0][0] if result else 0

    def get_all_tables(self) -> List[str]:
        """
        获取所有表名

        Returns:
            表名列表
        """
        query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'main'
        ORDER BY table_name
        """
        result = self.execute_query(query)
        return [row[0] for row in result]

    def drop_table(self, table_name: str, if_exists: bool = True):
        """
        删除表

        Args:
            table_name: 表名
            if_exists: 如果表不存在是否报错
        """
        if_exists_clause = "IF EXISTS" if if_exists else ""
        query = f"DROP TABLE {if_exists_clause} {table_name}"

        with self.get_cursor() as cursor:
            cursor.execute(query)

        self.logger.info(f"表 {table_name} 已删除")

    def create_table_from_csv(
        self, table_name: str, csv_path: Path, columns: Optional[List[str]] = None
    ) -> bool:
        """
        从CSV文件创建表

        Args:
            table_name: 表名
            csv_path: CSV文件路径
            columns: 指定的列名列表

        Returns:
            是否成功
        """
        try:
            if not csv_path.exists():
                self.logger.error(f"CSV文件不存在: {csv_path}")
                return False

            # 构建CREATE TABLE语句
            if columns:
                columns_clause = f"({', '.join(columns)})"
            else:
                columns_clause = ""

            query = f"""
            CREATE TABLE {table_name} AS 
            SELECT * FROM read_csv_auto('{csv_path}'{', columns={columns_clause}' if columns else ''})
            """

            with self.get_cursor() as cursor:
                cursor.execute(query)

            count = self.get_table_count(table_name)
            self.logger.info(f"从CSV创建表 {table_name}，导入 {count} 条记录")
            return True

        except Exception as e:
            self.logger.error(f"从CSV创建表失败: {e}")
            return False

    def insert_from_csv(
        self, table_name: str, csv_path: Path, replace: bool = False
    ) -> bool:
        """
        从CSV文件插入数据

        Args:
            table_name: 表名
            csv_path: CSV文件路径
            replace: 是否替换现有数据

        Returns:
            是否成功
        """
        try:
            if not csv_path.exists():
                self.logger.error(f"CSV文件不存在: {csv_path}")
                return False

            if replace:
                # 清空表
                self.execute_query(f"DELETE FROM {table_name}")

            # 获取目标表的列信息
            with self.get_cursor() as cursor:
                cursor.execute(f"DESCRIBE {table_name}")
                table_columns = [row[0] for row in cursor.fetchall()]

            # 读取CSV头部，匹配列顺序
            import pandas as pd

            sample_df = pd.read_csv(csv_path, nrows=0)  # 只读取头部
            csv_columns = sample_df.columns.tolist()

            # 构建列映射，确保按表结构顺序
            column_mapping = []
            for table_col in table_columns:
                if table_col in csv_columns:
                    column_mapping.append(table_col)
                else:
                    # 如果表中有列但CSV中没有，使用NULL填充
                    column_mapping.append("NULL as " + table_col)

            columns_clause = ", ".join(column_mapping)

            # 使用严格的列映射插入数据，避免重复
            # 根据表结构确定去重条件 - 支持futures_main_contract_kline的三字段主键
            dedup_conditions = []
            if "contract_code" in csv_columns:
                dedup_conditions.append(
                    f"{table_name}.contract_code = csv_data.contract_code"
                )
            if "trade_datetime" in csv_columns:
                dedup_conditions.append(
                    f"{table_name}.trade_datetime = csv_data.trade_datetime"
                )
            if "frequency" in csv_columns:
                dedup_conditions.append(f"{table_name}.frequency = csv_data.frequency")

            where_clause = " AND ".join(dedup_conditions) if dedup_conditions else "1=0"

            query = f"""
            INSERT INTO {table_name} 
            SELECT {columns_clause}
            FROM read_csv_auto('{csv_path}', header=true) AS csv_data
            WHERE NOT EXISTS (
                SELECT 1 FROM {table_name} 
                WHERE {where_clause}
            )
            """

            with self.get_cursor() as cursor:
                cursor.execute(query)

            count = self.get_table_count(table_name)
            self.logger.info(f"向表 {table_name} 插入数据，当前记录数: {count}")
            return True

        except Exception as e:
            self.logger.error(f"从CSV插入数据失败: {e}")
            return False

    def backup_table(self, table_name: str, backup_dir: Path) -> bool:
        """
        备份表到CSV文件

        Args:
            table_name: 表名
            backup_dir: 备份目录

        Returns:
            是否成功
        """
        try:
            if not self.table_exists(table_name):
                self.logger.warning(f"表 {table_name} 不存在，跳过备份")
                return False

            backup_dir.mkdir(parents=True, exist_ok=True)
            backup_file = (
                backup_dir
                / f"{table_name}_{get_beijing_time_now().strftime('%Y%m%d_%H%M%S')}.csv"
            )

            query = f"""
            COPY {table_name} TO '{backup_file}' (HEADER, DELIMITER ',')
            """

            with self.get_cursor() as cursor:
                cursor.execute(query)

            self.logger.info(f"表 {table_name} 已备份到: {backup_file}")
            return True

        except Exception as e:
            self.logger.error(f"备份表失败: {e}")
            return False

    def get_database_size(self) -> int:
        """
        获取数据库文件大小（字节）

        Returns:
            文件大小
        """
        if self._db_path.exists():
            return self._db_path.stat().st_size
        return 0

    def vacuum(self):
        """优化数据库"""
        with self.get_cursor() as cursor:
            cursor.execute("VACUUM")
        self.logger.info("数据库优化完成")

    def analyze(self):
        """分析数据库统计信息"""
        with self.get_cursor() as cursor:
            cursor.execute("ANALYZE")
        self.logger.info("数据库统计信息分析完成")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_connection()

    def __del__(self):
        """析构函数"""
        self.close_connection()
