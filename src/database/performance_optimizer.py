#!/usr/bin/env python3
"""
数据库性能优化器
提供数据库索引创建、查询优化、性能监控等功能

特性：
- 智能索引创建和管理
- 查询性能分析
- 数据库统计信息收集
- 性能基准测试
"""

import time
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path
import duckdb
import logging
from dataclasses import dataclass
from enum import Enum

from ..utils.config_loader import ConfigLoader
from ..utils.time_utils import get_beijing_time_now
from ..utils.logger import get_logger


class IndexType(Enum):
    """索引类型"""

    BTREE = "btree"
    HASH = "hash"
    COMPOSITE = "composite"


@dataclass
class IndexInfo:
    """索引信息"""

    table_name: str
    index_name: str
    columns: List[str]
    index_type: IndexType
    is_unique: bool = False
    description: str = ""


@dataclass
class QueryPerformance:
    """查询性能信息"""

    query: str
    execution_time: float
    rows_returned: int
    rows_examined: int
    index_used: bool
    timestamp: datetime


class DatabasePerformanceOptimizer:
    """数据库性能优化器"""

    def __init__(self, config_path: str = None, env: str = "dev"):
        # 使用统一路径管理获取配置路径
        from utils.paths import Paths
        if config_path is None:
            config_path = str(Paths.CONFIG / "settings.toml")

        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.get_config(env)
        self.logger = get_logger("DatabaseOptimizer")
        self.db_path = self.config.get("database", {}).get("path", "")

        # 预定义索引配置
        self.index_configs = [
            IndexInfo(
                table_name="stock_kline_daily",
                index_name="idx_stock_daily_symbol_date",
                columns=["symbol", "trade_date"],
                index_type=IndexType.COMPOSITE,
                description="股票日K线主查询索引",
            ),
            IndexInfo(
                table_name="stock_kline_daily",
                index_name="idx_stock_daily_date",
                columns=["trade_date"],
                index_type=IndexType.BTREE,
                description="股票日K线日期索引",
            ),
            IndexInfo(
                table_name="fut_main_contract_kline_15min",
                index_name="idx_futures_contract_time",
                columns=["contract_code", "trade_datetime"],
                index_type=IndexType.COMPOSITE,
                description="期货15分钟K线主查询索引",
            ),
            IndexInfo(
                table_name="fut_main_contract_kline_15min",
                index_name="idx_futures_time",
                columns=["trade_datetime"],
                index_type=IndexType.BTREE,
                description="期货15分钟K线时间索引",
            ),
            IndexInfo(
                table_name="stock_basic_info",
                index_name="idx_stock_basic_symbol",
                columns=["symbol"],
                index_type=IndexType.BTREE,
                is_unique=True,
                description="股票基础信息代码索引",
            ),
            IndexInfo(
                table_name="data_import_log",
                index_name="idx_import_log_time",
                columns=["start_time"],
                index_type=IndexType.BTREE,
                description="数据导入日志时间索引",
            ),
        ]

        self.performance_logs: List[QueryPerformance] = []

    def connect(self) -> duckdb.DuckDBPyConnection:
        """创建数据库连接"""
        if not Path(self.db_path).exists():
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")

        return duckdb.connect(self.db_path)

    def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            with self.connect() as conn:
                result = conn.execute(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ?",
                    [table_name],
                ).fetchone()
                return result[0] > 0
        except Exception as e:
            self.logger.error(f"检查表存在性失败: {e}")
            return False

    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            with self.connect() as conn:
                # 获取表结构
                schema_result = conn.execute(f"DESCRIBE {table_name}").fetchall()

                # 获取表统计信息
                stats_result = conn.execute(
                    f"SELECT COUNT(*) FROM {table_name}"
                ).fetchone()
                row_count = stats_result[0] if stats_result else 0

                # 获取表大小（估算）
                size_result = conn.execute(
                    f"SELECT pg_total_relation_size('{table_name}') as size"
                ).fetchone()
                table_size = size_result[0] if size_result else 0

                return {
                    "table_name": table_name,
                    "columns": [
                        {"name": col[0], "type": col[1]} for col in schema_result
                    ],
                    "row_count": row_count,
                    "table_size": table_size,
                    "exists": True,
                }
        except Exception as e:
            self.logger.error(f"获取表信息失败: {table_name}, {e}")
            return {"table_name": table_name, "exists": False}

    def create_index(self, index_info: IndexInfo) -> bool:
        """创建索引"""
        try:
            # 检查表是否存在
            if not self.check_table_exists(index_info.table_name):
                self.logger.warning(f"表不存在，跳过索引创建: {index_info.table_name}")
                return False

            with self.connect() as conn:
                # 检查索引是否已存在
                existing_indexes = conn.execute(
                    """
                    SELECT index_name 
                    FROM duckdb_indexes() 
                    WHERE table_name = ? AND index_name = ?
                    """,
                    [index_info.table_name, index_info.index_name],
                ).fetchall()

                if existing_indexes:
                    self.logger.info(f"索引已存在，跳过创建: {index_info.index_name}")
                    return True

                # 构建CREATE INDEX语句
                columns_str = ", ".join(index_info.columns)
                unique_clause = "UNIQUE " if index_info.is_unique else ""

                create_sql = f"""
                CREATE {unique_clause}INDEX {index_info.index_name} 
                ON {index_info.table_name} ({columns_str})
                """

                self.logger.info(f"创建索引: {index_info.index_name}")
                start_time = time.time()

                conn.execute(create_sql)

                execution_time = time.time() - start_time
                self.logger.info(
                    f"索引创建完成: {index_info.index_name}, 耗时: {execution_time:.2f}秒"
                )

                return True

        except Exception as e:
            self.logger.error(f"创建索引失败: {index_info.index_name}, {e}")
            return False

    def drop_index(self, index_name: str) -> bool:
        """删除索引"""
        try:
            with self.connect() as conn:
                conn.execute(f"DROP INDEX IF EXISTS {index_name}")
                self.logger.info(f"索引删除成功: {index_name}")
                return True
        except Exception as e:
            self.logger.error(f"删除索引失败: {index_name}, {e}")
            return False

    def create_all_indexes(self) -> Dict[str, bool]:
        """创建所有预定义索引"""
        results = {}

        self.logger.info("开始创建所有索引...")

        for index_info in self.index_configs:
            success = self.create_index(index_info)
            results[index_info.index_name] = success

        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        self.logger.info(f"索引创建完成: {success_count}/{total_count} 成功")

        return results

    def analyze_query_performance(self, query: str) -> QueryPerformance:
        """分析查询性能"""
        try:
            with self.connect() as conn:
                # 执行EXPLAIN分析
                explain_query = f"EXPLAIN ANALYZE {query}"

                start_time = time.time()
                result = conn.execute(query).fetchall()
                execution_time = time.time() - start_time

                # 获取查询计划
                explain_result = conn.execute(explain_query).fetchall()

                # 分析查询计划（简化版）
                index_used = any("Index Scan" in str(row) for row in explain_result)

                performance = QueryPerformance(
                    query=query,
                    execution_time=execution_time,
                    rows_returned=len(result),
                    rows_examined=len(result),  # 简化处理
                    index_used=index_used,
                    timestamp=get_beijing_time_now(),
                )

                self.performance_logs.append(performance)

                return performance

        except Exception as e:
            self.logger.error(f"查询性能分析失败: {e}")
            return QueryPerformance(
                query=query,
                execution_time=0,
                rows_returned=0,
                rows_examined=0,
                index_used=False,
                timestamp=get_beijing_time_now(),
            )

    def benchmark_queries(self) -> List[QueryPerformance]:
        """基准测试查询"""
        benchmark_queries = [
            "SELECT * FROM stock_kline_daily WHERE symbol = '000001' ORDER BY trade_date DESC LIMIT 100",
            "SELECT * FROM stock_kline_daily WHERE trade_date >= '2023-01-01' AND trade_date <= '2023-12-31' LIMIT 1000",
            "SELECT symbol, AVG(close) as avg_close FROM stock_kline_daily WHERE trade_date >= '2023-01-01' GROUP BY symbol LIMIT 100",
            "SELECT * FROM fut_main_contract_kline_15min WHERE contract_code = 'IF2312' ORDER BY trade_datetime DESC LIMIT 100",
            "SELECT COUNT(*) FROM stock_basic_info WHERE market = 'SZ'",
        ]

        results = []

        self.logger.info("开始基准测试...")

        for query in benchmark_queries:
            self.logger.info(f"执行查询: {query[:50]}...")
            performance = self.analyze_query_performance(query)
            results.append(performance)

            self.logger.info(
                f"查询完成: 耗时 {performance.execution_time:.3f}秒, "
                f"返回 {performance.rows_returned} 行, "
                f"使用索引: {performance.index_used}"
            )

        return results

    def get_database_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with self.connect() as conn:
                # 获取所有表的统计信息
                tables_info = []

                # 获取表列表
                tables_result = conn.execute(
                    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'main'"
                ).fetchall()

                for table_row in tables_result:
                    table_name = table_row[0]
                    table_info = self.get_table_info(table_name)
                    if table_info["exists"]:
                        tables_info.append(table_info)

                # 获取索引信息
                indexes_result = conn.execute(
                    "SELECT * FROM duckdb_indexes()"
                ).fetchall()

                return {
                    "database_path": self.db_path,
                    "tables": tables_info,
                    "indexes": indexes_result,
                    "total_tables": len(tables_info),
                    "total_indexes": len(indexes_result),
                    "timestamp": get_beijing_time_now(),
                }

        except Exception as e:
            self.logger.error(f"获取数据库统计信息失败: {e}")
            return {"error": str(e)}

    def optimize_database(self) -> Dict[str, Any]:
        """优化数据库"""
        optimization_results = {"start_time": get_beijing_time_now(), "steps": []}

        # 步骤1: 创建索引
        self.logger.info("步骤1: 创建优化索引...")
        index_results = self.create_all_indexes()
        optimization_results["steps"].append(
            {
                "step": "create_indexes",
                "results": index_results,
                "success": all(index_results.values()),
            }
        )

        # 步骤2: 分析查询性能
        self.logger.info("步骤2: 基准测试查询性能...")
        benchmark_results = self.benchmark_queries()
        optimization_results["steps"].append(
            {
                "step": "benchmark_queries",
                "results": benchmark_results,
                "average_time": sum(p.execution_time for p in benchmark_results)
                / len(benchmark_results),
            }
        )

        # 步骤3: 收集统计信息
        self.logger.info("步骤3: 收集数据库统计信息...")
        stats = self.get_database_statistics()
        optimization_results["steps"].append(
            {"step": "collect_statistics", "results": stats}
        )

        # 步骤4: 执行数据库维护
        self.logger.info("步骤4: 执行数据库维护...")
        maintenance_results = self.run_maintenance()
        optimization_results["steps"].append(
            {"step": "run_maintenance", "results": maintenance_results}
        )

        optimization_results["end_time"] = get_beijing_time_now()
        optimization_results["total_time"] = (
            optimization_results["end_time"] - optimization_results["start_time"]
        ).total_seconds()

        return optimization_results

    def run_maintenance(self) -> Dict[str, Any]:
        """运行数据库维护"""
        try:
            with self.connect() as conn:
                maintenance_results = {}

                # 更新表统计信息
                self.logger.info("更新表统计信息...")
                conn.execute("ANALYZE")
                maintenance_results["analyze"] = True

                # 检查数据库完整性
                self.logger.info("检查数据库完整性...")
                integrity_result = conn.execute("PRAGMA integrity_check").fetchall()
                maintenance_results["integrity_check"] = integrity_result

                return maintenance_results

        except Exception as e:
            self.logger.error(f"数据库维护失败: {e}")
            return {"error": str(e)}

    def print_optimization_report(self, results: Dict[str, Any]) -> None:
        """打印优化报告"""
        print("\n" + "=" * 60)
        print("📊 数据库性能优化报告")
        print("=" * 60)

        print(f"⏱️  开始时间: {results['start_time']}")
        print(f"⏱️  结束时间: {results['end_time']}")
        print(f"⏱️  总耗时: {results['total_time']:.2f}秒")

        for step_info in results["steps"]:
            step_name = step_info["step"]

            if step_name == "create_indexes":
                print("\n🔍 索引创建结果:")
                index_results = step_info["results"]
                success_count = sum(1 for success in index_results.values() if success)
                total_count = len(index_results)
                print(f"   ✅ 成功: {success_count}/{total_count}")

                for index_name, success in index_results.items():
                    status = "✅" if success else "❌"
                    print(f"   {status} {index_name}")

            elif step_name == "benchmark_queries":
                print("\n📈 查询性能测试:")
                benchmark_results = step_info["results"]
                avg_time = step_info["average_time"]
                print(f"   ⏱️  平均查询时间: {avg_time:.3f}秒")

                for i, perf in enumerate(benchmark_results, 1):
                    index_status = "📊 使用索引" if perf.index_used else "⚠️  未使用索引"
                    print(
                        f"   查询{i}: {perf.execution_time:.3f}秒, {perf.rows_returned}行, {index_status}"
                    )

            elif step_name == "collect_statistics":
                print("\n📋 数据库统计:")
                stats = step_info["results"]
                if "error" not in stats:
                    print(f"   📊 总表数: {stats['total_tables']}")
                    print(f"   🔍 总索引数: {stats['total_indexes']}")

                    for table_info in stats["tables"][:5]:  # 显示前5个表
                        print(
                            f"   📊 {table_info['table_name']}: {table_info['row_count']} 行"
                        )

        print("\n🎉 数据库优化完成!")
        print("=" * 60)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="数据库性能优化工具")
    parser.add_argument(
        "--env", "-e", choices=["dev", "test", "prod"], default="dev", help="环境"
    )
    parser.add_argument(
        "--config", "-c", default="config/settings.toml", help="配置文件"
    )
    parser.add_argument(
        "--action",
        choices=["optimize", "benchmark", "create-indexes", "stats"],
        default="optimize",
        help="执行动作",
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.basicConfig(level=logging.INFO)

    try:
        optimizer = DatabasePerformanceOptimizer(args.config, args.env)

        if args.action == "optimize":
            results = optimizer.optimize_database()
            optimizer.print_optimization_report(results)

        elif args.action == "benchmark":
            results = optimizer.benchmark_queries()
            print(f"基准测试完成，共执行 {len(results)} 个查询")

        elif args.action == "create-indexes":
            results = optimizer.create_all_indexes()
            print(f"索引创建完成: {sum(results.values())}/{len(results)} 成功")

        elif args.action == "stats":
            stats = optimizer.get_database_statistics()
            print(
                f"数据库统计信息: {stats['total_tables']} 表, {stats['total_indexes']} 索引"
            )

    except Exception as e:
        print(f"❌ 优化失败: {e}")
        exit(1)


if __name__ == "__main__":
    main()
