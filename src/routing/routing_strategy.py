#!/usr/bin/env python3
"""
路由策略定义

功能特性：
1. 路由策略基类
2. 具体策略实现
3. 策略选择逻辑
4. 动态策略配置
"""

import pandas as pd
from typing import Dict, Any, List
from abc import ABC, abstractmethod

from ..utils.logger import get_logger


class RoutingStrategy(ABC):
    """路由策略基类"""

    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(self.__class__.__name__)

    @abstractmethod
    def make_decision(
        self,
        data: pd.DataFrame,
        data_detection: Dict[str, Any],
        quality_result: Dict[str, Any],
        source_type: str,
    ) -> Dict[str, Any]:
        """制定路由决策"""
        pass

    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        pass


class SmartDetectionStrategy(RoutingStrategy):
    """智能检测策略"""

    def __init__(self):
        super().__init__("smart_detection")

    def make_decision(
        self,
        data: pd.DataFrame,
        data_detection: Dict[str, Any],
        quality_result: Dict[str, Any],
        source_type: str,
    ) -> Dict[str, Any]:
        """制定智能路由决策"""
        return {
            "strategy_type": "smart_detection",
            "confidence": data_detection["confidence"],
            "auto_routing": True,
        }

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        return True


class PerformanceOptimizedStrategy(RoutingStrategy):
    """性能优化策略"""

    def __init__(self):
        super().__init__("performance_optimized")

    def make_decision(
        self,
        data: pd.DataFrame,
        data_detection: Dict[str, Any],
        quality_result: Dict[str, Any],
        source_type: str,
    ) -> Dict[str, Any]:
        """制定性能优化路由决策"""
        # 根据数据量选择批处理大小
        batch_size = min(1000, max(100, len(data) // 10))

        return {
            "strategy_type": "performance_optimized",
            "batch_size": batch_size,
            "parallel_processing": len(data) > 1000,
            "compression_enabled": True,
        }

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        required_keys = ["max_batch_size", "parallel_threshold"]
        return all(key in config for key in required_keys)


class ManualStrategy(RoutingStrategy):
    """手动路由策略"""

    def __init__(self):
        super().__init__("manual")

    def make_decision(
        self,
        data: pd.DataFrame,
        data_detection: Dict[str, Any],
        quality_result: Dict[str, Any],
        source_type: str,
    ) -> Dict[str, Any]:
        """制定手动路由决策"""
        return {
            "strategy_type": "manual",
            "requires_user_input": True,
            "suggested_targets": self._suggest_targets(data_detection),
            "confidence": 0.5,  # 手动策略置信度较低
        }

    def _suggest_targets(self, data_detection: Dict[str, Any]) -> List[str]:
        """建议目标表"""
        data_type = data_detection.get("data_type", "unknown")
        frequency = data_detection.get("frequency", "daily")

        suggestions = []
        if data_type == "futures":
            suggestions.append(f"futures_{frequency}")
        elif data_type == "stock":
            suggestions.append(f"stock_{frequency}")
        else:
            suggestions.append("default_table")

        return suggestions

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        return "user_interaction_timeout" in config
