"""
数据采集命令
"""
import sys
from pathlib import Path
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from ..services.collect_service import CollectService
from src.config.data_source_config import (
    DataSourceConfig, 
    DataSourceNotConfiguredError, 
    DataSourceUnavailableError,
    DataCollectionError,
    UnsupportedDataSourceError
)

console = Console()


@click.command()
@click.argument('symbols', nargs=-1)
@click.option('--source', default='tushare', 
              type=click.Choice(['tushare', 'csv', 'mysql']),
              help='数据源选择（Real Data Only支持：TUSHARE(WebAPI), CSV(本地文件), MySQL(数据库)）')
@click.option('--type', 'data_type', default='stock',
              type=click.Choice(['stock', 'futures', 'options', 'bonds']),
              help='数据类型')
@click.option('--freq', default='daily',
              type=click.Choice(['daily', 'weekly', 'monthly', '1min', '5min', 
                               '15min', '30min', '60min', 'realtime', 'tick']),
              help='数据频率')
@click.option('--start-date', help='开始日期 (YYYY-MM-DD)')
@click.option('--end-date', help='结束日期 (YYYY-MM-DD)')
@click.option('--last-days', type=int, help='最近N天')
@click.option('--last-weeks', type=int, help='最近N周')
@click.option('--last-months', type=int, help='最近N月')
@click.option('--period', type=click.Choice(['ytd', 'qtd', 'mtd', 'wtd']),
              help='预设时间周期')
@click.option('--fields', default='basic',
              type=click.Choice(['basic', 'full', 'ohlcv', 'custom']),
              help='数据字段选择')
@click.option('--preview', is_flag=True, help='预览数据概览')
@click.option('--check-capabilities', is_flag=True, help='检查数据源能力')
@click.option('--interactive', is_flag=True, help='交互式配置向导')
@click.option('--config', type=click.Path(exists=True), help='使用配置文件')
@click.option('--template', help='使用预设模板')
@click.option('--parallel', is_flag=True, help='并发采集')
@click.option('--incremental', is_flag=True, help='增量更新')
@click.option('--compress', is_flag=True, help='数据压缩')
def collect_command(symbols, source, data_type, freq, start_date, end_date,
                   last_days, last_weeks, last_months, period, fields,
                   preview, check_capabilities, interactive, config, template,
                   parallel, incremental, compress):
    """
    数据采集命令 - Real Data Only版本
    
    支持三种真实数据源：
    • TUSHARE: 实时Web API数据源 (需要配置TUSHARE_TOKEN)
    • CSV: 本地文件数据源 (支持FROMC2C格式)
    • MySQL: 数据库数据源 (支持数据迁移到DuckDB)
    
    ⚠️  重要：系统采用"Real Data Only"设计理念，100%使用真实数据源
    """
    try:
        # Real Data Only 预检查：根据数据源类型进行配置验证
        try:
            data_config = DataSourceConfig()
            if source == 'tushare':
                data_config.validate()
            elif source == 'csv':
                # CSV数据源验证：检查是否存在本地CSV文件目录
                pass  # CSV数据源暂时跳过预验证，在实际执行时检查
            elif source == 'mysql':
                # MySQL数据源验证：检查数据库连接配置
                pass  # MySQL数据源暂时跳过预验证，在实际执行时检查
        except DataSourceNotConfiguredError as e:
            console.print(Panel(
                f"[red]{e}[/red]",
                title=f"{source.upper()}数据源配置错误",
                border_style="red"
            ))
            if source == 'tushare':
                console.print("\n[yellow]TUSHARE配置指南:[/yellow]")
                console.print("1. 访问 https://tushare.pro/ 注册并获取Token")
                console.print("2. 设置环境变量: export TUSHARE_TOKEN='your_token_here'")
                console.print("3. 验证配置: python -m src.cli.main collect --check-capabilities --source tushare")
            elif source == 'csv':
                console.print("\n[yellow]CSV配置指南:[/yellow]")
                console.print("1. 确保CSV文件存在于指定目录")
                console.print("2. 检查文件格式是否符合FROMC2C标准")
                console.print("3. 验证配置: python -m src.cli.main collect --check-capabilities --source csv")
            elif source == 'mysql':
                console.print("\n[yellow]MySQL配置指南:[/yellow]")
                console.print("1. 确保MySQL数据库服务正在运行")
                console.print("2. 配置数据库连接参数")
                console.print("3. 验证配置: python -m src.cli.main collect --check-capabilities --source mysql")
            return
        
        # 初始化服务
        service = CollectService()
        
        # 检查数据源能力
        if check_capabilities:
            console.print(f"[blue]检查 {source} 数据源能力...[/blue]")
            capabilities = service.check_capabilities(source)
            
            table = Table(title=f"{source.upper()} 数据源能力")
            table.add_column("项目", style="cyan")
            table.add_column("支持情况", style="green")
            
            table.add_row("状态", capabilities['status'])
            table.add_row("支持类型", ', '.join(capabilities['supported_types']))
            table.add_row("支持频率", ', '.join(capabilities['supported_frequencies']))
            
            if 'api_limit' in capabilities:
                table.add_row("API限制", capabilities['api_limit'])
            
            table.add_row("数据源类型", capabilities.get('data_source_type', 'real_only'))
            
            console.print(table)
            return
        
        # 处理模板
        if template:
            templates = service.get_predefined_templates()
            if template in templates:
                template_config = templates[template]
                console.print(f"[blue]使用模板: {template}[/blue]")
                console.print(f"描述: {template_config['description']}")
                
                # 应用模板参数
                symbols = template_config.get('symbols', symbols)
                source = template_config.get('source', source)  
                data_type = template_config.get('type', data_type)
                freq = template_config.get('freq', freq)
                fields = template_config.get('fields', fields)
            else:
                console.print(f"[red]模板 '{template}' 不存在[/red]")
                return
        
        # 处理配置文件
        if config:
            console.print(f"[blue]加载配置文件: {config}[/blue]")
            try:
                config_data = service.load_config_file(config)
                # 应用配置文件参数
                # TODO: 实现配置文件参数应用
                console.print("[green]配置文件加载成功[/green]")
            except Exception as e:
                console.print(f"[red]配置文件加载失败: {e}[/red]")
                return
        
        # 交互式模式
        if interactive:
            console.print("[blue]启动交互式配置向导...[/blue]")
            try:
                from ..services.wizard_service import WizardService
                wizard = WizardService()
                wizard_params = wizard.run_collect_wizard()
                
                if not wizard_params:
                    return
                
                # 使用向导参数更新命令参数
                symbols_list = wizard_params.get('symbols', symbols_list)
                source = wizard_params.get('source', source)
                data_type = wizard_params.get('data_type', data_type)
                freq = wizard_params.get('freq', freq)
                start_date = wizard_params.get('start', start_date)
                end_date = wizard_params.get('end', end_date)
                preview = wizard_params.get('preview', preview)
                
                console.print("[green]使用向导配置执行数据采集...[/green]")
            except Exception as e:
                console.print(f"[red]交互式向导执行失败: {e}[/red]")
                return
        
        # 转换symbols为列表
        symbols_list = list(symbols) if symbols else []
        
        if not symbols_list and not template and not config:
            console.print("[red]请指定要采集的标的代码[/red]")
            return
        
        # 构建参数字典
        params = {
            'start_date': start_date,
            'end_date': end_date,
            'last_days': last_days,
            'last_weeks': last_weeks,
            'last_months': last_months,
            'period': period,
            'fields': fields,
            'parallel': parallel,
            'incremental': incremental,
            'compress': compress
        }
        
        # 执行数据采集
        try:
            result = service.collect_data(
                symbols=symbols_list,
                source=source,
                data_type=data_type,
                freq=freq,
                preview=preview,
                **params
            )
        except DataSourceUnavailableError as e:
            console.print(Panel(
                f"[red]{e}[/red]",
                title="数据源不可用",
                border_style="red"
            ))
            console.print("\n[yellow]故障排除建议:[/yellow]")
            console.print("1. 检查网络连接: ping tushare.pro")
            console.print("2. 验证Token: python -m src.cli.main collect --check-capabilities")
            console.print("3. 访问 https://tushare.pro/ 查看服务状态")
            return
        except UnsupportedDataSourceError as e:
            console.print(f"[red]不支持的数据源: {e}[/red]")
            console.print("[yellow]系统支持的Real Data Only数据源：TUSHARE, CSV, MySQL[/yellow]")
            return
        except DataCollectionError as e:
            console.print(f"[red]数据采集错误: {e}[/red]")
            return
        
        # 显示结果
        if result['success']:
            if preview:
                # 显示预览信息
                preview_info = result['preview']
                
                panel_content = f"""
[bold]数据预览概览 - Real Data Only[/bold]
• 目标标的: {', '.join(preview_info['symbols'])}
• 数据源: {preview_info['source'].upper()} (真实数据)
• 数据源类型: {preview_info.get('data_source_type', 'real_tushare')}
• 数据类型: {preview_info['data_type']}
• 数据频率: {preview_info['frequency']}
• 预估行数: {preview_info['estimated_rows']:,}
• 预估大小: {preview_info['estimated_size']}
• 数据字段: {', '.join(preview_info.get('fields', []))}
                """
                
                console.print(Panel(panel_content, title="数据预览", border_style="blue"))
            else:
                # 显示采集结果
                console.print(f"[green]{result['message']}[/green]")
                console.print(f"采集行数: {result['total_rows']:,}")
                console.print(f"🔒 数据源: {result.get('data_source_type', 'real_tushare').upper()} (真实数据)")
                if 'database_path' in result:
                    console.print(f"💾 保存位置: {result['database_path']}")
                
        else:
            # 显示错误信息
            if 'errors' in result:
                for error in result['errors']:
                    console.print(f"[red]错误: {error}[/red]")
        
        # 显示警告信息
        if 'warnings' in result and result['warnings']:
            console.print("\n[yellow]警告信息:[/yellow]")
            for warning in result['warnings']:
                console.print(f"[yellow]• {warning}[/yellow]")
                
    except Exception as e:
        console.print(f"[red]命令执行失败: {e}[/red]")
        # 如果是开发模式，显示详细错误
        import traceback
        console.print(f"[dim]{traceback.format_exc()}[/dim]")