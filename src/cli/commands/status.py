"""
状态查询命令
"""
import sys
from pathlib import Path
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from ..services.status_service import StatusService

console = Console()


@click.command()
@click.option('--verbose', '-v', is_flag=True, help='详细信息模式')
@click.option('--performance', '-p', is_flag=True, help='显示性能指标')
@click.option('--activities', '-a', is_flag=True, help='显示最近活动')
def status_command(verbose, performance, activities):
    """
    显示AQUA系统运行状态和统计信息
    """
    try:
        service = StatusService()
        
        # 基础组件状态
        console.print("[bold blue]AQUA系统状态概览[/bold blue]\n")
        
        components = service.get_component_status()
        status_table = Table(title="核心组件状态")
        status_table.add_column("组件", style="cyan")
        status_table.add_column("状态", style="green")
        status_table.add_column("详情", style="blue")
        
        for component in components:
            status_table.add_row(
                component['name'],
                component['status'], 
                component['details']
            )
        
        console.print(status_table)
        
        # 详细模式
        if verbose:
            console.print("\n[bold]系统信息[/bold]")
            
            # 系统信息
            sys_info = service.get_system_info()
            sys_panel = f"""
[bold]运行环境[/bold]
• 操作系统: {sys_info['platform']} {sys_info['platform_version']}
• Python版本: {sys_info['python_version']}
• 系统架构: {sys_info['architecture']}
• 主机名: {sys_info['hostname']}
• 当前时间: {sys_info['current_time']}
• 时区: {sys_info['timezone']}
            """
            
            # 项目信息
            proj_info = service.get_project_info()
            proj_panel = f"""
[bold]项目状态[/bold]
• 项目根目录: {proj_info['project_root']}
• 配置文件: {'✅' if proj_info['config_exists'] else '❌'}
• 数据库文件: {'✅' if proj_info['database_exists'] else '❌'}
• 日志目录: {'✅' if proj_info['logs_dir_exists'] else '❌'}
            """
            
            if 'database_size' in proj_info:
                proj_panel += f"• 数据库大小: {proj_info['database_size']}\n"
            if 'log_files_count' in proj_info:
                proj_panel += f"• 日志文件数: {proj_info['log_files_count']}\n"
            
            # 数据统计
            data_stats = service.get_data_statistics()
            data_panel = f"""
[bold]数据统计[/bold]
• 数据表总数: {data_stats['total_tables']}
• 记录总数: {data_stats['total_records']:,}
• 数据大小: {data_stats['data_size']}
• 最后更新: {data_stats['last_update']}
• 股票标的: {data_stats['stock_symbols']}
• 期货合约: {data_stats['futures_contracts']}
• 数据时间范围: {data_stats['date_range']}
            """
            
            # 使用列布局显示面板
            panels = [
                Panel(sys_panel.strip(), title="系统信息", border_style="green"),
                Panel(proj_panel.strip(), title="项目信息", border_style="blue"),
                Panel(data_panel.strip(), title="数据统计", border_style="yellow")
            ]
            
            console.print(Columns(panels, equal=True, expand=True))
        
        # 性能指标
        if performance:
            console.print("\n[bold]性能指标[/bold]")
            
            perf_metrics = service.get_performance_metrics()
            perf_table = Table(title="系统性能")
            perf_table.add_column("指标", style="cyan")
            perf_table.add_column("当前值", style="green")
            
            perf_table.add_row("CPU使用率", perf_metrics['cpu_usage'])
            perf_table.add_row("内存使用率", perf_metrics['memory_usage'])
            perf_table.add_row("可用内存", perf_metrics['memory_available'])
            perf_table.add_row("磁盘使用率", perf_metrics['disk_usage'])
            perf_table.add_row("可用磁盘", perf_metrics['disk_free'])
            
            console.print(perf_table)
        
        # 最近活动
        if activities:
            console.print("\n[bold]最近活动[/bold]")
            
            recent_activities = service.get_recent_activities()
            activity_table = Table(title="活动记录")
            activity_table.add_column("时间", style="cyan")
            activity_table.add_column("活动", style="green")
            activity_table.add_column("详情", style="blue")
            
            for activity in recent_activities:
                activity_table.add_row(
                    activity['time'],
                    activity['activity'],
                    activity['details']
                )
            
            console.print(activity_table)
        
    except Exception as e:
        console.print(f"[red]状态查询失败: {e}[/red]")
        import traceback
        console.print(f"[dim]{traceback.format_exc()}[/dim]")