"""
初始化命令 - 系统配置向导
"""
import sys
from pathlib import Path
import click
from rich.console import Console

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from ..services.wizard_service import WizardService
from src.utils.config_loader import ConfigLoader

console = Console()


@click.command()
@click.option('--force', '-f', is_flag=True, help='强制重新初始化')
@click.option('--minimal', '-m', is_flag=True, help='最小化配置')
@click.option('--interactive', '-i', is_flag=True, default=True, help='交互式配置向导')
def init_command(force, minimal, interactive):
    """
    初始化AQUA系统配置
    
    创建必要的目录结构，配置数据源和基本设置
    """
    try:
        console.print("[bold blue]🚀 AQUA系统初始化[/bold blue]")
        
        # 检查是否已初始化
        config_loader = ConfigLoader()
        project_root = Path(__file__).parent.parent.parent.parent
        config_file = project_root / 'config' / 'settings.toml'
        
        if config_file.exists() and not force:
            console.print("[yellow]系统已初始化。使用 --force 选项强制重新初始化[/yellow]")
            return
        
        if minimal:
            # 最小化配置模式
            console.print("[blue]使用最小化配置模式...[/blue]")
            _create_minimal_config(project_root)
        elif interactive:
            # 交互式向导模式
            console.print("[blue]启动交互式配置向导...[/blue]")
            wizard = WizardService()
            config = wizard.run_init_wizard()
            
            if config:
                _apply_configuration(project_root, config)
                console.print("[green]✅ 系统初始化完成！[/green]")
            else:
                console.print("[yellow]初始化已取消[/yellow]")
        else:
            # 默认配置模式
            console.print("[blue]使用默认配置初始化...[/blue]")
            _create_default_config(project_root)
        
        # 创建必要的目录结构
        _create_directory_structure(project_root)
        
        # 显示后续步骤
        _show_next_steps()
        
    except Exception as e:
        console.print(f"[red]初始化失败: {e}[/red]")
        import traceback
        console.print(f"[dim]{traceback.format_exc()}[/dim]")


def _create_directory_structure(project_root: Path) -> None:
    """创建目录结构"""
    console.print("[blue]创建目录结构...[/blue]")
    
    directories = [
        'data',
        'logs',
        'config',
        'templates',
        'exports',
        'backups'
    ]
    
    for dir_name in directories:
        dir_path = project_root / dir_name
        dir_path.mkdir(exist_ok=True)
        console.print(f"  ✓ {dir_name}/")
    
    # 创建数据子目录
    data_subdirs = ['raw', 'processed', 'cache']
    for subdir in data_subdirs:
        (project_root / 'data' / subdir).mkdir(exist_ok=True)
        console.print(f"  ✓ data/{subdir}/")
    
    console.print("[green]目录结构创建完成[/green]")


def _create_minimal_config(project_root: Path) -> None:
    """创建最小化配置"""
    config_content = '''[environment]
name = "development"
debug = true

[storage]
database_path = "{datacenter_dir}/aqua.duckdb"
memory_limit_gb = 2

[logging]
level = "INFO"
file = "{logs_root}/aqua.log"

[datasources.api.tushare]
enabled = false
token = "${TUSHARE_TOKEN}"
rate_limit = 200
'''
    
    config_file = project_root / 'config' / 'settings.toml'
    config_file.write_text(config_content)
    console.print("[green]最小化配置创建完成[/green]")


def _create_default_config(project_root: Path) -> None:
    """创建默认配置"""
    config_content = '''[environment]
name = "development"
debug = true
log_level = "INFO"

[storage]
database_path = "{datacenter_dir}/aqua.duckdb"
memory_limit_gb = 4
enable_compression = true

[logging]
level = "INFO"
file = "{logs_root}/aqua.log"
rotation = "10MB"
retention = 7

[performance]
max_workers = 4
batch_size = 1000
enable_caching = true

[datasources.api.tushare]
enabled = false
token = "${TUSHARE_TOKEN}"
rate_limit = 200
points_per_minute = 2000

[datasources.database.mysql]
enabled = false
host = "localhost"
port = 3306
database = "aqua"
username = "${MYSQL_USER}"
password = "${MYSQL_PASSWORD}"
'''
    
    config_file = project_root / 'config' / 'settings.toml'
    config_file.write_text(config_content)
    console.print("[green]默认配置创建完成[/green]")


def _apply_configuration(project_root: Path, config: dict) -> None:
    """应用向导配置"""
    console.print("[blue]应用配置设置...[/blue]")
    
    # 构建配置文件内容
    config_lines = [
        "[environment]",
        "name = \"development\"",
        "debug = true",
        f"log_level = \"{config['logging']['level']}\"",
        "",
        "[storage]",
        f"database_path = \"{{datacenter_dir}}/{config['storage']['database_path'].split('/')[-1]}\"",
        f"memory_limit_gb = {config['storage']['memory_limit_gb']}",
        f"enable_compression = {str(config['storage']['enable_compression']).lower()}",
        "",
        "[logging]",
        f"level = \"{config['logging']['level']}\"",
        "file = \"{logs_root}/aqua.log\"",
        f"rotation = \"{config['logging']['max_file_size']}\"",
        f"retention = {7 if config['logging']['file_rotation'] else 0}",
        "",
        "[performance]",
        f"max_workers = {config['performance']['max_workers']}",
        f"batch_size = {config['performance']['batch_size']}",
        f"enable_caching = {str(config['performance']['enable_caching']).lower()}",
        "",
        "[datasources.api.tushare]"
    ]
    
    # Tushare配置
    tushare_config = config['datasources']['tushare']
    if tushare_config.get('enabled'):
        config_lines.extend([
            "enabled = true",
            f"token = \"{tushare_config['token']}\"",
            f"rate_limit = {tushare_config.get('rate_limit', 200)}",
            f"points_per_minute = {tushare_config.get('points_per_minute', 2000)}"
        ])
    else:
        config_lines.extend([
            "enabled = false",
            "token = \"${TUSHARE_TOKEN}\"",
            "rate_limit = 200"
        ])
    
    # 写入配置文件
    config_file = project_root / 'config' / 'settings.toml'
    config_file.write_text('\n'.join(config_lines))
    
    console.print("[green]配置应用完成[/green]")


def _show_next_steps() -> None:
    """显示后续步骤"""
    console.print("\n[bold]📋 后续步骤[/bold]")
    
    steps = [
        "1. 配置环境变量 (如TUSHARE_TOKEN)",
        "2. 运行 'aqua status' 检查系统状态",
        "3. 运行 'aqua collect --help' 查看数据采集帮助",
        "4. 开始使用 'aqua collect 000001.SZ' 采集数据"
    ]
    
    for step in steps:
        console.print(f"  {step}")
    
    console.print("\n[bold green]🎉 AQUA系统初始化完成！[/bold green]")
    console.print("运行 'aqua --help' 查看所有可用命令")