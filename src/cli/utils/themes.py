"""
主题和颜色管理工具
"""
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from rich.console import Console
from rich.theme import Theme
from rich.table import Table
from rich.panel import Panel


class ThemeType(Enum):
    """主题类型枚举"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


@dataclass
class ColorScheme:
    """颜色方案"""
    primary: str = "blue"
    secondary: str = "cyan"
    success: str = "green"
    warning: str = "yellow"
    error: str = "red"
    info: str = "blue"
    muted: str = "dim"
    accent: str = "magenta"


class ThemeManager:
    """主题管理器"""
    
    def __init__(self, console: Optional[Console] = None):
        """初始化主题管理器
        
        Args:
            console: Rich控制台实例
        """
        self.console = console or Console()
        self.current_theme = ThemeType.AUTO
        self.current_scheme = ColorScheme()
        self._themes = self._load_predefined_themes()
    
    def _load_predefined_themes(self) -> Dict[str, Dict[str, Any]]:
        """加载预定义主题"""
        return {
            "default": {
                "name": "AQUA默认主题",
                "description": "经典蓝色主调，适合量化分析",
                "colors": ColorScheme(
                    primary="blue",
                    secondary="cyan", 
                    success="green",
                    warning="yellow",
                    error="red",
                    info="blue",
                    muted="dim",
                    accent="magenta"
                ),
                "styles": {
                    "title": "bold blue",
                    "subtitle": "cyan",
                    "success": "bold green",
                    "warning": "bold yellow",
                    "error": "bold red",
                    "info": "blue",
                    "data": "white",
                    "header": "bold cyan",
                    "border": "blue"
                }
            },
            "professional": {
                "name": "专业商务主题",
                "description": "简洁商务风格，适合正式场合",
                "colors": ColorScheme(
                    primary="bright_blue",
                    secondary="blue",
                    success="bright_green", 
                    warning="bright_yellow",
                    error="bright_red",
                    info="bright_blue",
                    muted="dim",
                    accent="bright_magenta"
                ),
                "styles": {
                    "title": "bold bright_blue",
                    "subtitle": "blue",
                    "success": "bold bright_green",
                    "warning": "bold bright_yellow", 
                    "error": "bold bright_red",
                    "info": "bright_blue",
                    "data": "bright_white",
                    "header": "bold blue",
                    "border": "bright_blue"
                }
            },
            "dark": {
                "name": "深色主题",
                "description": "深色背景，保护视力",
                "colors": ColorScheme(
                    primary="bright_cyan",
                    secondary="cyan",
                    success="bright_green",
                    warning="bright_yellow",
                    error="bright_red", 
                    info="bright_cyan",
                    muted="dim white",
                    accent="bright_magenta"
                ),
                "styles": {
                    "title": "bold bright_cyan",
                    "subtitle": "cyan",
                    "success": "bold bright_green",
                    "warning": "bold bright_yellow",
                    "error": "bold bright_red",
                    "info": "bright_cyan",
                    "data": "bright_white",
                    "header": "bold cyan",
                    "border": "bright_cyan"
                }
            },
            "minimal": {
                "name": "极简主题",
                "description": "黑白简约风格，专注内容",
                "colors": ColorScheme(
                    primary="white",
                    secondary="bright_white",
                    success="bright_white",
                    warning="bright_white",
                    error="bright_white",
                    info="white",
                    muted="dim",
                    accent="bright_white"
                ),
                "styles": {
                    "title": "bold white",
                    "subtitle": "bright_white",
                    "success": "bold bright_white",
                    "warning": "bold dim", 
                    "error": "bold white",
                    "info": "white",
                    "data": "bright_white",
                    "header": "bold white",
                    "border": "white"
                }
            },
            "colorful": {
                "name": "多彩主题",
                "description": "丰富色彩，生动活泼",
                "colors": ColorScheme(
                    primary="bright_blue",
                    secondary="bright_cyan",
                    success="bright_green",
                    warning="bright_yellow",
                    error="bright_red",
                    info="bright_blue",
                    muted="dim",
                    accent="bright_magenta"
                ),
                "styles": {
                    "title": "bold bright_blue on bright_white",
                    "subtitle": "bright_cyan",
                    "success": "bold bright_green",
                    "warning": "bold bright_yellow on black",
                    "error": "bold bright_red on bright_white",
                    "info": "bright_blue",
                    "data": "bright_white",
                    "header": "bold bright_cyan",
                    "border": "bright_magenta"
                }
            }
        }
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表
        
        Returns:
            Dict: 主题ID -> 主题名称的映射
        """
        return {
            theme_id: theme_info["name"] 
            for theme_id, theme_info in self._themes.items()
        }
    
    def get_theme_info(self, theme_id: str) -> Optional[Dict[str, Any]]:
        """获取主题详细信息
        
        Args:
            theme_id: 主题ID
            
        Returns:
            Dict: 主题信息，如果不存在则返回None
        """
        return self._themes.get(theme_id)
    
    def apply_theme(self, theme_id: str) -> bool:
        """应用主题
        
        Args:
            theme_id: 主题ID
            
        Returns:
            bool: 应用是否成功
        """
        if theme_id not in self._themes:
            return False
        
        theme_info = self._themes[theme_id]
        self.current_scheme = theme_info["colors"]
        
        # 创建Rich主题
        rich_theme = Theme(theme_info["styles"])
        
        # 应用到控制台
        self.console = Console(theme=rich_theme)
        
        return True
    
    def preview_theme(self, theme_id: str) -> None:
        """预览主题效果
        
        Args:
            theme_id: 主题ID
        """
        if theme_id not in self._themes:
            self.console.print(f"[red]主题 '{theme_id}' 不存在[/red]")
            return
        
        theme_info = self._themes[theme_id]
        
        # 创建预览控制台
        preview_console = Console(theme=Theme(theme_info["styles"]))
        
        # 显示主题信息
        preview_console.print(f"\n[title]🎨 {theme_info['name']} 预览[/title]")
        preview_console.print(f"[subtitle]{theme_info['description']}[/subtitle]\n")
        
        # 样式演示表格
        demo_table = Table(title="样式演示", show_header=True)
        demo_table.add_column("样式类型", style="header")
        demo_table.add_column("显示效果", style="data")
        
        demo_table.add_row("标题", "[title]这是标题文字[/title]")
        demo_table.add_row("副标题", "[subtitle]这是副标题文字[/subtitle]")
        demo_table.add_row("成功信息", "[success]✅ 操作成功完成[/success]")
        demo_table.add_row("警告信息", "[warning]⚠️ 请注意此操作[/warning]")
        demo_table.add_row("错误信息", "[error]❌ 操作执行失败[/error]")
        demo_table.add_row("普通信息", "[info]ℹ️ 这是提示信息[/info]")
        demo_table.add_row("数据内容", "[data]股票代码: 000001.SZ[/data]")
        
        preview_console.print(demo_table)
        
        # 颜色方案展示
        colors = theme_info["colors"]
        color_panel = f"""
[{colors.primary}]主色调[/{colors.primary}] • [{colors.secondary}]次要色[/{colors.secondary}] • [{colors.success}]成功色[/{colors.success}] • 
[{colors.warning}]警告色[/{colors.warning}] • [{colors.error}]错误色[/{colors.error}] • [{colors.info}]信息色[/{colors.info}] • 
[{colors.muted}]静音色[/{colors.muted}] • [{colors.accent}]强调色[/{colors.accent}]
        """
        
        preview_console.print(Panel(
            color_panel.strip(),
            title="颜色方案",
            border_style="border"
        ))
    
    def show_all_themes(self) -> None:
        """显示所有可用主题"""
        self.console.print("[bold blue]🎨 AQUA CLI 主题库[/bold blue]\n")
        
        for theme_id, theme_info in self._themes.items():
            # 创建主题预览控制台
            theme_console = Console(theme=Theme(theme_info["styles"]))
            
            # 主题卡片
            card_content = f"""
[title]{theme_info['name']}[/title]
[subtitle]{theme_info['description']}[/subtitle]

[success]✓ 成功[/success] [warning]⚠ 警告[/warning] [error]✗ 错误[/error] [info]ℹ 信息[/info]
            """
            
            theme_console.print(Panel(
                card_content.strip(),
                title=f"主题: {theme_id}",
                border_style="border",
                width=60
            ))
            
            print()  # 空行分隔
    
    def save_theme_config(self, theme_id: str, config_path: Optional[Path] = None) -> bool:
        """保存主题配置到文件
        
        Args:
            theme_id: 主题ID
            config_path: 配置文件路径
            
        Returns:
            bool: 保存是否成功
        """
        if theme_id not in self._themes:
            return False
        
        if config_path is None:
            config_path = Path.home() / '.aqua' / 'theme.json'
        
        try:
            import json
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            config = {
                "current_theme": theme_id,
                "theme_info": self._themes[theme_id]
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception:
            return False
    
    def load_theme_config(self, config_path: Optional[Path] = None) -> bool:
        """从文件加载主题配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 加载是否成功
        """
        if config_path is None:
            config_path = Path.home() / '.aqua' / 'theme.json'
        
        if not config_path.exists():
            return False
        
        try:
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            theme_id = config.get("current_theme")
            if theme_id and theme_id in self._themes:
                return self.apply_theme(theme_id)
            
            return False
        except Exception:
            return False
    
    def create_custom_theme(self, theme_id: str, name: str, description: str,
                          colors: ColorScheme, styles: Dict[str, str]) -> bool:
        """创建自定义主题
        
        Args:
            theme_id: 主题ID
            name: 主题名称
            description: 主题描述
            colors: 颜色方案
            styles: 样式定义
            
        Returns:
            bool: 创建是否成功
        """
        try:
            self._themes[theme_id] = {
                "name": name,
                "description": description,
                "colors": colors,
                "styles": styles,
                "custom": True
            }
            return True
        except Exception:
            return False