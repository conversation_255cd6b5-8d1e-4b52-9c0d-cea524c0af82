"""
颜色工具和常量
"""
from typing import Dict, List, Optional
from enum import Enum


class ColorPalette(Enum):
    """颜色调色板枚举"""
    # 基础颜色
    BLACK = "black"
    RED = "red"
    GREEN = "green"
    YELLOW = "yellow"
    BLUE = "blue"
    MAGENTA = "magenta"
    CYAN = "cyan"
    WHITE = "white"
    
    # 亮色版本
    BRIGHT_BLACK = "bright_black"
    BRIGHT_RED = "bright_red"
    BRIGHT_GREEN = "bright_green"
    BRIGHT_YELLOW = "bright_yellow"
    BRIGHT_BLUE = "bright_blue"
    BRIGHT_MAGENTA = "bright_magenta"
    BRIGHT_CYAN = "bright_cyan"
    BRIGHT_WHITE = "bright_white"
    
    # 特殊颜色
    DIM = "dim"
    BOLD = "bold"
    ITALIC = "italic"
    UNDERLINE = "underline"


class ColorScheme:
    """颜色方案类"""
    
    # AQUA默认配色方案
    DEFAULT = {
        "primary": ColorPalette.BLUE.value,
        "secondary": ColorPalette.CYAN.value,
        "success": ColorPalette.GREEN.value,
        "warning": ColorPalette.YELLOW.value,
        "error": ColorPalette.RED.value,
        "info": ColorPalette.BLUE.value,
        "muted": ColorPalette.DIM.value,
        "accent": ColorPalette.MAGENTA.value
    }
    
    # 专业商务配色
    PROFESSIONAL = {
        "primary": ColorPalette.BRIGHT_BLUE.value,
        "secondary": ColorPalette.BLUE.value,
        "success": ColorPalette.BRIGHT_GREEN.value,
        "warning": ColorPalette.BRIGHT_YELLOW.value,
        "error": ColorPalette.BRIGHT_RED.value,
        "info": ColorPalette.BRIGHT_BLUE.value,
        "muted": ColorPalette.DIM.value,
        "accent": ColorPalette.BRIGHT_MAGENTA.value
    }
    
    # 深色主题配色
    DARK = {
        "primary": ColorPalette.BRIGHT_CYAN.value,
        "secondary": ColorPalette.CYAN.value,
        "success": ColorPalette.BRIGHT_GREEN.value,
        "warning": ColorPalette.BRIGHT_YELLOW.value,
        "error": ColorPalette.BRIGHT_RED.value,
        "info": ColorPalette.BRIGHT_CYAN.value,
        "muted": "dim white",
        "accent": ColorPalette.BRIGHT_MAGENTA.value
    }
    
    # 极简配色
    MINIMAL = {
        "primary": ColorPalette.WHITE.value,
        "secondary": ColorPalette.BRIGHT_WHITE.value,
        "success": ColorPalette.BRIGHT_WHITE.value,
        "warning": ColorPalette.BRIGHT_WHITE.value,
        "error": ColorPalette.BRIGHT_WHITE.value,
        "info": ColorPalette.WHITE.value,
        "muted": ColorPalette.DIM.value,
        "accent": ColorPalette.BRIGHT_WHITE.value
    }
    
    # 多彩配色
    COLORFUL = {
        "primary": ColorPalette.BRIGHT_BLUE.value,
        "secondary": ColorPalette.BRIGHT_CYAN.value,
        "success": ColorPalette.BRIGHT_GREEN.value,
        "warning": ColorPalette.BRIGHT_YELLOW.value,
        "error": ColorPalette.BRIGHT_RED.value,
        "info": ColorPalette.BRIGHT_BLUE.value,
        "muted": ColorPalette.DIM.value,
        "accent": ColorPalette.BRIGHT_MAGENTA.value
    }


class ColorConstants:
    """颜色常量类"""
    
    # 状态颜色
    STATUS_COLORS = {
        "success": ColorPalette.GREEN.value,
        "warning": ColorPalette.YELLOW.value,
        "error": ColorPalette.RED.value,
        "info": ColorPalette.BLUE.value,
        "pending": ColorPalette.CYAN.value,
        "running": ColorPalette.BRIGHT_BLUE.value,
        "completed": ColorPalette.BRIGHT_GREEN.value,
        "failed": ColorPalette.BRIGHT_RED.value,
        "cancelled": ColorPalette.BRIGHT_BLACK.value
    }
    
    # 数据类型颜色
    DATA_TYPE_COLORS = {
        "stock": ColorPalette.BLUE.value,
        "futures": ColorPalette.CYAN.value,
        "options": ColorPalette.MAGENTA.value,
        "bonds": ColorPalette.GREEN.value,
        "index": ColorPalette.YELLOW.value,
        "crypto": ColorPalette.BRIGHT_MAGENTA.value
    }
    
    # 组件颜色
    COMPONENT_COLORS = {
        "header": ColorPalette.BOLD.value + " " + ColorPalette.CYAN.value,
        "title": ColorPalette.BOLD.value + " " + ColorPalette.BLUE.value,
        "subtitle": ColorPalette.CYAN.value,
        "data": ColorPalette.WHITE.value,
        "border": ColorPalette.BLUE.value,
        "timestamp": ColorPalette.DIM.value,
        "number": ColorPalette.BRIGHT_WHITE.value,
        "string": ColorPalette.GREEN.value,
        "boolean": ColorPalette.YELLOW.value,
        "null": ColorPalette.DIM.value
    }


def get_color_for_status(status: str) -> str:
    """根据状态获取对应颜色
    
    Args:
        status: 状态字符串
        
    Returns:
        str: 颜色代码
    """
    return ColorConstants.STATUS_COLORS.get(status.lower(), ColorPalette.WHITE.value)


def get_color_for_data_type(data_type: str) -> str:
    """根据数据类型获取对应颜色
    
    Args:
        data_type: 数据类型字符串
        
    Returns:
        str: 颜色代码
    """
    return ColorConstants.DATA_TYPE_COLORS.get(data_type.lower(), ColorPalette.BLUE.value)


def format_with_color(text: str, color: str, style: Optional[str] = None) -> str:
    """使用颜色格式化文本
    
    Args:
        text: 要格式化的文本
        color: 颜色代码
        style: 可选的样式 (bold, italic, underline, dim)
        
    Returns:
        str: 格式化后的文本
    """
    if style:
        full_style = f"{style} {color}"
    else:
        full_style = color
    
    return f"[{full_style}]{text}[/{full_style}]"


def create_gradient_text(text: str, start_color: str, end_color: str) -> str:
    """创建渐变色文本 (简化版本)
    
    Args:
        text: 要渐变的文本
        start_color: 起始颜色
        end_color: 结束颜色
        
    Returns:
        str: 渐变色文本
    """
    # 简化实现：仅支持两种颜色的切换
    mid_point = len(text) // 2
    
    first_half = f"[{start_color}]{text[:mid_point]}[/{start_color}]"
    second_half = f"[{end_color}]{text[mid_point:]}[/{end_color}]"
    
    return first_half + second_half


def get_contrast_color(background_color: str) -> str:
    """根据背景色获取对比度最高的前景色
    
    Args:
        background_color: 背景颜色
        
    Returns:
        str: 对比色
    """
    # 简化实现：基于颜色名称判断
    dark_colors = [
        ColorPalette.BLACK.value,
        ColorPalette.BLUE.value,
        ColorPalette.RED.value,
        ColorPalette.GREEN.value,
        ColorPalette.MAGENTA.value
    ]
    
    if background_color in dark_colors:
        return ColorPalette.BRIGHT_WHITE.value
    else:
        return ColorPalette.BLACK.value


def validate_color(color: str) -> bool:
    """验证颜色是否有效
    
    Args:
        color: 颜色字符串
        
    Returns:
        bool: 是否为有效颜色
    """
    # 检查是否为枚举中的颜色
    try:
        return color in [c.value for c in ColorPalette]
    except:
        return False


def get_available_colors() -> List[str]:
    """获取所有可用颜色列表
    
    Returns:
        List[str]: 颜色列表
    """
    return [color.value for color in ColorPalette]


def get_color_schemes() -> Dict[str, Dict[str, str]]:
    """获取所有预定义配色方案
    
    Returns:
        Dict[str, Dict[str, str]]: 配色方案字典
    """
    return {
        "default": ColorScheme.DEFAULT,
        "professional": ColorScheme.PROFESSIONAL,
        "dark": ColorScheme.DARK,
        "minimal": ColorScheme.MINIMAL,
        "colorful": ColorScheme.COLORFUL
    }


def create_color_demo() -> str:
    """创建颜色演示文本
    
    Returns:
        str: 演示文本
    """
    demo_lines = []
    
    # 基础颜色演示
    demo_lines.append("[bold]基础颜色演示:[/bold]")
    basic_colors = [ColorPalette.RED, ColorPalette.GREEN, ColorPalette.BLUE, 
                   ColorPalette.YELLOW, ColorPalette.MAGENTA, ColorPalette.CYAN]
    
    for color in basic_colors:
        demo_lines.append(f"[{color.value}]● {color.name}[/{color.value}]")
    
    # 亮色演示
    demo_lines.append("\n[bold]亮色演示:[/bold]")
    bright_colors = [ColorPalette.BRIGHT_RED, ColorPalette.BRIGHT_GREEN, 
                    ColorPalette.BRIGHT_BLUE, ColorPalette.BRIGHT_YELLOW, 
                    ColorPalette.BRIGHT_MAGENTA, ColorPalette.BRIGHT_CYAN]
    
    for color in bright_colors:
        demo_lines.append(f"[{color.value}]● {color.name}[/{color.value}]")
    
    # 样式演示
    demo_lines.append("\n[bold]样式演示:[/bold]")
    demo_lines.append(f"[bold]粗体文本[/bold]")
    demo_lines.append(f"[italic]斜体文本[/italic]")
    demo_lines.append(f"[underline]下划线文本[/underline]")
    demo_lines.append(f"[dim]暗淡文本[/dim]")
    
    return "\n".join(demo_lines)