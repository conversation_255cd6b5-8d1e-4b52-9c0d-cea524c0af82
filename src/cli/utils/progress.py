"""
进度显示工具
"""
import time
from typing import Optional, Any, Dict, List
from threading import Thread, Event
from dataclasses import dataclass
from enum import Enum

from rich.console import Console
from rich.progress import (
    Progress, 
    TaskID, 
    SpinnerColumn, 
    TextColumn, 
    BarColumn, 
    TaskProgressColumn,
    TimeRemainingColumn,
    TimeElapsedColumn
)
from rich.live import Live
from rich.table import Table
from rich.panel import Panel


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskInfo:
    """任务信息"""
    name: str
    total: int = 100
    completed: int = 0
    status: TaskStatus = TaskStatus.PENDING
    description: str = ""
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error: Optional[str] = None


class ProgressManager:
    """进度管理器"""
    
    def __init__(self, console: Optional[Console] = None):
        """初始化进度管理器
        
        Args:
            console: Rich控制台实例
        """
        self.console = console or Console()
        self.tasks: Dict[str, TaskInfo] = {}
        self._progress = None
        self._task_ids: Dict[str, TaskID] = {}
        self._live = None
        self._update_thread = None
        self._stop_event = Event()
    
    def create_progress(self, show_spinner: bool = True, show_time: bool = True) -> Progress:
        """创建进度条
        
        Args:
            show_spinner: 是否显示旋转器
            show_time: 是否显示时间信息
            
        Returns:
            Progress: 进度条实例
        """
        columns = []
        
        if show_spinner:
            columns.append(SpinnerColumn())
        
        columns.extend([
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
        ])
        
        if show_time:
            columns.extend([
                TimeElapsedColumn(),
                TimeRemainingColumn()
            ])
        
        return Progress(*columns, console=self.console)
    
    def start_tracking(self) -> None:
        """开始进度跟踪"""
        if self._progress is None:
            self._progress = self.create_progress()
        
        self._live = Live(self._get_display(), console=self.console, refresh_per_second=4)
        self._live.start()
        
        # 启动更新线程
        self._stop_event.clear()
        self._update_thread = Thread(target=self._update_loop, daemon=True)
        self._update_thread.start()
    
    def stop_tracking(self) -> None:
        """停止进度跟踪"""
        if self._live:
            self._stop_event.set()
            if self._update_thread:
                self._update_thread.join(timeout=1.0)
            
            self._live.stop()
            self._live = None
        
        self._progress = None
        self._task_ids.clear()
    
    def add_task(self, task_id: str, name: str, total: int = 100, 
                 description: str = "") -> None:
        """添加任务
        
        Args:
            task_id: 任务ID
            name: 任务名称
            total: 总进度
            description: 任务描述
        """
        task_info = TaskInfo(
            name=name,
            total=total,
            description=description or name
        )
        self.tasks[task_id] = task_info
        
        if self._progress:
            progress_task_id = self._progress.add_task(
                description=task_info.description,
                total=total
            )
            self._task_ids[task_id] = progress_task_id
    
    def update_task(self, task_id: str, advance: int = 1, 
                   description: Optional[str] = None) -> None:
        """更新任务进度
        
        Args:
            task_id: 任务ID
            advance: 进度增量
            description: 新的任务描述
        """
        if task_id not in self.tasks:
            return
        
        task_info = self.tasks[task_id]
        task_info.completed = min(task_info.completed + advance, task_info.total)
        
        if description:
            task_info.description = description
        
        # 更新Rich进度条
        if task_id in self._task_ids and self._progress:
            self._progress.update(
                self._task_ids[task_id],
                advance=advance,
                description=task_info.description
            )
    
    def complete_task(self, task_id: str, success: bool = True, 
                     error: Optional[str] = None) -> None:
        """完成任务
        
        Args:
            task_id: 任务ID
            success: 是否成功
            error: 错误信息
        """
        if task_id not in self.tasks:
            return
        
        task_info = self.tasks[task_id]
        task_info.end_time = time.time()
        task_info.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        task_info.error = error
        
        if success:
            task_info.completed = task_info.total
        
        # 更新Rich进度条
        if task_id in self._task_ids and self._progress:
            if success:
                self._progress.update(
                    self._task_ids[task_id],
                    completed=task_info.total,
                    description=f"✅ {task_info.name}"
                )
            else:
                self._progress.update(
                    self._task_ids[task_id],
                    description=f"❌ {task_info.name} (失败)"
                )
    
    def start_task(self, task_id: str) -> None:
        """开始任务
        
        Args:
            task_id: 任务ID
        """
        if task_id not in self.tasks:
            return
        
        task_info = self.tasks[task_id]
        task_info.start_time = time.time()
        task_info.status = TaskStatus.RUNNING
        
        # 更新Rich进度条
        if task_id in self._task_ids and self._progress:
            self._progress.update(
                self._task_ids[task_id],
                description=f"🔄 {task_info.name}"
            )
    
    def get_task_summary(self) -> Dict[str, Any]:
        """获取任务摘要
        
        Returns:
            Dict: 任务摘要信息
        """
        total_tasks = len(self.tasks)
        completed_tasks = sum(1 for task in self.tasks.values() 
                            if task.status == TaskStatus.COMPLETED)
        failed_tasks = sum(1 for task in self.tasks.values()
                         if task.status == TaskStatus.FAILED)
        running_tasks = sum(1 for task in self.tasks.values()
                          if task.status == TaskStatus.RUNNING)
        
        return {
            'total': total_tasks,
            'completed': completed_tasks,
            'failed': failed_tasks,
            'running': running_tasks,
            'pending': total_tasks - completed_tasks - failed_tasks - running_tasks
        }
    
    def _get_display(self) -> Any:
        """获取显示内容"""
        if not self._progress:
            return ""
        
        # 创建任务摘要表格
        summary = self.get_task_summary()
        summary_table = Table(title="任务概览", show_header=False)
        summary_table.add_column("指标", style="cyan")
        summary_table.add_column("数量", style="green")
        
        summary_table.add_row("总任务数", str(summary['total']))
        summary_table.add_row("已完成", str(summary['completed']))
        summary_table.add_row("进行中", str(summary['running']))
        summary_table.add_row("待执行", str(summary['pending']))
        if summary['failed'] > 0:
            summary_table.add_row("失败", str(summary['failed']))
        
        # 组合显示内容
        return Panel.fit(
            self._progress,
            title="任务执行进度",
            border_style="blue"
        )
    
    def _update_loop(self) -> None:
        """更新循环"""
        while not self._stop_event.is_set():
            if self._live:
                self._live.update(self._get_display())
            time.sleep(0.25)


class TaskProgress:
    """单任务进度"""
    
    def __init__(self, name: str, total: int = 100, console: Optional[Console] = None):
        """初始化单任务进度
        
        Args:
            name: 任务名称
            total: 总进度
            console: Rich控制台实例
        """
        self.name = name
        self.total = total
        self.console = console or Console()
        self.completed = 0
        self.status = TaskStatus.PENDING
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        
        # 创建单任务进度条
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TimeElapsedColumn(),
            console=self.console
        )
        self.task_id = None
    
    def __enter__(self):
        """进入上下文管理器"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        if exc_type is None:
            self.complete()
        else:
            self.fail(str(exc_val) if exc_val else "未知错误")
        self.stop()
    
    def start(self) -> None:
        """开始任务"""
        self.start_time = time.time()
        self.status = TaskStatus.RUNNING
        
        self.progress.start()
        self.task_id = self.progress.add_task(
            description=f"🔄 {self.name}",
            total=self.total
        )
    
    def update(self, advance: int = 1, description: Optional[str] = None) -> None:
        """更新进度
        
        Args:
            advance: 进度增量
            description: 新描述
        """
        if self.task_id is None:
            return
        
        self.completed = min(self.completed + advance, self.total)
        
        self.progress.update(
            self.task_id,
            advance=advance,
            description=description or f"🔄 {self.name}"
        )
    
    def complete(self) -> None:
        """完成任务"""
        if self.task_id is None:
            return
        
        self.end_time = time.time()
        self.status = TaskStatus.COMPLETED
        self.completed = self.total
        
        self.progress.update(
            self.task_id,
            completed=self.total,
            description=f"✅ {self.name} (完成)"
        )
    
    def fail(self, error: str) -> None:
        """任务失败
        
        Args:
            error: 错误信息
        """
        if self.task_id is None:
            return
        
        self.end_time = time.time()
        self.status = TaskStatus.FAILED
        
        self.progress.update(
            self.task_id,
            description=f"❌ {self.name} (失败: {error})"
        )
    
    def stop(self) -> None:
        """停止进度显示"""
        if self.progress:
            self.progress.stop()


class MultiTaskProgress:
    """多任务进度管理器"""
    
    def __init__(self, console: Optional[Console] = None):
        """初始化多任务进度管理器
        
        Args:
            console: Rich控制台实例
        """
        self.console = console or Console()
        self.manager = ProgressManager(console=self.console)
    
    def __enter__(self):
        """进入上下文管理器"""
        self.manager.start_tracking()
        return self.manager
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.manager.stop_tracking()