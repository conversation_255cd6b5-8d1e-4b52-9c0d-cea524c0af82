"""
Shell补全功能工具
"""
import os
import click
from pathlib import Path
from typing import List, Optional


class CompletionManager:
    """补全管理器"""
    
    def __init__(self):
        """初始化补全管理器"""
        self.supported_shells = ['bash', 'zsh', 'fish']
    
    def setup_completion(self, shell: str = None) -> bool:
        """设置shell补全
        
        Args:
            shell: shell类型，如果为None则自动检测
            
        Returns:
            bool: 设置是否成功
        """
        if shell is None:
            shell = self._detect_shell()
        
        if shell not in self.supported_shells:
            return False
            
        return self._install_completion(shell)
    
    def _detect_shell(self) -> str:
        """自动检测当前shell"""
        shell_env = os.environ.get('SHELL', '')
        
        if 'zsh' in shell_env:
            return 'zsh'
        elif 'fish' in shell_env:
            return 'fish'
        else:
            return 'bash'
    
    def _install_completion(self, shell: str) -> bool:
        """安装补全脚本
        
        Args:
            shell: shell类型
            
        Returns:
            bool: 安装是否成功
        """
        try:
            if shell == 'bash':
                return self._install_bash_completion()
            elif shell == 'zsh':
                return self._install_zsh_completion()
            elif shell == 'fish':
                return self._install_fish_completion()
            return False
        except Exception:
            return False
    
    def _install_bash_completion(self) -> bool:
        """安装bash补全"""
        # 生成bash补全脚本
        completion_script = self._generate_bash_completion()
        
        # 获取bash补全目录
        completion_dirs = [
            Path.home() / '.bash_completion.d',
            Path('/usr/local/etc/bash_completion.d'),
            Path('/etc/bash_completion.d')
        ]
        
        # 找到可写的补全目录
        for comp_dir in completion_dirs:
            if comp_dir.exists() and os.access(comp_dir, os.W_OK):
                comp_file = comp_dir / 'aqua'
                try:
                    comp_file.write_text(completion_script)
                    return True
                except Exception:
                    continue
        
        # 如果没有系统补全目录，创建用户补全文件
        user_completion = Path.home() / '.bash_completion'
        try:
            if not user_completion.exists():
                user_completion.write_text(completion_script)
            else:
                content = user_completion.read_text()
                if 'aqua' not in content:
                    user_completion.write_text(content + '\n' + completion_script)
            return True
        except Exception:
            return False
    
    def _install_zsh_completion(self) -> bool:
        """安装zsh补全"""
        completion_script = self._generate_zsh_completion()
        
        # 获取zsh补全目录
        fpath_dirs = os.environ.get('fpath', '').split(':')
        zsh_dirs = [
            Path.home() / '.zsh' / 'completions',
            Path('/usr/local/share/zsh/site-functions'),
            Path('/usr/share/zsh/site-functions')
        ]
        
        # 添加fpath中的目录
        for fpath_dir in fpath_dirs:
            if fpath_dir:
                zsh_dirs.append(Path(fpath_dir))
        
        # 找到可写的补全目录
        for comp_dir in zsh_dirs:
            if comp_dir.exists() and os.access(comp_dir, os.W_OK):
                comp_file = comp_dir / '_aqua'
                try:
                    comp_file.write_text(completion_script)
                    return True
                except Exception:
                    continue
        
        # 创建用户补全目录
        user_comp_dir = Path.home() / '.zsh' / 'completions'
        try:
            user_comp_dir.mkdir(parents=True, exist_ok=True)
            comp_file = user_comp_dir / '_aqua'
            comp_file.write_text(completion_script)
            return True
        except Exception:
            return False
    
    def _install_fish_completion(self) -> bool:
        """安装fish补全"""
        completion_script = self._generate_fish_completion()
        
        # fish补全目录
        fish_dirs = [
            Path.home() / '.config' / 'fish' / 'completions',
            Path('/usr/local/share/fish/completions'),
            Path('/usr/share/fish/completions')
        ]
        
        # 找到可写的补全目录
        for comp_dir in fish_dirs:
            if comp_dir.exists() and os.access(comp_dir, os.W_OK):
                comp_file = comp_dir / 'aqua.fish'
                try:
                    comp_file.write_text(completion_script)
                    return True
                except Exception:
                    continue
        
        # 创建用户补全目录
        user_comp_dir = Path.home() / '.config' / 'fish' / 'completions'
        try:
            user_comp_dir.mkdir(parents=True, exist_ok=True)
            comp_file = user_comp_dir / 'aqua.fish'
            comp_file.write_text(completion_script)
            return True
        except Exception:
            return False


def generate_bash_completion() -> str:
    """生成bash补全脚本"""
    return '''
_aqua_completion() {
    local cur prev opts
    COMPREPLY=()
    cur="${COMP_WORDS[COMP_CWORD]}"
    prev="${COMP_WORDS[COMP_CWORD-1]}"
    
    # 主命令补全
    if [[ ${COMP_CWORD} == 1 ]]; then
        opts="collect status init config --help --version"
        COMPREPLY=( $(compgen -W "${opts}" -- ${cur}) )
        return 0
    fi
    
    # collect命令补全
    if [[ ${COMP_WORDS[1]} == "collect" ]]; then
        case "${prev}" in
            --source)
                COMPREPLY=( $(compgen -W "tushare mysql csv api" -- ${cur}) )
                return 0
                ;;
            --data-type)
                COMPREPLY=( $(compgen -W "daily kline bar tick fundamental" -- ${cur}) )
                return 0
                ;;
            --freq)
                COMPREPLY=( $(compgen -W "1min 5min 15min 30min 1h 4h daily weekly monthly" -- ${cur}) )
                return 0
                ;;
            --format)
                COMPREPLY=( $(compgen -W "json csv parquet" -- ${cur}) )
                return 0
                ;;
            *)
                opts="--source --data-type --freq --start --end --output --format --preview --template --interactive --help"
                COMPREPLY=( $(compgen -W "${opts}" -- ${cur}) )
                return 0
                ;;
        esac
    fi
    
    # status命令补全
    if [[ ${COMP_WORDS[1]} == "status" ]]; then
        opts="--verbose --performance --activities --help"
        COMPREPLY=( $(compgen -W "${opts}" -- ${cur}) )
        return 0
    fi
    
    # config命令补全
    if [[ ${COMP_WORDS[1]} == "config" ]]; then
        opts="--show --edit --validate --reset --help"
        COMPREPLY=( $(compgen -W "${opts}" -- ${cur}) )
        return 0
    fi
}

complete -F _aqua_completion aqua
'''


def generate_zsh_completion() -> str:
    """生成zsh补全脚本"""
    return '''
#compdef _aqua aqua

_aqua() {
    local context state line
    
    _arguments -C \
        '1: :_aqua_commands' \
        '*::arg:->args'
    
    case $state in
        args)
            case $words[1] in
                collect)
                    _arguments \
                        '--source[数据源]:source:(tushare mysql csv api)' \
                        '--data-type[数据类型]:type:(daily kline bar tick fundamental)' \
                        '--freq[数据频率]:freq:(1min 5min 15min 30min 1h 4h daily weekly monthly)' \
                        '--start[开始日期]:start:' \
                        '--end[结束日期]:end:' \
                        '--output[输出目录]:output:_files -/' \
                        '--format[输出格式]:format:(json csv parquet)' \
                        '--preview[预览模式]' \
                        '--template[使用模板]:template:' \
                        '--interactive[交互模式]' \
                        '--help[显示帮助]' \
                        '*:股票代码:'
                    ;;
                status)
                    _arguments \
                        '--verbose[详细模式]' \
                        '--performance[性能指标]' \
                        '--activities[最近活动]' \
                        '--help[显示帮助]'
                    ;;
                config)
                    _arguments \
                        '--show[显示配置]' \
                        '--edit[编辑配置]' \
                        '--validate[验证配置]' \
                        '--reset[重置配置]' \
                        '--help[显示帮助]'
                    ;;
                init)
                    _arguments \
                        '--help[显示帮助]'
                    ;;
            esac
            ;;
    esac
}

_aqua_commands() {
    local commands
    commands=(
        'collect:采集股票数据'
        'status:显示系统状态'
        'init:初始化配置'
        'config:配置管理'
    )
    _describe 'commands' commands
}

_aqua "$@"
'''


def generate_fish_completion() -> str:
    """生成fish补全脚本"""
    return '''
# aqua命令补全

# 主命令
complete -c aqua -n "__fish_use_subcommand" -a collect -d "采集股票数据"
complete -c aqua -n "__fish_use_subcommand" -a status -d "显示系统状态"
complete -c aqua -n "__fish_use_subcommand" -a init -d "初始化配置"
complete -c aqua -n "__fish_use_subcommand" -a config -d "配置管理"

# collect命令选项
complete -c aqua -n "__fish_seen_subcommand_from collect" -l source -a "tushare mysql csv api" -d "数据源"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l data-type -a "daily kline bar tick fundamental" -d "数据类型"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l freq -a "1min 5min 15min 30min 1h 4h daily weekly monthly" -d "数据频率"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l start -d "开始日期"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l end -d "结束日期"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l output -d "输出目录"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l format -a "json csv parquet" -d "输出格式"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l preview -d "预览模式"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l template -d "使用模板"
complete -c aqua -n "__fish_seen_subcommand_from collect" -l interactive -d "交互模式"

# status命令选项
complete -c aqua -n "__fish_seen_subcommand_from status" -l verbose -s v -d "详细模式"
complete -c aqua -n "__fish_seen_subcommand_from status" -l performance -s p -d "性能指标"
complete -c aqua -n "__fish_seen_subcommand_from status" -l activities -s a -d "最近活动"

# config命令选项
complete -c aqua -n "__fish_seen_subcommand_from config" -l show -d "显示配置"
complete -c aqua -n "__fish_seen_subcommand_from config" -l edit -d "编辑配置"
complete -c aqua -n "__fish_seen_subcommand_from config" -l validate -d "验证配置"
complete -c aqua -n "__fish_seen_subcommand_from config" -l reset -d "重置配置"

# 全局选项
complete -c aqua -s h -l help -d "显示帮助"
complete -c aqua -l version -d "显示版本"
'''


def setup_completion(shell: str = None) -> bool:
    """设置shell补全的便捷函数"""
    manager = CompletionManager()
    return manager.setup_completion(shell)