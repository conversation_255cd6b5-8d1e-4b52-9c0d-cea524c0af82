"""
输出格式化工具
"""
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from decimal import Decimal
from pathlib import Path
import json
import csv

from rich.console import Console
from rich.table import Table
from rich.tree import Tree
from rich.panel import Panel
from rich.columns import Columns
from rich.progress import Progress


class OutputFormatter:
    """输出格式化器"""
    
    def __init__(self, console: Optional[Console] = None):
        """初始化格式化器
        
        Args:
            console: Rich控制台实例
        """
        self.console = console or Console()
        self.formats = {
            'table': self._format_as_table,
            'json': self._format_as_json,
            'tree': self._format_as_tree,
            'panel': self._format_as_panel,
            'columns': self._format_as_columns,
            'csv': self._format_as_csv,
            'summary': self._format_as_summary
        }
    
    def format_data(self, data: Any, format_type: str = 'table', **kwargs) -> str:
        """格式化数据
        
        Args:
            data: 要格式化的数据
            format_type: 格式化类型
            **kwargs: 格式化选项
            
        Returns:
            str: 格式化后的字符串
        """
        if format_type not in self.formats:
            format_type = 'table'
        
        formatter = self.formats[format_type]
        return formatter(data, **kwargs)
    
    def _format_as_table(self, data: Any, **kwargs) -> str:
        """格式化为表格"""
        if isinstance(data, dict):
            return self._dict_to_table(data, **kwargs)
        elif isinstance(data, list):
            return self._list_to_table(data, **kwargs)
        else:
            return str(data)
    
    def _dict_to_table(self, data: Dict[str, Any], title: str = "数据表格", **kwargs) -> str:
        """字典转表格"""
        table = Table(title=title, show_header=True)
        table.add_column("键", style="cyan", width=20)
        table.add_column("值", style="green")
        
        for key, value in data.items():
            # 格式化值
            if isinstance(value, (int, float, Decimal)):
                formatted_value = f"{value:,}" if isinstance(value, int) else f"{value:.4f}"
            elif isinstance(value, datetime):
                formatted_value = value.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(value, (list, dict)):
                formatted_value = json.dumps(value, ensure_ascii=False, indent=2)[:100] + "..." if len(str(value)) > 100 else json.dumps(value, ensure_ascii=False)
            else:
                formatted_value = str(value)
            
            table.add_row(str(key), formatted_value)
        
        return table
    
    def _list_to_table(self, data: List[Any], title: str = "数据列表", **kwargs) -> str:
        """列表转表格"""
        if not data:
            return Panel("暂无数据", title=title)
        
        # 如果列表元素是字典，创建多列表格
        if isinstance(data[0], dict):
            headers = list(data[0].keys())
            table = Table(title=title, show_header=True)
            
            for header in headers:
                table.add_column(str(header), style="cyan")
            
            for item in data:
                row = []
                for header in headers:
                    value = item.get(header, "")
                    if isinstance(value, (int, float, Decimal)):
                        formatted_value = f"{value:,}" if isinstance(value, int) else f"{value:.4f}"
                    elif isinstance(value, datetime):
                        formatted_value = value.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        formatted_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    row.append(formatted_value)
                table.add_row(*row)
        else:
            # 简单列表
            table = Table(title=title, show_header=True)
            table.add_column("序号", style="dim", width=8)
            table.add_column("值", style="green")
            
            for i, item in enumerate(data, 1):
                table.add_row(str(i), str(item))
        
        return table
    
    def _format_as_json(self, data: Any, **kwargs) -> str:
        """格式化为JSON"""
        indent = kwargs.get('indent', 2)
        ensure_ascii = kwargs.get('ensure_ascii', False)
        
        try:
            # 处理特殊类型
            def json_serializer(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, Decimal):
                    return float(obj)
                elif isinstance(obj, Path):
                    return str(obj)
                else:
                    return str(obj)
            
            json_str = json.dumps(data, indent=indent, ensure_ascii=ensure_ascii, default=json_serializer)
            
            # 使用Rich的JSON高亮
            from rich.syntax import Syntax
            return Syntax(json_str, "json", theme="monokai", line_numbers=True)
        except Exception as e:
            return f"JSON格式化失败: {e}"
    
    def _format_as_tree(self, data: Any, **kwargs) -> str:
        """格式化为树形结构"""
        root_label = kwargs.get('root_label', '数据结构')
        tree = Tree(root_label)
        
        def add_to_tree(node, key, value):
            if isinstance(value, dict):
                branch = node.add(f"[bold blue]{key}[/bold blue]")
                for k, v in value.items():
                    add_to_tree(branch, k, v)
            elif isinstance(value, list):
                branch = node.add(f"[bold green]{key}[/bold green] ({len(value)} 项)")
                for i, item in enumerate(value[:10]):  # 限制显示前10项
                    add_to_tree(branch, f"[{i}]", item)
                if len(value) > 10:
                    branch.add("[dim]...(更多项目)[/dim]")
            else:
                # 格式化叶子节点值
                if isinstance(value, (int, float, Decimal)):
                    formatted_value = f"{value:,}" if isinstance(value, int) else f"{value:.4f}"
                elif isinstance(value, datetime):
                    formatted_value = value.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    formatted_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                
                node.add(f"[cyan]{key}[/cyan]: [green]{formatted_value}[/green]")
        
        if isinstance(data, dict):
            for key, value in data.items():
                add_to_tree(tree, key, value)
        elif isinstance(data, list):
            for i, item in enumerate(data):
                add_to_tree(tree, f"项目 {i+1}", item)
        else:
            tree.add(str(data))
        
        return tree
    
    def _format_as_panel(self, data: Any, **kwargs) -> str:
        """格式化为面板"""
        title = kwargs.get('title', '信息面板')
        border_style = kwargs.get('border_style', 'blue')
        
        if isinstance(data, dict):
            content_lines = []
            for key, value in data.items():
                if isinstance(value, (int, float, Decimal)):
                    formatted_value = f"{value:,}" if isinstance(value, int) else f"{value:.4f}"
                elif isinstance(value, datetime):
                    formatted_value = value.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    formatted_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                
                content_lines.append(f"[cyan]{key}[/cyan]: [green]{formatted_value}[/green]")
            
            content = "\n".join(content_lines)
        else:
            content = str(data)
        
        return Panel(content, title=title, border_style=border_style)
    
    def _format_as_columns(self, data: Any, **kwargs) -> str:
        """格式化为多列显示"""
        if isinstance(data, dict):
            panels = []
            for key, value in data.items():
                panel_content = str(value)[:200] + "..." if len(str(value)) > 200 else str(value)
                panels.append(Panel(panel_content, title=str(key), border_style="green"))
            
            columns_count = kwargs.get('columns', 2)
            return Columns(panels, equal=True, expand=True, column_first=True)
        elif isinstance(data, list):
            panels = []
            for i, item in enumerate(data):
                panel_content = str(item)[:200] + "..." if len(str(item)) > 200 else str(item)
                panels.append(Panel(panel_content, title=f"项目 {i+1}", border_style="blue"))
            
            columns_count = kwargs.get('columns', 2)
            return Columns(panels, equal=True, expand=True, column_first=True)
        else:
            return Panel(str(data), title="数据", border_style="yellow")
    
    def _format_as_csv(self, data: Any, **kwargs) -> str:
        """格式化为CSV"""
        output_file = kwargs.get('output_file')
        delimiter = kwargs.get('delimiter', ',')
        
        csv_lines = []
        
        if isinstance(data, list) and data and isinstance(data[0], dict):
            # 字典列表转CSV
            headers = list(data[0].keys())
            csv_lines.append(delimiter.join(headers))
            
            for item in data:
                row = []
                for header in headers:
                    value = item.get(header, "")
                    # CSV转义
                    if isinstance(value, str) and (delimiter in value or '"' in value or '\n' in value):
                        escaped_value = value.replace('"', '""')
                        value = f'"{escaped_value}"'
                    row.append(str(value))
                csv_lines.append(delimiter.join(row))
        elif isinstance(data, dict):
            # 字典转CSV
            csv_lines.append("键,值")
            for key, value in data.items():
                csv_lines.append(f"{key},{value}")
        else:
            csv_lines.append(str(data))
        
        csv_content = "\n".join(csv_lines)
        
        # 如果指定了输出文件，写入文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8', newline='') as f:
                    f.write(csv_content)
                return f"CSV数据已保存到: {output_file}"
            except Exception as e:
                return f"CSV保存失败: {e}"
        
        return csv_content
    
    def _format_as_summary(self, data: Any, **kwargs) -> str:
        """格式化为摘要显示"""
        if isinstance(data, dict):
            summary_items = []
            
            # 统计信息
            total_keys = len(data)
            summary_items.append(f"[cyan]字段数量[/cyan]: {total_keys}")
            
            # 数据类型统计
            type_counts = {}
            for value in data.values():
                type_name = type(value).__name__
                type_counts[type_name] = type_counts.get(type_name, 0) + 1
            
            if type_counts:
                type_summary = ", ".join([f"{t}: {c}" for t, c in type_counts.items()])
                summary_items.append(f"[cyan]数据类型[/cyan]: {type_summary}")
            
            # 重要字段预览
            important_keys = ['total', 'count', 'sum', 'avg', 'max', 'min', 'status', 'result']
            preview_items = []
            for key in important_keys:
                if key in data:
                    value = data[key]
                    if isinstance(value, (int, float, Decimal)):
                        formatted_value = f"{value:,}" if isinstance(value, int) else f"{value:.4f}"
                    else:
                        formatted_value = str(value)
                    preview_items.append(f"[green]{key}[/green]: {formatted_value}")
            
            if preview_items:
                summary_items.append("[cyan]关键字段[/cyan]:")
                summary_items.extend([f"  • {item}" for item in preview_items])
            
            return Panel("\n".join(summary_items), title="数据摘要", border_style="blue")
        
        elif isinstance(data, list):
            summary_items = []
            
            # 基本统计
            summary_items.append(f"[cyan]项目数量[/cyan]: {len(data)}")
            
            if data:
                # 数据类型
                first_item_type = type(data[0]).__name__
                summary_items.append(f"[cyan]项目类型[/cyan]: {first_item_type}")
                
                # 如果是字典列表，显示字段信息
                if isinstance(data[0], dict):
                    all_keys = set()
                    for item in data:
                        if isinstance(item, dict):
                            all_keys.update(item.keys())
                    
                    summary_items.append(f"[cyan]字段数量[/cyan]: {len(all_keys)}")
                    if all_keys:
                        key_preview = ", ".join(list(all_keys)[:5])
                        if len(all_keys) > 5:
                            key_preview += f" ... (+{len(all_keys)-5} 更多)"
                        summary_items.append(f"[cyan]字段列表[/cyan]: {key_preview}")
            
            return Panel("\n".join(summary_items), title="列表摘要", border_style="green")
        
        else:
            return Panel(f"[green]数据内容[/green]: {str(data)[:200]}", title="简单摘要", border_style="yellow")
    
    def print_formatted(self, data: Any, format_type: str = 'table', **kwargs) -> None:
        """打印格式化数据
        
        Args:
            data: 要打印的数据
            format_type: 格式化类型
            **kwargs: 格式化选项
        """
        formatted_output = self.format_data(data, format_type, **kwargs)
        self.console.print(formatted_output)
    
    def get_available_formats(self) -> List[str]:
        """获取可用的格式化类型
        
        Returns:
            List[str]: 格式化类型列表
        """
        return list(self.formats.keys())
    
    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化后的文件大小
        """
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def format_duration(self, seconds: float) -> str:
        """格式化时间间隔
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化后的时间间隔
        """
        if seconds < 60:
            return f"{seconds:.1f} 秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f} 分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f} 小时"
    
    def format_percentage(self, value: float, total: float) -> str:
        """格式化百分比
        
        Args:
            value: 当前值
            total: 总值
            
        Returns:
            str: 格式化后的百分比
        """
        if total == 0:
            return "0.0%"
        
        percentage = (value / total) * 100
        return f"{percentage:.1f}%"