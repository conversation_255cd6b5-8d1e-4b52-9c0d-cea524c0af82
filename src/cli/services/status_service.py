"""
状态查询服务
"""
import sys
from pathlib import Path
from typing import Dict, Any, List
import platform
import time
from datetime import datetime

from rich.console import Console
from rich.table import Table

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.config_loader import ConfigLoader


class StatusService:
    """状态查询服务"""
    
    def __init__(self):
        """初始化状态服务"""
        self.console = Console()
        self.config_loader = ConfigLoader()
        
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'python_version': platform.python_version(),
            'architecture': platform.machine(),
            'hostname': platform.node(),
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'timezone': time.tzname[0] if time.tzname else 'UTC'
        }
    
    def get_project_info(self) -> Dict[str, Any]:
        """获取项目信息"""
        project_root = Path(__file__).parent.parent.parent.parent
        
        info = {
            'project_root': str(project_root),
            'config_exists': (project_root / 'config' / 'settings.toml').exists(),
            'database_exists': (project_root / 'data' / 'aqua.duckdb').exists(),
            'logs_dir_exists': (project_root / 'logs').exists(),
        }
        
        # 检查数据库文件大小
        db_file = project_root / 'data' / 'aqua.duckdb'
        if db_file.exists():
            info['database_size'] = f"{db_file.stat().st_size / 1024 / 1024:.1f} MB"
        
        # 检查日志文件数量
        logs_dir = project_root / 'logs'
        if logs_dir.exists():
            log_files = list(logs_dir.glob('*.log'))
            info['log_files_count'] = len(log_files)
        
        return info
    
    def get_component_status(self) -> List[Dict[str, str]]:
        """获取组件状态"""
        components = []
        
        # 数据库连接状态
        try:
            # 暂时模拟数据库状态检查
            components.append({
                'name': '数据库连接', 
                'status': '✅ 正常',
                'details': 'DuckDB连接正常'
            })
        except Exception as e:
            components.append({
                'name': '数据库连接',
                'status': '❌ 异常', 
                'details': f'连接失败: {str(e)}'
            })
        
        # 存储管理器状态
        try:
            # 暂时模拟存储管理器状态
            components.append({
                'name': '存储管理器',
                'status': '⚠️ 开发中',
                'details': 'UnifiedStorageManager'
            })
        except Exception as e:
            components.append({
                'name': '存储管理器', 
                'status': '❌ 异常',
                'details': f'初始化失败: {str(e)}'
            })
        
        # 任务队列状态
        components.append({
            'name': '任务队列',
            'status': '⚠️ 开发中', 
            'details': 'PriorityQueueManager'
        })
        
        # AI代理状态
        components.append({
            'name': 'AI代理',
            'status': '⚠️ 开发中',
            'details': 'AI智能代理系统'
        })
        
        # CLI状态
        components.append({
            'name': 'CLI接口',
            'status': '✅ 正常',
            'details': 'AQUA CLI v1.0.0'
        })
        
        return components
    
    def get_data_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        # 暂时返回模拟数据
        return {
            'total_tables': 12,
            'total_records': 150000,
            'data_size': '45.2 MB',
            'last_update': '2025-07-30 10:30:00',
            'stock_symbols': 28,
            'futures_contracts': 5,
            'date_range': '2024-01-01 to 2025-07-30'
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            import psutil
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_usage': f'{cpu_percent:.1f}%',
                'memory_usage': f'{memory.percent:.1f}%',
                'memory_available': f'{memory.available / 1024 / 1024 / 1024:.1f} GB',
                'disk_usage': f'{disk.percent:.1f}%',
                'disk_free': f'{disk.free / 1024 / 1024 / 1024:.1f} GB'
            }
        except ImportError:
            return {
                'cpu_usage': 'N/A (需要psutil)',
                'memory_usage': 'N/A',
                'memory_available': 'N/A',
                'disk_usage': 'N/A', 
                'disk_free': 'N/A'
            }
    
    def get_recent_activities(self) -> List[Dict[str, str]]:
        """获取最近活动"""
        # 暂时返回模拟数据
        return [
            {
                'time': '2025-07-30 10:25:00',
                'activity': '数据采集',
                'details': '采集000001.SZ股票数据'
            },
            {
                'time': '2025-07-30 09:30:00', 
                'activity': '系统启动',
                'details': 'AQUA CLI系统启动'
            },
            {
                'time': '2025-07-29 18:00:00',
                'activity': '数据备份',
                'details': '自动备份数据库'
            }
        ]