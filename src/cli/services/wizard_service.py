"""
交互式向导服务
"""
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
import click
from rich.console import Console
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.config_loader import ConfigLoader


class WizardService:
    """向导服务"""
    
    def __init__(self):
        """初始化向导服务"""
        self.console = Console()
        self.config_loader = ConfigLoader()
    
    def run_collect_wizard(self) -> Dict[str, Any]:
        """运行数据采集向导
        
        Returns:
            Dict: 采集参数
        """
        self.console.print("\n[bold blue]🧙‍♂️ AQUA数据采集向导[/bold blue]\n")
        
        # 步骤1: 选择股票代码
        symbols = self._get_symbols_input()
        
        # 步骤2: 选择数据源
        source = self._get_data_source()
        
        # 步骤3: 选择数据类型
        data_type = self._get_data_type(source)
        
        # 步骤4: 选择数据频率
        freq = self._get_frequency(data_type)
        
        # 步骤5: 选择时间范围
        date_range = self._get_date_range()
        
        # 步骤6: 选择输出选项
        output_options = self._get_output_options()
        
        # 整理参数
        params = {
            'symbols': symbols,
            'source': source,
            'data_type': data_type,
            'freq': freq,
            'start': date_range['start'],
            'end': date_range['end'],
            **output_options
        }
        
        # 显示配置摘要
        self._show_configuration_summary(params)
        
        # 确认执行
        if Confirm.ask("\n是否执行数据采集"):
            return params
        else:
            self.console.print("[yellow]操作已取消[/yellow]")
            return {}
    
    def run_init_wizard(self) -> Dict[str, Any]:
        """运行初始化向导
        
        Returns:
            Dict: 初始化配置
        """
        self.console.print("\n[bold green]🚀 AQUA系统初始化向导[/bold green]\n")
        
        # 步骤1: 数据源配置
        datasource_config = self._configure_datasources()
        
        # 步骤2: 存储配置
        storage_config = self._configure_storage()
        
        # 步骤3: 日志配置
        logging_config = self._configure_logging()
        
        # 步骤4: 性能配置
        performance_config = self._configure_performance()
        
        config = {
            'datasources': datasource_config,
            'storage': storage_config,
            'logging': logging_config,
            'performance': performance_config
        }
        
        # 显示配置摘要
        self._show_init_summary(config)
        
        # 确认保存
        if Confirm.ask("\n是否保存配置"):
            return config
        else:
            self.console.print("[yellow]初始化已取消[/yellow]")
            return {}
    
    def _get_symbols_input(self) -> List[str]:
        """获取股票代码输入"""
        self.console.print("[bold]步骤1: 选择股票代码[/bold]")
        
        # 提供常用股票选项
        common_stocks = [
            ("000001.SZ", "平安银行"),
            ("000002.SZ", "万科A"),
            ("600000.SH", "浦发银行"),
            ("600036.SH", "招商银行"),
            ("600519.SH", "贵州茅台")
        ]
        
        table = Table(title="常用股票代码")
        table.add_column("代码", style="cyan")
        table.add_column("名称", style="green")
        
        for code, name in common_stocks:
            table.add_row(code, name)
        
        self.console.print(table)
        
        choice = Prompt.ask(
            "\n请选择输入方式",
            choices=["manual", "list", "file"],
            default="manual"
        )
        
        if choice == "manual":
            symbols_input = Prompt.ask("请输入股票代码(多个用逗号分隔)")
            symbols = [s.strip().upper() for s in symbols_input.split(',')]
        elif choice == "list":
            selected = Prompt.ask("请输入要选择的股票序号(多个用逗号分隔，1-5)")
            indices = [int(i.strip()) - 1 for i in selected.split(',')]
            symbols = [common_stocks[i][0] for i in indices if 0 <= i < len(common_stocks)]
        else:  # file
            file_path = Prompt.ask("请输入包含股票代码的文件路径")
            try:
                with open(file_path, 'r') as f:
                    symbols = [line.strip().upper() for line in f if line.strip()]
            except Exception as e:
                self.console.print(f"[red]文件读取失败: {e}[/red]")
                symbols = ["000001.SZ"]  # 默认值
        
        self.console.print(f"[green]已选择股票: {', '.join(symbols)}[/green]\n")
        return symbols
    
    def _get_data_source(self) -> str:
        """获取数据源选择"""
        self.console.print("[bold]步骤2: 选择数据源[/bold]")
        
        sources = {
            "tushare": "Tushare Pro (推荐，数据全面)",
            "mysql": "MySQL数据库",
            "csv": "CSV文件导入",
            "api": "自定义API"
        }
        
        table = Table(title="可用数据源")
        table.add_column("代码", style="cyan")
        table.add_column("描述", style="green")
        
        for code, desc in sources.items():
            table.add_row(code, desc)
        
        self.console.print(table)
        
        source = Prompt.ask(
            "\n选择数据源",
            choices=list(sources.keys()),
            default="tushare"
        )
        
        self.console.print(f"[green]已选择数据源: {sources[source]}[/green]\n")
        return source
    
    def _get_data_type(self, source: str) -> str:
        """获取数据类型选择"""
        self.console.print("[bold]步骤3: 选择数据类型[/bold]")
        
        if source == "tushare":
            types = {
                "daily": "日线数据 (开高低收量)",
                "kline": "K线数据 (分钟级)",
                "fundamental": "基本面数据",
                "tick": "Tick数据 (逐笔)",
                "bar": "Bar数据 (聚合)"
            }
        else:
            types = {
                "daily": "日线数据",
                "kline": "K线数据",
                "bar": "Bar数据"
            }
        
        table = Table(title="数据类型")
        table.add_column("类型", style="cyan")
        table.add_column("描述", style="green")
        
        for typ, desc in types.items():
            table.add_row(typ, desc)
        
        self.console.print(table)
        
        data_type = Prompt.ask(
            "\n选择数据类型",
            choices=list(types.keys()),
            default="daily"
        )
        
        self.console.print(f"[green]已选择数据类型: {types[data_type]}[/green]\n")
        return data_type
    
    def _get_frequency(self, data_type: str) -> str:
        """获取数据频率选择"""
        self.console.print("[bold]步骤4: 选择数据频率[/bold]")
        
        if data_type in ["kline", "bar"]:
            frequencies = {
                "1min": "1分钟",
                "5min": "5分钟",
                "15min": "15分钟",
                "30min": "30分钟",
                "1h": "1小时",
                "4h": "4小时",
                "daily": "日线",
                "weekly": "周线",
                "monthly": "月线"
            }
        else:
            frequencies = {
                "daily": "日线",
                "weekly": "周线",
                "monthly": "月线"
            }
        
        table = Table(title="数据频率")
        table.add_column("频率", style="cyan")
        table.add_column("描述", style="green")
        
        for freq, desc in frequencies.items():
            table.add_row(freq, desc)
        
        self.console.print(table)
        
        freq = Prompt.ask(
            "\n选择数据频率",
            choices=list(frequencies.keys()),
            default="daily"
        )
        
        self.console.print(f"[green]已选择频率: {frequencies[freq]}[/green]\n")
        return freq
    
    def _get_date_range(self) -> Dict[str, str]:
        """获取时间范围"""
        self.console.print("[bold]步骤5: 设置时间范围[/bold]")
        
        quick_options = {
            "1": ("最近7天", "7d"),
            "2": ("最近30天", "30d"),
            "3": ("最近90天", "90d"),
            "4": ("今年", "ytd"),
            "5": ("自定义", "custom")
        }
        
        for key, (desc, _) in quick_options.items():
            self.console.print(f"{key}. {desc}")
        
        choice = Prompt.ask(
            "\n选择时间范围",
            choices=list(quick_options.keys()),
            default="2"
        )
        
        if choice != "5":
            _, period = quick_options[choice]
            if period == "7d":
                start, end = "2025-07-23", "2025-07-30"
            elif period == "30d":
                start, end = "2025-06-30", "2025-07-30"
            elif period == "90d":
                start, end = "2025-05-01", "2025-07-30"
            else:  # ytd
                start, end = "2025-01-01", "2025-07-30"
        else:
            start = Prompt.ask("请输入开始日期 (YYYY-MM-DD)", default="2025-06-01")
            end = Prompt.ask("请输入结束日期 (YYYY-MM-DD)", default="2025-07-30")
        
        self.console.print(f"[green]时间范围: {start} 到 {end}[/green]\n")
        return {"start": start, "end": end}
    
    def _get_output_options(self) -> Dict[str, Any]:
        """获取输出选项"""
        self.console.print("[bold]步骤6: 输出设置[/bold]")
        
        # 输出格式
        format_choice = Prompt.ask(
            "选择输出格式",
            choices=["json", "csv", "parquet"],
            default="json"
        )
        
        # 输出目录
        use_default_output = Confirm.ask("使用默认输出目录?", default=True)
        if use_default_output:
            output_dir = None
        else:
            output_dir = Prompt.ask("请输入输出目录路径")
        
        # 其他选项
        preview = Confirm.ask("启用预览模式?", default=False)
        
        options = {
            "format": format_choice,
            "preview": preview
        }
        
        if output_dir:
            options["output"] = output_dir
        
        self.console.print(f"[green]输出设置完成[/green]\n")
        return options
    
    def _configure_datasources(self) -> Dict[str, Any]:
        """配置数据源"""
        self.console.print("[bold]配置数据源[/bold]")
        
        # Tushare配置
        enable_tushare = Confirm.ask("启用Tushare数据源?", default=True)
        tushare_config = {}
        
        if enable_tushare:
            has_token = Confirm.ask("已有Tushare Token?", default=False)
            if has_token:
                token = Prompt.ask("请输入Tushare Token", password=True)
                tushare_config = {
                    "enabled": True,
                    "token": token,
                    "rate_limit": 200,
                    "points_per_minute": 2000
                }
            else:
                self.console.print("[yellow]请先注册Tushare账户获取Token[/yellow]")
                tushare_config = {"enabled": False}
        
        return {"tushare": tushare_config}
    
    def _configure_storage(self) -> Dict[str, Any]:
        """配置存储"""
        self.console.print("[bold]配置存储[/bold]")
        
        # 数据库路径
        use_default_db = Confirm.ask("使用默认数据库路径?", default=True)
        if use_default_db:
            db_path = "data/aqua.duckdb"
        else:
            db_path = Prompt.ask("请输入数据库路径", default="data/aqua.duckdb")
        
        # 内存限制
        memory_limit = IntPrompt.ask("内存限制(GB)", default=4)
        
        return {
            "database_path": db_path,
            "memory_limit_gb": memory_limit,
            "enable_compression": True
        }
    
    def _configure_logging(self) -> Dict[str, Any]:
        """配置日志"""
        self.console.print("[bold]配置日志[/bold]")
        
        log_level = Prompt.ask(
            "日志级别",
            choices=["DEBUG", "INFO", "WARNING", "ERROR"],
            default="INFO"
        )
        
        return {
            "level": log_level,
            "file_rotation": True,
            "max_file_size": "10MB"
        }
    
    def _configure_performance(self) -> Dict[str, Any]:
        """配置性能"""
        self.console.print("[bold]配置性能[/bold]")
        
        max_workers = IntPrompt.ask("最大工作线程数", default=4)
        batch_size = IntPrompt.ask("批处理大小", default=1000)
        
        return {
            "max_workers": max_workers,
            "batch_size": batch_size,
            "enable_caching": True
        }
    
    def _show_configuration_summary(self, params: Dict[str, Any]) -> None:
        """显示配置摘要"""
        self.console.print("\n[bold]📋 配置摘要[/bold]")
        
        # 创建摘要表格
        table = Table(title="数据采集配置")
        table.add_column("参数", style="cyan")
        table.add_column("值", style="green")
        
        table.add_row("股票代码", ", ".join(params.get('symbols', [])))
        table.add_row("数据源", params.get('source', ''))
        table.add_row("数据类型", params.get('data_type', ''))
        table.add_row("数据频率", params.get('freq', ''))
        table.add_row("开始日期", params.get('start', ''))
        table.add_row("结束日期", params.get('end', ''))
        table.add_row("输出格式", params.get('format', ''))
        table.add_row("预览模式", "是" if params.get('preview') else "否")
        
        self.console.print(table)
    
    def _show_init_summary(self, config: Dict[str, Any]) -> None:
        """显示初始化摘要"""
        self.console.print("\n[bold]📋 初始化配置摘要[/bold]")
        
        # 数据源配置
        ds_panel = f"Tushare: {'启用' if config['datasources']['tushare'].get('enabled') else '禁用'}"
        
        # 存储配置
        storage_panel = f"""
数据库路径: {config['storage']['database_path']}
内存限制: {config['storage']['memory_limit_gb']}GB
压缩: {'启用' if config['storage']['enable_compression'] else '禁用'}
        """
        
        # 日志配置
        logging_panel = f"""
日志级别: {config['logging']['level']}
文件轮转: {'启用' if config['logging']['file_rotation'] else '禁用'}
最大文件大小: {config['logging']['max_file_size']}
        """
        
        # 性能配置
        perf_panel = f"""
最大工作线程: {config['performance']['max_workers']}
批处理大小: {config['performance']['batch_size']}
缓存: {'启用' if config['performance']['enable_caching'] else '禁用'}
        """
        
        panels = [
            Panel(ds_panel, title="数据源", border_style="green"),
            Panel(storage_panel.strip(), title="存储", border_style="blue"),
            Panel(logging_panel.strip(), title="日志", border_style="yellow"),
            Panel(perf_panel.strip(), title="性能", border_style="red")
        ]
        
        self.console.print(Columns(panels, equal=True, expand=True))