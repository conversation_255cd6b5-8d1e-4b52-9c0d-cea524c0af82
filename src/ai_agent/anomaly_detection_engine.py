#!/usr/bin/env python3
"""
基础异常检测引擎

MVP版本：基于统计方法的异常检测
复用率：80% - 基于ConflictResolutionEngine架构 + 简化异常检测算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any
import warnings

warnings.filterwarnings("ignore")

try:
    from ..storage.conflict_resolution_engine import NotificationManager, AuditLogger
    from ..utils.logger import get_logger
    from ..utils.time_utils import get_beijing_time_now
    from .ai_strategy import AnomalyDetectionStrategy
except ImportError:
    # 支持测试环境的绝对导入
    from src.storage.conflict_resolution_engine import NotificationManager, AuditLogger
    from src.utils.logger import get_logger
    from src.utils.time_utils import get_beijing_time_now


class AnomalyDetectionEngine:
    """
    基础异常检测引擎

    复用ConflictResolutionEngine的检测逻辑、评分机制、通知管理
    实现统计方法的异常检测
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化异常检测引擎

        Args:
            config: 配置字典
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config

        # 异常检测配置
        self.detection_config = config.get("anomaly_detection", {})
        self.confidence_threshold = self.detection_config.get(
            "confidence_threshold", 0.8
        )
        self.z_score_threshold = self.detection_config.get("z_score_threshold", 3.0)

        # 完全复用ConflictResolutionEngine的组件
        self.notification_manager = NotificationManager(self)
        self.audit_logger = AuditLogger(self)

        # 简化的异常检测器
        self.statistical_detector = StatisticalAnomalyDetector()

        # 异常记录
        self.detected_anomalies = []

        self.logger.info("异常检测引擎初始化完成")

    def detect_anomalies(
        self, data: pd.DataFrame, method: str = "z_score", **kwargs
    ) -> Dict[str, Any]:
        """
        检测异常

        Args:
            data: 输入数据
            method: 检测方法
            **kwargs: 其他参数

        Returns:
            Dict: 检测结果
        """
        try:
            if data.empty:
                return {"success": False, "error": "输入数据为空", "anomalies_found": 0}

            # 根据方法选择检测器
            if method == "z_score":
                result = self._detect_z_score_anomalies(data, **kwargs)
            elif method == "iqr":
                result = self._detect_iqr_anomalies(data, **kwargs)
            elif method == "percentage":
                result = self._detect_percentage_anomalies(data, **kwargs)
            else:
                return {
                    "success": False,
                    "error": f"不支持的检测方法: {method}",
                    "anomalies_found": 0,
                }

            # 计算严重程度分数 - 复用ConflictResolutionEngine._calculate_severity_score模式
            for anomaly in result.get("anomaly_details", []):
                anomaly["severity_score"] = self._calculate_anomaly_severity_score(
                    anomaly
                )

            # 分类异常 - 复用ConflictResolutionEngine.classify_conflicts
            if result.get("anomaly_details"):
                classified = self.classify_anomalies(result["anomaly_details"])
                result["classified_anomalies"] = classified

            # 记录审计日志
            if self.audit_logger.enabled:
                self.audit_logger.log_detection("anomaly_detection", result)

            return result

        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
            return {"success": False, "error": str(e), "anomalies_found": 0}

    def _detect_z_score_anomalies(
        self, data: pd.DataFrame, features: List[str] = None
    ) -> Dict[str, Any]:
        """基于Z分数的异常检测"""
        if features is None:
            features = ["close", "vol", "pct_chg"]

        # 过滤存在的特征
        available_features = [f for f in features if f in data.columns]
        if not available_features:
            return {"success": False, "error": "没有可用的特征列", "anomalies_found": 0}

        anomalies = []

        for feature in available_features:
            try:
                # 计算Z分数
                mean_val = data[feature].mean()
                std_val = data[feature].std()

                if std_val == 0:
                    continue

                z_scores = np.abs((data[feature] - mean_val) / std_val)
                anomaly_mask = z_scores > self.z_score_threshold

                # 收集异常点
                anomaly_indices = data[anomaly_mask].index.tolist()
                for idx in anomaly_indices:
                    anomalies.append(
                        {
                            "index": int(idx),
                            "feature": feature,
                            "value": float(data.loc[idx, feature]),
                            "z_score": float(z_scores.loc[idx]),
                            "anomaly_type": "statistical_outlier",
                            "detection_method": "z_score",
                        }
                    )

            except Exception as e:
                self.logger.warning(f"特征 {feature} 异常检测失败: {e}")
                continue

        return {
            "success": True,
            "detection_method": "z_score",
            "anomalies_found": len(anomalies),
            "anomaly_details": anomalies,
            "threshold_used": self.z_score_threshold,
        }

    def _detect_iqr_anomalies(
        self, data: pd.DataFrame, features: List[str] = None
    ) -> Dict[str, Any]:
        """基于IQR的异常检测"""
        if features is None:
            features = ["close", "vol", "pct_chg"]

        available_features = [f for f in features if f in data.columns]
        anomalies = []

        for feature in available_features:
            try:
                Q1 = data[feature].quantile(0.25)
                Q3 = data[feature].quantile(0.75)
                IQR = Q3 - Q1

                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                # 找出异常值
                anomaly_mask = (data[feature] < lower_bound) | (
                    data[feature] > upper_bound
                )
                anomaly_indices = data[anomaly_mask].index.tolist()

                for idx in anomaly_indices:
                    anomalies.append(
                        {
                            "index": int(idx),
                            "feature": feature,
                            "value": float(data.loc[idx, feature]),
                            "lower_bound": float(lower_bound),
                            "upper_bound": float(upper_bound),
                            "anomaly_type": "iqr_outlier",
                            "detection_method": "iqr",
                        }
                    )

            except Exception as e:
                self.logger.warning(f"特征 {feature} IQR检测失败: {e}")
                continue

        return {
            "success": True,
            "detection_method": "iqr",
            "anomalies_found": len(anomalies),
            "anomaly_details": anomalies,
        }

    def _detect_percentage_anomalies(
        self, data: pd.DataFrame, threshold: float = 0.1
    ) -> Dict[str, Any]:
        """检测异常涨跌幅"""
        anomalies = []

        if "pct_chg" not in data.columns:
            return {
                "success": False,
                "error": "数据中没有pct_chg列",
                "anomalies_found": 0,
            }

        # 检测异常涨跌幅（超过阈值的变化）
        threshold_percent = threshold * 100  # 转换为百分比
        anomaly_mask = np.abs(data["pct_chg"]) > threshold_percent
        anomaly_indices = data[anomaly_mask].index.tolist()

        for idx in anomaly_indices:
            pct_change = data.loc[idx, "pct_chg"]
            anomalies.append(
                {
                    "index": int(idx),
                    "feature": "pct_chg",
                    "value": float(pct_change),
                    "threshold": threshold_percent,
                    "anomaly_type": "extreme_price_change",
                    "detection_method": "percentage",
                    "direction": "up" if pct_change > 0 else "down",
                }
            )

        return {
            "success": True,
            "detection_method": "percentage",
            "anomalies_found": len(anomalies),
            "anomaly_details": anomalies,
            "threshold_used": threshold_percent,
        }

    def _calculate_anomaly_severity_score(self, anomaly_data: Dict[str, Any]) -> float:
        """
        计算异常严重程度分数
        复用ConflictResolutionEngine._calculate_severity_score模式
        """
        score = 0.0

        # 基于检测方法调整分数
        if anomaly_data.get("detection_method") == "z_score":
            z_score = anomaly_data.get("z_score", 0)
            score = min(1.0, (z_score - 2.0) / 3.0)  # Z分数越高，严重程度越高
        elif anomaly_data.get("detection_method") == "percentage":
            pct_change = abs(anomaly_data.get("value", 0))
            score = min(1.0, pct_change / 20.0)  # 20%以上涨跌幅为最高严重程度
        elif anomaly_data.get("detection_method") == "iqr":
            score = 0.6  # IQR异常给予中等严重程度

        return max(0.0, score)

    def classify_anomalies(
        self, anomalies: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        异常分类
        完全复用ConflictResolutionEngine.classify_conflicts逻辑
        """
        classified = {"critical": [], "major": [], "minor": []}

        for anomaly in anomalies:
            severity_score = anomaly.get("severity_score", 0.0)

            # 分类逻辑与ConflictResolutionEngine保持一致
            if severity_score >= 0.8:
                classified["critical"].append(
                    {
                        **anomaly,
                        "classification_reason": "High severity score and significant deviation",
                    }
                )
            elif severity_score >= 0.5:
                classified["major"].append(
                    {**anomaly, "classification_reason": "Moderate severity score"}
                )
            else:
                classified["minor"].append(
                    {**anomaly, "classification_reason": "Low severity score"}
                )

        return classified

    def send_anomaly_notifications(self, anomalies: List[Dict[str, Any]]):
        """
        发送异常通知
        完全复用NotificationManager
        """
        if not self.notification_manager.enabled:
            return

        severity_distribution = self._get_severity_distribution(anomalies)

        notification_data = {
            "notification_type": "anomaly_detection",
            "anomaly_count": len(anomalies),
            "severity_distribution": severity_distribution,
            "detected_at": get_beijing_time_now(),
            "detection_summary": self._generate_detection_summary(anomalies),
        }

        self.notification_manager.send_notification(notification_data)

    def _get_severity_distribution(
        self, anomalies: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """获取严重程度分布"""
        distribution = {"critical": 0, "major": 0, "minor": 0}

        for anomaly in anomalies:
            severity_score = anomaly.get("severity_score", 0.0)
            if severity_score >= 0.8:
                distribution["critical"] += 1
            elif severity_score >= 0.5:
                distribution["major"] += 1
            else:
                distribution["minor"] += 1

        return distribution

    def _generate_detection_summary(self, anomalies: List[Dict[str, Any]]) -> str:
        """生成检测摘要"""
        if not anomalies:
            return "未检测到异常"

        summary_parts = []

        # 按检测方法分组
        method_counts = {}
        for anomaly in anomalies:
            method = anomaly.get("detection_method", "unknown")
            method_counts[method] = method_counts.get(method, 0) + 1

        for method, count in method_counts.items():
            summary_parts.append(f"{method}方法检测到{count}个异常")

        return "; ".join(summary_parts)

    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        return {
            "total_detections": len(self.detected_anomalies),
            "confidence_threshold": self.confidence_threshold,
            "z_score_threshold": self.z_score_threshold,
            "notification_enabled": self.notification_manager.enabled,
            "audit_enabled": self.audit_logger.enabled,
        }


class StatisticalAnomalyDetector:
    """统计异常检测器"""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)

    def detect_univariate_anomalies(
        self, series: pd.Series, method: str = "z_score"
    ) -> List[int]:
        """单变量异常检测"""
        if method == "z_score":
            return self._z_score_detection(series)
        elif method == "iqr":
            return self._iqr_detection(series)
        else:
            return []

    def _z_score_detection(
        self, series: pd.Series, threshold: float = 3.0
    ) -> List[int]:
        """Z分数异常检测"""
        if series.std() == 0:
            return []

        z_scores = np.abs((series - series.mean()) / series.std())
        return series[z_scores > threshold].index.tolist()

    def _iqr_detection(self, series: pd.Series) -> List[int]:
        """IQR异常检测"""
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1

        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        anomaly_mask = (series < lower_bound) | (series > upper_bound)
        return series[anomaly_mask].index.tolist()
