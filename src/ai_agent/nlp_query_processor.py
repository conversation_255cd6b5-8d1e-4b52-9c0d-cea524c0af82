#!/usr/bin/env python3
"""
简化NLP查询处理器

MVP版本：基础的自然语言到SQL转换
复用率：85% - 基于UnifiedStorageManager + 简化NLP逻辑
"""

import re
from typing import Dict, List, Any
from datetime import datetime
import hashlib

try:
    from ..storage.unified_storage_manager import UnifiedStorageManager
    from ..utils.logger import get_logger
    from ..utils.time_utils import get_beijing_time_now
    from .ai_strategy import NLPQueryStrategy
except ImportError:
    # 支持测试环境的绝对导入
    from src.storage.unified_storage_manager import UnifiedStorageManager
    from src.utils.logger import get_logger


class NLPQueryProcessor:
    """
    简化NLP查询处理器

    复用UnifiedStorageManager的数据访问、连接管理、性能监控功能
    实现基础的自然语言查询功能
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化NLP查询处理器

        Args:
            config: 配置字典
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config

        # 完全复用存储管理器
        self.storage_manager = UnifiedStorageManager(config)

        # NLP配置
        self.nlp_config = config.get("nlp_query", {})
        self.max_query_length = self.nlp_config.get("max_query_length", 500)
        self.confidence_threshold = self.nlp_config.get("confidence_threshold", 0.7)

        # 简单的查询缓存
        self.query_cache = {}
        self.cache_enabled = self.nlp_config.get("enable_cache", True)

        # 基础的中文关键词映射
        self.keyword_mapping = {
            "股价": ["close", "open", "high", "low"],
            "成交量": ["vol"],
            "涨幅": ["pct_chg"],
            "昨日": "WHERE trade_date = DATE('now', '-1 day')",
            "今日": "WHERE trade_date = DATE('now')",
            "最近": "ORDER BY trade_date DESC LIMIT",
            "最高": "ORDER BY {} DESC LIMIT 1",
            "最低": "ORDER BY {} ASC LIMIT 1",
            "平均": "AVG({})",
            "总计": "SUM({})",
        }

        # 股票代码映射（简化版）
        self.stock_mapping = {
            "平安银行": "000001.SZ",
            "万科A": "000002.SZ",
            "招商银行": "600036.SH",
            "贵州茅台": "600519.SH",
        }

        self.logger.info("NLP查询处理器初始化完成")

    def parse_natural_query(self, query: str) -> Dict[str, Any]:
        """
        解析自然语言查询（简化版）

        Args:
            query: 自然语言查询

        Returns:
            Dict: 解析结果
        """
        try:
            if len(query) > self.max_query_length:
                return {
                    "success": False,
                    "error": f"查询长度超过限制 {self.max_query_length}",
                    "confidence": 0.0,
                }

            # 基础的意图识别
            intent = self._identify_intent(query)

            # 实体提取
            entities = self._extract_entities(query)

            # 计算置信度
            confidence = self._calculate_confidence(intent, entities)

            if confidence < self.confidence_threshold:
                return {
                    "success": False,
                    "error": "查询理解置信度过低",
                    "confidence": confidence,
                }

            return {
                "success": True,
                "parsed_intent": intent,
                "entities": entities,
                "confidence": confidence,
                "original_query": query,
            }

        except Exception as e:
            self.logger.error(f"查询解析失败: {e}")
            return {"success": False, "error": str(e), "confidence": 0.0}

    def _identify_intent(self, query: str) -> str:
        """简化的意图识别"""
        query_lower = query.lower()

        if any(word in query_lower for word in ["查询", "显示", "看看", "查看"]):
            return "query_data"
        elif any(word in query_lower for word in ["统计", "计算", "分析"]):
            return "analyze_data"
        elif any(word in query_lower for word in ["排行", "排名", "前", "后"]):
            return "ranking_query"
        else:
            return "general_query"

    def _extract_entities(self, query: str) -> Dict[str, Any]:
        """简化的实体提取"""
        entities = {}

        # 提取股票名称
        for stock_name, stock_code in self.stock_mapping.items():
            if stock_name in query:
                entities["stock_name"] = stock_name
                entities["ts_code"] = stock_code
                break

        # 提取数据类型
        for data_type, columns in self.keyword_mapping.items():
            if data_type in query and isinstance(columns, list):
                entities["data_fields"] = columns
                entities["data_type"] = data_type
                break

        # 提取时间范围
        if "昨日" in query or "昨天" in query:
            entities["time_filter"] = "yesterday"
        elif "今日" in query or "今天" in query:
            entities["time_filter"] = "today"
        elif "最近" in query:
            # 提取数字
            numbers = re.findall(r"\d+", query)
            if numbers:
                entities["time_filter"] = f"recent_{numbers[0]}"

        return entities

    def _calculate_confidence(self, intent: str, entities: Dict[str, Any]) -> float:
        """计算解析置信度"""
        confidence = 0.5  # 基础分数

        if intent != "general_query":
            confidence += 0.2

        if "ts_code" in entities:
            confidence += 0.2

        if "data_fields" in entities:
            confidence += 0.1

        return min(1.0, confidence)

    def generate_sql(self, parsed_query: Dict[str, Any]) -> str:
        """
        生成SQL查询（简化版）

        Args:
            parsed_query: 解析后的查询结构

        Returns:
            str: SQL查询语句
        """
        try:
            entities = parsed_query["entities"]
            intent = parsed_query["parsed_intent"]

            # 基础SELECT语句
            if "data_fields" in entities:
                fields = ", ".join(entities["data_fields"])
            else:
                fields = "*"

            sql = f"SELECT {fields} FROM stock_daily"

            # 添加WHERE条件
            where_conditions = []

            if "ts_code" in entities:
                where_conditions.append(f"ts_code = '{entities['ts_code']}'")

            if "time_filter" in entities:
                time_filter = entities["time_filter"]
                if time_filter == "today":
                    where_conditions.append("trade_date = DATE('now')")
                elif time_filter == "yesterday":
                    where_conditions.append("trade_date = DATE('now', '-1 day')")
                elif time_filter.startswith("recent_"):
                    days = time_filter.split("_")[1]
                    where_conditions.append(
                        f"trade_date >= DATE('now', '-{days} days')"
                    )

            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)

            # 添加排序和限制
            if intent == "ranking_query":
                sql += " ORDER BY pct_chg DESC LIMIT 10"
            else:
                sql += " ORDER BY trade_date DESC LIMIT 100"

            return sql

        except Exception as e:
            self.logger.error(f"SQL生成失败: {e}")
            raise ValueError(f"无法生成有效SQL: {e}")

    def execute_nlp_query(self, natural_query: str, **kwargs) -> Dict[str, Any]:
        """
        执行NLP查询

        Args:
            natural_query: 自然语言查询
            **kwargs: 其他参数

        Returns:
            Dict: 查询结果
        """
        start_time = datetime.now()

        try:
            # 检查缓存
            if self.cache_enabled:
                cache_key = self._generate_cache_key(natural_query)
                if cache_key in self.query_cache:
                    cached_result = self.query_cache[cache_key].copy()
                    cached_result["cache_hit"] = True
                    return cached_result

            # 解析查询
            parsed_result = self.parse_natural_query(natural_query)
            if not parsed_result["success"]:
                return parsed_result

            # 生成SQL
            sql = self.generate_sql(parsed_result)

            # 执行查询 - 完全复用UnifiedStorageManager
            environment = kwargs.get("environment", "dev")
            data = self.storage_manager.query_data(
                environment=environment,
                table_name="stock_daily",
                where_clause=None,  # SQL已包含WHERE条件
            )

            # 处理结果
            execution_time = (datetime.now() - start_time).total_seconds()

            result = {
                "success": True,
                "data": data,
                "sql_generated": sql,
                "query_confidence": parsed_result["confidence"],
                "execution_time": execution_time,
                "cache_hit": False,
                "record_count": len(data) if not data.empty else 0,
            }

            # 缓存结果
            if self.cache_enabled and len(data) < 1000:  # 只缓存小结果集
                self.query_cache[cache_key] = result.copy()

            return result

        except Exception as e:
            self.logger.error(f"NLP查询执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": (datetime.now() - start_time).total_seconds(),
            }

    def _generate_cache_key(self, query: str) -> str:
        """生成缓存键"""
        return hashlib.md5(query.encode("utf-8")).hexdigest()[:16]

    def get_supported_queries(self) -> List[str]:
        """获取支持的查询示例"""
        return [
            "查询平安银行今日股价",
            "显示万科A最近5天的成交量",
            "看看招商银行昨日涨幅",
            "贵州茅台股价统计",
            "显示平安银行最近股价数据",
        ]

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        cache_size = len(self.query_cache)

        return {
            "total_queries": getattr(self, "_total_queries", 0),
            "cache_size": cache_size,
            "cache_enabled": self.cache_enabled,
            "supported_stocks": len(self.stock_mapping),
            "confidence_threshold": self.confidence_threshold,
        }
