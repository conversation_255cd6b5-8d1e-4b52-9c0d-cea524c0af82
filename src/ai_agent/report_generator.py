#!/usr/bin/env python3
"""
简单报告生成器

MVP版本：基础HTML报告生成
复用率：85% - 基于UnifiedStorageManager + PriorityQueueManager + 简化报告引擎
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

try:
    from ..storage.unified_storage_manager import UnifiedStorageManager
    from ..storage.priority_queue_manager import PriorityQueueManager, TaskPriority
    from ..storage.conflict_resolution_engine import NotificationManager
    from ..utils.logger import get_logger
    from ..utils.time_utils import get_beijing_time_now
    from .ai_strategy import ReportGenerationStrategy
except ImportError:
    # 支持测试环境的绝对导入
    from src.storage.unified_storage_manager import UnifiedStorageManager
    from src.storage.priority_queue_manager import PriorityQueueManager, TaskPriority
    from src.storage.conflict_resolution_engine import NotificationManager
    from src.utils.logger import get_logger
    from src.utils.time_utils import get_beijing_time_now


class ReportGenerator:
    """
    简单报告生成器

    复用UnifiedStorageManager数据访问、PriorityQueueManager任务调度
    实现基础HTML报告生成功能
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化报告生成器

        Args:
            config: 配置字典
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config

        # 完全复用存储管理器和任务管理器
        self.storage_manager = UnifiedStorageManager(config)
        self.task_manager = PriorityQueueManager(config)

        # 复用通知管理器
        self.notification_manager = NotificationManager(self)

        # 报告配置
        self.report_config = config.get("report_generation", {})
        self.output_path = Path(self.report_config.get("output_path", "reports/"))
        self.template_path = Path(self.report_config.get("template_path", "templates/"))

        # 确保目录存在
        self.output_path.mkdir(parents=True, exist_ok=True)

        # 注册报告生成任务处理器
        self.task_manager.register_task_handler(
            "generate_report", self._handle_report_generation
        )

        # 简单的报告模板
        self.templates = {
            "daily_summary": self._get_daily_summary_template(),
            "stock_analysis": self._get_stock_analysis_template(),
            "basic_report": self._get_basic_report_template(),
        }

        self.logger.info("报告生成器初始化完成")

    def schedule_report(
        self,
        report_config: Dict[str, Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
    ) -> str:
        """
        调度报告生成任务
        完全复用PriorityQueueManager

        Args:
            report_config: 报告配置
            priority: 任务优先级

        Returns:
            str: 任务ID
        """
        try:
            task_id = self.task_manager.submit_task(
                task_type="generate_report",
                payload=report_config,
                priority=priority,
                timeout_seconds=self.report_config.get("generation_timeout", 300),
            )

            self.logger.info(f"报告生成任务已调度: {task_id}")
            return task_id

        except Exception as e:
            self.logger.error(f"报告任务调度失败: {e}")
            raise

    def generate_report(self, report_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成报告

        Args:
            report_config: 报告配置

        Returns:
            Dict: 生成结果
        """
        start_time = datetime.now()

        try:
            report_type = report_config.get("report_type", "basic_report")
            output_format = report_config.get("output_format", "html")

            # 获取数据
            data = self._fetch_report_data(report_config)
            if data is None or data.empty:
                return {
                    "success": False,
                    "error": "无法获取报告数据",
                    "report_type": report_type,
                }

            # 生成报告内容
            if report_type == "daily_summary":
                content = self._generate_daily_summary(data, report_config)
            elif report_type == "stock_analysis":
                content = self._generate_stock_analysis(data, report_config)
            else:
                content = self._generate_basic_report(data, report_config)

            # 保存报告
            file_path = self._save_report(content, report_type, output_format)

            generation_time = (datetime.now() - start_time).total_seconds()

            result = {
                "success": True,
                "report_type": report_type,
                "output_format": output_format,
                "file_path": str(file_path),
                "generation_time": generation_time,
                "data_rows": len(data),
                "generated_at": get_beijing_time_now(),
            }

            self.logger.info(f"报告生成成功: {file_path}")
            return result

        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "report_type": report_config.get("report_type", "unknown"),
                "generation_time": (datetime.now() - start_time).total_seconds(),
            }

    def _fetch_report_data(
        self, report_config: Dict[str, Any]
    ) -> Optional[pd.DataFrame]:
        """
        获取报告数据
        完全复用UnifiedStorageManager
        """
        try:
            data_source = report_config.get("data_source", "stock_daily")
            environment = report_config.get("environment", "dev")

            # 构建查询条件
            where_clause = self._build_where_clause(report_config)

            # 执行查询
            data = self.storage_manager.query_data(
                environment=environment,
                table_name=data_source,
                where_clause=where_clause,
            )

            return data

        except Exception as e:
            self.logger.error(f"获取报告数据失败: {e}")
            return None

    def _build_where_clause(self, report_config: Dict[str, Any]) -> Optional[str]:
        """构建WHERE查询条件"""
        conditions = []

        # 日期过滤
        if "start_date" in report_config:
            conditions.append(f"trade_date >= '{report_config['start_date']}'")
        if "end_date" in report_config:
            conditions.append(f"trade_date <= '{report_config['end_date']}'")

        # 股票代码过滤
        if "ts_codes" in report_config:
            codes = "', '".join(report_config["ts_codes"])
            conditions.append(f"ts_code IN ('{codes}')")
        elif "ts_code" in report_config:
            conditions.append(f"ts_code = '{report_config['ts_code']}'")

        return " AND ".join(conditions) if conditions else None

    def _generate_daily_summary(
        self, data: pd.DataFrame, config: Dict[str, Any]
    ) -> str:
        """生成日度摘要报告"""
        # 计算统计数据
        stats = self._calculate_market_stats(data)

        # 使用模板生成HTML
        template = self.templates["daily_summary"]

        content = template.format(
            report_date=config.get("report_date", datetime.now().strftime("%Y-%m-%d")),
            total_stocks=stats["total_stocks"],
            avg_price_change=stats["avg_price_change"],
            total_volume=stats["total_volume"],
            gainers_count=stats["gainers_count"],
            losers_count=stats["losers_count"],
            top_gainers=self._format_top_stocks(stats["top_gainers"]),
            top_losers=self._format_top_stocks(stats["top_losers"]),
            generated_time=get_beijing_time_now(),
        )

        return content

    def _generate_stock_analysis(
        self, data: pd.DataFrame, config: Dict[str, Any]
    ) -> str:
        """生成股票分析报告"""
        # 计算技术指标
        analysis = self._calculate_technical_indicators(data)

        template = self.templates["stock_analysis"]

        content = template.format(
            stock_code=config.get("ts_code", "未指定"),
            analysis_period=f"{config.get('start_date', '')} 至 {config.get('end_date', '')}",
            current_price=analysis["current_price"],
            price_change=analysis["price_change"],
            avg_volume=analysis["avg_volume"],
            volatility=analysis["volatility"],
            price_trend=analysis["price_trend"],
            volume_trend=analysis["volume_trend"],
            generated_time=get_beijing_time_now(),
        )

        return content

    def _generate_basic_report(self, data: pd.DataFrame, config: Dict[str, Any]) -> str:
        """生成基础报告"""
        template = self.templates["basic_report"]

        # 基础统计
        basic_stats = {
            "total_records": len(data),
            "date_range": (
                f"{data['trade_date'].min()} 至 {data['trade_date'].max()}"
                if not data.empty
                else "无数据"
            ),
            "avg_close": data["close"].mean() if "close" in data.columns else 0,
            "max_close": data["close"].max() if "close" in data.columns else 0,
            "min_close": data["close"].min() if "close" in data.columns else 0,
        }

        # 生成数据表格
        data_table = self._generate_data_table(data.head(20))  # 只显示前20行

        content = template.format(
            report_title=config.get("title", "基础数据报告"),
            total_records=basic_stats["total_records"],
            date_range=basic_stats["date_range"],
            avg_close=f"{basic_stats['avg_close']:.2f}",
            max_close=f"{basic_stats['max_close']:.2f}",
            min_close=f"{basic_stats['min_close']:.2f}",
            data_table=data_table,
            generated_time=get_beijing_time_now(),
        )

        return content

    def _calculate_market_stats(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算市场统计数据"""
        if data.empty:
            return {
                "total_stocks": 0,
                "avg_price_change": 0,
                "total_volume": 0,
                "gainers_count": 0,
                "losers_count": 0,
                "top_gainers": [],
                "top_losers": [],
            }

        # 基础统计
        total_stocks = (
            data["ts_code"].nunique() if "ts_code" in data.columns else len(data)
        )
        avg_price_change = data["pct_chg"].mean() if "pct_chg" in data.columns else 0
        total_volume = data["vol"].sum() if "vol" in data.columns else 0

        # 涨跌统计
        gainers_count = (
            len(data[data["pct_chg"] > 0]) if "pct_chg" in data.columns else 0
        )
        losers_count = (
            len(data[data["pct_chg"] < 0]) if "pct_chg" in data.columns else 0
        )

        # 涨跌幅排行
        if "pct_chg" in data.columns:
            top_gainers = data.nlargest(5, "pct_chg")[["ts_code", "pct_chg"]].to_dict(
                "records"
            )
            top_losers = data.nsmallest(5, "pct_chg")[["ts_code", "pct_chg"]].to_dict(
                "records"
            )
        else:
            top_gainers = []
            top_losers = []

        return {
            "total_stocks": total_stocks,
            "avg_price_change": round(avg_price_change, 2),
            "total_volume": int(total_volume),
            "gainers_count": gainers_count,
            "losers_count": losers_count,
            "top_gainers": top_gainers,
            "top_losers": top_losers,
        }

    def _calculate_technical_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算技术指标"""
        if data.empty or "close" not in data.columns:
            return {
                "current_price": 0,
                "price_change": 0,
                "avg_volume": 0,
                "volatility": 0,
                "price_trend": "无数据",
                "volume_trend": "无数据",
            }

        # 确保按日期排序
        data_sorted = (
            data.sort_values("trade_date") if "trade_date" in data.columns else data
        )

        current_price = data_sorted["close"].iloc[-1]
        previous_price = (
            data_sorted["close"].iloc[-2] if len(data_sorted) > 1 else current_price
        )
        price_change = (
            ((current_price - previous_price) / previous_price * 100)
            if previous_price != 0
            else 0
        )

        avg_volume = data_sorted["vol"].mean() if "vol" in data_sorted.columns else 0
        volatility = data_sorted["close"].std() if len(data_sorted) > 1 else 0

        # 简单趋势判断
        if len(data_sorted) >= 5:
            recent_prices = data_sorted["close"].tail(5)
            price_trend = (
                "上升" if recent_prices.iloc[-1] > recent_prices.iloc[0] else "下降"
            )

            if "vol" in data_sorted.columns:
                recent_volumes = data_sorted["vol"].tail(5)
                volume_trend = (
                    "增加"
                    if recent_volumes.iloc[-1] > recent_volumes.mean()
                    else "减少"
                )
            else:
                volume_trend = "无数据"
        else:
            price_trend = "数据不足"
            volume_trend = "数据不足"

        return {
            "current_price": round(current_price, 2),
            "price_change": round(price_change, 2),
            "avg_volume": int(avg_volume),
            "volatility": round(volatility, 2),
            "price_trend": price_trend,
            "volume_trend": volume_trend,
        }

    def _format_top_stocks(self, stocks_list: List[Dict]) -> str:
        """格式化股票列表为HTML"""
        if not stocks_list:
            return "<li>无数据</li>"

        html_items = []
        for stock in stocks_list:
            ts_code = stock.get("ts_code", "")
            pct_chg = stock.get("pct_chg", 0)
            html_items.append(f"<li>{ts_code}: {pct_chg:.2f}%</li>")

        return "\n".join(html_items)

    def _generate_data_table(self, data: pd.DataFrame) -> str:
        """生成数据表格HTML"""
        if data.empty:
            return "<tr><td colspan='100%'>无数据</td></tr>"

        # 生成表头
        headers = "</th><th>".join(data.columns)
        header_row = f"<tr><th>{headers}</th></tr>"

        # 生成数据行
        rows = []
        for _, row in data.iterrows():
            cells = "</td><td>".join([str(val) for val in row.values])
            rows.append(f"<tr><td>{cells}</td></tr>")

        return header_row + "\n" + "\n".join(rows)

    def _save_report(self, content: str, report_type: str, output_format: str) -> Path:
        """保存报告文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{report_type}_{timestamp}.{output_format}"
        file_path = self.output_path / filename

        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)

        return file_path

    def _handle_report_generation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """处理报告生成任务"""
        return self.generate_report(payload)

    def distribute_report(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分发报告
        复用NotificationManager
        """
        try:
            notification_data = {
                "notification_type": "report_distribution",
                "report_id": report_data.get("report_id", "unknown"),
                "report_type": report_data.get("report_type", "unknown"),
                "file_path": report_data.get("file_path", ""),
                "recipients": report_data.get("recipients", []),
                "generated_at": get_beijing_time_now(),
            }

            self.notification_manager.send_notification(notification_data)

            return {
                "success": True,
                "distributed_to": len(report_data.get("recipients", [])),
            }

        except Exception as e:
            self.logger.error(f"报告分发失败: {e}")
            return {"success": False, "error": str(e)}

    def get_report_templates(self) -> List[str]:
        """获取可用报告模板"""
        return list(self.templates.keys())

    def _get_daily_summary_template(self) -> str:
        """日度摘要模板"""
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>日度市场摘要</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
        .stats {{ display: flex; justify-content: space-around; margin: 20px 0; }}
        .stat-box {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
        ul {{ list-style-type: none; padding: 0; }}
        li {{ padding: 5px; border-bottom: 1px solid #eee; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>日度市场摘要</h1>
        <p>报告日期: {report_date}</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>{total_stocks}</h3>
            <p>交易股票数</p>
        </div>
        <div class="stat-box">
            <h3>{avg_price_change:.2f}%</h3>
            <p>平均涨跌幅</p>
        </div>
        <div class="stat-box">
            <h3>{total_volume:,}</h3>
            <p>总成交量</p>
        </div>
    </div>
    
    <div style="display: flex; justify-content: space-between;">
        <div style="width: 45%;">
            <h3>涨幅榜前5 ({gainers_count}只上涨)</h3>
            <ul>{top_gainers}</ul>
        </div>
        <div style="width: 45%;">
            <h3>跌幅榜前5 ({losers_count}只下跌)</h3>
            <ul>{top_losers}</ul>
        </div>
    </div>
    
    <div style="margin-top: 30px; font-size: 12px; color: #666;">
        生成时间: {generated_time}
    </div>
</body>
</html>
        """

    def _get_stock_analysis_template(self) -> str:
        """股票分析模板"""
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>股票分析报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
        .analysis-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }}
        .analysis-box {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>股票分析报告</h1>
        <p>股票代码: {stock_code}</p>
        <p>分析期间: {analysis_period}</p>
    </div>
    
    <div class="analysis-grid">
        <div class="analysis-box">
            <h3>价格信息</h3>
            <p>当前价格: ¥{current_price}</p>
            <p>价格变化: {price_change:.2f}%</p>
            <p>价格趋势: {price_trend}</p>
        </div>
        <div class="analysis-box">
            <h3>成交量信息</h3>
            <p>平均成交量: {avg_volume:,}</p>
            <p>成交量趋势: {volume_trend}</p>
        </div>
        <div class="analysis-box">
            <h3>波动性分析</h3>
            <p>价格波动率: {volatility:.2f}</p>
        </div>
    </div>
    
    <div style="margin-top: 30px; font-size: 12px; color: #666;">
        生成时间: {generated_time}
    </div>
</body>
</html>
        """

    def _get_basic_report_template(self) -> str:
        """基础报告模板"""
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{report_title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
        .summary {{ background-color: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{report_title}</h1>
    </div>
    
    <div class="summary">
        <h3>数据摘要</h3>
        <p>总记录数: {total_records}</p>
        <p>数据范围: {date_range}</p>
        <p>平均收盘价: ¥{avg_close}</p>
        <p>最高价: ¥{max_close}</p>
        <p>最低价: ¥{min_close}</p>
    </div>
    
    <h3>数据详情 (前20条)</h3>
    <table>
        {data_table}
    </table>
    
    <div style="margin-top: 30px; font-size: 12px; color: #666;">
        生成时间: {generated_time}
    </div>
</body>
</html>
        """
