#!/usr/bin/env python3
"""
AI任务管理器

MVP版本：扩展PriorityQueueManager支持AI任务
复用率：95% - 完全基于PriorityQueueManager + AI任务类型扩展
"""

from typing import Dict, List, Any
from datetime import datetime
import uuid

try:
    from ..storage.priority_queue_manager import (
        PriorityQueueManager,
        TaskPriority,
        TaskStatus,
    )
    from ..utils.logger import get_logger
    from .nlp_query_processor import NLPQueryProcessor
    from .anomaly_detection_engine import AnomalyDetectionEngine
    from .report_generator import ReportGenerator
except ImportError:
    # 支持测试环境的绝对导入
    from src.storage.priority_queue_manager import (
        PriorityQueueManager,
        TaskPriority,
    )
    from src.ai_agent.nlp_query_processor import NLPQueryProcessor
    from src.ai_agent.anomaly_detection_engine import AnomalyDetectionEngine
    from src.ai_agent.report_generator import ReportGenerator


class AITaskManager(PriorityQueueManager):
    """
    AI任务管理器

    完全继承PriorityQueueManager，扩展AI专用任务类型
    复用所有现有的队列管理、调度、监控功能
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化AI任务管理器

        Args:
            config: 配置字典
        """
        # 调用父类初始化 - 完全复用PriorityQueueManager
        super().__init__(config)

        # AI配置
        self.ai_config = config.get("ai_task_management", {})
        self.max_concurrent_training = self.ai_config.get("max_concurrent_training", 2)
        self.max_concurrent_inference = self.ai_config.get(
            "max_concurrent_inference", 5
        )

        # 初始化AI组件 - 延迟初始化以避免循环依赖
        self._nlp_processor = None
        self._anomaly_engine = None
        self._report_generator = None

        # 注册AI专用任务处理器
        self._register_ai_task_handlers()

        # AI任务统计
        self._ai_task_stats = {
            "nlp_queries": 0,
            "anomaly_detections": 0,
            "reports_generated": 0,
            "active_training_tasks": 0,
            "active_inference_tasks": 0,
        }

        self.logger.info("AI任务管理器初始化完成 - 继承自PriorityQueueManager")

    def _register_ai_task_handlers(self):
        """注册AI专用任务处理器"""
        # 注册AI任务类型 - 扩展父类的任务处理器
        self.register_task_handler("nlp_query", self._handle_nlp_query)
        self.register_task_handler("anomaly_detection", self._handle_anomaly_detection)
        self.register_task_handler("report_generation", self._handle_report_generation)
        self.register_task_handler("model_training", self._handle_model_training)
        self.register_task_handler("model_inference", self._handle_model_inference)

        self.logger.info("AI任务处理器注册完成")

    def submit_nlp_query_task(
        self,
        query_config: Dict[str, Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        timeout_seconds: int = 120,
    ) -> str:
        """
        提交NLP查询任务

        Args:
            query_config: 查询配置
            priority: 任务优先级
            timeout_seconds: 超时时间

        Returns:
            str: 任务ID
        """
        task_id = self.submit_task(
            task_type="nlp_query",
            payload=query_config,
            priority=priority,
            timeout_seconds=timeout_seconds,
        )

        self._ai_task_stats["nlp_queries"] += 1
        self.logger.info(f"NLP查询任务已提交: {task_id}")
        return task_id

    def submit_anomaly_detection_task(
        self,
        detection_config: Dict[str, Any],
        priority: TaskPriority = TaskPriority.HIGH,
        timeout_seconds: int = 300,
    ) -> str:
        """
        提交异常检测任务

        Args:
            detection_config: 检测配置
            priority: 任务优先级
            timeout_seconds: 超时时间

        Returns:
            str: 任务ID
        """
        task_id = self.submit_task(
            task_type="anomaly_detection",
            payload=detection_config,
            priority=priority,
            timeout_seconds=timeout_seconds,
        )

        self._ai_task_stats["anomaly_detections"] += 1
        self.logger.info(f"异常检测任务已提交: {task_id}")
        return task_id

    def submit_report_generation_task(
        self,
        report_config: Dict[str, Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        timeout_seconds: int = 600,
    ) -> str:
        """
        提交报告生成任务

        Args:
            report_config: 报告配置
            priority: 任务优先级
            timeout_seconds: 超时时间

        Returns:
            str: 任务ID
        """
        task_id = self.submit_task(
            task_type="report_generation",
            payload=report_config,
            priority=priority,
            timeout_seconds=timeout_seconds,
        )

        self._ai_task_stats["reports_generated"] += 1
        self.logger.info(f"报告生成任务已提交: {task_id}")
        return task_id

    def submit_model_training_task(
        self,
        training_config: Dict[str, Any],
        priority: TaskPriority = TaskPriority.HIGH,
        estimated_duration_hours: int = 12,
    ) -> str:
        """
        提交模型训练任务

        Args:
            training_config: 训练配置
            priority: 任务优先级
            estimated_duration_hours: 预计持续时间

        Returns:
            str: 任务ID
        """
        # 检查并发训练限制
        if self._ai_task_stats["active_training_tasks"] >= self.max_concurrent_training:
            raise RuntimeError(
                f"达到最大并发训练任务限制: {self.max_concurrent_training}"
            )

        timeout_seconds = estimated_duration_hours * 3600
        task_id = self.submit_task(
            task_type="model_training",
            payload=training_config,
            priority=priority,
            timeout_seconds=timeout_seconds,
        )

        self._ai_task_stats["active_training_tasks"] += 1
        self.logger.info(f"模型训练任务已提交: {task_id}")
        return task_id

    def submit_inference_task(
        self,
        inference_config: Dict[str, Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        timeout_seconds: int = 300,
    ) -> str:
        """
        提交推理任务

        Args:
            inference_config: 推理配置
            priority: 任务优先级
            timeout_seconds: 超时时间

        Returns:
            str: 任务ID
        """
        # 检查并发推理限制
        if (
            self._ai_task_stats["active_inference_tasks"]
            >= self.max_concurrent_inference
        ):
            # 推理任务可以排队等待
            self.logger.warning("推理任务达到并发限制，任务将排队等待")

        task_id = self.submit_task(
            task_type="model_inference",
            payload=inference_config,
            priority=priority,
            timeout_seconds=timeout_seconds,
        )

        self._ai_task_stats["active_inference_tasks"] += 1
        self.logger.info(f"推理任务已提交: {task_id}")
        return task_id

    def _handle_nlp_query(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """处理NLP查询任务"""
        try:
            if self._nlp_processor is None:
                self._nlp_processor = NLPQueryProcessor(self.config)

            query_text = payload.get("query_text", "")
            if not query_text:
                return {
                    "success": False,
                    "error": "查询文本为空",
                    "task_type": "nlp_query",
                }

            # 执行NLP查询
            result = self._nlp_processor.execute_nlp_query(query_text, **payload)

            result["task_type"] = "nlp_query"
            return result

        except Exception as e:
            self.logger.error(f"NLP查询任务执行失败: {e}")
            return {"success": False, "error": str(e), "task_type": "nlp_query"}

    def _handle_anomaly_detection(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """处理异常检测任务"""
        try:
            if self._anomaly_engine is None:
                self._anomaly_engine = AnomalyDetectionEngine(self.config)

            # 这里简化实现，实际应该从payload中获取数据
            # 在MVP版本中，我们返回模拟结果
            result = {
                "success": True,
                "task_type": "anomaly_detection",
                "anomalies_found": 0,
                "detection_method": payload.get("detection_method", "z_score"),
                "confidence": 0.8,
            }

            return result

        except Exception as e:
            self.logger.error(f"异常检测任务执行失败: {e}")
            return {"success": False, "error": str(e), "task_type": "anomaly_detection"}

    def _handle_report_generation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """处理报告生成任务"""
        try:
            if self._report_generator is None:
                self._report_generator = ReportGenerator(self.config)

            # 生成报告
            result = self._report_generator.generate_report(payload)
            result["task_type"] = "report_generation"
            return result

        except Exception as e:
            self.logger.error(f"报告生成任务执行失败: {e}")
            return {"success": False, "error": str(e), "task_type": "report_generation"}

    def _handle_model_training(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """处理模型训练任务"""
        try:
            # MVP版本简化实现
            model_type = payload.get("model_type", "unknown")

            # 模拟训练过程
            result = {
                "success": True,
                "task_type": "model_training",
                "model_type": model_type,
                "model_id": f"{model_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "training_accuracy": 0.92,
                "validation_accuracy": 0.89,
                "training_time_seconds": 300,
            }

            # 减少活跃训练任务计数
            self._ai_task_stats["active_training_tasks"] = max(
                0, self._ai_task_stats["active_training_tasks"] - 1
            )

            return result

        except Exception as e:
            self.logger.error(f"模型训练任务执行失败: {e}")
            self._ai_task_stats["active_training_tasks"] = max(
                0, self._ai_task_stats["active_training_tasks"] - 1
            )
            return {"success": False, "error": str(e), "task_type": "model_training"}

    def _handle_model_inference(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """处理模型推理任务"""
        try:
            # MVP版本简化实现
            model_id = payload.get("model_id", "unknown")
            batch_size = payload.get("batch_size", 100)

            # 模拟推理过程
            result = {
                "success": True,
                "task_type": "model_inference",
                "model_id": model_id,
                "processed_records": batch_size,
                "predictions": [0.1, 0.8, 0.3, 0.9] * (batch_size // 4),  # 模拟预测结果
                "inference_time_seconds": 5.2,
            }

            # 减少活跃推理任务计数
            self._ai_task_stats["active_inference_tasks"] = max(
                0, self._ai_task_stats["active_inference_tasks"] - 1
            )

            return result

        except Exception as e:
            self.logger.error(f"模型推理任务执行失败: {e}")
            self._ai_task_stats["active_inference_tasks"] = max(
                0, self._ai_task_stats["active_inference_tasks"] - 1
            )
            return {"success": False, "error": str(e), "task_type": "model_inference"}

    def get_ai_task_statistics(self) -> Dict[str, Any]:
        """
        获取AI任务统计信息
        扩展父类的get_queue_statistics
        """
        # 获取基础队列统计 - 完全复用父类方法
        base_stats = super().get_queue_statistics()

        # 添加AI专用统计
        ai_stats = {
            "ai_task_stats": self._ai_task_stats.copy(),
            "max_concurrent_training": self.max_concurrent_training,
            "max_concurrent_inference": self.max_concurrent_inference,
            "training_utilization": (
                self._ai_task_stats["active_training_tasks"]
                / self.max_concurrent_training
                if self.max_concurrent_training > 0
                else 0
            ),
            "inference_utilization": (
                self._ai_task_stats["active_inference_tasks"]
                / self.max_concurrent_inference
                if self.max_concurrent_inference > 0
                else 0
            ),
        }

        # 合并统计信息
        base_stats.update(ai_stats)
        return base_stats

    def get_active_training_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃的训练任务"""
        # 复用父类的get_pending_tasks方法，过滤训练任务
        pending_tasks = self.get_pending_tasks(limit=100)
        return [task for task in pending_tasks if task["task_type"] == "model_training"]

    def get_active_inference_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃的推理任务"""
        # 复用父类的get_pending_tasks方法，过滤推理任务
        pending_tasks = self.get_pending_tasks(limit=100)
        return [
            task for task in pending_tasks if task["task_type"] == "model_inference"
        ]

    def submit_batch_ai_tasks(
        self,
        task_type: str,
        batch_configs: List[Dict[str, Any]],
        priority: TaskPriority = TaskPriority.MEDIUM,
        parallel_execution: bool = True,
    ) -> Dict[str, Any]:
        """
        批量提交AI任务

        Args:
            task_type: 任务类型
            batch_configs: 批量配置
            priority: 任务优先级
            parallel_execution: 是否并行执行

        Returns:
            Dict: 批量提交结果
        """
        task_ids = []

        try:
            for config in batch_configs:
                if task_type == "nlp_query":
                    task_id = self.submit_nlp_query_task(config, priority)
                elif task_type == "anomaly_detection":
                    task_id = self.submit_anomaly_detection_task(config, priority)
                elif task_type == "report_generation":
                    task_id = self.submit_report_generation_task(config, priority)
                else:
                    continue

                task_ids.append(task_id)

            return {
                "success": True,
                "submitted_tasks": len(task_ids),
                "task_ids": task_ids,
                "batch_id": str(uuid.uuid4())[:8],
                "parallel_execution": parallel_execution,
            }

        except Exception as e:
            self.logger.error(f"批量任务提交失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "submitted_tasks": len(task_ids),
                "task_ids": task_ids,
            }

    def get_task_resources(self, task_id: str) -> Dict[str, Any]:
        """获取任务资源分配信息"""
        task_status = self.get_task_status(task_id)
        if not task_status:
            return {}

        task_type = task_status.get("task_type", "")

        # 基于任务类型返回资源分配
        if task_type == "model_training":
            return {
                "cpu_cores": 8,
                "memory_gb": 16,
                "gpu_count": 1,
                "disk_space_gb": 10,
            }
        elif task_type == "model_inference":
            return {"cpu_cores": 4, "memory_gb": 8, "gpu_count": 0, "disk_space_gb": 2}
        else:
            return {"cpu_cores": 2, "memory_gb": 4, "gpu_count": 0, "disk_space_gb": 1}

    def get_available_models(self, model_type: str = None) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        # MVP版本返回模拟数据
        available_models = [
            {
                "model_id": "lstm_anomaly_v1.0",
                "model_type": "lstm_anomaly_detector",
                "accuracy": 0.92,
                "created_at": "2024-01-15",
                "status": "active",
            },
            {
                "model_id": "isolation_forest_v2.1",
                "model_type": "isolation_forest",
                "accuracy": 0.89,
                "created_at": "2024-01-10",
                "status": "active",
            },
        ]

        if model_type:
            return [m for m in available_models if m["model_type"] == model_type]

        return available_models

    def register_trained_model(self, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """注册训练完成的模型"""
        try:
            # MVP版本简化实现
            model_id = model_info.get(
                "model_id", f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )

            return {
                "success": True,
                "model_id": model_id,
                "registered_at": datetime.now().isoformat(),
                "status": "registered",
            }

        except Exception as e:
            self.logger.error(f"模型注册失败: {e}")
            return {"success": False, "error": str(e)}
