#!/usr/bin/env python3
"""
AI智能代理系统模块

Feature 5 MVP实现 - 基础AI功能
复用现有Feature 4组件，实现最小可行产品版本

核心组件：
- AIStrategy: AI策略基类（基于RoutingStrategy模式）
- NLPQueryProcessor: 简化自然语言查询处理器
- AnomalyDetectionEngine: 基础异常检测引擎
- ReportGenerator: 简单HTML报告生成器
- AITaskManager: AI任务管理器（扩展PriorityQueueManager）
"""

from .ai_strategy import AIStrategy
from .nlp_query_processor import NLPQueryProcessor
from .anomaly_detection_engine import AnomalyDetectionEngine
from .report_generator import ReportGenerator
from .ai_task_manager import AITaskManager

__version__ = "1.0.0"
__all__ = [
    "AIStrategy",
    "NLPQueryProcessor",
    "AnomalyDetectionEngine",
    "ReportGenerator",
    "AITaskManager",
]
