"""
增量采集助手

提供增量采集的核心逻辑，包括增量时间范围计算、历史状态查询、
交易日历集成等功能。实现严格无重叠的增量采集策略。

Author: AQUA Team
Date: 2025-01-26
Version: 1.0
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
import os

logger = logging.getLogger(__name__)


class IncrementalCollectionHelper:
    """轻量级增量采集助手 - 基于交易日历的精准增量"""
    
    def __init__(self):
        """初始化增量采集助手"""
        self.history_manager = None
        self.trading_calendar = None
        self.config_loader = None
        self._initialized = False
        
    def _get_history_manager(self):
        """延迟初始化历史管理器"""
        if self.history_manager is None:
            try:
                from src.data_import.import_history_manager import ImportHistoryManager
                self.history_manager = ImportHistoryManager()
                logger.debug("增量采集助手：历史管理器初始化成功")
            except ImportError as e:
                logger.error(f"增量采集助手：无法导入历史管理器 - {e}")
                raise
        return self.history_manager
    
    def _get_trading_calendar(self):
        """延迟初始化交易日历管理器"""
        if self.trading_calendar is None:
            try:
                from src.data_import.trading_calendar_manager import TradingCalendarManager
                self.trading_calendar = TradingCalendarManager()
                logger.debug("增量采集助手：交易日历管理器初始化成功")
            except ImportError as e:
                logger.error(f"增量采集助手：无法导入交易日历管理器 - {e}")
                raise
        return self.trading_calendar
    
    def _get_config_loader(self):
        """延迟初始化配置加载器"""
        if self.config_loader is None:
            try:
                from src.utils.config_loader import ConfigLoader
                self.config_loader = ConfigLoader()
                logger.debug("增量采集助手：配置加载器初始化成功")
            except ImportError as e:
                logger.error(f"增量采集助手：无法导入配置加载器 - {e}")
                raise
        return self.config_loader
    
    def calculate_incremental_range(self, symbols: List[str], data_type: str, 
                                  freq: str = 'daily', source: str = 'TUSHARE') -> Optional[Dict[str, str]]:
        """
        计算严格无重叠的增量时间范围
        
        Args:
            symbols: 标的代码列表，如 ['000001.SZ', '600000.SH'] 或 ['RB2501', 'CU2501']
            data_type: 数据类型，'stocks' 或 'futures'
            freq: 数据频率，'daily', '1min', '5min' 等
            source: 数据源，默认 'TUSHARE'
            
        Returns:
            Optional[Dict[str, str]]: 增量时间范围 {'start_date': 'YYYY-MM-DD', 'end_date': 'YYYY-MM-DD'}
                                    如果无新数据需要采集，返回None
        """
        logger.info(f"计算增量采集范围: symbols={symbols}, data_type={data_type}, freq={freq}, source={source}")
        
        try:
            # 查询最后采集时间
            last_dates = self._get_last_collection_dates(symbols, data_type, freq, source)
            
            # 如果没有历史记录，使用settings.toml默认配置
            if not any(last_dates.values()):
                logger.info("未找到历史采集记录，使用默认配置")
                return self._get_default_range_from_settings(data_type, freq)
            
            # 计算增量范围（基于交易日历）
            incremental_range = self._calculate_range_from_history(
                last_dates, symbols, data_type, freq
            )
            
            if incremental_range:
                logger.info(f"计算得到增量范围: {incremental_range}")
            else:
                logger.info("无新数据需要采集")
                
            return incremental_range
            
        except Exception as e:
            logger.error(f"计算增量采集范围失败: {e}")
            # 发生错误时返回None，让调用方决定是否回退到全量采集
            return None
    
    def _get_last_collection_dates(self, symbols: List[str], data_type: str, 
                                 freq: str, source: str) -> Dict[str, Optional[str]]:
        """
        获取每个标的的最后采集日期
        
        Args:
            symbols: 标的代码列表
            data_type: 数据类型
            freq: 数据频率
            source: 数据源
            
        Returns:
            Dict[str, Optional[str]]: {symbol: last_date} 映射
        """
        history_manager = self._get_history_manager()
        last_dates = {}
        
        for symbol in symbols:
            try:
                # 查询最后导入日期
                last_date = history_manager.get_last_import_date(
                    symbol=symbol,
                    data_type=data_type,
                    source=source,
                    freq=freq
                )
                last_dates[symbol] = last_date
                logger.debug(f"标的 {symbol} 最后采集日期: {last_date}")
                
            except Exception as e:
                logger.warning(f"查询标的 {symbol} 历史记录失败: {e}")
                last_dates[symbol] = None
        
        return last_dates
    
    def _calculate_range_from_history(self, last_dates: Dict[str, Optional[str]], 
                                    symbols: List[str], data_type: str, 
                                    freq: str) -> Optional[Dict[str, str]]:
        """
        基于历史记录计算增量范围
        
        Args:
            last_dates: 最后采集日期映射
            symbols: 标的代码列表
            data_type: 数据类型
            freq: 数据频率
            
        Returns:
            Optional[Dict[str, str]]: 增量时间范围
        """
        # 过滤出有效的最后日期
        valid_dates = [date for date in last_dates.values() if date]
        
        if not valid_dates:
            return None
        
        # 使用最早的最后采集日期作为基准（保守策略）
        min_last_date = min(valid_dates)
        logger.debug(f"使用最早的最后采集日期作为基准: {min_last_date}")
        
        # 获取下一个交易日
        market_type = 'STOCK' if data_type == 'stocks' else 'FUTURES'
        exchange_code = self._get_exchange_code(symbols[0], data_type)
        
        trading_calendar = self._get_trading_calendar()
        next_trading_date = trading_calendar.get_next_trading_date(
            min_last_date, market_type, exchange_code
        )
        
        if not next_trading_date:
            logger.info(f"未找到 {min_last_date} 之后的交易日")
            return None
        
        # 检查是否已经是最新数据
        today = datetime.now().strftime('%Y-%m-%d')
        if next_trading_date > today:
            logger.info(f"下一交易日 {next_trading_date} 超过今天 {today}，无新数据")
            return None
        
        return {
            'start_date': next_trading_date,
            'end_date': today
        }
    
    def _get_default_range_from_settings(self, data_type: str, freq: str) -> Dict[str, str]:
        """
        从settings.toml获取默认时间范围

        Args:
            data_type: 数据类型
            freq: 数据频率

        Returns:
            Dict[str, str]: 默认时间范围
        """
        try:
            config_loader = self._get_config_loader()
            config = config_loader.get_config("test")  # 使用test环境配置

            # 根据数据类型和频率获取默认天数
            if freq in ['1min', '5min', '15min', '30min', '60min']:
                # 分钟数据默认采集较短时间
                default_days = config.get('data_collection', {}).get('default_days', {}).get('minutes', 7)
            else:
                # 日线数据根据类型设置
                data_collection_config = config.get('data_collection', {})
                default_days_config = data_collection_config.get('default_days', {})

                if data_type == 'stocks':
                    default_days = default_days_config.get('stocks', 30)
                elif data_type == 'futures':
                    default_days = default_days_config.get('futures', 60)
                else:
                    default_days = default_days_config.get('stocks', 30)  # 默认使用股票配置

            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=default_days)).strftime('%Y-%m-%d')

            logger.info(f"使用默认配置: {data_type} {freq} -> {default_days}天 ({start_date} to {end_date})")

            return {
                'start_date': start_date,
                'end_date': end_date
            }

        except Exception as e:
            logger.error(f"获取默认配置失败: {e}")
            # 使用硬编码的默认值
            if freq in ['1min', '5min', '15min', '30min', '60min']:
                default_days = 7  # 分钟数据默认7天
            elif data_type == 'futures':
                default_days = 60  # 期货数据默认60天
            else:
                default_days = 30  # 股票数据默认30天

            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=default_days)).strftime('%Y-%m-%d')

            logger.info(f"使用硬编码默认值: {data_type} {freq} -> {default_days}天")

            return {
                'start_date': start_date,
                'end_date': end_date
            }
    
    def _get_exchange_code(self, symbol: str, data_type: str) -> str:
        """
        根据标的代码获取交易所代码
        
        Args:
            symbol: 标的代码
            data_type: 数据类型
            
        Returns:
            str: 交易所代码
        """
        if data_type == 'stocks':
            # 股票交易所判断
            if symbol.endswith('.SH'):
                return 'SSE'  # 上海证券交易所
            elif symbol.endswith('.SZ'):
                return 'SZSE'  # 深圳证券交易所
            else:
                # 默认上交所
                return 'SSE'
        else:
            # 期货交易所映射
            futures_exchange_map = {
                # 上海期货交易所
                'RB': 'SHFE', 'CU': 'SHFE', 'AL': 'SHFE', 'ZN': 'SHFE', 
                'PB': 'SHFE', 'NI': 'SHFE', 'SN': 'SHFE', 'AU': 'SHFE', 'AG': 'SHFE',
                'RU': 'SHFE', 'BU': 'SHFE', 'HC': 'SHFE', 'SS': 'SHFE', 'WR': 'SHFE',
                
                # 中金所
                'IF': 'CFFEX', 'IC': 'CFFEX', 'IH': 'CFFEX', 'T': 'CFFEX', 'TF': 'CFFEX',
                
                # 大商所
                'A': 'DCE', 'B': 'DCE', 'C': 'DCE', 'CS': 'DCE', 'I': 'DCE', 'J': 'DCE',
                'JM': 'DCE', 'L': 'DCE', 'M': 'DCE', 'P': 'DCE', 'PP': 'DCE', 'V': 'DCE',
                'Y': 'DCE', 'JD': 'DCE', 'FB': 'DCE', 'BB': 'DCE', 'LH': 'DCE',
                
                # 郑商所
                'CF': 'CZCE', 'CY': 'CZCE', 'FG': 'CZCE', 'MA': 'CZCE', 'OI': 'CZCE',
                'RM': 'CZCE', 'RS': 'CZCE', 'SF': 'CZCE', 'SM': 'CZCE', 'SR': 'CZCE',
                'TA': 'CZCE', 'WH': 'CZCE', 'ZC': 'CZCE', 'AP': 'CZCE', 'CJ': 'CZCE',
                'UR': 'CZCE', 'SA': 'CZCE', 'PF': 'CZCE'
            }
            
            # 提取品种代码（去掉数字和月份）
            product_code = ''.join([c for c in symbol if c.isalpha()]).upper()
            
            exchange = futures_exchange_map.get(product_code, 'SHFE')  # 默认上期所
            logger.debug(f"期货品种 {symbol} -> 产品代码 {product_code} -> 交易所 {exchange}")
            
            return exchange
    
    def should_use_incremental(self, symbols: List[str], data_type: str, 
                             freq: str = 'daily', **kwargs) -> bool:
        """
        判断是否应该使用增量采集
        
        Args:
            symbols: 标的代码列表
            data_type: 数据类型
            freq: 数据频率
            **kwargs: 其他参数
            
        Returns:
            bool: True表示建议使用增量采集，False表示建议全量采集
        """
        try:
            # 如果用户明确指定了时间范围，不建议使用增量采集
            if 'start_date' in kwargs and 'end_date' in kwargs:
                logger.info("用户指定了时间范围，不建议使用增量采集")
                return False
            
            # 检查是否有历史记录
            history_manager = self._get_history_manager()
            has_history = False
            
            for symbol in symbols:
                last_date = history_manager.get_last_import_date(
                    symbol=symbol,
                    data_type=data_type,
                    source=kwargs.get('source', 'TUSHARE'),
                    freq=freq
                )
                if last_date:
                    has_history = True
                    break
            
            if has_history:
                logger.info("找到历史记录，建议使用增量采集")
                return True
            else:
                logger.info("未找到历史记录，建议首次全量采集")
                return False
                
        except Exception as e:
            logger.warning(f"判断是否使用增量采集失败: {e}")
            return False
