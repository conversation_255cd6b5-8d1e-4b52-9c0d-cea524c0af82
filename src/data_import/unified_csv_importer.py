#!/usr/bin/env python3
"""
统一CSV导入器（重构版）

基于数据字典权威性的统一CSV导入解决方案
支持多种数据源的标准化导入到DATA_DICTIONARY.md定义的表结构
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Optional
import pandas as pd

from ..database.connection_manager import DuckDBConnectionManager
from ..database.data_dictionary_schema import DataDictionarySchema
from ..utils.config_loader import ConfigLoader
from ..utils.time_utils import get_beijing_time_now
from .validators.csv_validator import CSVValidator
from .mappers.data_dictionary_mapper import DataDictionaryMapper
from .fromC2C_csv_main_contract_importer import FromC2C_csv_main_contract_importer


class UnifiedCSVImporter:
    """统一CSV导入器"""

    def __init__(self, environment: str = "development"):
        """
        初始化统一CSV导入器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.config_loader = ConfigLoader()
        self.config = self.config_loader.get_config(environment)

        # CSV配置
        self.csv_config = self.config_loader.get_csv_config(environment)

        # 核心组件
        self.connection_manager = DuckDBConnectionManager(environment)
        self.schema_manager = DataDictionarySchema(environment)
        self.data_dictionary_mapper = DataDictionaryMapper()
        validator_config = {
            "encoding": self.csv_config.get("encoding", "utf-8"),
            "delimiter": self.csv_config.get("delimiter", ","),
            "max_file_size_mb": self.csv_config.get("max_file_size_mb", 200),
        }
        self.validator = CSVValidator(validator_config)

        # 专用数据源导入器
        self.fromC2C_importer = FromC2C_csv_main_contract_importer(environment)

        # 日志配置
        self.logger = logging.getLogger(__name__)

        # 支持的数据源
        self.supported_data_sources = {
            "FromC2C": {
                "description": "C2C期货主力合约数据",
                "data_path": "/Users/<USER>/Documents/Data/FromC2C",
                "importer": self.fromC2C_importer,
                "supported_tables": [
                    "fut_main_contract_kline_5min",
                    "fut_main_contract_kline_15min",
                    "fut_main_contract_kline_30min",
                ],
            }
        }

        # 导入统计
        self.import_stats = {
            "total_files": 0,
            "processed_files": 0,
            "skipped_files": 0,
            "error_files": 0,
            "total_records": 0,
            "imported_records": 0,
            "data_sources": {},
            "errors": [],
        }

    def detect_data_source(self, file_path: Path) -> Optional[str]:
        """
        检测文件的数据源类型

        Args:
            file_path: 文件路径

        Returns:
            Optional[str]: 数据源名称
        """
        file_path_str = str(file_path.absolute())

        # 检查是否为FromC2C数据源
        if "/Users/<USER>/Documents/Data/FromC2C" in file_path_str:
            return "FromC2C"

        # 其他数据源检测逻辑可以在这里扩展

        return None

    def import_single_file_unified(
        self,
        file_path: Path,
        target_table: Optional[str] = None,
        data_source: Optional[str] = None,
    ) -> Dict:
        """
        统一的单文件导入方法

        Args:
            file_path: 文件路径
            target_table: 目标表名（可选，自动检测）
            data_source: 数据源名称（可选，自动检测）

        Returns:
            Dict: 导入结果
        """
        start_time = get_beijing_time_now()

        import_result = {
            "success": False,
            "file_path": str(file_path),
            "data_source": data_source,
            "target_table": target_table,
            "records_imported": 0,
            "error": None,
            "warnings": [],
            "duration_seconds": 0,
            "import_method": "unified",
        }

        try:
            # 1. 检测数据源
            if not data_source:
                data_source = self.detect_data_source(file_path)
                import_result["data_source"] = data_source

            # 2. 检测目标表
            if not target_table:
                target_table = (
                    self.data_dictionary_mapper.map_file_to_data_dictionary_table(
                        file_path
                    )
                )
                import_result["target_table"] = target_table

            # 3. 验证目标表是否为数据字典表
            if not self.data_dictionary_mapper.is_data_dictionary_table(target_table):
                import_result["error"] = (
                    f"目标表 {target_table} 不在DATA_DICTIONARY.md定义中"
                )
                return import_result

            # 4. 根据数据源选择导入方法
            if data_source == "FromC2C":
                # 使用FromC2C专用导入器
                result = self.fromC2C_importer.import_single_fromC2C_file(
                    file_path, target_table
                )
                import_result.update(result)
                import_result["import_method"] = "FromC2C_specialized"
            else:
                # 使用通用导入方法
                result = self._import_generic_csv_file(file_path, target_table)
                import_result.update(result)
                import_result["import_method"] = "generic"

        except Exception as e:
            import_result["error"] = f"统一导入异常: {str(e)}"
            self.logger.error(f"统一导入异常: {file_path.name} - {str(e)}")

        finally:
            # 计算耗时
            end_time = get_beijing_time_now()
            import_result["duration_seconds"] = (end_time - start_time).total_seconds()

        return import_result

    def _import_generic_csv_file(self, file_path: Path, target_table: str) -> Dict:
        """
        通用CSV文件导入方法

        Args:
            file_path: 文件路径
            target_table: 目标表名

        Returns:
            Dict: 导入结果
        """
        import_result = {
            "success": False,
            "records_imported": 0,
            "error": None,
            "warnings": [],
        }

        try:
            # 1. 文件验证
            validation = self.validator.validate_file_format(file_path)
            if not validation["valid"]:
                import_result["error"] = (
                    f"文件验证失败: {', '.join(validation['errors'])}"
                )
                return import_result

            import_result["warnings"].extend(validation["warnings"])

            # 2. 读取CSV数据
            df = pd.read_csv(
                file_path, encoding=self.csv_config.get("encoding", "utf-8")
            )

            # 3. 数据标准化（基于数据字典）
            standardized_df = self._standardize_generic_data(df, target_table)

            # 4. 验证数据完整性
            compliance = self._validate_data_dictionary_compliance(
                standardized_df, target_table
            )
            if not compliance["compliant"]:
                import_result["error"] = (
                    f"数据字典合规性验证失败: {', '.join(compliance['missing_columns'])}"
                )
                return import_result

            # 5. 导入到数据库
            success = self._insert_to_database(standardized_df, target_table)
            if success:
                import_result["success"] = True
                import_result["records_imported"] = len(standardized_df)
            else:
                import_result["error"] = "数据库插入失败"

        except Exception as e:
            import_result["error"] = f"通用导入异常: {str(e)}"

        return import_result

    def _standardize_generic_data(
        self, df: pd.DataFrame, target_table: str
    ) -> pd.DataFrame:
        """
        通用数据标准化

        Args:
            df: 原始数据
            target_table: 目标表名

        Returns:
            pd.DataFrame: 标准化后的数据
        """
        standardized_df = df.copy()

        # 获取表的架构定义
        table_schema = self.data_dictionary_mapper.get_table_schema(target_table)
        if not table_schema:
            raise ValueError(f"未找到表 {target_table} 的架构定义")

        # 通用字段映射
        common_mappings = {
            "datetime": "trade_datetime",
            "date": "trade_date" if "daily" in target_table else "trade_datetime",
            "money": "amount",
            "成交额": "amount",
            "成交量": "volume",
            "开盘价": "open",
            "最高价": "high",
            "最低价": "low",
            "收盘价": "close",
        }

        # 应用字段映射
        for old_col, new_col in common_mappings.items():
            if old_col in standardized_df.columns:
                standardized_df = standardized_df.rename(columns={old_col: new_col})

        # 添加系统字段
        current_time = get_beijing_time_now()
        standardized_df["created_at"] = current_time
        standardized_df["updated_at"] = current_time

        return standardized_df

    def _validate_data_dictionary_compliance(
        self, df: pd.DataFrame, target_table: str
    ) -> Dict:
        """
        验证数据字典合规性

        Args:
            df: 数据
            target_table: 目标表名

        Returns:
            Dict: 验证结果
        """
        columns = df.columns.tolist()
        return self.data_dictionary_mapper.validate_table_compliance(
            target_table, columns
        )

    def _insert_to_database(self, df: pd.DataFrame, target_table: str) -> bool:
        """
        插入数据到数据库

        Args:
            df: 数据
            target_table: 目标表名

        Returns:
            bool: 是否成功
        """
        try:
            # 确保表存在
            if not self.connection_manager.table_exists(target_table):
                # 创建符合数据字典的表
                with self.connection_manager.get_connection() as conn:
                    success = self.schema_manager.create_data_dictionary_table(
                        target_table, conn
                    )
                    if not success:
                        self.logger.error(f"创建表 {target_table} 失败")
                        return False

            # 保存数据为临时CSV文件
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".csv", delete=False, encoding="utf-8"
            ) as temp_file:
                temp_csv_path = Path(temp_file.name)
                df.to_csv(temp_file, index=False)

            try:
                # 插入数据
                success = self.connection_manager.insert_from_csv(
                    target_table, temp_csv_path, replace=False
                )
                return success

            finally:
                # 清理临时文件
                if temp_csv_path.exists():
                    temp_csv_path.unlink()

        except Exception as e:
            self.logger.error(f"数据库插入异常: {e}")
            return False

    def import_from_data_source(
        self, data_source: str, max_files: Optional[int] = None
    ) -> Dict:
        """
        从指定数据源导入数据

        Args:
            data_source: 数据源名称
            max_files: 最大文件数限制

        Returns:
            Dict: 导入结果
        """
        if data_source not in self.supported_data_sources:
            return {
                "success": False,
                "error": f"不支持的数据源: {data_source}",
                "supported_sources": list(self.supported_data_sources.keys()),
            }

        source_config = self.supported_data_sources[data_source]

        if data_source == "FromC2C":
            # 使用FromC2C专用导入器
            return self.fromC2C_importer.import_all_fromC2C_data(
                max_files_per_table=max_files
            )
        else:
            # 通用数据源导入逻辑
            return self._import_generic_data_source(data_source, max_files)

    def _import_generic_data_source(
        self, data_source: str, max_files: Optional[int] = None
    ) -> Dict:
        """
        通用数据源导入

        Args:
            data_source: 数据源名称
            max_files: 最大文件数限制

        Returns:
            Dict: 导入结果
        """
        # 这里可以实现通用的数据源导入逻辑
        return {"success": False, "error": f"数据源 {data_source} 的通用导入尚未实现"}

    def get_unified_import_summary(self) -> Dict:
        """
        获取统一导入摘要

        Returns:
            Dict: 导入摘要
        """
        return {
            "importer_type": "UnifiedCSVImporter",
            "environment": self.environment,
            "supported_data_sources": list(self.supported_data_sources.keys()),
            "data_dictionary_tables": self.data_dictionary_mapper.get_supported_tables(),
            "import_statistics": self.import_stats.copy(),
            "timestamp": get_beijing_time_now().isoformat(),
        }

    def validate_environment_setup(self) -> Dict:
        """
        验证环境设置

        Returns:
            Dict: 验证结果
        """
        validation_result = {
            "valid": True,
            "database_connection": False,
            "data_sources_accessible": {},
            "data_dictionary_tables": {},
            "errors": [],
        }

        try:
            # 验证数据库连接
            with self.connection_manager.get_connection() as conn:
                validation_result["database_connection"] = True

                # 验证数据字典表
                for table_name in self.data_dictionary_mapper.get_supported_tables():
                    exists = self.schema_manager.validate_table_exists(table_name, conn)
                    validation_result["data_dictionary_tables"][table_name] = exists

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"数据库连接验证失败: {e}")

        # 验证数据源可访问性
        for source_name, source_config in self.supported_data_sources.items():
            data_path = Path(source_config["data_path"])
            accessible = data_path.exists() and data_path.is_dir()
            validation_result["data_sources_accessible"][source_name] = accessible
            if not accessible:
                validation_result["valid"] = False
                validation_result["errors"].append(
                    f"数据源 {source_name} 路径不可访问: {data_path}"
                )

        return validation_result

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.connection_manager.close_connection()
        if hasattr(self.fromC2C_importer, "__exit__"):
            self.fromC2C_importer.__exit__(exc_type, exc_val, exc_tb)
