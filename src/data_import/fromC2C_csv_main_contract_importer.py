#!/usr/bin/env python3
"""
FromC2C CSV期货主力合约导入器

专门处理 /Users/<USER>/Documents/Data/FromC2C 目录下的期货主力合约CSV数据
严格遵循DATA_DICTIONARY.md权威数据结构定义

数据源特征：
- 来源：C2C数据提供商
- 内容：期货主力合约K线数据
- 频率：5分钟、15分钟、30分钟
- 品种：89个期货品种
- 格式：CSV，字段为 index,open,close,high,low,volume,money,open_interest,contract_code,date

目标表结构（DATA_DICTIONARY.md）：
- fut_main_contract_kline_5min
- fut_main_contract_kline_15min
- fut_main_contract_kline_30min
"""

import uuid
import logging
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import re

from ..database.connection_manager import DuckDBConnectionManager
from ..utils.config_loader_v2 import ConfigLoaderV2
from ..utils.time_utils import get_beijing_time_now
from .validators.csv_validator import CSVValidator


class FromC2C_csv_main_contract_importer:
    def __init__(
        self, environment: str = "dev", config_override: Optional[Dict] = None
    ):
        self.config_loader = ConfigLoaderV2()
        self.config = self.config_loader.get_config(environment)
        if config_override:
            # Deep merge the override
            self.config = self._merge_configs(self.config, config_override)

        self.connection_manager = DuckDBConnectionManager(environment)
        # 使用新的ConfigLoaderV2获取CSV配置
        csv_config = self.config_loader.get_csv_config(environment)
        self.base_path = Path(
            csv_config.get("fromc2c_dir", "~/Documents/Data/FromC2C")
        ).expanduser()
        self.batch_size = self.config.get("processing", {}).get("batch_size", 10000)
        self.encoding = csv_config.get("encoding", "utf-8")

        self.environment = environment

        # 当前导入会话ID - 用于追踪导入历史
        self.current_import_id = None

        # 验证器
        validator_config = {
            "encoding": self.encoding,
            "delimiter": csv_config.get("delimiter", ","),
            "max_file_size_mb": 200,
        }
        self.validator = CSVValidator(validator_config)

        # 日志配置
        self.logger = logging.getLogger(__name__)

        # 检测Rich环境，避免日志冲突
        self._rich_active = False
        try:
            import rich

            # 如果在Rich环境中，减少日志输出
            if hasattr(rich.get_console(), "_live"):
                self._rich_active = True
        except:
            pass

        # 跨平台编码处理 - 支持UTF-8 BOM
        self.encoding_variants = ["utf-8", "utf-8-sig", "gbk"]  # Windows兼容性编码选项

        # FromC2C数据源的字段映射定义 - v4.0统一架构
        self.fromC2C_field_mapping = {
            # FromC2C CSV字段 -> DATA_DICTIONARY.md v4.0统一字段
            "index": None,  # 丢弃，时间信息已在date字段
            "open": "open_price",  # 开盘价
            "close": "close_price",  # 收盘价
            "high": "high_price",  # 最高价
            "low": "low_price",  # 最低价
            "volume": "volume",  # 成交量
            "money": "amount",  # 成交金额（FromC2C特有字段名）
            "open_interest": "open_interest",  # 持仓量
            "contract_code": "contract_code",  # 合约代码
            "date": "trade_datetime",  # 交易时间
        }

        # DATA_DICTIONARY.md v4.0权威表定义 - 统一业务表
        self.data_dictionary_tables = {
            "futures_main_contract_kline": {
                "required_columns": [
                    "contract_code",
                    "product_code",
                    "frequency",
                    "trade_datetime",
                    "open_price",
                    "high_price",
                    "low_price",
                    "close_price",
                    "volume",
                    "amount",
                    "open_interest",
                    "data_source",
                ],
                "primary_key": ["contract_code", "frequency", "trade_datetime"],
            }
        }

        # 导入统计
        self.import_stats = {
            "total_files": 0,
            "processed_files": 0,
            "skipped_files": 0,
            "error_files": 0,
            "total_records": 0,
            "imported_records": 0,
            "errors": [],
        }

    def _merge_configs(self, base_config: Dict, override_config: Dict) -> Dict:
        """深度合并配置"""
        result = base_config.copy()
        for key, value in override_config.items():
            if (
                key in result
                and isinstance(result[key], dict)
                and isinstance(value, dict)
            ):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result

    def detect_frequency_from_directory(self, file_path: Path) -> Optional[str]:
        """
        根据目录名检测数据频率并映射到DATA_DICTIONARY.md表名

        Args:
            file_path: CSV文件路径

        Returns:
            Optional[str]: 对应的表名
        """
        directory_name = file_path.parent.name

        # 基于目录名的频率映射 - v4.0统一表架构
        frequency_mapping = {
            "期货主连5min": ("futures_main_contract_kline", "5min"),
            "期货主连15min": ("futures_main_contract_kline", "15min"),
            "期货主连30min": ("futures_main_contract_kline", "30min"),
        }

        mapping_result = frequency_mapping.get(directory_name)
        if mapping_result:
            target_table, frequency = mapping_result
            self.logger.info(
                f"目录 '{directory_name}' 映射到表: {target_table} (频率: {frequency})"
            )
            return (target_table, frequency)
        else:
            self.logger.warning(f"未知的FromC2C目录: {directory_name}")
            return None

    def _read_csv_with_fallback_encoding(self, file_path: Path) -> pd.DataFrame:
        """
        跨平台编码兼容的CSV读取方法

        Args:
            file_path: CSV文件路径

        Returns:
            pd.DataFrame: 读取的DataFrame

        Raises:
            Exception: 所有编码都失败时抛出异常
        """
        last_error = None

        for encoding in self.encoding_variants:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                if encoding != "utf-8":
                    self.logger.info(f"文件 {file_path.name} 使用编码: {encoding}")
                return df
            except (UnicodeDecodeError, UnicodeError) as e:
                last_error = e
                self.logger.debug(f"编码 {encoding} 读取失败: {e}")
                continue
            except Exception as e:
                # 非编码相关错误，直接抛出
                raise e

        # 所有编码都失败
        raise Exception(f"无法使用任何编码读取文件 {file_path}: {last_error}")

    def extract_contract_symbol(self, contract_code: str) -> Optional[str]:
        """
        核心函数5: 从合约代码提取品种符号信息

        解析FromC2C的期货合约代码格式，提取品种符号用于
        分类和统计分析。支持标准的期货合约命名规范。

        Args:
            contract_code (str): 合约代码字符串，遵循FromC2C格式
                示例: "AL0503.XSGE", "RB2501.XSHE", "CU2406.XSGE"

        Returns:
            Optional[str]: 提取的品种符号，如"AL"、"RB"、"CU"
                如果解析失败则返回None

        Examples:
            >>> importer = FromC2C_csv_main_contract_importer()
            >>> symbol = importer.extract_contract_symbol("AL0503.XSGE")
            >>> print(symbol)  # 输出: "AL"
            >>> symbol = importer.extract_contract_symbol("RB2501.XSHE")
            >>> print(symbol)  # 输出: "RB"

        Notes:
            - 支持品种符号长度: 1-2个字母
            - 合约月份格式: 4位数字
            - 交易所后缀格式: .X + 3位交易所代码
        """
        if not contract_code:
            return None

        # 匹配标准格式: AL0503.XSGE -> AL, RB2501.XSHE -> RB
        match = re.match(r"^([A-Z]{1,2})\d{4}\.X[A-Z]{3}$", contract_code)
        if match:
            return match.group(1)

        # 匹配中金所格式: IC1610.CCFX -> IC
        match = re.match(r"^([A-Z]{1,2})\d{4}\.C[A-Z]{3}$", contract_code)
        if match:
            return match.group(1)

        # 兜底：尝试简单提取字母前缀
        match = re.match(r"^([A-Z]{1,2})\d+", contract_code)
        if match:
            # 在Rich环境下不输出debug信息
            if not self._rich_active:
                self.logger.debug(
                    f"使用兜底规则解析合约代码: {contract_code} -> {match.group(1)}"
                )
            return match.group(1)

        # 只有无法解析时才输出警告，避免大量重复日志
        if not hasattr(self, "_warned_contracts"):
            self._warned_contracts = set()

        if contract_code not in self._warned_contracts and not self._rich_active:
            self.logger.warning(f"无法解析合约代码格式: {contract_code}")
            self._warned_contracts.add(contract_code)

        return None

    def standardize_fromC2C_data(
        self, df: pd.DataFrame, target_table: str, frequency: str
    ) -> pd.DataFrame:
        """
        将FromC2C数据标准化为DATA_DICTIONARY.md格式

        Args:
            df: 原始FromC2C数据
            target_table: 目标表名

        Returns:
            pd.DataFrame: 标准化后的数据
        """
        standardized_df = df.copy()

        # 1. 字段重命名（基于FromC2C映射）
        rename_mapping = {}
        for fromC2C_col, standard_col in self.fromC2C_field_mapping.items():
            if standard_col and fromC2C_col in standardized_df.columns:
                rename_mapping[fromC2C_col] = standard_col

        standardized_df = standardized_df.rename(columns=rename_mapping)

        # 2. 丢弃不需要的字段
        drop_columns = [
            col
            for col in standardized_df.columns
            if col not in self.data_dictionary_tables[target_table]["required_columns"]
            and col not in ["created_at", "updated_at"]
        ]
        if drop_columns:
            standardized_df = standardized_df.drop(columns=drop_columns)
            self.logger.debug(f"丢弃字段: {drop_columns}")

        # 3. 添加统一表架构的新字段
        # 3.1 提取品种代码
        if "contract_code" in standardized_df.columns:
            standardized_df["product_code"] = standardized_df["contract_code"].apply(
                self.extract_contract_symbol
            )

        # 3.2 添加频率字段
        standardized_df["frequency"] = frequency

        # 3.3 添加数据源标识
        standardized_df["data_source"] = "FROMC2C"
        standardized_df["source_quality"] = "NORMAL"

        # 3.4 时间字段标准化
        if "trade_datetime" in standardized_df.columns:
            # FromC2C的date字段已经是datetime格式，直接转换
            standardized_df["trade_datetime"] = pd.to_datetime(
                standardized_df["trade_datetime"]
            )

        # 4. 数据清理和类型转换（符合DATA_DICTIONARY.md v4.0规范）
        # 清理NaN值，特别是对于必需字段
        price_columns = ["open_price", "high_price", "low_price", "close_price"]
        for col in price_columns:
            if col in standardized_df.columns:
                # 价格字段NaN值填充为前一个有效值，如果没有则填充为0
                standardized_df[col] = standardized_df[col].ffill().fillna(0.0)

        if "open_interest" in standardized_df.columns:
            # 将NaN值填充为0
            standardized_df["open_interest"] = standardized_df["open_interest"].fillna(
                0
            )

        if "volume" in standardized_df.columns:
            # 成交量NaN值填充为0
            standardized_df["volume"] = standardized_df["volume"].fillna(0)

        if "amount" in standardized_df.columns:
            # 成交金额NaN值填充为0
            standardized_df["amount"] = standardized_df["amount"].fillna(0.0)

        type_conversions = {
            "open_price": "float",
            "high_price": "float",
            "low_price": "float",
            "close_price": "float",
            "volume": "int",
            "amount": "float",
            "open_interest": "int",
        }

        for col, dtype in type_conversions.items():
            if col in standardized_df.columns:
                try:
                    # 特殊处理整数字段的NaN值
                    if dtype == "int" and standardized_df[col].isna().any():
                        standardized_df[col] = standardized_df[col].fillna(0)
                    standardized_df[col] = standardized_df[col].astype(dtype)
                except Exception as e:
                    self.logger.warning(f"字段 {col} 类型转换失败: {e}")
                    # 如果转换失败，尝试填充默认值后再转换
                    if dtype == "int":
                        standardized_df[col] = (
                            standardized_df[col].fillna(0).astype(dtype)
                        )
                    else:
                        standardized_df[col] = (
                            standardized_df[col].fillna(0.0).astype(dtype)
                        )

        # 5. 添加系统字段
        current_time = get_beijing_time_now()
        standardized_df["created_at"] = current_time
        standardized_df["updated_at"] = current_time

        # 5.1 转换numpy类型为Python原生类型（DuckDB兼容性）
        import numpy as np

        for col in standardized_df.columns:
            if (
                standardized_df[col].dtype == np.int64
                or standardized_df[col].dtype == "int64"
            ):
                standardized_df[col] = standardized_df[col].astype("int64").astype(int)
            elif standardized_df[col].dtype == np.float64 or standardized_df[
                col
            ].dtype.name.startswith("float"):
                standardized_df[col] = (
                    standardized_df[col].astype("float64").astype(float)
                )
            elif pd.api.types.is_integer_dtype(standardized_df[col]):
                standardized_df[col] = standardized_df[col].astype(int)
            elif pd.api.types.is_float_dtype(standardized_df[col]):
                standardized_df[col] = standardized_df[col].astype(float)

        # 6. 确保所有必需字段存在
        required_columns = self.data_dictionary_tables[target_table]["required_columns"]
        missing_columns = [
            col for col in required_columns if col not in standardized_df.columns
        ]
        if missing_columns:
            raise ValueError(f"标准化后缺少必需字段: {missing_columns}")

        # 7. 去除重复记录（基于主键）
        primary_key_columns = self.data_dictionary_tables[target_table]["primary_key"]
        if all(col in standardized_df.columns for col in primary_key_columns):
            original_count = len(standardized_df)
            standardized_df = standardized_df.drop_duplicates(
                subset=primary_key_columns, keep="first"
            )
            deduped_count = len(standardized_df)
            if original_count > deduped_count:
                self.logger.info(f"去除重复记录: {original_count - deduped_count} 条")

        self.logger.info(f"FromC2C数据标准化完成: {len(standardized_df)} 条记录")
        return standardized_df

    def validate_fromC2C_data_compliance(
        self, standardized_dataframe: pd.DataFrame, target_table: str
    ) -> Dict[str, Any]:
        """
        核心函数4: FromC2C数据业务合规性验证器

        对标准化后的数据执行严格的业务规则检查，包括价格逻辑、
        数据范围、合约代码格式、OHLC关系等金融数据特有验证。

        Args:
            standardized_dataframe (pd.DataFrame): 经过standardize_fromC2C_data()处理的数据
                必须包含: contract_code, trade_datetime, open, high, low, close,
                         volume, amount, open_interest等字段
            target_table (str): DATA_DICTIONARY.md中定义的目标表名

        Returns:
            Dict[str, Any]: 验证结果详情
                - valid (bool): 数据是否通过所有关键验证
                - errors (List[str]): 严重错误列表（导致valid=False）
                - warnings (List[str]): 警告信息列表（不影响导入）

        Validation Rules:
            - 价格字段（open/high/low/close）不允许负值
            - 成交量和持仓量不允许负值
            - OHLC价格逻辑关系检查（high≥max(o,c), low≤min(o,c)）
            - 合约代码格式验证（XX9999.XXXX格式）
            - 交易时间字段非空检查
        """
        result = {"valid": True, "errors": [], "warnings": []}

        # 1. 价格字段合规性检查
        price_columns = ["open", "high", "low", "close"]
        for col in price_columns:
            if col in standardized_dataframe.columns:
                # 检查负价格
                negative_prices = standardized_dataframe[
                    standardized_dataframe[col] < 0
                ]
                if not negative_prices.empty:
                    result["valid"] = False
                    result["errors"].append(
                        f"{col}字段存在负价格，行数: {len(negative_prices)}"
                    )

                # 检查异常价格（过大或过小）
                price_values = standardized_dataframe[col].dropna()
                if not price_values.empty:
                    if price_values.max() > 1000000:  # 100万以上可能异常
                        result["warnings"].append(
                            f"{col}字段存在异常高价格: {price_values.max()}"
                        )
                    if (
                        price_values.min() < 0.01 and price_values.min() > 0
                    ):  # 接近0但不为0
                        result["warnings"].append(
                            f"{col}字段存在异常低价格: {price_values.min()}"
                        )

        # 2. 成交量和持仓量检查
        volume_columns = ["volume", "open_interest"]
        for col in volume_columns:
            if col in standardized_dataframe.columns:
                negative_volumes = standardized_dataframe[
                    standardized_dataframe[col] < 0
                ]
                if not negative_volumes.empty:
                    result["valid"] = False
                    result["errors"].append(
                        f"{col}字段存在负值，行数: {len(negative_volumes)}"
                    )

        # 3. 合约代码格式检查
        if "contract_code" in standardized_dataframe.columns:
            invalid_contracts = standardized_dataframe[
                ~standardized_dataframe["contract_code"].str.match(
                    r"^[A-Z]{1,2}\d{4}\.X[A-Z]{3}$", na=False
                )
            ]
            if not invalid_contracts.empty:
                result["warnings"].append(
                    f"合约代码格式异常，行数: {len(invalid_contracts)}"
                )

        # 4. 时间字段检查
        if "trade_datetime" in standardized_dataframe.columns:
            null_dates = standardized_dataframe[
                standardized_dataframe["trade_datetime"].isna()
            ]
            if not null_dates.empty:
                result["valid"] = False
                result["errors"].append(
                    f"交易时间字段存在空值，行数: {len(null_dates)}"
                )

        # 5. OHLC逻辑关系检查
        if all(
            col in standardized_dataframe.columns
            for col in ["open", "high", "low", "close"]
        ):
            # high应该是最高价
            invalid_high = standardized_dataframe[
                (standardized_dataframe["high"] < standardized_dataframe["open"])
                | (standardized_dataframe["high"] < standardized_dataframe["close"])
                | (standardized_dataframe["high"] < standardized_dataframe["low"])
            ]
            if not invalid_high.empty:
                result["warnings"].append(f"最高价逻辑异常，行数: {len(invalid_high)}")

            # low应该是最低价
            invalid_low = standardized_dataframe[
                (standardized_dataframe["low"] > standardized_dataframe["open"])
                | (standardized_dataframe["low"] > standardized_dataframe["close"])
                | (standardized_dataframe["low"] > standardized_dataframe["high"])
            ]
            if not invalid_low.empty:
                result["warnings"].append(f"最低价逻辑异常，行数: {len(invalid_low)}")

        return result

    def start_import_session(self, import_type: str = "manual") -> Optional[str]:
        """
        开始导入会话，记录到data_import_history表

        Args:
            import_type: 导入类型 (manual/auto/batch)

        Returns:
            import_id: 导入会话ID，失败时返回None
        """
        import_id = (
            f"fromC2C_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        )

        try:
            with self.connection_manager.get_cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO data_import_history (
                        import_id, data_source, table_name, import_status,
                        start_time, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """,
                    (
                        import_id,
                        "FROMC2C",
                        "futures_main_contract_kline",
                        "RUNNING",
                        datetime.now(),
                        f"FromC2C_importer_{import_type}",
                    ),
                )

            self.current_import_id = import_id
            self.logger.info(f"📋 导入会话开始: {import_id}")
            return import_id

        except Exception as e:
            self.logger.error(f"❌ 无法创建导入会话: {e}")
            return None

    def end_import_session(
        self, records_imported: int, records_failed: int, error_message: str = None
    ):
        """
        结束导入会话，更新历史记录

        Args:
            records_imported: 成功导入的记录数
            records_failed: 失败的记录数
            error_message: 错误信息
        """
        if not self.current_import_id:
            return

        status = (
            "SUCCESS"
            if records_failed == 0
            else ("PARTIAL" if records_imported > 0 else "FAILED")
        )

        try:
            with self.connection_manager.get_cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE data_import_history 
                    SET end_time = ?, import_status = ?, 
                        records_imported = ?, records_failed = ?, error_message = ?
                    WHERE import_id = ?
                """,
                    (
                        datetime.now(),
                        status,
                        records_imported,
                        records_failed,
                        error_message,
                        self.current_import_id,
                    ),
                )

            self.logger.info(
                f"📋 导入会话结束: {self.current_import_id}, 状态: {status}"
            )

        except Exception as e:
            self.logger.error(f"❌ 无法更新导入会话: {e}")
        finally:
            self.current_import_id = None

    def auto_discover_contract_info(self, df: pd.DataFrame):
        """
        自动发现和更新合约信息到futures_contract_info表

        Args:
            df: 包含合约数据的DataFrame
        """
        if "contract_code" not in df.columns or "product_code" not in df.columns:
            return

        unique_contracts = df[["contract_code", "product_code"]].drop_duplicates()

        try:
            with self.connection_manager.get_cursor() as cursor:
                for _, row in unique_contracts.iterrows():
                    contract_code = row["contract_code"]
                    product_code = row["product_code"]

                    # 检查合约是否已存在
                    cursor.execute(
                        "SELECT COUNT(*) FROM futures_contract_info WHERE contract_code = ?",
                        (contract_code,),
                    )

                    if cursor.fetchone()[0] == 0:
                        # 自动推断交易所和品种信息
                        exchange_info = self._infer_exchange_info(product_code)

                        cursor.execute(
                            """
                            INSERT INTO futures_contract_info (
                                contract_code, product_code, product_name,
                                exchange_code, exchange_name, contract_size, tick_size,
                                data_source, created_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """,
                            (
                                contract_code,
                                product_code,
                                exchange_info["product_name"],
                                exchange_info["exchange_code"],
                                exchange_info["exchange_name"],
                                exchange_info.get("contract_size", 10),  # 默认合约乘数
                                exchange_info.get("tick_size", 1.0),  # 默认最小变动价位
                                "AUTO_DISCOVER",
                                datetime.now(),
                            ),
                        )

                        self.logger.info(
                            f"🔍 自动发现新合约: {contract_code} ({exchange_info['product_name']})"
                        )

        except Exception as e:
            self.logger.error(f"❌ 合约信息自动发现失败: {e}")

    def _infer_exchange_info(self, product_code: str) -> Dict[str, str]:
        """
        根据品种代码推断交易所信息

        Args:
            product_code: 品种代码

        Returns:
            包含品种名称、交易所代码和名称的字典
        """
        # 品种到交易所的映射 - 基于中国期货市场实际情况
        exchange_mapping = {
            # 上海期货交易所 (SHFE)
            "RB": {
                "name": "螺纹钢",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "HC": {
                "name": "热卷",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "CU": {
                "name": "铜",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 5,
                "tick_size": 10.0,
            },
            "AL": {
                "name": "铝",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 5,
                "tick_size": 5.0,
            },
            "ZN": {
                "name": "锌",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 5,
                "tick_size": 5.0,
            },
            "PB": {
                "name": "铅",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 5,
                "tick_size": 5.0,
            },
            "NI": {
                "name": "镍",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 1,
                "tick_size": 10.0,
            },
            "SN": {
                "name": "锡",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 1,
                "tick_size": 10.0,
            },
            "AG": {
                "name": "白银",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 15,
                "tick_size": 1.0,
            },
            "AU": {
                "name": "黄金",
                "exchange": "SHFE",
                "exchange_name": "上海期货交易所",
                "contract_size": 1000,
                "tick_size": 0.02,
            },
            # 大连商品交易所 (DCE)
            "A": {
                "name": "豆一",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "B": {
                "name": "豆二",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "M": {
                "name": "豆粕",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "Y": {
                "name": "豆油",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 10,
                "tick_size": 2.0,
            },
            "C": {
                "name": "玉米",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "L": {
                "name": "聚乙烯",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 5,
                "tick_size": 5.0,
            },
            "V": {
                "name": "PVC",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 5,
                "tick_size": 5.0,
            },
            "P": {
                "name": "棕榈油",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 10,
                "tick_size": 2.0,
            },
            "J": {
                "name": "焦炭",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 100,
                "tick_size": 0.5,
            },
            "JM": {
                "name": "焦煤",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 60,
                "tick_size": 0.5,
            },
            "I": {
                "name": "铁矿石",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 100,
                "tick_size": 0.5,
            },
            "PP": {
                "name": "聚丙烯",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 5,
                "tick_size": 1.0,
            },
            "EG": {
                "name": "乙二醇",
                "exchange": "DCE",
                "exchange_name": "大连商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            # 郑州商品交易所 (CZCE)
            "SR": {
                "name": "白糖",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "CF": {
                "name": "棉花",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 5,
                "tick_size": 5.0,
            },
            "ZC": {
                "name": "动力煤",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 100,
                "tick_size": 0.2,
            },
            "TA": {
                "name": "PTA",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 5,
                "tick_size": 2.0,
            },
            "OI": {
                "name": "菜籽油",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "MA": {
                "name": "甲醇",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "FG": {
                "name": "玻璃",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 20,
                "tick_size": 1.0,
            },
            "RM": {
                "name": "菜粕",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "SA": {
                "name": "纯碱",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 20,
                "tick_size": 1.0,
            },
            "AP": {
                "name": "苹果",
                "exchange": "CZCE",
                "exchange_name": "郑州商品交易所",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            # 上海国际能源交易中心 (INE)
            "SC": {
                "name": "原油",
                "exchange": "INE",
                "exchange_name": "上海国际能源交易中心",
                "contract_size": 1000,
                "tick_size": 0.1,
            },
            "LU": {
                "name": "低硫燃料油",
                "exchange": "INE",
                "exchange_name": "上海国际能源交易中心",
                "contract_size": 10,
                "tick_size": 1.0,
            },
            "NR": {
                "name": "20号胶",
                "exchange": "INE",
                "exchange_name": "上海国际能源交易中心",
                "contract_size": 10,
                "tick_size": 5.0,
            },
        }

        if product_code in exchange_mapping:
            info = exchange_mapping[product_code]
            return {
                "product_name": info["name"],
                "exchange_code": info["exchange"],
                "exchange_name": info["exchange_name"],
                "contract_size": info["contract_size"],
                "tick_size": info["tick_size"],
            }
        else:
            return {
                "product_name": product_code,
                "exchange_code": "UNKNOWN",
                "exchange_name": "未知交易所",
                "contract_size": 10,  # 默认值
                "tick_size": 1.0,  # 默认值
            }

    def log_quality_issue(
        self, check_type: str, severity: str, description: str, issue_count: int = 1
    ):
        """
        记录数据质量问题到data_quality_log表

        Args:
            check_type: 检查类型 (DUPLICATE/MISSING/OUTLIER/FORMAT等)
            severity: 严重程度 (INFO/WARNING/ERROR)
            description: 问题描述
            issue_count: 问题数量
        """
        try:
            with self.connection_manager.get_cursor() as cursor:
                log_id = f"quality_{uuid.uuid4().hex[:8]}"

                cursor.execute(
                    """
                    INSERT INTO data_quality_log (
                        log_id, table_name, check_type, severity,
                        issue_count, description, check_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        log_id,
                        "futures_main_contract_kline",
                        check_type,
                        severity,
                        issue_count,
                        description,
                        datetime.now(),
                    ),
                )

                self.logger.info(f"📊 记录质量问题: {check_type} - {description}")

        except Exception as e:
            self.logger.error(f"❌ 无法记录质量问题: {e}")

    def get_fromC2C_csv_files(self) -> Dict[str, List[Path]]:
        """
        核心函数1: 扫描FromC2C目录下的所有CSV文件，按频率分组

        根据目录结构（期货主连5min、15min、30min）自动识别频率类型，
        并映射到对应的DATA_DICTIONARY.md分层表名。支持跨平台路径处理。

        Returns:
            Dict[str, List[Path]]: 按目标表名分组的CSV文件路径列表
                - Key: 目标表名（如'fut_main_contract_kline_15min'）
                - Value: 该频率下的所有CSV文件路径列表

        Raises:
            None: 目录不存在时返回空字典，不抛出异常

        Examples:
            >>> importer = FromC2C_csv_main_contract_importer()
            >>> files_dict = importer.get_fromC2C_csv_files()
            >>> print(files_dict.keys())
            dict_keys(['fut_main_contract_kline_15min', 'fut_main_contract_kline_5min'])
        """
        if not self.base_path.exists():
            self.logger.error(f"FromC2C数据目录不存在: {self.base_path}")
            return {}

        files_by_table = {}

        # 扫描每个频率目录
        frequency_dirs = ["期货主连5min", "期货主连15min", "期货主连30min"]

        for freq_dir in frequency_dirs:
            freq_path = self.base_path / freq_dir
            if freq_path.exists() and freq_path.is_dir():
                # 获取目录下所有CSV文件
                csv_files = list(freq_path.glob("*.csv"))
                csv_files = [f for f in csv_files if not f.name.startswith(".")]
                csv_files.sort()

                # 检测对应的表名和频率
                if csv_files:
                    sample_file = csv_files[0]
                    mapping_result = self.detect_frequency_from_directory(sample_file)
                    if mapping_result:
                        target_table, frequency = mapping_result
                        # 使用频率作为键值以区分不同频率的文件
                        table_key = f"{target_table}_{frequency}"
                        files_by_table[table_key] = {
                            "table": target_table,
                            "frequency": frequency,
                            "files": csv_files,
                        }
                        self.logger.info(
                            f"目录 {freq_dir}: 找到 {len(csv_files)} 个CSV文件 -> {target_table}({frequency})"
                        )

        total_files = sum(len(info["files"]) for info in files_by_table.values())
        self.logger.info(
            f"FromC2C目录扫描完成: 共 {total_files} 个CSV文件，分布在 {len(files_by_table)} 个频率"
        )

        return files_by_table

    def import_single_fromC2C_file(
        self, file_path: Path, target_table: str, frequency: str
    ) -> Dict[str, Any]:
        """
        核心函数2: 处理单个FromC2C CSV文件的完整导入流程

        包括文件验证、跨平台编码处理、数据清洗标准化、
        业务合规性检查、数据库写入等完整流程。

        Args:
            file_path (Path): CSV文件路径，支持跨平台路径格式
            target_table (str): DATA_DICTIONARY.md中定义的目标表名
                如'fut_main_contract_kline_15min'

        Returns:
            Dict[str, Any]: 文件导入结果详情
                - success (bool): 导入是否成功
                - file_path (str): 源文件路径
                - target_table (str): 目标表名
                - records_imported (int): 成功导入的记录数
                - error (Optional[str]): 错误信息（如有）
                - warnings (List[str]): 警告信息列表
                - duration_seconds (float): 导入耗时（秒）

        Raises:
            Exception: 当文件读取或数据库操作发生不可恢复错误时抛出
        """
        start_time = get_beijing_time_now()

        import_result = {
            "success": False,
            "file_path": str(file_path),
            "target_table": target_table,
            "records_imported": 0,
            "error": None,
            "warnings": [],
            "duration_seconds": 0,
        }

        try:
            self.logger.info(f"开始导入FromC2C文件: {file_path.name} -> {target_table}")

            # 1. 验证文件格式
            validation = self.validator.validate_file_format(file_path)
            if not validation["valid"]:
                import_result["error"] = (
                    f"文件验证失败: {', '.join(validation['errors'])}"
                )
                return import_result

            import_result["warnings"].extend(validation["warnings"])

            # 2. 读取FromC2C CSV数据 - 跨平台编码处理
            df = self._read_csv_with_fallback_encoding(file_path)
            self.logger.info(f"读取FromC2C文件: {len(df)} 条记录")

            # 3. 数据标准化
            standardized_df = self.standardize_fromC2C_data(df, target_table, frequency)

            # 4. 数据合规性验证
            compliance = self.validate_fromC2C_data_compliance(
                standardized_df, target_table
            )
            if not compliance["valid"]:
                import_result["error"] = (
                    f"数据合规性验证失败: {', '.join(compliance['errors'])}"
                )
                return import_result

            import_result["warnings"].extend(compliance["warnings"])

            # 5. 保存标准化数据为临时文件（DuckDB兼容性处理）
            # 转换numpy数据类型为Python原生类型

            df_for_csv = standardized_df.copy()
            for col in df_for_csv.columns:
                if df_for_csv[col].dtype.kind in ["i", "u"]:  # 整数类型
                    df_for_csv[col] = (
                        df_for_csv[col]
                        .astype(object)
                        .apply(lambda x: int(x) if pd.notna(x) else None)
                    )
                elif df_for_csv[col].dtype.kind == "f":  # 浮点类型
                    df_for_csv[col] = (
                        df_for_csv[col]
                        .astype(object)
                        .apply(lambda x: float(x) if pd.notna(x) else None)
                    )
                elif df_for_csv[col].dtype.kind == "O" and col in [
                    "contract_code",
                    "product_code",
                    "frequency",
                    "data_source",
                    "source_quality",
                ]:
                    # 字符串类型确保是str
                    df_for_csv[col] = df_for_csv[col].astype(str)

            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".csv", delete=False, encoding=self.encoding
            ) as temp_file:
                temp_csv_path = Path(temp_file.name)
                df_for_csv.to_csv(temp_file, index=False)

            try:
                # 6. 导入到数据库
                table_exists = self.connection_manager.table_exists(target_table)

                if table_exists:
                    # 表存在，插入数据
                    success = self.connection_manager.insert_from_csv(
                        target_table, temp_csv_path, replace=False
                    )
                else:
                    # 表不存在，创建表并导入数据
                    success = self.connection_manager.create_table_from_csv(
                        target_table, temp_csv_path
                    )

                if success:
                    import_result["records_imported"] = len(standardized_df)
                    import_result["success"] = True

                    # 自动发现和更新合约信息
                    self.auto_discover_contract_info(standardized_df)

                    self.logger.info(
                        f"FromC2C文件导入成功: {file_path.name} -> {target_table} ({import_result['records_imported']} 条记录)"
                    )
                else:
                    import_result["error"] = "数据库操作失败"
                    # 记录质量问题
                    self.log_quality_issue(
                        "IMPORT_ERROR", "ERROR", f"数据库导入失败: {file_path.name}"
                    )

            finally:
                # 清理临时文件
                if temp_csv_path.exists():
                    temp_csv_path.unlink()

        except Exception as e:
            import_result["error"] = f"FromC2C文件导入异常: {str(e)}"
            self.logger.error(f"FromC2C文件导入异常: {file_path.name} - {str(e)}")

        finally:
            # 计算耗时
            end_time = get_beijing_time_now()
            import_result["duration_seconds"] = (end_time - start_time).total_seconds()

        return import_result

    def import_all_fromC2C_data(
        self, max_files_per_table: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        核心函数3: 全量数据导入，带进度跟踪和统计报告

        扫描所有FromC2C频率目录，批量处理CSV文件，
        提供完整的进度跟踪、错误处理和统计报告。

        Args:
            max_files_per_table (Optional[int], optional): 每个表的最大文件数限制。
                用于测试或分批处理，None表示处理所有文件。

        Returns:
            Dict[str, Any]: 全量导入结果统计
                - success (bool): 整体导入是否成功（无错误文件）
                - tables (Dict): 各表导入结果详情
                - summary (Dict): 整体统计信息
                    - total_tables (int): 处理的表数
                    - total_files (int): 总文件数
                    - processed_files (int): 成功处理文件数
                    - error_files (int): 错误文件数
                    - total_records (int): 总记录数
                    - errors (List): 错误信息列表
                - duration_seconds (float): 总耗时（秒）

        Examples:
            >>> importer = FromC2C_csv_main_contract_importer()
            >>> result = importer.import_all_fromC2C_data(max_files_per_table=10)
            >>> print(f"成功导入 {result['summary']['processed_files']} 个文件")
        """
        start_time = get_beijing_time_now()

        # 开始导入会话
        self.start_import_session("batch")

        # 获取所有CSV文件
        files_by_table = self.get_fromC2C_csv_files()

        if not files_by_table:
            # 结束导入会话
            self.end_import_session(0, 0, "未找到FromC2C CSV文件")
            return {
                "success": False,
                "error": "未找到FromC2C CSV文件",
                "tables": {},
                "summary": {},
            }

        import_result = {
            "success": True,
            "tables": {},
            "summary": {
                "total_tables": len(files_by_table),
                "total_files": 0,
                "processed_files": 0,
                "error_files": 0,
                "total_records": 0,
                "errors": [],
            },
            "duration_seconds": 0,
        }

        # 按频率处理文件
        for table_key, table_info in files_by_table.items():
            target_table = table_info["table"]
            frequency = table_info["frequency"]
            file_list = table_info["files"]

            self.logger.info(
                f"开始处理表 {target_table}({frequency}): {len(file_list)} 个文件"
            )

            # 限制文件数量
            if max_files_per_table and len(file_list) > max_files_per_table:
                file_list = file_list[:max_files_per_table]
                self.logger.info(f"限制文件数量为: {max_files_per_table}")

            table_result = {
                "target_table": target_table,
                "frequency": frequency,
                "total_files": len(file_list),
                "processed_files": 0,
                "error_files": 0,
                "total_records": 0,
                "files": [],
                "errors": [],
            }

            # 导入该表的所有文件
            for file_path in file_list:
                file_result = self.import_single_fromC2C_file(
                    file_path, target_table, frequency
                )
                table_result["files"].append(file_result)

                if file_result["success"]:
                    table_result["processed_files"] += 1
                    table_result["total_records"] += file_result["records_imported"]
                else:
                    table_result["error_files"] += 1
                    table_result["errors"].append(
                        {"file": file_path.name, "error": file_result["error"]}
                    )

            import_result["tables"][table_key] = table_result

            # 更新总计
            import_result["summary"]["total_files"] += table_result["total_files"]
            import_result["summary"]["processed_files"] += table_result[
                "processed_files"
            ]
            import_result["summary"]["error_files"] += table_result["error_files"]
            import_result["summary"]["total_records"] += table_result["total_records"]
            import_result["summary"]["errors"].extend(table_result["errors"])

            self.logger.info(
                f"表 {target_table}({frequency}) 处理完成: {table_result['processed_files']}/{table_result['total_files']} 文件成功"
            )

        # 计算总耗时
        end_time = get_beijing_time_now()
        import_result["duration_seconds"] = (end_time - start_time).total_seconds()

        # 判断整体是否成功
        import_result["success"] = import_result["summary"]["error_files"] == 0

        # 结束导入会话
        error_message = (
            "; ".join(import_result["summary"]["errors"])
            if import_result["summary"]["errors"]
            else None
        )
        self.end_import_session(
            import_result["summary"]["total_records"],
            import_result["summary"]["error_files"],
            error_message,
        )

        self.logger.info(
            f"FromC2C数据导入完成: {import_result['summary']['processed_files']}/{import_result['summary']['total_files']} 文件成功"
        )

        return import_result

    def get_fromC2C_import_summary(self) -> Dict:
        """
        获取FromC2C导入摘要

        Returns:
            Dict: 导入摘要
        """
        return {
            "importer_type": "FromC2C_csv_main_contract_importer",
            "data_source": str(self.base_path),
            "environment": self.environment,
            "supported_tables": list(self.data_dictionary_tables.keys()),
            "field_mapping": self.fromC2C_field_mapping,
            "statistics": self.import_stats.copy(),
            "timestamp": get_beijing_time_now().isoformat(),
        }

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.connection_manager.close_connection()
