#!/usr/bin/env python3
"""
WebSocket实时进度监控管理器

提供导入任务的实时进度推送功能
基于AQUA宪法的配置驱动和复用优先原则
"""

import logging
import json
import asyncio
from typing import Dict, Set, Any, Optional
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect

from ..utils.time_utils import get_beijing_time_now
from .task_control_manager import TaskControlManager


class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self, environment: str = "dev"):
        """
        初始化WebSocket管理器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.logger = logging.getLogger(__name__)

        # 使用WeakSet存储WebSocket连接，避免内存泄漏
        self._connections: Set[WebSocket] = set()

        # 任务订阅映射 {task_id: set(websockets)}
        self._task_subscriptions: Dict[str, Set[WebSocket]] = {}

        # 连接元数据 {websocket_id: metadata}
        self._connection_metadata: Dict[int, Dict[str, Any]] = {}

        # 任务控制管理器引用
        self._task_control_manager: Optional[TaskControlManager] = None

        # 心跳任务
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._heartbeat_interval = 30  # 30秒心跳间隔

    def set_task_control_manager(self, manager: TaskControlManager):
        """设置任务控制管理器引用"""
        self._task_control_manager = manager

    async def connect(self, websocket: WebSocket, client_info: Optional[Dict] = None):
        """
        接受WebSocket连接

        Args:
            websocket: WebSocket连接
            client_info: 客户端信息
        """
        try:
            await websocket.accept()

            # 添加连接
            self._connections.add(websocket)

            # 存储连接元数据
            ws_id = id(websocket)
            self._connection_metadata[ws_id] = {
                "connected_at": get_beijing_time_now(),
                "client_info": client_info or {},
                "subscribed_tasks": set(),
                "last_heartbeat": get_beijing_time_now(),
            }

            self.logger.info(f"WebSocket连接建立: {ws_id}")

            # 发送连接确认消息
            await self._send_to_websocket(
                websocket,
                {
                    "type": "connection_established",
                    "data": {
                        "connection_id": ws_id,
                        "server_time": get_beijing_time_now().isoformat(),
                        "environment": self.environment,
                    },
                    "timestamp": get_beijing_time_now().isoformat(),
                },
            )

            # 启动心跳任务（如果还没有）
            if self._heartbeat_task is None or self._heartbeat_task.done():
                self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())

        except Exception as e:
            self.logger.error(f"WebSocket连接失败: {e}")
            raise

    async def disconnect(self, websocket: WebSocket):
        """
        断开WebSocket连接

        Args:
            websocket: WebSocket连接
        """
        try:
            ws_id = id(websocket)

            # 移除连接
            self._connections.discard(websocket)

            # 清理订阅
            metadata = self._connection_metadata.get(ws_id, {})
            subscribed_tasks = metadata.get("subscribed_tasks", set())

            for task_id in subscribed_tasks:
                if task_id in self._task_subscriptions:
                    self._task_subscriptions[task_id].discard(websocket)
                    if not self._task_subscriptions[task_id]:
                        del self._task_subscriptions[task_id]

            # 清理元数据
            if ws_id in self._connection_metadata:
                del self._connection_metadata[ws_id]

            self.logger.info(f"WebSocket连接断开: {ws_id}")

        except Exception as e:
            self.logger.error(f"WebSocket断开处理失败: {e}")

    async def subscribe_task(self, websocket: WebSocket, task_id: str) -> bool:
        """
        订阅任务进度更新

        Args:
            websocket: WebSocket连接
            task_id: 任务ID

        Returns:
            bool: 是否订阅成功
        """
        try:
            ws_id = id(websocket)

            # 检查连接是否有效
            if websocket not in self._connections:
                return False

            # 添加订阅
            if task_id not in self._task_subscriptions:
                self._task_subscriptions[task_id] = set()

            self._task_subscriptions[task_id].add(websocket)

            # 更新连接元数据
            if ws_id in self._connection_metadata:
                self._connection_metadata[ws_id]["subscribed_tasks"].add(task_id)

            # 发送订阅确认
            await self._send_to_websocket(
                websocket,
                {
                    "type": "subscription_confirmed",
                    "data": {
                        "task_id": task_id,
                        "subscribed_at": get_beijing_time_now().isoformat(),
                    },
                    "timestamp": get_beijing_time_now().isoformat(),
                },
            )

            # 立即发送当前任务状态
            await self._send_current_task_status(websocket, task_id)

            self.logger.debug(f"WebSocket {ws_id} 订阅任务: {task_id}")
            return True

        except Exception as e:
            self.logger.error(f"订阅任务失败: {e}")
            return False

    async def unsubscribe_task(self, websocket: WebSocket, task_id: str) -> bool:
        """
        取消订阅任务进度更新

        Args:
            websocket: WebSocket连接
            task_id: 任务ID

        Returns:
            bool: 是否取消订阅成功
        """
        try:
            ws_id = id(websocket)

            # 移除订阅
            if task_id in self._task_subscriptions:
                self._task_subscriptions[task_id].discard(websocket)
                if not self._task_subscriptions[task_id]:
                    del self._task_subscriptions[task_id]

            # 更新连接元数据
            if ws_id in self._connection_metadata:
                self._connection_metadata[ws_id]["subscribed_tasks"].discard(task_id)

            # 发送取消订阅确认
            await self._send_to_websocket(
                websocket,
                {
                    "type": "unsubscription_confirmed",
                    "data": {
                        "task_id": task_id,
                        "unsubscribed_at": get_beijing_time_now().isoformat(),
                    },
                    "timestamp": get_beijing_time_now().isoformat(),
                },
            )

            self.logger.debug(f"WebSocket {ws_id} 取消订阅任务: {task_id}")
            return True

        except Exception as e:
            self.logger.error(f"取消订阅任务失败: {e}")
            return False

    async def broadcast_task_progress(
        self, task_id: str, progress_data: Dict[str, Any]
    ):
        """
        广播任务进度更新

        Args:
            task_id: 任务ID
            progress_data: 进度数据
        """
        if task_id not in self._task_subscriptions:
            return

        # 准备广播消息
        message = {
            "type": "task_progress",
            "data": {
                "task_id": task_id,
                "progress": progress_data,
                "updated_at": get_beijing_time_now().isoformat(),
            },
            "timestamp": get_beijing_time_now().isoformat(),
        }

        # 广播给所有订阅者
        disconnected_websockets = set()

        for websocket in self._task_subscriptions[task_id].copy():
            try:
                await self._send_to_websocket(websocket, message)
            except WebSocketDisconnect:
                disconnected_websockets.add(websocket)
            except Exception as e:
                self.logger.warning(f"广播消息失败: {e}")
                disconnected_websockets.add(websocket)

        # 清理断开的连接
        for websocket in disconnected_websockets:
            await self.disconnect(websocket)

    async def broadcast_task_status_change(
        self,
        task_id: str,
        old_status: str,
        new_status: str,
        metadata: Optional[Dict] = None,
    ):
        """
        广播任务状态变更

        Args:
            task_id: 任务ID
            old_status: 旧状态
            new_status: 新状态
            metadata: 额外元数据
        """
        if task_id not in self._task_subscriptions:
            return

        message = {
            "type": "task_status_change",
            "data": {
                "task_id": task_id,
                "old_status": old_status,
                "new_status": new_status,
                "metadata": metadata or {},
                "changed_at": get_beijing_time_now().isoformat(),
            },
            "timestamp": get_beijing_time_now().isoformat(),
        }

        # 广播给所有订阅者
        disconnected_websockets = set()

        for websocket in self._task_subscriptions[task_id].copy():
            try:
                await self._send_to_websocket(websocket, message)
            except WebSocketDisconnect:
                disconnected_websockets.add(websocket)
            except Exception as e:
                self.logger.warning(f"广播状态变更失败: {e}")
                disconnected_websockets.add(websocket)

        # 清理断开的连接
        for websocket in disconnected_websockets:
            await self.disconnect(websocket)

    async def handle_client_message(
        self, websocket: WebSocket, message: Dict[str, Any]
    ):
        """
        处理客户端消息

        Args:
            websocket: WebSocket连接
            message: 客户端消息
        """
        try:
            message_type = message.get("type")
            data = message.get("data", {})

            if message_type == "subscribe":
                task_id = data.get("task_id")
                if task_id:
                    await self.subscribe_task(websocket, task_id)
                else:
                    await self._send_error(
                        websocket, "task_id is required for subscription"
                    )

            elif message_type == "unsubscribe":
                task_id = data.get("task_id")
                if task_id:
                    await self.unsubscribe_task(websocket, task_id)
                else:
                    await self._send_error(
                        websocket, "task_id is required for unsubscription"
                    )

            elif message_type == "ping":
                await self._send_to_websocket(
                    websocket,
                    {
                        "type": "pong",
                        "data": {"received_at": get_beijing_time_now().isoformat()},
                        "timestamp": get_beijing_time_now().isoformat(),
                    },
                )

                # 更新心跳时间
                ws_id = id(websocket)
                if ws_id in self._connection_metadata:
                    self._connection_metadata[ws_id][
                        "last_heartbeat"
                    ] = get_beijing_time_now()

            elif message_type == "get_connection_info":
                await self._send_connection_info(websocket)

            else:
                await self._send_error(
                    websocket, f"Unknown message type: {message_type}"
                )

        except Exception as e:
            self.logger.error(f"处理客户端消息失败: {e}")
            await self._send_error(websocket, f"Message processing failed: {str(e)}")

    async def _send_to_websocket(self, websocket: WebSocket, message: Dict[str, Any]):
        """发送消息到WebSocket"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except WebSocketDisconnect:
            # 连接已断开，触发清理
            await self.disconnect(websocket)
            raise
        except Exception as e:
            self.logger.error(f"发送WebSocket消息失败: {e}")
            raise

    async def _send_error(self, websocket: WebSocket, error_message: str):
        """发送错误消息"""
        await self._send_to_websocket(
            websocket,
            {
                "type": "error",
                "data": {
                    "message": error_message,
                    "timestamp": get_beijing_time_now().isoformat(),
                },
                "timestamp": get_beijing_time_now().isoformat(),
            },
        )

    async def _send_connection_info(self, websocket: WebSocket):
        """发送连接信息"""
        ws_id = id(websocket)
        metadata = self._connection_metadata.get(ws_id, {})

        await self._send_to_websocket(
            websocket,
            {
                "type": "connection_info",
                "data": {
                    "connection_id": ws_id,
                    "connected_at": metadata.get("connected_at", "unknown"),
                    "subscribed_tasks": list(metadata.get("subscribed_tasks", set())),
                    "total_connections": len(self._connections),
                    "environment": self.environment,
                },
                "timestamp": get_beijing_time_now().isoformat(),
            },
        )

    async def _send_current_task_status(self, websocket: WebSocket, task_id: str):
        """发送当前任务状态"""
        try:
            if self._task_control_manager:
                status_result = self._task_control_manager.get_task_status(task_id)

                await self._send_to_websocket(
                    websocket,
                    {
                        "type": "current_task_status",
                        "data": {
                            "task_id": task_id,
                            "status": status_result,
                            "retrieved_at": get_beijing_time_now().isoformat(),
                        },
                        "timestamp": get_beijing_time_now().isoformat(),
                    },
                )
        except Exception as e:
            self.logger.warning(f"发送当前任务状态失败: {e}")

    async def _heartbeat_loop(self):
        """心跳循环，检测断开的连接"""
        while self._connections:
            try:
                await asyncio.sleep(self._heartbeat_interval)

                current_time = get_beijing_time_now()
                disconnected_websockets = set()

                # 检查连接心跳
                for websocket in self._connections.copy():
                    ws_id = id(websocket)
                    metadata = self._connection_metadata.get(ws_id)

                    if metadata:
                        last_heartbeat = metadata.get("last_heartbeat")
                        if last_heartbeat:
                            # 计算心跳间隔
                            if isinstance(last_heartbeat, str):
                                last_heartbeat = datetime.fromisoformat(last_heartbeat)

                            seconds_since_heartbeat = (
                                current_time - last_heartbeat
                            ).total_seconds()

                            # 如果超过2倍心跳间隔没有心跳，认为连接断开
                            if seconds_since_heartbeat > self._heartbeat_interval * 2:
                                disconnected_websockets.add(websocket)
                                continue

                    # 发送心跳ping
                    try:
                        await self._send_to_websocket(
                            websocket,
                            {
                                "type": "heartbeat",
                                "data": {"server_time": current_time.isoformat()},
                                "timestamp": current_time.isoformat(),
                            },
                        )
                    except:
                        disconnected_websockets.add(websocket)

                # 清理断开的连接
                for websocket in disconnected_websockets:
                    await self.disconnect(websocket)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"心跳循环错误: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取WebSocket统计信息

        Returns:
            Dict: 统计信息
        """
        return {
            "total_connections": len(self._connections),
            "total_subscriptions": sum(
                len(subs) for subs in self._task_subscriptions.values()
            ),
            "tasks_with_subscribers": len(self._task_subscriptions),
            "subscribed_tasks": list(self._task_subscriptions.keys()),
            "heartbeat_interval": self._heartbeat_interval,
            "environment": self.environment,
            "connections_metadata": {
                ws_id: {
                    "connected_at": meta.get("connected_at"),
                    "subscribed_tasks_count": len(meta.get("subscribed_tasks", set())),
                    "client_info": meta.get("client_info", {}),
                }
                for ws_id, meta in self._connection_metadata.items()
            },
        }

    async def shutdown(self):
        """关闭WebSocket管理器"""
        try:
            # 取消心跳任务
            if self._heartbeat_task and not self._heartbeat_task.done():
                self._heartbeat_task.cancel()
                try:
                    await self._heartbeat_task
                except asyncio.CancelledError:
                    pass

            # 关闭所有连接
            disconnection_tasks = []
            for websocket in self._connections.copy():
                disconnection_tasks.append(self.disconnect(websocket))

            if disconnection_tasks:
                await asyncio.gather(*disconnection_tasks, return_exceptions=True)

            self.logger.info("WebSocket管理器已关闭")

        except Exception as e:
            self.logger.error(f"关闭WebSocket管理器失败: {e}")


# 全局WebSocket管理器实例
_websocket_manager = None


def get_websocket_manager(environment: str = "dev") -> WebSocketManager:
    """获取WebSocket管理器实例"""
    global _websocket_manager
    if _websocket_manager is None:
        _websocket_manager = WebSocketManager(environment)
    return _websocket_manager
