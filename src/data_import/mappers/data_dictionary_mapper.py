#!/usr/bin/env python3
"""
数据字典权威映射器

严格基于DATA_DICTIONARY.md权威定义进行文件到表的映射
核心原则：数据字典是唯一事实来源，不可变更
"""

import re
import logging
from pathlib import Path
from typing import Dict, Optional, List


class DataDictionaryMapper:
    """数据字典权威映射器"""

    def __init__(self):
        """初始化映射器"""
        self.logger = logging.getLogger(__name__)

        # DATA_DICTIONARY.md权威表定义（不可变更）
        self.data_dictionary_tables = {
            # 期货主力合约K线表（按频率分表，包含所有品种）
            "fut_main_contract_kline_5min": {
                "description": "期货主力合约5分钟K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_datetime",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "primary_key": ["contract_code", "trade_datetime"],
                "data_scope": "all_futures_contracts",  # 包含所有期货品种
            },
            "fut_main_contract_kline_15min": {
                "description": "期货主力合约15分钟K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_datetime",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "primary_key": ["contract_code", "trade_datetime"],
                "data_scope": "all_futures_contracts",
            },
            "fut_main_contract_kline_30min": {
                "description": "期货主力合约30分钟K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_datetime",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "primary_key": ["contract_code", "trade_datetime"],
                "data_scope": "all_futures_contracts",
            },
            "fut_main_contract_kline_daily": {
                "description": "期货主力合约日K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_date",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "primary_key": ["contract_code", "trade_date"],
                "data_scope": "all_futures_contracts",
            },
            # 股票数据表
            "stock_kline_daily": {
                "description": "股票日K线数据表",
                "required_columns": [
                    "symbol",
                    "trade_date",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "turnover_rate",
                ],
                "primary_key": ["symbol", "trade_date"],
                "data_scope": "all_stocks",
            },
            "stock_basic_info": {
                "description": "股票基础信息表",
                "required_columns": ["symbol", "name", "market", "industry"],
                "primary_key": ["symbol"],
                "data_scope": "all_stocks",
            },
            # 期货基础信息表
            "fut_basic_info": {
                "description": "期货基础信息表",
                "required_columns": [
                    "product_code",
                    "product_name",
                    "exchange",
                    "contract_size",
                    "tick_size",
                    "trading_unit",
                ],
                "primary_key": ["product_code"],
                "data_scope": "all_futures_products",
            },
        }

    def map_file_to_data_dictionary_table(self, file_path: Path) -> str:
        """
        将文件映射到DATA_DICTIONARY.md权威表定义

        核心原则：
        1. 基于目录结构识别数据频率（优先）
        2. 所有同频率数据映射到同一张表
        3. 通过主键字段区分不同实体

        Args:
            file_path: 文件路径

        Returns:
            str: DATA_DICTIONARY.md中的标准表名
        """
        filename = file_path.stem.lower()
        directory_name = file_path.parent.name if file_path.parent else ""

        self.logger.debug(
            f"映射文件到数据字典表: {file_path.name}, 目录: {directory_name}"
        )

        # 1. 基于目录结构的权威映射（支持结构化数据源）
        directory_to_table = {
            # FromC2C数据源目录映射
            "期货主连5min": "fut_main_contract_kline_5min",
            "期货主连15min": "fut_main_contract_kline_15min",
            "期货主连30min": "fut_main_contract_kline_30min",
            "期货主连1min": "fut_main_contract_kline_1min",
            "期货主连日线": "fut_main_contract_kline_daily",
            "期货主连daily": "fut_main_contract_kline_daily",
            # 其他可能的数据源目录
            "futures_5min": "fut_main_contract_kline_5min",
            "futures_15min": "fut_main_contract_kline_15min",
            "futures_30min": "fut_main_contract_kline_30min",
            "futures_daily": "fut_main_contract_kline_daily",
            "stocks_daily": "stock_kline_daily",
            "stock_basic": "stock_basic_info",
            "futures_basic": "fut_basic_info",
        }

        if directory_name in directory_to_table:
            target_table = directory_to_table[directory_name]
            self.logger.info(
                f"目录映射到数据字典表: {directory_name} -> {target_table}"
            )
            return target_table

        # 2. 基于文件名模式的权威映射

        # 期货K线文件名模式（所有品种映射到对应频率表）
        futures_patterns = [
            # 30分钟（优先匹配长时间周期）
            (
                r".*(?:主力合约|main_contract).*30(?:分钟|min).*",
                "fut_main_contract_kline_30min",
            ),
            # 15分钟
            (
                r".*(?:主力合约|main_contract).*15(?:分钟|min).*",
                "fut_main_contract_kline_15min",
            ),
            # 5分钟
            (
                r".*(?:主力合约|main_contract).*5(?:分钟|min).*",
                "fut_main_contract_kline_5min",
            ),
            # 1分钟
            (
                r".*(?:主力合约|main_contract).*1(?:分钟|min).*",
                "fut_main_contract_kline_1min",
            ),
            # 日线
            (
                r".*(?:主力合约|main_contract).*(?:日|daily|1d).*",
                "fut_main_contract_kline_daily",
            ),
        ]

        for pattern, table_name in futures_patterns:
            if re.search(pattern, filename, re.IGNORECASE):
                self.logger.info(
                    f"期货文件映射到数据字典表: {file_path.name} -> {table_name}"
                )
                return table_name

        # 股票文件名模式
        stock_patterns = [
            (r".*(?:股票|stock).*(?:日|daily).*", "stock_kline_daily"),
            (r".*(?:股票基础|stock_basic).*", "stock_basic_info"),
        ]

        for pattern, table_name in stock_patterns:
            if re.search(pattern, filename, re.IGNORECASE):
                self.logger.info(
                    f"股票文件映射到数据字典表: {file_path.name} -> {table_name}"
                )
                return table_name

        # 期货基础信息
        if re.search(r".*(?:期货基础|fut_basic|合约信息).*", filename, re.IGNORECASE):
            self.logger.info(
                f"期货基础信息文件映射: {file_path.name} -> fut_basic_info"
            )
            return "fut_basic_info"

        # 3. 兜底机制：生成符合规范的表名
        legacy_table_name = self._generate_standard_table_name(file_path)
        self.logger.warning(f"使用兜底映射: {file_path.name} -> {legacy_table_name}")
        return legacy_table_name

    def _generate_standard_table_name(self, file_path: Path) -> str:
        """生成符合命名规范的表名"""
        table_name = file_path.stem.lower()

        # 替换特殊字符为下划线
        table_name = re.sub(r"[^a-z0-9_]", "_", table_name)

        # 确保以字母开头
        if not table_name[0].isalpha():
            table_name = "data_" + table_name

        # 移除重复下划线
        table_name = re.sub(r"_+", "_", table_name)

        return table_name.strip("_")

    def is_data_dictionary_table(self, table_name: str) -> bool:
        """
        检查是否为DATA_DICTIONARY.md定义的标准表

        Args:
            table_name: 表名

        Returns:
            bool: 是否为标准表
        """
        return table_name in self.data_dictionary_tables

    def get_table_schema(self, table_name: str) -> Optional[Dict]:
        """
        获取表的权威架构定义

        Args:
            table_name: 表名

        Returns:
            Optional[Dict]: 表架构定义
        """
        return self.data_dictionary_tables.get(table_name)

    def get_supported_tables(self) -> List[str]:
        """
        获取所有支持的DATA_DICTIONARY.md表

        Returns:
            List[str]: 支持的表名列表
        """
        return list(self.data_dictionary_tables.keys())

    def validate_table_compliance(self, table_name: str, columns: List[str]) -> Dict:
        """
        验证表结构是否符合DATA_DICTIONARY.md规范

        Args:
            table_name: 表名
            columns: 实际列名列表

        Returns:
            Dict: 验证结果
        """
        result = {
            "compliant": False,
            "missing_columns": [],
            "extra_columns": [],
            "table_definition": None,
        }

        if not self.is_data_dictionary_table(table_name):
            result["error"] = f"表 {table_name} 不在DATA_DICTIONARY.md定义中"
            return result

        table_def = self.data_dictionary_tables[table_name]
        required_columns = set(table_def["required_columns"])
        actual_columns = set(columns)

        # 允许的系统字段
        system_columns = {"created_at", "updated_at"}
        actual_columns_clean = actual_columns - system_columns

        # 检查缺失字段
        missing = required_columns - actual_columns_clean
        # 检查多余字段
        extra = actual_columns_clean - required_columns

        result["missing_columns"] = list(missing)
        result["extra_columns"] = list(extra)
        result["table_definition"] = table_def
        result["compliant"] = len(missing) == 0

        return result

    def get_mapping_summary(self) -> Dict:
        """
        获取映射器统计摘要

        Returns:
            Dict: 映射器摘要
        """
        return {
            "mapper_type": "DataDictionaryMapper",
            "authority_source": "DATA_DICTIONARY.md",
            "total_tables": len(self.data_dictionary_tables),
            "futures_tables": len(
                [t for t in self.data_dictionary_tables.keys() if "fut_" in t]
            ),
            "stock_tables": len(
                [t for t in self.data_dictionary_tables.keys() if "stock_" in t]
            ),
            "supported_tables": self.get_supported_tables(),
            "mapping_principle": "frequency_based_aggregation",  # 按频率聚合，不按品种分表
        }
