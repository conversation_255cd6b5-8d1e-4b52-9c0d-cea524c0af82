#!/usr/bin/env python3
"""
业务表映射器
统一的文件名到业务表映射和字段标准化功能
"""

import re
import logging
from pathlib import Path
from typing import Dict, Optional, List
import pandas as pd

from ...utils.time_utils import get_beijing_time_now


class BusinessTableMapper:
    """业务表映射器"""

    def __init__(self):
        """初始化映射器"""
        self.logger = logging.getLogger(__name__)

        # DATA_DICTIONARY.md权威表结构定义（严格对应权威数据字典）
        self.standard_table_schemas = {
            # 期货主力合约5分钟K线表（DATA_DICTIONARY.md扩展）
            "fut_main_contract_kline_5min": {
                "description": "期货主力合约5分钟K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_datetime",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "column_mapping": {
                    "datetime": "trade_datetime",
                    "date": "trade_datetime",
                    "money": "amount",  # FromC2C特有映射
                    "合约代码": "contract_code",
                    "交易时间": "trade_datetime",
                    "开盘价": "open",
                    "最高价": "high",
                    "最低价": "low",
                    "收盘价": "close",
                    "成交量": "volume",
                    "成交额": "amount",
                    "持仓量": "open_interest",
                },
                "primary_key": ["contract_code", "trade_datetime"],
                "contains_multiple_contracts": True,  # 包含多个品种合约
            },
            # 期货主力合约15分钟K线表（DATA_DICTIONARY.md标准定义）
            "fut_main_contract_kline_15min": {
                "description": "期货主力合约15分钟K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_datetime",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "column_mapping": {
                    "datetime": "trade_datetime",
                    "date": "trade_datetime",
                    "money": "amount",  # FromC2C特有映射
                    "合约代码": "contract_code",
                    "交易时间": "trade_datetime",
                    "开盘价": "open",
                    "最高价": "high",
                    "最低价": "low",
                    "收盘价": "close",
                    "成交量": "volume",
                    "成交额": "amount",
                    "持仓量": "open_interest",
                },
                "primary_key": ["contract_code", "trade_datetime"],
                "contains_multiple_contracts": True,  # 包含多个品种合约
            },
            # 期货主力合约30分钟K线表（DATA_DICTIONARY.md扩展）
            "fut_main_contract_kline_30min": {
                "description": "期货主力合约30分钟K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_datetime",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "column_mapping": {
                    "datetime": "trade_datetime",
                    "date": "trade_datetime",
                    "money": "amount",  # FromC2C特有映射
                    "合约代码": "contract_code",
                    "交易时间": "trade_datetime",
                    "开盘价": "open",
                    "最高价": "high",
                    "最低价": "low",
                    "收盘价": "close",
                    "成交量": "volume",
                    "成交额": "amount",
                    "持仓量": "open_interest",
                },
                "primary_key": ["contract_code", "trade_datetime"],
                "contains_multiple_contracts": True,  # 包含多个品种合约
            },
            # 期货主力合约日K线表（DATA_DICTIONARY.md扩展）
            "fut_main_contract_kline_daily": {
                "description": "期货主力合约日K线数据表（所有品种）",
                "required_columns": [
                    "contract_code",
                    "trade_date",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "open_interest",
                ],
                "column_mapping": {
                    "date": "trade_date",
                    "datetime": "trade_date",
                    "money": "amount",  # FromC2C特有映射
                    "合约代码": "contract_code",
                    "交易日期": "trade_date",
                    "开盘价": "open",
                    "最高价": "high",
                    "最低价": "low",
                    "收盘价": "close",
                    "成交量": "volume",
                    "成交额": "amount",
                    "持仓量": "open_interest",
                },
                "primary_key": ["contract_code", "trade_date"],
                "contains_multiple_contracts": True,  # 包含多个品种合约
            },
            # 股票日K线（完善字段）
            "stock_kline_daily": {
                "required_columns": [
                    "symbol",
                    "trade_date",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                    "amount",
                    "turnover_rate",
                ],
                "column_mapping": {
                    "股票代码": "symbol",
                    "交易日期": "trade_date",
                    "开盘价": "open",
                    "最高价": "high",
                    "最低价": "low",
                    "收盘价": "close",
                    "成交量": "volume",
                    "成交额": "amount",
                    "换手率": "turnover_rate",
                },
            },
            # 期货基础信息（完善字段）
            "fut_basic_info": {
                "required_columns": [
                    "product_code",
                    "product_name",
                    "exchange",
                    "contract_size",
                    "tick_size",
                    "trading_unit",
                ],
                "column_mapping": {
                    "品种代码": "product_code",
                    "品种名称": "product_name",
                    "交易所": "exchange",
                    "合约乘数": "contract_size",
                    "最小变动价位": "tick_size",
                    "交易单位": "trading_unit",
                    "交割月份": "delivery_month",
                },
            },
            # 股票基础信息（完善字段）
            "stock_basic_info": {
                "required_columns": ["symbol", "name", "market", "industry"],
                "column_mapping": {
                    "股票代码": "symbol",
                    "股票名称": "name",
                    "市场": "market",
                    "行业": "industry",
                    "上市日期": "list_date",
                    "交易状态": "status",
                },
            },
        }

    def map_file_to_table(self, file_path: Path) -> str:
        """
        文件到业务表的映射

        Args:
            file_path: 文件路径

        Returns:
            str: 目标业务表名
        """
        filename = file_path.stem.lower()

        # 期货主力合约K线映射规则（支持多种频率）

        # 1分钟K线映射
        futures_1min_patterns = [
            r".*主力合约.*1分钟.*",
            r".*main_contract.*1min.*",
            r".*_1分钟数据.*",
            r"^[a-z]{1,2}_主力合约_1分钟数据$",
            r"^[a-z]{1,2}\d{4}_主力合约_1分钟$",
        ]

        for pattern in futures_1min_patterns:
            if re.match(pattern, filename):
                self.logger.info(f"文件 {file_path.name} 映射到期货1分钟K线表")
                return "fut_main_contract_kline_1min"

        # 15分钟K线映射（需要在5分钟之前检查）
        futures_15min_patterns = [
            r".*主力合约.*15分钟.*",
            r".*main_contract.*15min.*",
            r".*_15分钟数据.*",
            r"^[a-z]{1,2}_主力合约_15分钟数据$",  # 如: al_主力合约_15分钟数据
            r"^[a-z]{1,2}\d{4}_主力合约_15分钟$",  # 如: al2501_主力合约_15分钟
        ]

        for pattern in futures_15min_patterns:
            if re.match(pattern, filename):
                self.logger.info(f"文件 {file_path.name} 映射到期货15分钟K线表")
                return "fut_main_contract_kline_15min"

        # 5分钟K线映射（在15分钟之后检查）
        futures_5min_patterns = [
            r".*主力合约.*5分钟.*",
            r".*main_contract.*5min.*",
            r".*_5分钟数据.*",
            r"^[a-z]{1,2}_主力合约_5分钟数据$",
            r"^[a-z]{1,2}\d{4}_主力合约_5分钟$",
        ]

        for pattern in futures_5min_patterns:
            if re.match(pattern, filename):
                self.logger.info(f"文件 {file_path.name} 映射到期货5分钟K线表")
                return "fut_main_contract_kline_5min"

        # 期货主力合约日K线映射规则
        futures_daily_patterns = [
            r".*主力合约.*日.*",
            r".*main_contract.*daily.*",
            r".*主力合约.*1d.*",
            r"^[a-z]{1,2}_主力合约_日K线$",
            r"^[a-z]{1,2}\d{4}_主力合约_日线$",
        ]

        for pattern in futures_daily_patterns:
            if re.match(pattern, filename):
                self.logger.info(f"文件 {file_path.name} 映射到期货日K线表")
                return "fut_main_contract_kline_daily"

        # 股票日K线映射规则
        stock_daily_patterns = [
            r".*股票.*日k.*",
            r".*stock.*daily.*",
            r".*a股.*日.*",
            r"^\d{6}.*日k.*",  # 如: 000001_日K线
        ]

        for pattern in stock_daily_patterns:
            if re.match(pattern, filename):
                self.logger.info(f"文件 {file_path.name} 映射到股票日K线表")
                return "stock_kline_daily"

        # 期货基础信息映射
        if any(
            keyword in filename for keyword in ["期货基础", "fut_basic", "合约信息"]
        ):
            self.logger.info(f"文件 {file_path.name} 映射到期货基础信息表")
            return "fut_basic_info"

        # 股票基础信息映射
        if any(
            keyword in filename for keyword in ["股票基础", "stock_basic", "股票列表"]
        ):
            self.logger.info(f"文件 {file_path.name} 映射到股票基础信息表")
            return "stock_basic_info"

        # 兜底：使用传统表名生成
        legacy_table_name = self._generate_legacy_table_name(file_path)
        self.logger.info(f"文件 {file_path.name} 使用传统表名: {legacy_table_name}")
        return legacy_table_name

    def _generate_legacy_table_name(self, file_path: Path) -> str:
        """生成传统表名（兜底机制）"""
        table_name = file_path.stem.lower()

        # 替换特殊字符
        table_name = table_name.replace("-", "_").replace(" ", "_")

        # 确保表名符合DuckDB命名规范
        if not table_name[0].isalpha():
            table_name = "csv_" + table_name

        return table_name

    def map_columns_to_standard(
        self, df: pd.DataFrame, table_type: str, file_path: Path
    ) -> pd.DataFrame:
        """
        字段标准化映射

        Args:
            df: 原始数据
            table_type: 目标表类型
            file_path: 文件路径（用于提取额外信息）

        Returns:
            pd.DataFrame: 标准化后的数据
        """
        if table_type not in self.standard_table_schemas:
            self.logger.warning(f"未知表类型 {table_type}，跳过字段标准化")
            return df

        schema = self.standard_table_schemas[table_type]
        standardized_df = df.copy()

        # 字段名映射
        column_mapping = schema["column_mapping"]
        for old_col, new_col in column_mapping.items():
            if old_col in standardized_df.columns:
                standardized_df = standardized_df.rename(columns={old_col: new_col})
                self.logger.debug(f"字段映射: {old_col} -> {new_col}")

        # 期货主力合约K线特殊处理（支持所有频率）
        if table_type in [
            "fut_main_contract_kline_1min",
            "fut_main_contract_kline_5min",
            "fut_main_contract_kline_15min",
            "fut_main_contract_kline_daily",
        ]:
            standardized_df = self._process_futures_data(
                standardized_df, file_path, table_type
            )

        # 股票K线特殊处理
        elif table_type == "stock_kline_daily":
            standardized_df = self._process_stock_data(standardized_df, file_path)

        # 期货基础信息处理
        elif table_type == "fut_basic_info":
            standardized_df = self._process_futures_basic_data(
                standardized_df, file_path
            )

        # 股票基础信息处理
        elif table_type == "stock_basic_info":
            standardized_df = self._process_stock_basic_data(standardized_df, file_path)

        # 添加系统字段
        current_time = get_beijing_time_now()
        standardized_df["created_at"] = current_time
        standardized_df["updated_at"] = current_time

        self.logger.info(
            f"字段标准化完成，表类型: {table_type}，数据行数: {len(standardized_df)}"
        )
        return standardized_df

    def _process_futures_data(
        self, df: pd.DataFrame, file_path: Path, table_type: str
    ) -> pd.DataFrame:
        """处理期货数据的特殊逻辑"""
        # 添加contract_code字段
        if "contract_code" not in df.columns:
            contract_code = self._extract_contract_code_from_filename(file_path)
            if contract_code:
                df["contract_code"] = contract_code
                self.logger.debug(f"从文件名提取合约代码: {contract_code}")
            else:
                raise ValueError(f"无法从文件名提取合约代码: {file_path.name}")

        # 时间字段标准化（根据表类型处理）
        if table_type == "fut_main_contract_kline_daily":
            # 日K线使用trade_date字段
            if "trade_date" not in df.columns and "date" in df.columns:
                df["trade_date"] = pd.to_datetime(df["date"]).dt.date
            elif "trade_date" in df.columns:
                df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
        else:
            # 分钟级K线使用trade_datetime字段
            if "trade_datetime" not in df.columns and "datetime" in df.columns:
                df["trade_datetime"] = pd.to_datetime(df["datetime"])
            elif "trade_datetime" in df.columns:
                df["trade_datetime"] = pd.to_datetime(df["trade_datetime"])

        return df

    def _process_stock_data(self, df: pd.DataFrame, file_path: Path) -> pd.DataFrame:
        """处理股票数据的特殊逻辑"""
        # 时间字段标准化
        if "trade_date" not in df.columns and "date" in df.columns:
            df["trade_date"] = pd.to_datetime(df["date"]).dt.date
        elif "trade_date" in df.columns:
            df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date

        return df

    def _process_futures_basic_data(
        self, df: pd.DataFrame, file_path: Path
    ) -> pd.DataFrame:
        """处理期货基础信息数据"""
        # 这里可以添加期货基础信息的特殊处理逻辑
        return df

    def _process_stock_basic_data(
        self, df: pd.DataFrame, file_path: Path
    ) -> pd.DataFrame:
        """处理股票基础信息数据"""
        # 这里可以添加股票基础信息的特殊处理逻辑
        return df

    def _extract_contract_code_from_filename(self, file_path: Path) -> Optional[str]:
        """
        从文件名提取合约代码

        Args:
            file_path: 文件路径

        Returns:
            Optional[str]: 合约代码
        """
        filename = file_path.stem

        # 匹配模式: al_主力合约_15分钟数据 -> AL
        match = re.match(r"^([a-zA-Z]{1,2})_.*", filename)
        if match:
            return match.group(1).upper()

        # 匹配模式: al2501_主力合约_15分钟 -> AL
        match = re.match(r"^([a-zA-Z]{1,2})\d{4}_.*", filename)
        if match:
            return match.group(1).upper()

        # 其他匹配规则可以在这里扩展
        self.logger.warning(f"无法从文件名 {filename} 提取合约代码")
        return None

    def get_required_columns(self, table_type: str) -> List[str]:
        """
        获取指定表类型的必需字段

        Args:
            table_type: 表类型

        Returns:
            List[str]: 必需字段列表
        """
        if table_type in self.standard_table_schemas:
            return self.standard_table_schemas[table_type]["required_columns"]
        return []

    def validate_column_mapping(self, df: pd.DataFrame, table_type: str) -> Dict:
        """
        验证字段映射的完整性

        Args:
            df: 数据DataFrame
            table_type: 表类型

        Returns:
            Dict: 验证结果
        """
        result = {
            "valid": True,
            "missing_columns": [],
            "available_columns": list(df.columns),
            "mapped_columns": [],
        }

        if table_type not in self.standard_table_schemas:
            result["valid"] = False
            result["error"] = f"未知的表类型: {table_type}"
            return result

        schema = self.standard_table_schemas[table_type]
        required_columns = schema["required_columns"]
        column_mapping = schema["column_mapping"]

        # 检查直接匹配和映射匹配
        available_columns = set(df.columns)
        mapped_columns = set()

        for old_col, new_col in column_mapping.items():
            if old_col in available_columns:
                mapped_columns.add(new_col)
                result["mapped_columns"].append(f"{old_col} -> {new_col}")

        # 合并直接匹配和映射匹配
        all_available = available_columns.union(mapped_columns)

        # 排除系统自动添加的字段
        system_fields = {"created_at", "updated_at"}
        required_user_columns = [
            col for col in required_columns if col not in system_fields
        ]

        # 检查缺失字段
        missing_columns = [
            col for col in required_user_columns if col not in all_available
        ]
        result["missing_columns"] = missing_columns
        result["valid"] = len(missing_columns) == 0

        return result

    def is_business_table(self, table_name: str) -> bool:
        """
        判断是否为业务表

        Args:
            table_name: 表名

        Returns:
            bool: 是否为业务表
        """
        return table_name in self.standard_table_schemas

    def get_supported_table_types(self) -> List[str]:
        """
        获取支持的业务表类型列表

        Returns:
            List[str]: 支持的表类型列表
        """
        return list(self.standard_table_schemas.keys())

    def get_mapping_statistics(self) -> Dict:
        """
        获取映射器统计信息

        Returns:
            Dict: 统计信息
        """
        return {
            "total_business_tables": len(self.standard_table_schemas),
            "supported_table_types": self.get_supported_table_types(),
            "mapping_patterns": {
                "futures_1min": [
                    "*_主力合约_1分钟*",
                    "*_main_contract_1min*",
                    "al_主力合约_1分钟数据",
                    "rb2501_主力合约_1分钟",
                ],
                "futures_5min": [
                    "*_主力合约_5分钟*",
                    "*_main_contract_5min*",
                    "al_主力合约_5分钟数据",
                    "rb2501_主力合约_5分钟",
                ],
                "futures_15min": [
                    "*_主力合约_15分钟*",
                    "*_main_contract_15min*",
                    "al_主力合约_15分钟数据",
                    "rb2501_主力合约_15分钟",
                ],
                "futures_daily": [
                    "*_主力合约_日*",
                    "*_main_contract_daily*",
                    "al_主力合约_日K线",
                    "rb2501_主力合约_日线",
                ],
                "stock_daily": ["*_股票_日K*", "*_stock_daily*", "000001_股票日K线"],
                "futures_basic": ["期货基础信息", "fut_basic_info", "合约信息"],
                "stock_basic": ["股票基础信息", "stock_basic_info", "股票列表"],
            },
        }
