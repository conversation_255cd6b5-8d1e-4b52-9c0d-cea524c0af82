#!/usr/bin/env python3
"""
统一CSV文件验证器
提供标准化的CSV文件验证功能，消除重复验证逻辑
"""

import csv
import logging
from pathlib import Path
from typing import Dict
import pandas as pd


class CSVValidator:
    """统一的CSV文件验证器"""

    def __init__(self, config: Dict):
        """初始化验证器"""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 支持的文件扩展名
        self.supported_extensions = {".csv", ".tsv", ".txt"}

        # 配置参数
        self.encoding = config.get("encoding", "utf-8")
        self.delimiter = config.get("delimiter", ",")
        self.max_file_size_mb = config.get("max_file_size_mb", 200)

    def validate_file_format(self, file_path: Path) -> Dict:
        """统一的文件格式验证"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "file_info": {
                "path": str(file_path),
                "exists": False,
                "size_mb": 0,
                "columns": 0,
                "sample_header": [],
            },
        }

        try:
            # 检查文件是否存在
            if not file_path.exists():
                validation_result["valid"] = False
                validation_result["errors"].append("文件不存在")
                return validation_result

            validation_result["file_info"]["exists"] = True

            # 检查文件扩展名
            if file_path.suffix.lower() not in self.supported_extensions:
                validation_result["warnings"].append(
                    f"不支持的文件扩展名: {file_path.suffix}"
                )

            # 检查文件大小
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            validation_result["file_info"]["size_mb"] = file_size_mb

            if file_size_mb > self.max_file_size_mb:
                validation_result["warnings"].append(f"文件过大 ({file_size_mb:.1f}MB)")

            # 检查文件内容格式
            try:
                with open(file_path, "r", encoding=self.encoding) as f:
                    # 读取前几行检查格式
                    sample_lines = []
                    for i, line in enumerate(f):
                        if i >= 5:  # 只读前5行
                            break
                        sample_lines.append(line.strip())

                if not sample_lines:
                    validation_result["valid"] = False
                    validation_result["errors"].append("文件为空")
                    return validation_result

                # 检查CSV格式
                reader = csv.reader(sample_lines, delimiter=self.delimiter)
                rows = list(reader)

                if not rows:
                    validation_result["valid"] = False
                    validation_result["errors"].append("无法解析CSV格式")
                    return validation_result

                # 检查列数一致性
                if len(rows) > 1:
                    col_counts = [len(row) for row in rows]
                    if len(set(col_counts)) > 1:
                        validation_result["warnings"].append("列数不一致")

                validation_result["file_info"]["columns"] = len(rows[0]) if rows else 0
                validation_result["file_info"]["sample_header"] = (
                    rows[0] if rows else []
                )

            except UnicodeDecodeError:
                validation_result["valid"] = False
                validation_result["errors"].append(
                    f"文件编码错误，期望: {self.encoding}"
                )
            except csv.Error as e:
                validation_result["valid"] = False
                validation_result["errors"].append(f"CSV格式错误: {str(e)}")

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"文件验证异常: {str(e)}")

        return validation_result

    def validate_data_compliance(self, df: pd.DataFrame, table_type: str) -> Dict:
        """统一的数据合规验证"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "data_info": {
                "rows": len(df),
                "columns": len(df.columns),
                "table_type": table_type,
            },
        }

        try:
            # 基础数据检查
            if df.empty:
                validation_result["valid"] = False
                validation_result["errors"].append("数据为空")
                return validation_result

            # 检查重复行
            duplicate_rows = df.duplicated().sum()
            if duplicate_rows > 0:
                validation_result["warnings"].append(
                    f"发现 {duplicate_rows} 行重复数据"
                )

            # 期货主力合约K线特殊验证
            if table_type == "fut_main_contract_kline_15min":
                self._validate_futures_data(df, validation_result)

            # 股票K线特殊验证
            elif table_type == "stock_kline_daily":
                self._validate_stock_data(df, validation_result)

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"数据验证异常: {str(e)}")

        return validation_result

    def _validate_futures_data(self, df: pd.DataFrame, result: Dict):
        """验证期货数据"""
        # 验证合约代码格式
        if "contract_code" in df.columns:
            invalid_codes = df[
                ~df["contract_code"].str.match(r"^[A-Z]{1,2}$", na=False)
            ]
            if not invalid_codes.empty:
                result["errors"].append(f"合约代码格式错误: {len(invalid_codes)}条记录")

        # 验证价格字段为正数
        price_columns = ["open", "high", "low", "close"]
        for col in price_columns:
            if col in df.columns:
                negative_prices = df[df[col] <= 0]
                if not negative_prices.empty:
                    result["errors"].append(
                        f"{col}字段存在非正数: {len(negative_prices)}条记录"
                    )

        # 更新验证状态
        result["valid"] = len(result["errors"]) == 0

    def _validate_stock_data(self, df: pd.DataFrame, result: Dict):
        """验证股票数据"""
        # 验证股票代码格式 (6位数字)
        if "symbol" in df.columns:
            invalid_symbols = df[~df["symbol"].str.match(r"^\d{6}$", na=False)]
            if not invalid_symbols.empty:
                result["warnings"].append(
                    f"股票代码格式可能错误: {len(invalid_symbols)}条记录"
                )

        # 验证价格字段为正数
        price_columns = ["open", "high", "low", "close"]
        for col in price_columns:
            if col in df.columns:
                negative_prices = df[df[col] <= 0]
                if not negative_prices.empty:
                    result["errors"].append(
                        f"{col}字段存在非正数: {len(negative_prices)}条记录"
                    )

        # 更新验证状态
        result["valid"] = len(result["errors"]) == 0
