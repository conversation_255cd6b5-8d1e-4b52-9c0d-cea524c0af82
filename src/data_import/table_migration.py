#!/usr/bin/env python3
"""
数据表迁移工具
将分散的CSV导入表数据合并到标准业务表

特性：
- 扫描分散的期货表数据
- 数据标准化和合规验证
- 安全的表迁移和备份
- 详细的迁移日志和报告
"""

import logging
from pathlib import Path
from typing import Dict, List
from datetime import datetime
import pandas as pd
import re

from ..database.connection_manager import DuckDBConnectionManager
from ..utils.config_loader import ConfigLoader
from ..utils.time_utils import get_beijing_time_now
from .csv_importer import CSVImporter


class TableMigrationManager:
    """数据表迁移管理器"""

    def __init__(self, environment: str = "test"):
        """
        初始化迁移管理器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.config_loader = ConfigLoader()
        self.config = self.config_loader.get_config(environment)

        # 数据库连接管理器
        self.connection_manager = DuckDBConnectionManager(environment)

        # CSV导入器（用于数据标准化）
        self.csv_importer = CSVImporter(environment)

        # 日志配置
        self.logger = logging.getLogger(__name__)

        # 迁移统计
        self.migration_stats = {
            "total_tables": 0,
            "migrated_tables": 0,
            "skipped_tables": 0,
            "error_tables": 0,
            "total_records": 0,
            "migrated_records": 0,
            "errors": [],
        }

    def scan_scattered_futures_tables(self) -> List[str]:
        """
        扫描分散的期货表

        Returns:
            List[str]: 分散的期货表名列表
        """
        # 获取所有表名
        all_tables = self.connection_manager.get_all_tables()

        # 期货主力合约15分钟K线相关表的匹配模式
        futures_15min_patterns = [
            r"^[a-z]{1,2}_主力合约_15分钟数据$",  # 如: al_主力合约_15分钟数据
            r"^[a-z]{1,2}\d{4}_主力合约_15分钟$",  # 如: al2501_主力合约_15分钟
            r"^[a-z]{1,2}_15分钟数据$",  # 如: al_15分钟数据
            r"^[a-z]{1,2}_主力合约.*15分钟.*$",  # 其他变体
        ]

        scattered_tables = []
        for table_name in all_tables:
            # 排除已经是标准业务表的
            if table_name in [
                "fut_main_contract_kline_15min",
                "stock_kline_daily",
                "fut_basic_info",
                "stock_basic_info",
            ]:
                continue

            # 检查是否匹配期货表模式
            for pattern in futures_15min_patterns:
                if re.match(pattern, table_name):
                    scattered_tables.append(table_name)
                    break

        self.logger.info(f"发现 {len(scattered_tables)} 个分散的期货表需要迁移")
        return scattered_tables

    def analyze_table_structure(self, table_name: str) -> Dict:
        """
        分析表结构

        Args:
            table_name: 表名

        Returns:
            Dict: 表结构分析结果
        """
        analysis_result = {
            "table_name": table_name,
            "record_count": 0,
            "columns": [],
            "sample_data": None,
            "target_business_table": None,
            "migration_feasible": False,
            "issues": [],
        }

        try:
            # 获取记录数
            analysis_result["record_count"] = self.connection_manager.get_table_count(
                table_name
            )

            # 获取表结构
            columns_info = self.connection_manager.get_table_columns(table_name)
            analysis_result["columns"] = [col["name"] for col in columns_info]

            # 获取样本数据
            query = f"SELECT * FROM {table_name} LIMIT 5"
            sample_data = self.connection_manager.execute_query(query)
            analysis_result["sample_data"] = sample_data

            # 判断目标业务表
            if self._is_futures_15min_table(table_name, analysis_result["columns"]):
                analysis_result["target_business_table"] = (
                    "fut_main_contract_kline_15min"
                )
                analysis_result["migration_feasible"] = True
            elif self._is_stock_daily_table(table_name, analysis_result["columns"]):
                analysis_result["target_business_table"] = "stock_kline_daily"
                analysis_result["migration_feasible"] = True
            else:
                analysis_result["issues"].append("无法识别目标业务表类型")

            # 检查必需字段
            if analysis_result["target_business_table"]:
                missing_fields = self._check_required_fields(
                    analysis_result["columns"], analysis_result["target_business_table"]
                )
                if missing_fields:
                    analysis_result["issues"].extend(
                        [f"缺少必需字段: {field}" for field in missing_fields]
                    )
                    analysis_result["migration_feasible"] = False

        except Exception as e:
            analysis_result["issues"].append(f"表结构分析异常: {str(e)}")
            self.logger.error(f"分析表 {table_name} 结构失败: {str(e)}")

        return analysis_result

    def _is_futures_15min_table(self, table_name: str, columns: List[str]) -> bool:
        """判断是否为期货15分钟K线表"""
        # 表名匹配
        name_patterns = [r".*15分钟.*", r".*主力合约.*", r"^[a-z]{1,2}_.*"]
        name_match = any(re.search(pattern, table_name) for pattern in name_patterns)

        # 字段匹配（需要包含价格和时间字段）
        price_fields = {
            "open",
            "high",
            "low",
            "close",
            "开盘价",
            "最高价",
            "最低价",
            "收盘价",
        }
        time_fields = {"datetime", "time", "date", "时间", "日期", "交易时间"}

        has_price = any(
            col.lower() in price_fields or col in price_fields for col in columns
        )
        has_time = any(
            col.lower() in time_fields or col in time_fields for col in columns
        )

        return name_match and has_price and has_time

    def _is_stock_daily_table(self, table_name: str, columns: List[str]) -> bool:
        """判断是否为股票日K线表"""
        # 表名匹配
        name_patterns = [r".*股票.*", r".*stock.*", r"^\d{6}.*"]  # 股票代码开头
        name_match = any(re.search(pattern, table_name) for pattern in name_patterns)

        # 字段匹配
        price_fields = {
            "open",
            "high",
            "low",
            "close",
            "开盘价",
            "最高价",
            "最低价",
            "收盘价",
        }
        has_price = any(
            col.lower() in price_fields or col in price_fields for col in columns
        )

        return name_match and has_price

    def _check_required_fields(
        self, columns: List[str], target_table: str
    ) -> List[str]:
        """检查必需字段"""
        if target_table not in self.csv_importer.standard_table_schemas:
            return []

        required_columns = self.csv_importer.standard_table_schemas[target_table][
            "required_columns"
        ]
        column_mapping = self.csv_importer.standard_table_schemas[target_table][
            "column_mapping"
        ]

        # 检查直接匹配和映射匹配
        available_columns = set(columns)
        mapped_columns = set()

        for old_col, new_col in column_mapping.items():
            if old_col in available_columns:
                mapped_columns.add(new_col)

        # 合并直接匹配和映射匹配
        all_available = available_columns.union(mapped_columns)

        # 排除系统自动添加的字段
        system_fields = {"created_at", "updated_at"}
        required_user_columns = [
            col for col in required_columns if col not in system_fields
        ]

        missing_fields = [
            col for col in required_user_columns if col not in all_available
        ]
        return missing_fields

    def migrate_table_data(self, source_table: str, target_table: str) -> Dict:
        """
        迁移单个表数据

        Args:
            source_table: 源表名
            target_table: 目标表名

        Returns:
            Dict: 迁移结果
        """
        start_time = get_beijing_time_now()

        migration_result = {
            "success": False,
            "source_table": source_table,
            "target_table": target_table,
            "records_migrated": 0,
            "error": None,
            "warnings": [],
            "duration_seconds": 0,
        }

        try:
            self.logger.info(f"开始迁移表数据: {source_table} -> {target_table}")

            # 分析源表结构
            analysis = self.analyze_table_structure(source_table)
            if not analysis["migration_feasible"]:
                migration_result["error"] = (
                    f"表结构不符合迁移条件: {', '.join(analysis['issues'])}"
                )
                return migration_result

            # 读取源表数据
            query = f"SELECT * FROM {source_table}"
            source_data = self.connection_manager.execute_query(query)

            if not source_data:
                migration_result["error"] = "源表无数据"
                return migration_result

            # 转换为DataFrame进行数据标准化
            df = pd.DataFrame(source_data)

            # 推断文件路径（用于标准化处理）
            fake_file_path = Path(f"/tmp/{source_table}.csv")

            # 数据标准化
            standardized_df = self.csv_importer.standardize_columns(
                df, target_table, fake_file_path
            )

            # 数据合规验证
            compliance = self.csv_importer.validate_data_compliance(
                standardized_df, target_table
            )
            if not compliance["valid"]:
                migration_result["error"] = (
                    f"数据合规验证失败: {', '.join(compliance['errors'])}"
                )
                return migration_result

            migration_result["warnings"].extend(compliance["warnings"])

            # 创建临时CSV文件用于导入
            temp_csv_path = Path(
                f"/tmp/migration_{source_table}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )
            standardized_df.to_csv(temp_csv_path, index=False, encoding="utf-8")

            try:
                # 确保目标表存在
                if not self.connection_manager.table_exists(target_table):
                    # 创建目标表
                    success = self.connection_manager.create_table_from_csv(
                        target_table, temp_csv_path
                    )
                    if not success:
                        migration_result["error"] = "创建目标表失败"
                        return migration_result
                else:
                    # 插入数据到现有表
                    success = self.connection_manager.insert_from_csv(
                        target_table, temp_csv_path, replace=False
                    )
                    if not success:
                        migration_result["error"] = "插入数据到目标表失败"
                        return migration_result

                # 验证迁移的记录数
                migration_result["records_migrated"] = len(standardized_df)
                migration_result["success"] = True

                self.logger.info(
                    f"表数据迁移成功: {source_table} -> {target_table} ({migration_result['records_migrated']} 条记录)"
                )

            finally:
                # 清理临时文件
                if temp_csv_path.exists():
                    temp_csv_path.unlink()

        except Exception as e:
            migration_result["error"] = str(e)
            self.logger.error(
                f"表数据迁移异常: {source_table} -> {target_table} - {str(e)}"
            )

        # 计算耗时
        end_time = get_beijing_time_now()
        migration_result["duration_seconds"] = (end_time - start_time).total_seconds()

        return migration_result

    def backup_table(self, table_name: str, backup_suffix: str = None) -> str:
        """
        备份表

        Args:
            table_name: 表名
            backup_suffix: 备份后缀

        Returns:
            str: 备份表名
        """
        if backup_suffix is None:
            backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")

        backup_table_name = f"{table_name}_backup_{backup_suffix}"

        try:
            # 创建备份表
            query = f"CREATE TABLE {backup_table_name} AS SELECT * FROM {table_name}"
            self.connection_manager.execute_query(query)

            self.logger.info(f"表备份成功: {table_name} -> {backup_table_name}")
            return backup_table_name

        except Exception as e:
            self.logger.error(f"表备份失败: {table_name} - {str(e)}")
            raise e

    def execute_full_migration(self, dry_run: bool = True) -> Dict:
        """
        执行完整的数据迁移

        Args:
            dry_run: 是否为试运行模式

        Returns:
            Dict: 迁移报告
        """
        start_time = get_beijing_time_now()

        migration_report = {
            "success": True,
            "dry_run": dry_run,
            "total_tables": 0,
            "migrated_tables": 0,
            "error_tables": 0,
            "total_records": 0,
            "results": [],
            "errors": [],
            "duration_seconds": 0,
        }

        try:
            # 扫描需要迁移的表
            scattered_tables = self.scan_scattered_futures_tables()
            migration_report["total_tables"] = len(scattered_tables)

            if not scattered_tables:
                self.logger.info("未发现需要迁移的分散表")
                return migration_report

            self.logger.info(
                f"{'[试运行]' if dry_run else '[实际执行]'} 开始数据迁移，共 {len(scattered_tables)} 个表"
            )

            # 逐个迁移表
            for table_name in scattered_tables:
                self.logger.info(f"处理表: {table_name}")

                try:
                    # 分析表结构
                    analysis = self.analyze_table_structure(table_name)

                    if not analysis["migration_feasible"]:
                        error_msg = f"表 {table_name} 不符合迁移条件: {', '.join(analysis['issues'])}"
                        self.logger.warning(error_msg)
                        migration_report["errors"].append(error_msg)
                        migration_report["error_tables"] += 1
                        continue

                    target_table = analysis["target_business_table"]

                    if not dry_run:
                        # 实际执行迁移

                        # 1. 备份源表
                        backup_name = self.backup_table(table_name)

                        # 2. 迁移数据
                        result = self.migrate_table_data(table_name, target_table)
                        migration_report["results"].append(result)

                        if result["success"]:
                            migration_report["migrated_tables"] += 1
                            migration_report["total_records"] += result[
                                "records_migrated"
                            ]

                            # 3. 删除源表（已有备份）
                            self.connection_manager.drop_table(table_name)
                            self.logger.info(
                                f"已删除源表: {table_name} (备份: {backup_name})"
                            )
                        else:
                            migration_report["error_tables"] += 1
                            migration_report["errors"].append(result["error"])
                    else:
                        # 试运行模式，只记录分析结果
                        migration_report["results"].append(
                            {
                                "source_table": table_name,
                                "target_table": target_table,
                                "records_count": analysis["record_count"],
                                "analysis": analysis,
                            }
                        )
                        migration_report["migrated_tables"] += 1
                        migration_report["total_records"] += analysis["record_count"]

                except Exception as e:
                    error_msg = f"处理表 {table_name} 异常: {str(e)}"
                    self.logger.error(error_msg)
                    migration_report["errors"].append(error_msg)
                    migration_report["error_tables"] += 1

        except Exception as e:
            migration_report["success"] = False
            migration_report["errors"].append(f"迁移过程异常: {str(e)}")
            self.logger.error(f"数据迁移异常: {str(e)}")

        # 计算总耗时
        end_time = get_beijing_time_now()
        migration_report["duration_seconds"] = (end_time - start_time).total_seconds()

        # 判断整体是否成功
        migration_report["success"] = migration_report["error_tables"] == 0

        self.logger.info(
            f"{'[试运行]' if dry_run else '[实际执行]'} 数据迁移完成: {migration_report['migrated_tables']}/{migration_report['total_tables']} 表成功"
        )

        return migration_report

    def get_migration_summary(self) -> Dict:
        """
        获取迁移摘要

        Returns:
            Dict: 迁移摘要
        """
        return {
            "environment": self.environment,
            "statistics": self.migration_stats.copy(),
            "timestamp": get_beijing_time_now().isoformat(),
        }

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.connection_manager.close_connection()
