#!/usr/bin/env python3
"""
CSV数据导入器（重构版）
提供CSV文件到DuckDB的数据导入功能

特性：
- 使用统一的验证器和映射器组件
- 智能业务表映射
- 数据标准化和合规验证
- 错误处理和重试机制
- 进度跟踪和日志记录
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, List, Optional
import pandas as pd

from ..database.connection_manager import DuckDBConnectionManager
from ..utils.config_loader import ConfigLoader
from ..utils.time_utils import get_beijing_time_now
from .validators.csv_validator import CSVValidator
from .mappers.business_table_mapper import BusinessTableMapper
from .loaders.data_loader import DataLoader


class CSVImporter:
    """CSV数据导入器（重构版）"""

    def __init__(self, environment: str = "test"):
        """
        初始化CSV导入器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.config_loader = ConfigLoader()
        self.config = self.config_loader.get_config(environment)

        # CSV配置
        self.csv_config = self.config_loader.get_csv_config(environment)
        self.data_dir = Path(self.csv_config["data_dir"])
        self.batch_size = self.csv_config["batch_size"]
        self.max_file_size_mb = self.csv_config["max_file_size_mb"]
        self.enable_parallel = self.csv_config["enable_parallel"]

        # 初始化统一组件
        self.validator = CSVValidator(self.csv_config)
        self.mapper = BusinessTableMapper()
        self.loader = DataLoader(
            environment=environment, encoding=self.csv_config["encoding"]
        )

        # 数据库连接管理器（保留兼容性）
        self.connection_manager = DuckDBConnectionManager(environment)

        # 日志配置
        self.logger = logging.getLogger(__name__)

        # 支持的文件扩展名
        self.supported_extensions = {".csv", ".tsv", ".txt"}

        # 导入统计
        self.import_stats = {
            "total_files": 0,
            "processed_files": 0,
            "skipped_files": 0,
            "error_files": 0,
            "total_records": 0,
            "imported_records": 0,
            "errors": [],
        }

    def get_csv_files(self) -> List[Path]:
        """
        获取CSV文件列表（递归搜索所有子目录）

        Returns:
            List[Path]: CSV文件路径列表
        """
        if not self.data_dir.exists():
            self.logger.error(f"CSV数据目录不存在: {self.data_dir}")
            return []

        csv_files = []

        # 递归搜索所有子目录中的CSV文件
        for ext in self.supported_extensions:
            files = list(self.data_dir.glob(f"**/*{ext}"))
            csv_files.extend(files)

        # 过滤掉可能的系统文件或临时文件
        csv_files = [f for f in csv_files if not f.name.startswith(".") and f.is_file()]

        # 按文件路径排序
        csv_files.sort()

        self.logger.info(f"找到 {len(csv_files)} 个CSV文件（包括子目录）")
        return csv_files

    def validate_csv_file(self, file_path: Path) -> Dict:
        """验证CSV文件（使用统一验证器）"""
        return self.validator.validate_file_format(file_path)

    def detect_table_name(self, file_path: Path) -> str:
        """检测表名（使用统一映射器）"""
        return self.mapper.map_file_to_table(file_path)

    def import_single_file(
        self, file_path: Path, table_name: Optional[str] = None
    ) -> Dict:
        """
        导入单个CSV文件

        Args:
            file_path: CSV文件路径
            table_name: 目标表名，如果不指定则自动检测

        Returns:
            Dict: 导入结果
        """
        start_time = get_beijing_time_now()

        # 导入结果
        import_result = {
            "success": False,
            "file_path": str(file_path),
            "table_name": table_name,
            "records_imported": 0,
            "error": None,
            "warnings": [],
            "duration_seconds": 0,
        }

        try:
            # 验证文件
            validation = self.validate_csv_file(file_path)
            if not validation["valid"]:
                import_result["error"] = (
                    f"文件验证失败: {', '.join(validation['errors'])}"
                )
                return import_result

            # 添加警告
            import_result["warnings"].extend(validation["warnings"])

            # 确定表名（使用统一映射器）
            if not table_name:
                table_name = self.detect_table_name(file_path)
                self.logger.info(f"文件映射到业务表: {file_path.name} -> {table_name}")

            import_result["table_name"] = table_name

            self.logger.info(f"开始导入文件: {file_path} -> {table_name}")

            # 判断是否为业务表（通过映射器检查）
            is_business_table = table_name in self.mapper.standard_table_schemas

            if is_business_table:
                # 业务表：使用统一组件处理
                success = self._import_business_table_data(
                    file_path, table_name, import_result
                )
            else:
                # 非业务表：使用传统方式
                success = self._import_legacy_table_data(
                    file_path, table_name, import_result
                )

            if success:
                import_result["success"] = True
                self.logger.info(
                    f"文件导入成功: {file_path} -> {table_name} ({import_result['records_imported']} 条记录)"
                )
            else:
                self.logger.error(f"文件导入失败: {file_path} -> {table_name}")

        except Exception as e:
            import_result["error"] = str(e)
            self.logger.error(f"文件导入异常: {file_path} - {str(e)}")

        # 计算耗时
        end_time = get_beijing_time_now()
        import_result["duration_seconds"] = (end_time - start_time).total_seconds()

        # 更新统计
        self.import_stats["total_files"] += 1
        if import_result["success"]:
            self.import_stats["processed_files"] += 1
            self.import_stats["imported_records"] += import_result["records_imported"]
        else:
            self.import_stats["error_files"] += 1
            self.import_stats["errors"].append(
                {"file": str(file_path), "error": import_result["error"]}
            )

        return import_result

    def _import_business_table_data(
        self, file_path: Path, table_name: str, import_result: Dict
    ) -> bool:
        """
        导入业务表数据（使用统一组件）

        Args:
            file_path: CSV文件路径
            table_name: 目标表名
            import_result: 导入结果字典

        Returns:
            bool: 是否成功
        """
        try:
            # 读取CSV数据
            df = pd.read_csv(file_path, encoding=self.csv_config["encoding"])

            # 字段标准化
            df = self.standardize_columns(df, table_name, file_path)

            # 数据合规性验证
            compliance = self.validate_data_compliance(df, table_name)
            if not compliance["valid"]:
                import_result["error"] = (
                    f"数据合规性验证失败: {', '.join(compliance['errors'])}"
                )
                return False

            import_result["warnings"].extend(compliance["warnings"])

            # 保存标准化后的数据为临时CSV
            with tempfile.NamedTemporaryFile(
                mode="w",
                suffix=".csv",
                delete=False,
                encoding=self.csv_config["encoding"],
            ) as temp_file:
                temp_csv_path = Path(temp_file.name)
                df.to_csv(temp_file, index=False)

            try:
                # 检查表是否存在
                table_exists = self.connection_manager.table_exists(table_name)

                if table_exists:
                    # 表存在，插入数据
                    success = self.connection_manager.insert_from_csv(
                        table_name, temp_csv_path, replace=False
                    )
                else:
                    # 表不存在，创建表并导入数据
                    success = self.connection_manager.create_table_from_csv(
                        table_name, temp_csv_path
                    )

                if success:
                    import_result["records_imported"] = len(df)
                else:
                    import_result["error"] = "数据库操作失败"
                    return False

            finally:
                # 清理临时文件
                if temp_csv_path.exists():
                    temp_csv_path.unlink()

            return True

        except Exception as e:
            import_result["error"] = f"业务表数据导入异常: {str(e)}"
            return False

    def _import_legacy_table_data(
        self, file_path: Path, table_name: str, import_result: Dict
    ) -> bool:
        """
        导入传统表数据（使用原有逻辑）

        Args:
            file_path: CSV文件路径
            table_name: 目标表名
            import_result: 导入结果字典

        Returns:
            bool: 是否成功
        """
        try:
            # 检查表是否存在
            table_exists = self.connection_manager.table_exists(table_name)

            if table_exists:
                # 表存在，插入数据
                success = self.connection_manager.insert_from_csv(
                    table_name, file_path, replace=False
                )
            else:
                # 表不存在，创建表并导入数据
                success = self.connection_manager.create_table_from_csv(
                    table_name, file_path
                )

            if success:
                import_result["records_imported"] = (
                    self.connection_manager.get_table_count(table_name)
                )
            else:
                import_result["error"] = "数据库操作失败"
                return False

            return True

        except Exception as e:
            import_result["error"] = f"传统表数据导入异常: {str(e)}"
            return False

    def import_batch_files(
        self,
        file_paths: Optional[List[Path]] = None,
        max_files: Optional[int] = None,
        limit_per_file: Optional[int] = None,
    ) -> Dict:
        """
        批量导入CSV文件

        Args:
            file_paths: 文件路径列表，如果不指定则自动扫描
            max_files: 最大处理文件数
            limit_per_file: 每个文件的最大记录数限制

        Returns:
            Dict: 批量导入结果
        """
        start_time = get_beijing_time_now()

        # 获取文件列表
        if file_paths is None:
            file_paths = self.get_csv_files()

        # 限制文件数量
        if max_files and len(file_paths) > max_files:
            file_paths = file_paths[:max_files]

        # 批量导入结果
        batch_result = {
            "success": True,
            "total_files": len(file_paths),
            "processed_files": 0,
            "error_files": 0,
            "total_records": 0,
            "results": [],
            "errors": [],
            "duration_seconds": 0,
        }

        self.logger.info(f"开始批量导入 {len(file_paths)} 个CSV文件")

        # 逐个导入文件
        for i, file_path in enumerate(file_paths, 1):
            self.logger.info(f"处理文件 {i}/{len(file_paths)}: {file_path.name}")

            try:
                # 导入单个文件
                result = self.import_single_file(file_path)
                batch_result["results"].append(result)

                if result["success"]:
                    batch_result["processed_files"] += 1
                    batch_result["total_records"] += result["records_imported"]
                else:
                    batch_result["error_files"] += 1
                    batch_result["errors"].append(
                        {"file": str(file_path), "error": result["error"]}
                    )

            except Exception as e:
                error_msg = f"文件 {file_path} 处理异常: {str(e)}"
                self.logger.error(error_msg)
                batch_result["error_files"] += 1
                batch_result["errors"].append(
                    {"file": str(file_path), "error": error_msg}
                )

        # 计算总耗时
        end_time = get_beijing_time_now()
        batch_result["duration_seconds"] = (end_time - start_time).total_seconds()

        # 判断整体是否成功
        batch_result["success"] = batch_result["error_files"] == 0

        self.logger.info(
            f"批量导入完成: {batch_result['processed_files']}/{batch_result['total_files']} 文件成功"
        )

        return batch_result

    def get_import_summary(self) -> Dict:
        """
        获取导入摘要

        Returns:
            Dict: 导入摘要
        """
        return {
            "environment": self.environment,
            "data_directory": str(self.data_dir),
            "batch_size": self.batch_size,
            "max_file_size_mb": self.max_file_size_mb,
            "statistics": self.import_stats.copy(),
            "timestamp": get_beijing_time_now().isoformat(),
        }

    def reset_statistics(self):
        """重置导入统计"""
        self.import_stats = {
            "total_files": 0,
            "processed_files": 0,
            "skipped_files": 0,
            "error_files": 0,
            "total_records": 0,
            "imported_records": 0,
            "errors": [],
        }

    def get_file_preview(self, file_path: Path, max_rows: int = 10) -> Dict:
        """
        获取CSV文件预览

        Args:
            file_path: CSV文件路径
            max_rows: 最大预览行数

        Returns:
            Dict: 文件预览信息
        """
        preview_result = {
            "success": False,
            "file_path": str(file_path),
            "headers": [],
            "sample_rows": [],
            "total_rows": 0,
            "error": None,
        }

        try:
            # 验证文件
            validation = self.validate_csv_file(file_path)
            if not validation["valid"]:
                preview_result["error"] = (
                    f"文件验证失败: {', '.join(validation['errors'])}"
                )
                return preview_result

            # 使用统一加载器读取文件
            df = self.loader.load_csv(file_path, nrows=max_rows)

            preview_result["headers"] = df.columns.tolist()
            preview_result["sample_rows"] = df.values.tolist()
            preview_result["total_rows"] = len(df)
            preview_result["success"] = True

        except Exception as e:
            preview_result["error"] = str(e)

        return preview_result

    # 保留兼容性方法
    def standardize_columns(
        self, df: pd.DataFrame, table_name: str, file_path: Path
    ) -> pd.DataFrame:
        """字段标准化（使用统一映射器）"""
        return self.mapper.map_columns_to_standard(df, table_name, file_path)

    def validate_data_compliance(self, df: pd.DataFrame, table_name: str) -> Dict:
        """数据合规性验证（使用统一验证器）"""
        return self.validator.validate_data_compliance(df, table_name)

    def get_component_statistics(self) -> Dict:
        """获取组件统计信息"""
        return {
            "validator_info": {
                "supported_extensions": list(self.supported_extensions),
                "max_file_size_mb": self.max_file_size_mb,
                "encoding": self.csv_config["encoding"],
            },
            "mapper_info": self.mapper.get_mapping_statistics(),
            "loader_info": self.loader.get_import_statistics(),
        }

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.connection_manager.close_connection()
        if hasattr(self, "loader"):
            self.loader.__exit__(exc_type, exc_val, exc_tb)
