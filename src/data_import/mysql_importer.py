#!/usr/bin/env python3
"""
MySQL数据导入器
提供MySQL数据到DuckDB的迁移功能

特性：
- MySQL表结构自动识别
- 数据类型自动转换
- 批量数据迁移
- 错误处理和重试机制
- 进度跟踪和日志记录
- 支持表映射和过滤
"""

import logging
from typing import Dict, List, Optional
import pymysql
from pymysql import Error as PyMySQLError

from ..database.connection_manager import DuckDBConnectionManager
from ..utils.config_loader import ConfigLoader
from ..utils.time_utils import get_beijing_time_now


class MySQLImporter:
    """MySQL数据导入器"""

    def __init__(self, environment: str = "test"):
        """
        初始化MySQL导入器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.config_loader = ConfigLoader()
        self.config = self.config_loader.get_config(environment)

        # MySQL配置
        self.mysql_config = self.config_loader.get_mysql_config(environment)

        # DuckDB连接管理器
        self.connection_manager = DuckDBConnectionManager(environment)

        # 日志配置
        self.logger = logging.getLogger(__name__)

        # MySQL连接
        self._mysql_connection: Optional[pymysql.Connection] = None

        # 类型映射
        self.type_mapping = {
            "TINYINT": "TINYINT",
            "SMALLINT": "SMALLINT",
            "MEDIUMINT": "INTEGER",
            "INT": "INTEGER",
            "INTEGER": "INTEGER",
            "BIGINT": "BIGINT",
            "FLOAT": "FLOAT",
            "DOUBLE": "DOUBLE",
            "DECIMAL": "DECIMAL",
            "NUMERIC": "DECIMAL",
            "CHAR": "VARCHAR",
            "VARCHAR": "VARCHAR",
            "TEXT": "VARCHAR",
            "TINYTEXT": "VARCHAR",
            "MEDIUMTEXT": "VARCHAR",
            "LONGTEXT": "VARCHAR",
            "DATE": "DATE",
            "TIME": "TIME",
            "DATETIME": "TIMESTAMP",
            "TIMESTAMP": "TIMESTAMP",
            "YEAR": "INTEGER",
            "BINARY": "BLOB",
            "VARBINARY": "BLOB",
            "TINYBLOB": "BLOB",
            "BLOB": "BLOB",
            "MEDIUMBLOB": "BLOB",
            "LONGBLOB": "BLOB",
            "JSON": "JSON",
            "BOOLEAN": "BOOLEAN",
            "BOOL": "BOOLEAN",
        }

        # 导入统计
        self.import_stats = {
            "total_tables": 0,
            "processed_tables": 0,
            "skipped_tables": 0,
            "error_tables": 0,
            "total_records": 0,
            "imported_records": 0,
            "errors": [],
        }

    def get_mysql_connection(self) -> pymysql.Connection:
        """
        获取MySQL连接

        Returns:
            pymysql.Connection: MySQL连接对象
        """
        if self._mysql_connection is None or not self._mysql_connection.open:
            try:
                self._mysql_connection = pymysql.connect(
                    host=self.mysql_config["host"],
                    port=self.mysql_config["port"],
                    user=self.mysql_config["user"],
                    password=self.mysql_config["password"],
                    database=self.mysql_config["database"],
                    charset=self.mysql_config["charset"],
                    connect_timeout=self.mysql_config["connect_timeout"],
                    read_timeout=self.mysql_config["read_timeout"],
                    autocommit=True,
                )
                self.logger.info(
                    f"成功连接到MySQL: {self.mysql_config['host']}:{self.mysql_config['port']}"
                )
            except PyMySQLError as e:
                self.logger.error(f"MySQL连接失败: {str(e)}")
                raise

        return self._mysql_connection

    def close_mysql_connection(self):
        """关闭MySQL连接"""
        if self._mysql_connection and self._mysql_connection.open:
            self._mysql_connection.close()
            self._mysql_connection = None
            self.logger.info("MySQL连接已关闭")

    def test_mysql_connection(self) -> Dict:
        """
        测试MySQL连接

        Returns:
            Dict: 连接测试结果
        """
        test_result = {
            "success": False,
            "message": "",
            "server_info": None,
            "database_name": None,
        }

        try:
            conn = self.get_mysql_connection()

            # 获取服务器信息
            with conn.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                test_result["server_info"] = version[0] if version else "Unknown"

                cursor.execute("SELECT DATABASE()")
                database = cursor.fetchone()
                test_result["database_name"] = database[0] if database else "Unknown"

            test_result["success"] = True
            test_result["message"] = "MySQL连接测试成功"

        except Exception as e:
            test_result["message"] = f"MySQL连接测试失败: {str(e)}"

        return test_result

    def get_mysql_tables(self) -> List[str]:
        """
        获取MySQL数据库中的表列表

        Returns:
            List[str]: 表名列表
        """
        try:
            conn = self.get_mysql_connection()

            with conn.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = [row[0] for row in cursor.fetchall()]

            self.logger.info(f"找到 {len(tables)} 个MySQL表")
            return tables

        except Exception as e:
            self.logger.error(f"获取MySQL表列表失败: {str(e)}")
            return []

    def get_table_structure(self, table_name: str) -> Dict:
        """
        获取MySQL表结构

        Args:
            table_name: 表名

        Returns:
            Dict: 表结构信息
        """
        try:
            conn = self.get_mysql_connection()

            with conn.cursor() as cursor:
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()

                # 获取表注释
                cursor.execute(
                    f"""
                    SELECT TABLE_COMMENT 
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = '{self.mysql_config["database"]}' 
                    AND TABLE_NAME = '{table_name}'
                """
                )
                comment_result = cursor.fetchone()
                table_comment = comment_result[0] if comment_result else ""

                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                record_count = cursor.fetchone()[0]

            # 解析列信息
            column_info = []
            for col in columns:
                column_info.append(
                    {
                        "field": col[0],
                        "type": col[1],
                        "null": col[2],
                        "key": col[3],
                        "default": col[4],
                        "extra": col[5],
                    }
                )

            return {
                "table_name": table_name,
                "columns": column_info,
                "comment": table_comment,
                "record_count": record_count,
            }

        except Exception as e:
            self.logger.error(f"获取表结构失败: {table_name} - {str(e)}")
            return {}

    def convert_mysql_type_to_duckdb(self, mysql_type: str) -> str:
        """
        将MySQL数据类型转换为DuckDB类型

        Args:
            mysql_type: MySQL数据类型

        Returns:
            str: DuckDB数据类型
        """
        # 提取基本类型（去掉长度和其他修饰符）
        base_type = mysql_type.split("(")[0].upper()

        # 特殊处理带长度的类型
        if "(" in mysql_type:
            if base_type in ["VARCHAR", "CHAR"]:
                return mysql_type.upper()
            elif base_type in ["DECIMAL", "NUMERIC"]:
                return mysql_type.upper()

        return self.type_mapping.get(base_type, "VARCHAR")

    def create_duckdb_table_from_mysql(
        self, mysql_table: str, duckdb_table: str
    ) -> bool:
        """
        根据MySQL表结构创建DuckDB表

        Args:
            mysql_table: MySQL表名
            duckdb_table: DuckDB表名

        Returns:
            bool: 是否成功
        """
        try:
            # 获取MySQL表结构
            table_structure = self.get_table_structure(mysql_table)
            if not table_structure:
                return False

            # 构建CREATE TABLE语句
            columns_sql = []
            for column in table_structure["columns"]:
                col_name = column["field"]
                col_type = self.convert_mysql_type_to_duckdb(column["type"])

                # 处理NOT NULL约束
                null_clause = "" if column["null"] == "YES" else " NOT NULL"

                # 处理默认值
                default_clause = ""
                if column["default"] is not None:
                    if column["default"] == "CURRENT_TIMESTAMP":
                        default_clause = " DEFAULT CURRENT_TIMESTAMP"
                    elif isinstance(column["default"], str):
                        default_clause = f" DEFAULT '{column['default']}'"
                    else:
                        default_clause = f" DEFAULT {column['default']}"

                col_sql = f"{col_name} {col_type}{null_clause}{default_clause}"
                columns_sql.append(col_sql)

            # 创建表
            columns_joined = ",\n    ".join(columns_sql)
            create_sql = f"CREATE TABLE {duckdb_table} (\n    {columns_joined}\n)"

            # 执行创建表语句
            with self.connection_manager.get_cursor() as cursor:
                cursor.execute(create_sql)

            self.logger.info(f"成功创建DuckDB表: {duckdb_table}")
            return True

        except Exception as e:
            self.logger.error(f"创建DuckDB表失败: {duckdb_table} - {str(e)}")
            return False

    def import_single_table(
        self,
        mysql_table: str,
        duckdb_table: Optional[str] = None,
        batch_size: int = 1000,
    ) -> Dict:
        """
        导入单个MySQL表到DuckDB

        Args:
            mysql_table: MySQL表名
            duckdb_table: DuckDB表名，如果不指定则使用MySQL表名
            batch_size: 批量大小

        Returns:
            Dict: 导入结果
        """
        start_time = get_beijing_time_now()

        # 导入结果
        import_result = {
            "success": False,
            "mysql_table": mysql_table,
            "duckdb_table": duckdb_table,
            "records_imported": 0,
            "total_records": 0,
            "error": None,
            "warnings": [],
            "duration_seconds": 0,
        }

        try:
            # 确定目标表名
            if not duckdb_table:
                duckdb_table = mysql_table

            import_result["duckdb_table"] = duckdb_table

            self.logger.info(f"开始导入表: {mysql_table} -> {duckdb_table}")

            # 获取MySQL表结构
            table_structure = self.get_table_structure(mysql_table)
            if not table_structure:
                import_result["error"] = "无法获取MySQL表结构"
                return import_result

            import_result["total_records"] = table_structure["record_count"]

            # 检查DuckDB表是否存在
            if self.connection_manager.table_exists(duckdb_table):
                # 表存在，清空数据
                self.connection_manager.execute_query(f"DELETE FROM {duckdb_table}")
                self.logger.info(f"清空现有表: {duckdb_table}")
            else:
                # 表不存在，创建表
                if not self.create_duckdb_table_from_mysql(mysql_table, duckdb_table):
                    import_result["error"] = "创建DuckDB表失败"
                    return import_result

            # 批量导入数据
            conn = self.get_mysql_connection()

            with conn.cursor() as cursor:
                # 获取数据
                cursor.execute(f"SELECT * FROM {mysql_table}")

                # 获取列名
                columns = [desc[0] for desc in cursor.description]

                # 构建INSERT语句
                placeholders = ", ".join(["?" for _ in columns])
                insert_sql = f"INSERT INTO {duckdb_table} ({', '.join(columns)}) VALUES ({placeholders})"

                # 批量插入
                records_imported = 0
                batch_data = []

                for row in cursor:
                    batch_data.append(row)

                    if len(batch_data) >= batch_size:
                        # 插入批量数据
                        rows_affected = self.connection_manager.execute_many(
                            insert_sql, batch_data
                        )
                        records_imported += len(batch_data)
                        batch_data = []

                        self.logger.debug(f"已导入 {records_imported} 条记录")

                # 插入剩余数据
                if batch_data:
                    rows_affected = self.connection_manager.execute_many(
                        insert_sql, batch_data
                    )
                    records_imported += len(batch_data)

            import_result["records_imported"] = records_imported
            import_result["success"] = True

            self.logger.info(
                f"表导入成功: {mysql_table} -> {duckdb_table} ({records_imported} 条记录)"
            )

        except Exception as e:
            import_result["error"] = str(e)
            self.logger.error(f"表导入失败: {mysql_table} - {str(e)}")

        # 计算耗时
        end_time = get_beijing_time_now()
        import_result["duration_seconds"] = (end_time - start_time).total_seconds()

        # 更新统计
        self.import_stats["total_tables"] += 1
        if import_result["success"]:
            self.import_stats["processed_tables"] += 1
            self.import_stats["imported_records"] += import_result["records_imported"]
        else:
            self.import_stats["error_tables"] += 1
            self.import_stats["errors"].append(
                {"table": mysql_table, "error": import_result["error"]}
            )

        return import_result

    def import_batch_mysql_tables(
        self, table_names: Optional[List[str]] = None, batch_size: int = 1000
    ) -> Dict:
        """
        批量导入MySQL表

        Args:
            table_names: 表名列表，如果不指定则导入所有表
            batch_size: 批量大小

        Returns:
            Dict: 批量导入结果
        """
        start_time = get_beijing_time_now()

        # 获取表列表
        if table_names is None:
            table_names = self.get_mysql_tables()

        # 批量导入结果
        batch_result = {
            "success": True,
            "total_tables": len(table_names),
            "processed_tables": 0,
            "error_tables": 0,
            "total_records": 0,
            "results": [],
            "errors": [],
            "duration_seconds": 0,
        }

        self.logger.info(f"开始批量导入 {len(table_names)} 个MySQL表")

        # 逐个导入表
        for i, table_name in enumerate(table_names, 1):
            self.logger.info(f"处理表 {i}/{len(table_names)}: {table_name}")

            try:
                # 导入单个表
                result = self.import_single_table(table_name, batch_size=batch_size)
                batch_result["results"].append(result)

                if result["success"]:
                    batch_result["processed_tables"] += 1
                    batch_result["total_records"] += result["records_imported"]
                else:
                    batch_result["error_tables"] += 1
                    batch_result["errors"].append(
                        {"table": table_name, "error": result["error"]}
                    )

            except Exception as e:
                error_msg = f"表 {table_name} 处理异常: {str(e)}"
                self.logger.error(error_msg)
                batch_result["error_tables"] += 1
                batch_result["errors"].append({"table": table_name, "error": error_msg})

        # 计算总耗时
        end_time = get_beijing_time_now()
        batch_result["duration_seconds"] = (end_time - start_time).total_seconds()

        # 判断整体是否成功
        batch_result["success"] = batch_result["error_tables"] == 0

        self.logger.info(
            f"批量导入完成: {batch_result['processed_tables']}/{batch_result['total_tables']} 表成功"
        )

        return batch_result

    def get_import_tables(self) -> List[str]:
        """
        获取需要导入的表列表（基于配置或规则）

        Returns:
            List[str]: 表名列表
        """
        all_tables = self.get_mysql_tables()

        # 这里可以根据配置或规则过滤表
        # 示例：只导入特定前缀的表
        import_tables = []

        for table in all_tables:
            # 导入所有表（可以根据需要修改规则）
            import_tables.append(table)

        return import_tables

    def get_import_summary(self) -> Dict:
        """
        获取导入摘要

        Returns:
            Dict: 导入摘要
        """
        return {
            "environment": self.environment,
            "mysql_config": {
                "host": self.mysql_config["host"],
                "port": self.mysql_config["port"],
                "database": self.mysql_config["database"],
            },
            "statistics": self.import_stats.copy(),
            "timestamp": get_beijing_time_now().isoformat(),
        }

    def reset_statistics(self):
        """重置导入统计"""
        self.import_stats = {
            "total_tables": 0,
            "processed_tables": 0,
            "skipped_tables": 0,
            "error_tables": 0,
            "total_records": 0,
            "imported_records": 0,
            "errors": [],
        }

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_mysql_connection()
        self.connection_manager.close_connection()
