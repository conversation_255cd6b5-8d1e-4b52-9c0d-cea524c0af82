#!/usr/bin/env python3
"""
任务控制管理器

提供导入任务的暂停、恢复、取消等控制功能
基于AQUA宪法的配置驱动和复用优先原则
"""

import logging
import threading
from typing import Dict, Optional, Any
from datetime import datetime
from enum import Enum

from ..utils.time_utils import get_beijing_time_now
from .import_history_manager import ImportHistoryManager


class TaskStatus(Enum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskControlManager:
    """任务控制管理器"""

    def __init__(self, environment: str = "dev"):
        """
        初始化任务控制管理器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.logger = logging.getLogger(__name__)
        self.history_manager = ImportHistoryManager(environment)

        # 任务控制信号存储
        self._task_controls: Dict[str, Dict[str, Any]] = {}
        self._control_lock = threading.Lock()

        # 支持的任务类型映射
        self._task_storages = {
            "csv": None,  # 将由外部设置
            "fromc2c": None,  # 将由外部设置
            "mysql": None,  # 将由外部设置
        }

    def register_task_storage(self, task_type: str, storage: Dict):
        """
        注册任务存储引用

        Args:
            task_type: 任务类型 (csv, fromc2c, mysql)
            storage: 任务存储字典引用
        """
        if task_type in self._task_storages:
            self._task_storages[task_type] = storage
            self.logger.info(f"注册任务存储: {task_type}")
        else:
            self.logger.warning(f"不支持的任务类型: {task_type}")

    def set_task_control_signal(
        self, task_id: str, control_type: str, metadata: Optional[Dict] = None
    ):
        """
        设置任务控制信号

        Args:
            task_id: 任务ID
            control_type: 控制类型 (pause, resume, cancel)
            metadata: 控制元数据
        """
        with self._control_lock:
            if task_id not in self._task_controls:
                self._task_controls[task_id] = {}

            self._task_controls[task_id].update(
                {
                    "control_type": control_type,
                    "timestamp": get_beijing_time_now(),
                    "metadata": metadata or {},
                    "processed": False,
                }
            )

            self.logger.info(f"设置任务控制信号: {task_id} - {control_type}")

    def get_task_control_signal(self, task_id: str) -> Optional[Dict]:
        """
        获取任务控制信号

        Args:
            task_id: 任务ID

        Returns:
            Optional[Dict]: 控制信号信息
        """
        with self._control_lock:
            return self._task_controls.get(task_id)

    def mark_control_processed(self, task_id: str):
        """
        标记控制信号已处理

        Args:
            task_id: 任务ID
        """
        with self._control_lock:
            if task_id in self._task_controls:
                self._task_controls[task_id]["processed"] = True

    def clear_task_control(self, task_id: str):
        """
        清除任务控制信号

        Args:
            task_id: 任务ID
        """
        with self._control_lock:
            if task_id in self._task_controls:
                del self._task_controls[task_id]
                self.logger.debug(f"清除任务控制信号: {task_id}")

    def find_task_by_session_id(self, session_id: str) -> Optional[Dict[str, str]]:
        """
        根据session_id查找任务

        Args:
            session_id: 会话ID（可能是task_id或import_id）

        Returns:
            Optional[Dict]: 包含task_type和task_id的信息
        """
        # 检查各种任务存储
        for task_type, storage in self._task_storages.items():
            if storage is None:
                continue

            # 直接查找task_id
            if session_id in storage:
                return {
                    "task_type": task_type,
                    "task_id": session_id,
                    "storage": storage,
                }

            # 查找session_info中包含session_id的任务
            for task_id, task_data in storage.items():
                if isinstance(task_data, dict):
                    session_info = task_data.get("session_info", {})
                    if (
                        isinstance(session_info, dict)
                        and session_info.get("session_id") == session_id
                    ):
                        return {
                            "task_type": task_type,
                            "task_id": task_id,
                            "storage": storage,
                        }

        return None

    def cancel_task(self, session_id: str) -> Dict[str, Any]:
        """
        取消导入任务

        Args:
            session_id: 会话ID

        Returns:
            Dict: 操作结果
        """
        try:
            # 查找任务
            task_info = self.find_task_by_session_id(session_id)

            if not task_info:
                return {"success": False, "message": f"未找到任务: {session_id}"}

            task_id = task_info["task_id"]
            task_type = task_info["task_type"]
            storage = task_info["storage"]

            # 检查任务状态
            current_task = storage.get(task_id, {})
            current_status = current_task.get("status", "unknown")

            if current_status in ["completed", "failed", "cancelled"]:
                return {
                    "success": False,
                    "message": f"任务已结束，无法取消。当前状态: {current_status}",
                }

            # 设置取消信号
            self.set_task_control_signal(
                task_id, "cancel", {"reason": "用户主动取消", "task_type": task_type}
            )

            # 立即更新任务状态
            if task_id in storage:
                storage[task_id]["status"] = "cancelled"
                storage[task_id]["message"] = "任务已被用户取消"
                storage[task_id]["cancelled_at"] = get_beijing_time_now().isoformat()

            # 更新历史记录
            try:
                # 查找对应的import_id
                import_history = self.history_manager.get_import_history(
                    limit=1000, offset=0
                )
                if import_history.get("success"):
                    for record in import_history["data"]["records"]:
                        session_info = record.get("session_info", {})
                        if (
                            isinstance(session_info, dict)
                            and session_info.get("task_id") == task_id
                        ):
                            self.history_manager.complete_import(
                                import_id=record["import_id"],
                                status="cancelled",
                                error_message="任务被用户取消",
                            )
                            break
            except Exception as e:
                self.logger.warning(f"更新历史记录失败: {e}")

            self.logger.info(f"任务取消成功: {session_id} ({task_type})")

            return {
                "success": True,
                "message": f"任务取消成功: {session_id}",
                "task_type": task_type,
                "task_id": task_id,
            }

        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return {"success": False, "message": f"取消任务失败: {str(e)}"}

    def pause_task(self, session_id: str) -> Dict[str, Any]:
        """
        暂停导入任务

        Args:
            session_id: 会话ID

        Returns:
            Dict: 操作结果
        """
        try:
            # 查找任务
            task_info = self.find_task_by_session_id(session_id)

            if not task_info:
                return {"success": False, "message": f"未找到任务: {session_id}"}

            task_id = task_info["task_id"]
            task_type = task_info["task_type"]
            storage = task_info["storage"]

            # 检查任务状态
            current_task = storage.get(task_id, {})
            current_status = current_task.get("status", "unknown")

            if current_status != "running":
                return {
                    "success": False,
                    "message": f"只能暂停运行中的任务。当前状态: {current_status}",
                }

            # 设置暂停信号
            self.set_task_control_signal(
                task_id, "pause", {"reason": "用户主动暂停", "task_type": task_type}
            )

            # 立即更新任务状态
            if task_id in storage:
                storage[task_id]["status"] = "paused"
                storage[task_id]["message"] = "任务已暂停"
                storage[task_id]["paused_at"] = get_beijing_time_now().isoformat()

            self.logger.info(f"任务暂停成功: {session_id} ({task_type})")

            return {
                "success": True,
                "message": f"任务暂停成功: {session_id}",
                "task_type": task_type,
                "task_id": task_id,
            }

        except Exception as e:
            self.logger.error(f"暂停任务失败: {e}")
            return {"success": False, "message": f"暂停任务失败: {str(e)}"}

    def resume_task(self, session_id: str) -> Dict[str, Any]:
        """
        恢复导入任务

        Args:
            session_id: 会话ID

        Returns:
            Dict: 操作结果
        """
        try:
            # 查找任务
            task_info = self.find_task_by_session_id(session_id)

            if not task_info:
                return {"success": False, "message": f"未找到任务: {session_id}"}

            task_id = task_info["task_id"]
            task_type = task_info["task_type"]
            storage = task_info["storage"]

            # 检查任务状态
            current_task = storage.get(task_id, {})
            current_status = current_task.get("status", "unknown")

            if current_status != "paused":
                return {
                    "success": False,
                    "message": f"只能恢复暂停中的任务。当前状态: {current_status}",
                }

            # 设置恢复信号
            self.set_task_control_signal(
                task_id, "resume", {"reason": "用户主动恢复", "task_type": task_type}
            )

            # 立即更新任务状态
            if task_id in storage:
                storage[task_id]["status"] = "running"
                storage[task_id]["message"] = "任务已恢复运行"
                storage[task_id]["resumed_at"] = get_beijing_time_now().isoformat()

            self.logger.info(f"任务恢复成功: {session_id} ({task_type})")

            return {
                "success": True,
                "message": f"任务恢复成功: {session_id}",
                "task_type": task_type,
                "task_id": task_id,
            }

        except Exception as e:
            self.logger.error(f"恢复任务失败: {e}")
            return {"success": False, "message": f"恢复任务失败: {str(e)}"}

    def get_task_status(self, session_id: str) -> Dict[str, Any]:
        """
        获取任务状态

        Args:
            session_id: 会话ID

        Returns:
            Dict: 任务状态信息
        """
        try:
            # 查找任务
            task_info = self.find_task_by_session_id(session_id)

            if not task_info:
                return {
                    "success": False,
                    "message": f"未找到任务: {session_id}",
                    "status": "not_found",
                }

            task_id = task_info["task_id"]
            task_type = task_info["task_type"]
            storage = task_info["storage"]

            # 获取任务详情
            task_data = storage.get(task_id, {})

            # 获取控制信号
            control_signal = self.get_task_control_signal(task_id)

            return {
                "success": True,
                "task_type": task_type,
                "task_id": task_id,
                "status": task_data.get("status", "unknown"),
                "progress": task_data.get("progress", 0),
                "message": task_data.get("message", ""),
                "result": task_data.get("result"),
                "error": task_data.get("error"),
                "control_signal": control_signal,
                "timestamp": get_beijing_time_now().isoformat(),
            }

        except Exception as e:
            self.logger.error(f"获取任务状态失败: {e}")
            return {
                "success": False,
                "message": f"获取任务状态失败: {str(e)}",
                "status": "error",
            }

    def check_control_signal(self, task_id: str) -> Optional[str]:
        """
        检查任务控制信号（供任务执行器调用）

        Args:
            task_id: 任务ID

        Returns:
            Optional[str]: 控制类型 (pause, resume, cancel) 或 None
        """
        signal = self.get_task_control_signal(task_id)
        if signal and not signal.get("processed", False):
            control_type = signal.get("control_type")
            self.mark_control_processed(task_id)
            return control_type
        return None

    def cleanup_finished_tasks(self, max_age_hours: int = 24):
        """
        清理已完成的任务控制信号

        Args:
            max_age_hours: 最大保留时间（小时）
        """
        try:
            current_time = get_beijing_time_now()
            tasks_to_remove = []

            with self._control_lock:
                for task_id, control_data in self._task_controls.items():
                    timestamp = control_data.get("timestamp")
                    if timestamp:
                        if isinstance(timestamp, str):
                            timestamp = datetime.fromisoformat(
                                timestamp.replace("Z", "+00:00")
                            )

                        # 确保时区一致性
                        if (
                            hasattr(timestamp, "tzinfo")
                            and timestamp.tzinfo is not None
                        ):
                            if current_time.tzinfo is None:
                                current_time = current_time.replace(
                                    tzinfo=timestamp.tzinfo
                                )
                        elif current_time.tzinfo is not None:
                            if hasattr(timestamp, "replace"):
                                timestamp = timestamp.replace(
                                    tzinfo=current_time.tzinfo
                                )

                        age_hours = (current_time - timestamp).total_seconds() / 3600
                        if age_hours > max_age_hours:
                            tasks_to_remove.append(task_id)

                for task_id in tasks_to_remove:
                    del self._task_controls[task_id]

            if tasks_to_remove:
                self.logger.info(f"清理了 {len(tasks_to_remove)} 个过期任务控制信号")

        except Exception as e:
            self.logger.error(f"清理任务控制信号失败: {e}")

    def get_control_statistics(self) -> Dict[str, Any]:
        """
        获取控制操作统计

        Returns:
            Dict: 统计信息
        """
        try:
            stats = {
                "total_controls": len(self._task_controls),
                "by_type": {},
                "by_status": {"processed": 0, "pending": 0},
                "registered_storages": list(self._task_storages.keys()),
            }

            with self._control_lock:
                for control_data in self._task_controls.values():
                    control_type = control_data.get("control_type", "unknown")
                    is_processed = control_data.get("processed", False)

                    # 按类型统计
                    stats["by_type"][control_type] = (
                        stats["by_type"].get(control_type, 0) + 1
                    )

                    # 按状态统计
                    if is_processed:
                        stats["by_status"]["processed"] += 1
                    else:
                        stats["by_status"]["pending"] += 1

            return stats

        except Exception as e:
            self.logger.error(f"获取控制统计失败: {e}")
            return {"error": str(e)}
