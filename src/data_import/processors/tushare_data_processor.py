#!/usr/bin/env python3
"""
TUSHARE数据处理器
实现TUSHARE数据到V4.0统一业务表的完整处理流程

主要功能：
1. TUSHARE数据获取和预处理
2. 数据映射和类型转换
3. 数据验证和质量控制
4. 元数据管理和数据源追溯
5. 批量数据处理
"""

import uuid
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from typing import Dict, Any, Optional, Union

from .data_mapping_engine import DataMappingEngine
from .data_quality_controller import DataQualityController
from ..mappers.business_table_mapper import BusinessTableMapper
from ..mappers.data_dictionary_mapper import DataDictionaryMapper
from ...utils.logger import get_logger
from ...utils.time_utils import get_beijing_time_now


class TushareDataProcessor:
    """
    TUSHARE数据处理器主类

    提供从数据获取到最终存储的完整数据处理流程。
    """

    def __init__(self):
        """初始化TUSHARE数据处理器"""
        self.logger = get_logger(self.__class__.__name__)

        # 初始化组件
        self.mapping_engine = DataMappingEngine()
        self.quality_controller = DataQualityController()
        self.business_mapper = BusinessTableMapper()
        self.dictionary_mapper = DataDictionaryMapper()

        # 追溯信息存储
        self._trace_storage = {}

        # 统计信息
        self._processing_stats = {
            "total_processed": 0,
            "success_count": 0,
            "error_count": 0,
            "quality_issues": 0,
        }

        self.logger.info("TUSHARE数据处理器初始化完成")

    def process_futures_data(
        self,
        data: pd.DataFrame,
        frequency: str,
        trace_info: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        处理期货数据

        Args:
            data: TUSHARE期货数据
            frequency: 数据频率 (daily, 5min, 15min, 30min)
            trace_info: 追溯信息

        Returns:
            Dict: 处理结果

        Raises:
            ValueError: 输入参数错误时
        """
        if data is None or data.empty:
            raise ValueError("数据不能为空")

        if frequency not in ["daily", "5min", "15min", "30min", "1min"]:
            raise ValueError(f"不支持的数据频率: {frequency}")

        self.logger.info(f"开始处理期货数据，频率: {frequency}，数据行数: {len(data)}")

        try:
            # 1. 创建追溯记录
            trace_id = self._create_processing_trace(trace_info, "futures", frequency)

            # 2. 执行数据映射
            mapped_data = self.mapping_engine.map_fields(
                data=data,
                source_type="tushare",
                target_type="v4_business_table",
                data_category="futures",
                frequency=frequency,
            )

            # 3. 执行类型转换
            converted_data = self.mapping_engine.convert_types(
                data=mapped_data,
                source_type="tushare",
                target_type="v4_business_table",
                data_category="futures",
            )

            # 4. 数据验证和质量控制
            validation_result = self.quality_controller.validate(
                data=converted_data, data_type="futures", frequency=frequency
            )

            # 5. 生成元数据
            metadata = self.generate_metadata(
                data=converted_data,
                data_type="futures",
                frequency=frequency,
                source="tushare",
                trace_id=trace_id,
                validation_result=validation_result,
            )

            # 6. 更新统计信息
            self._update_processing_stats(True, validation_result)

            result = {
                "mapped_data": converted_data,
                "metadata": metadata,
                "validation_result": validation_result,
                "trace_id": trace_id,
                "processing_timestamp": get_beijing_time_now(),
            }

            self.logger.info(
                f"期货数据处理成功，记录数: {len(converted_data)}，质量评分: {validation_result.get('quality_score', 0):.3f}"
            )
            return result

        except Exception as e:
            self._update_processing_stats(False)
            self.logger.error(f"期货数据处理失败: {e}")
            raise

    def process_stock_data(
        self,
        data: pd.DataFrame,
        frequency: str,
        trace_info: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        处理股票数据

        Args:
            data: TUSHARE股票数据
            frequency: 数据频率
            trace_info: 追溯信息

        Returns:
            Dict: 处理结果
        """
        if data is None or data.empty:
            raise ValueError("数据不能为空")

        self.logger.info(f"开始处理股票数据，频率: {frequency}，数据行数: {len(data)}")

        try:
            # 1. 创建追溯记录
            trace_id = self._create_processing_trace(trace_info, "stock", frequency)

            # 2. 执行数据映射
            mapped_data = self.mapping_engine.map_fields(
                data=data,
                source_type="tushare",
                target_type="v4_business_table",
                data_category="stock",
                frequency=frequency,
            )

            # 3. 执行类型转换
            converted_data = self.mapping_engine.convert_types(
                data=mapped_data,
                source_type="tushare",
                target_type="v4_business_table",
                data_category="stock",
            )

            # 4. 数据验证和质量控制
            validation_result = self.quality_controller.validate(
                data=converted_data, data_type="stock", frequency=frequency
            )

            # 5. 生成元数据
            metadata = self.generate_metadata(
                data=converted_data,
                data_type="stock",
                frequency=frequency,
                source="tushare",
                trace_id=trace_id,
                validation_result=validation_result,
            )

            # 6. 更新统计信息
            self._update_processing_stats(True, validation_result)

            result = {
                "mapped_data": converted_data,
                "metadata": metadata,
                "validation_result": validation_result,
                "trace_id": trace_id,
                "processing_timestamp": get_beijing_time_now(),
            }

            self.logger.info(
                f"股票数据处理成功，记录数: {len(converted_data)}，质量评分: {validation_result.get('quality_score', 0):.3f}"
            )
            return result

        except Exception as e:
            self._update_processing_stats(False)
            self.logger.error(f"股票数据处理失败: {e}")
            raise

    def validate_data_quality(
        self, data: pd.DataFrame, data_type: str, frequency: str
    ) -> Dict[str, Any]:
        """
        数据质量验证

        Args:
            data: 数据DataFrame
            data_type: 数据类型
            frequency: 数据频率

        Returns:
            Dict: 验证结果
        """
        if data_type not in ["futures", "stock"]:
            raise ValueError(f"不支持的数据类型: {data_type}")

        return self.quality_controller.validate(
            data=data, data_type=data_type, frequency=frequency
        )

    def generate_metadata(
        self,
        data: pd.DataFrame,
        data_type: str,
        frequency: str,
        source: str,
        trace_id: Optional[str] = None,
        validation_result: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """
        生成数据元数据

        Args:
            data: 数据DataFrame
            data_type: 数据类型
            frequency: 数据频率
            source: 数据源
            trace_id: 追溯标识
            validation_result: 验证结果

        Returns:
            Dict: 元数据
        """
        processing_time = get_beijing_time_now()

        # 基本信息
        metadata = {
            "data_source": source,
            "data_type": data_type,
            "frequency": frequency,
            "record_count": len(data),
            "column_count": len(data.columns),
            "processing_timestamp": processing_time,
            "trace_id": trace_id,
        }

        # 数据范围信息
        if not data.empty:
            # 日期范围
            date_columns = []
            if "trade_date" in data.columns:
                date_columns.append("trade_date")
            if "trade_datetime" in data.columns:
                date_columns.append("trade_datetime")

            data_range = {}
            for col in date_columns:
                try:
                    non_null_dates = data[col].dropna()
                    if len(non_null_dates) > 0:
                        data_range[col] = {
                            "start": str(non_null_dates.min()),
                            "end": str(non_null_dates.max()),
                            "count": len(non_null_dates),
                        }
                except Exception as e:
                    self.logger.warning(f"获取日期范围失败 {col}: {e}")

            metadata["data_range"] = data_range

            # 数据统计信息
            metadata["data_statistics"] = {
                "null_count": data.isnull().sum().sum(),
                "null_percentage": (
                    data.isnull().sum().sum() / (len(data) * len(data.columns))
                )
                * 100,
                "duplicate_count": data.duplicated().sum(),
                "memory_usage_mb": data.memory_usage(deep=True).sum() / 1024 / 1024,
            }

        # 质量指标
        if validation_result:
            metadata["quality_metrics"] = {
                "quality_score": validation_result.get("quality_score", 0),
                "is_valid": validation_result.get("is_valid", False),
                "issues_count": len(validation_result.get("issues", [])),
                "completeness": validation_result.get("metrics", {}).get(
                    "completeness", 0
                ),
                "accuracy": validation_result.get("metrics", {}).get("accuracy", 0),
                "consistency": validation_result.get("metrics", {}).get(
                    "consistency", 0
                ),
            }

        # 处理统计
        metadata["processing_info"] = {
            "processor_version": "1.0.0",
            "mapping_engine_version": self.mapping_engine.get_mapping_statistics().get(
                "engine_version", "1.0.0"
            ),
            "processing_duration_ms": 0,  # 在实际使用中需要计算
            "platform": "cross_platform",
        }

        return metadata

    def create_data_trace(self, trace_info: Dict[str, Any]) -> str:
        """
        创建数据追溯记录

        Args:
            trace_info: 追溯信息

        Returns:
            str: 追溯标识
        """
        trace_id = str(uuid.uuid4())
        trace_record = {
            "trace_id": trace_id,
            "created_timestamp": get_beijing_time_now(),
            **trace_info,
        }

        self._trace_storage[trace_id] = trace_record
        self.logger.debug(f"创建追溯记录: {trace_id}")

        return trace_id

    def get_data_trace(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """
        获取数据追溯信息

        Args:
            trace_id: 追溯标识

        Returns:
            Optional[Dict]: 追溯信息
        """
        return self._trace_storage.get(trace_id)

    def process_batch_data(
        self, batch_data: Dict[str, pd.DataFrame], data_type: str, frequency: str
    ) -> Dict[str, Dict[str, Any]]:
        """
        批量处理数据

        Args:
            batch_data: 批量数据字典
            data_type: 数据类型
            frequency: 数据频率

        Returns:
            Dict: 批量处理结果
        """
        self.logger.info(
            f"开始批量处理数据，批次数: {len(batch_data)}，类型: {data_type}"
        )

        results = {}
        total_records = 0
        successful_batches = 0

        for batch_id, data in batch_data.items():
            try:
                # 根据数据类型选择处理方法
                if data_type == "futures":
                    result = self.process_futures_data(data, frequency)
                elif data_type == "stock":
                    result = self.process_stock_data(data, frequency)
                else:
                    raise ValueError(f"不支持的数据类型: {data_type}")

                results[batch_id] = result
                total_records += len(result["mapped_data"])
                successful_batches += 1

                self.logger.debug(
                    f"批次 {batch_id} 处理成功，记录数: {len(result['mapped_data'])}"
                )

            except Exception as e:
                self.logger.error(f"批次 {batch_id} 处理失败: {e}")
                results[batch_id] = {
                    "error": str(e),
                    "batch_id": batch_id,
                    "processing_timestamp": get_beijing_time_now(),
                }

        self.logger.info(
            f"批量处理完成，成功: {successful_batches}/{len(batch_data)}，总记录数: {total_records}"
        )
        return results

    def _create_processing_trace(
        self, trace_info: Optional[Dict[str, Any]], data_type: str, frequency: str
    ) -> str:
        """创建处理追溯记录"""
        base_trace = {
            "data_type": data_type,
            "frequency": frequency,
            "processor": "TushareDataProcessor",
            "version": "1.0.0",
        }

        if trace_info:
            base_trace.update(trace_info)

        return self.create_data_trace(base_trace)

    def _update_processing_stats(
        self, success: bool, validation_result: Optional[Dict] = None
    ):
        """更新处理统计信息"""
        self._processing_stats["total_processed"] += 1

        if success:
            self._processing_stats["success_count"] += 1
            if validation_result and not validation_result.get("is_valid", True):
                self._processing_stats["quality_issues"] += 1
        else:
            self._processing_stats["error_count"] += 1

    def _normalize_path(self, path: Path) -> Path:
        """
        标准化文件路径（跨平台兼容）

        Args:
            path: 原始路径

        Returns:
            Path: 标准化后的路径
        """
        return path.resolve()

    def _normalize_date(self, date_str: str) -> Optional[Union[date, datetime]]:
        """
        标准化日期格式（跨平台兼容）

        Args:
            date_str: 日期字符串

        Returns:
            Optional[Union[date, datetime]]: 标准化后的日期
        """
        try:
            # 尝试多种日期格式
            date_formats = [
                "%Y%m%d",  # 20250129
                "%Y-%m-%d",  # 2025-01-29
                "%Y/%m/%d",  # 2025/01/29
                "%d/%m/%Y",  # 29/01/2025
                "%m/%d/%Y",  # 01/29/2025
            ]

            for fmt in date_formats:
                try:
                    return datetime.strptime(date_str, fmt).date()
                except ValueError:
                    continue

            self.logger.warning(f"无法解析日期格式: {date_str}")
            return None

        except Exception as e:
            self.logger.error(f"日期标准化失败: {e}")
            return None

    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        获取处理统计信息

        Returns:
            Dict: 统计信息
        """
        success_rate = (
            self._processing_stats["success_count"]
            / self._processing_stats["total_processed"]
            if self._processing_stats["total_processed"] > 0
            else 0
        )

        return {
            **self._processing_stats,
            "success_rate": success_rate,
            "quality_issue_rate": (
                self._processing_stats["quality_issues"]
                / self._processing_stats["success_count"]
                if self._processing_stats["success_count"] > 0
                else 0
            ),
            "active_traces": len(self._trace_storage),
            "mapping_engine_stats": self.mapping_engine.get_mapping_statistics(),
            "quality_controller_stats": {
                "total_validations": self._processing_stats["total_processed"],
                "quality_issues": self._processing_stats["quality_issues"],
            },
        }

    def clear_trace_storage(self, keep_recent_hours: int = 24):
        """
        清理追溯存储（保留最近N小时的记录）

        Args:
            keep_recent_hours: 保留最近多少小时的记录
        """
        cutoff_time = datetime.now() - pd.Timedelta(hours=keep_recent_hours)

        traces_to_remove = []
        for trace_id, trace_record in self._trace_storage.items():
            if trace_record["created_timestamp"] < cutoff_time:
                traces_to_remove.append(trace_id)

        for trace_id in traces_to_remove:
            del self._trace_storage[trace_id]

        self.logger.info(
            f"清理追溯记录: {len(traces_to_remove)}条，保留: {len(self._trace_storage)}条"
        )
