#!/usr/bin/env python3
"""
字段映射器
实现不同数据源字段到V4.0业务表字段的映射转换

支持的数据源：
1. TUSHARE API
2. CSV文件
3. 自定义数据源
"""

import pandas as pd
from typing import Dict, List, Any

from ...utils.logger import get_logger


class FieldMapper:
    """
    字段映射器主类

    负责管理和执行不同数据源的字段名映射规则。
    """

    def __init__(self):
        """初始化字段映射器"""
        self.logger = get_logger(self.__class__.__name__)

        # 映射规则存储
        self._mapping_rules = self._initialize_mapping_rules()

        # 统计信息
        self._mapping_count = 0

        self.logger.info("字段映射器初始化完成")

    def _initialize_mapping_rules(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """
        初始化映射规则

        Returns:
            Dict: 映射规则字典
        """
        return {
            # TUSHARE数据源映射规则
            "tushare": {
                "futures": {
                    "daily": {
                        "ts_code": "contract_code",
                        "trade_date": "trade_date",
                        "open": "open",
                        "high": "high",
                        "low": "low",
                        "close": "close",
                        "vol": "volume",
                        "amount": "amount",
                        "oi": "open_interest",
                    },
                    "5min": {
                        "ts_code": "contract_code",
                        "trade_time": "trade_datetime",
                        "open": "open",
                        "high": "high",
                        "low": "low",
                        "close": "close",
                        "vol": "volume",
                        "amount": "amount",
                        "oi": "open_interest",
                    },
                    "15min": {
                        "ts_code": "contract_code",
                        "trade_time": "trade_datetime",
                        "open": "open",
                        "high": "high",
                        "low": "low",
                        "close": "close",
                        "vol": "volume",
                        "amount": "amount",
                        "oi": "open_interest",
                    },
                    "30min": {
                        "ts_code": "contract_code",
                        "trade_time": "trade_datetime",
                        "open": "open",
                        "high": "high",
                        "low": "low",
                        "close": "close",
                        "vol": "volume",
                        "amount": "amount",
                        "oi": "open_interest",
                    },
                },
                "stock": {
                    "daily": {
                        "ts_code": "symbol",
                        "trade_date": "trade_date",
                        "open": "open",
                        "high": "high",
                        "low": "low",
                        "close": "close",
                        "vol": "volume",
                        "amount": "amount",
                        "turnover_rate": "turnover_rate",
                    }
                },
            },
            # CSV数据源映射规则
            "csv": {
                "futures": {
                    "daily": {
                        "合约代码": "contract_code",
                        "交易日期": "trade_date",
                        "开盘价": "open",
                        "最高价": "high",
                        "最低价": "low",
                        "收盘价": "close",
                        "成交量": "volume",
                        "成交额": "amount",
                        "持仓量": "open_interest",
                    }
                },
                "stock": {
                    "daily": {
                        "股票代码": "symbol",
                        "交易日期": "trade_date",
                        "开盘价": "open",
                        "最高价": "high",
                        "最低价": "low",
                        "收盘价": "close",
                        "成交量": "volume",
                        "成交额": "amount",
                        "换手率": "turnover_rate",
                    }
                },
            },
        }

    def get_mapping_rules(
        self, source_type: str, data_category: str, frequency: str
    ) -> Dict[str, str]:
        """
        获取指定数据源的映射规则

        Args:
            source_type: 数据源类型
            data_category: 数据分类
            frequency: 数据频率

        Returns:
            Dict[str, str]: 映射规则字典

        Raises:
            KeyError: 找不到对应的映射规则时
        """
        try:
            rules = self._mapping_rules[source_type][data_category][frequency]
            self.logger.debug(
                f"获取映射规则: {source_type}.{data_category}.{frequency}, 规则数: {len(rules)}"
            )
            return rules
        except KeyError as e:
            self.logger.error(
                f"找不到映射规则: {source_type}.{data_category}.{frequency}"
            )
            raise KeyError(
                f"不支持的映射组合: {source_type}.{data_category}.{frequency}"
            ) from e

    def apply_mapping(
        self, data: pd.DataFrame, source_type: str, data_category: str, frequency: str
    ) -> pd.DataFrame:
        """
        应用字段映射到数据

        Args:
            data: 原始数据DataFrame
            source_type: 数据源类型
            data_category: 数据分类
            frequency: 数据频率

        Returns:
            pd.DataFrame: 映射后的数据
        """
        # 获取映射规则
        mapping_rules = self.get_mapping_rules(source_type, data_category, frequency)

        # 复制数据以避免修改原始数据
        mapped_data = data.copy()

        # 执行字段映射
        mapping_applied = []
        for old_field, new_field in mapping_rules.items():
            if old_field in mapped_data.columns:
                # 重命名字段
                mapped_data = mapped_data.rename(columns={old_field: new_field})
                mapping_applied.append(f"{old_field} -> {new_field}")
                self.logger.debug(f"字段映射: {old_field} -> {new_field}")
            else:
                self.logger.warning(f"源字段不存在: {old_field}")

        # 更新统计信息
        self._mapping_count += len(mapping_applied)

        self.logger.info(f"字段映射完成，应用映射: {len(mapping_applied)}个")
        return mapped_data

    def add_custom_mapping(
        self,
        source_type: str,
        data_category: str,
        frequency: str,
        mapping_rules: Dict[str, str],
    ):
        """
        添加自定义映射规则

        Args:
            source_type: 数据源类型
            data_category: 数据分类
            frequency: 数据频率
            mapping_rules: 映射规则字典
        """
        # 确保结构存在
        if source_type not in self._mapping_rules:
            self._mapping_rules[source_type] = {}
        if data_category not in self._mapping_rules[source_type]:
            self._mapping_rules[source_type][data_category] = {}

        # 添加映射规则
        self._mapping_rules[source_type][data_category][frequency] = mapping_rules

        self.logger.info(
            f"添加自定义映射规则: {source_type}.{data_category}.{frequency}"
        )

    def validate_mapping(
        self, data: pd.DataFrame, source_type: str, data_category: str, frequency: str
    ) -> Dict[str, Any]:
        """
        验证数据对映射规则的兼容性

        Args:
            data: 数据DataFrame
            source_type: 数据源类型
            data_category: 数据分类
            frequency: 数据频率

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            mapping_rules = self.get_mapping_rules(
                source_type, data_category, frequency
            )
        except KeyError:
            return {
                "is_valid": False,
                "error": f"找不到映射规则: {source_type}.{data_category}.{frequency}",
                "missing_fields": [],
                "extra_fields": [],
            }

        # 检查缺少的字段
        missing_fields = []
        for source_field in mapping_rules.keys():
            if source_field not in data.columns:
                missing_fields.append(source_field)

        # 检查多余的字段
        expected_fields = set(mapping_rules.keys())
        actual_fields = set(data.columns)
        extra_fields = list(actual_fields - expected_fields)

        is_valid = len(missing_fields) == 0

        return {
            "is_valid": is_valid,
            "missing_fields": missing_fields,
            "extra_fields": extra_fields,
            "total_rules": len(mapping_rules),
            "applicable_rules": len(mapping_rules) - len(missing_fields),
        }

    def get_reverse_mapping(
        self, source_type: str, data_category: str, frequency: str
    ) -> Dict[str, str]:
        """
        获取反向映射规则（从目标字段到源字段）

        Args:
            source_type: 数据源类型
            data_category: 数据分类
            frequency: 数据频率

        Returns:
            Dict[str, str]: 反向映射规则
        """
        mapping_rules = self.get_mapping_rules(source_type, data_category, frequency)
        return {v: k for k, v in mapping_rules.items()}

    def get_supported_combinations(self) -> List[Dict[str, str]]:
        """
        获取所有支持的数据源组合

        Returns:
            List[Dict[str, str]]: 支持的组合列表
        """
        combinations = []

        for source_type, categories in self._mapping_rules.items():
            for data_category, frequencies in categories.items():
                for frequency in frequencies.keys():
                    combinations.append(
                        {
                            "source_type": source_type,
                            "data_category": data_category,
                            "frequency": frequency,
                        }
                    )

        return combinations

    def get_field_coverage(
        self, data: pd.DataFrame, source_type: str, data_category: str, frequency: str
    ) -> Dict[str, Any]:
        """
        获取字段覆盖率统计

        Args:
            data: 数据DataFrame
            source_type: 数据源类型
            data_category: 数据分类
            frequency: 数据频率

        Returns:
            Dict[str, Any]: 覆盖率统计
        """
        try:
            mapping_rules = self.get_mapping_rules(
                source_type, data_category, frequency
            )

            covered_fields = 0
            for source_field in mapping_rules.keys():
                if source_field in data.columns:
                    covered_fields += 1

            coverage_rate = covered_fields / len(mapping_rules) if mapping_rules else 0

            return {
                "total_rules": len(mapping_rules),
                "covered_fields": covered_fields,
                "coverage_rate": coverage_rate,
                "missing_fields": [
                    f for f in mapping_rules.keys() if f not in data.columns
                ],
            }

        except KeyError:
            return {
                "total_rules": 0,
                "covered_fields": 0,
                "coverage_rate": 0,
                "missing_fields": [],
                "error": "找不到映射规则",
            }

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取映射器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_mapping_count": self._mapping_count,
            "supported_sources": list(self._mapping_rules.keys()),
            "supported_combinations": len(self.get_supported_combinations()),
            "mapping_rules_stats": {
                source: {
                    "categories": len(categories),
                    "total_rules": sum(
                        len(rules)
                        for freq_rules in categories.values()
                        for rules in freq_rules.values()
                    ),
                }
                for source, categories in self._mapping_rules.items()
            },
        }
