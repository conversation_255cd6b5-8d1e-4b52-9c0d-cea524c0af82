#!/usr/bin/env python3
"""
数据质量控制器
实现数据质量验证、评估和监控

功能特性：
1. 数据完整性验证
2. 数据准确性检查
3. 数据一致性验证
4. 质量评分系统
5. 实时监控和报警
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any
from collections import deque

from .data_validator import DataValidator
from .quality_metrics import QualityMetrics
from ...utils.logger import get_logger
from ...utils.time_utils import get_beijing_time_now


class DataQualityController:
    """
    数据质量控制器主类

    提供全面的数据质量验证、评估和监控功能。
    """

    def __init__(self):
        """初始化数据质量控制器"""
        self.logger = get_logger(self.__class__.__name__)

        # 初始化组件
        self.validator = DataValidator()
        self.metrics = QualityMetrics()

        # 实时监控数据
        self._alerts = deque(maxlen=1000)  # 保由1000条最近的报警
        self._quality_history = deque(maxlen=10000)  # 保留质量历史记录

        # 统计信息
        self._validation_count = 0
        self._total_issues = 0

        self.logger.info("数据质量控制器初始化完成")

    def validate(
        self, data: pd.DataFrame, data_type: str, frequency: str
    ) -> Dict[str, Any]:
        """
        执行全面的数据质量验证

        Args:
            data: 数据DataFrame
            data_type: 数据类型 (futures, stock)
            frequency: 数据频率

        Returns:
            Dict: 验证结果
        """
        validation_start = datetime.now()
        self.logger.debug(
            f"开始数据质量验证: {data_type}, 频率: {frequency}, 数据行数: {len(data)}"
        )

        # 1. 完整性检查
        completeness_result = self.check_completeness(
            data, self._get_required_fields(data_type)
        )

        # 2. 准确性检查
        accuracy_result = self.check_accuracy(data, data_type)

        # 3. 一致性检查
        consistency_result = self.check_consistency(data, data_type)

        # 4. 计算综合质量评分
        quality_score = self.calculate_quality_score(data, data_type)

        # 5. 收集所有问题
        all_issues = []
        all_issues.extend(completeness_result.get("issues", []))
        all_issues.extend(accuracy_result.get("accuracy_issues", []))
        all_issues.extend(consistency_result.get("consistency_issues", []))

        # 6. 生成改进建议
        recommendations = self._generate_recommendations(all_issues, quality_score)

        # 7. 汇总结果
        validation_result = {
            "is_valid": len(all_issues) == 0,
            "quality_score": quality_score,
            "issues": all_issues,
            "metrics": {
                "completeness": completeness_result["completeness_score"],
                "accuracy": accuracy_result["accuracy_score"],
                "consistency": consistency_result["consistency_score"],
            },
            "recommendations": recommendations,
            "validation_timestamp": get_beijing_time_now(),
            "processing_time_ms": (datetime.now() - validation_start).total_seconds()
            * 1000,
        }

        # 8. 更新统计信息
        self._validation_count += 1
        self._total_issues += len(all_issues)

        # 9. 记录质量历史
        self._quality_history.append(
            {
                "timestamp": validation_result["validation_timestamp"],
                "data_type": data_type,
                "quality_score": quality_score,
                "issues_count": len(all_issues),
                "record_count": len(data),
            }
        )

        self.logger.info(
            f"数据质量验证完成，评分: {quality_score:.3f}，问题数: {len(all_issues)}"
        )
        return validation_result

    def check_completeness(
        self, data: pd.DataFrame, required_fields: List[str]
    ) -> Dict[str, Any]:
        """
        检查数据完整性

        Args:
            data: 数据DataFrame
            required_fields: 必需字段列表

        Returns:
            Dict: 完整性检查结果
        """
        # 检查缺失的字段
        missing_fields = [
            field for field in required_fields if field not in data.columns
        ]

        # 检查空值
        null_values = {}
        for col in data.columns:
            null_count = data[col].isnull().sum()
            if null_count > 0:
                null_values[col] = {
                    "count": int(null_count),
                    "percentage": float(null_count / len(data) * 100),
                }

        # 计算完整性评分
        completeness_score = self.metrics.calculate_completeness(data)

        # 生成问题列表
        issues = []
        for field in missing_fields:
            issues.append(
                {
                    "type": "missing_field",
                    "severity": "high",
                    "field": field,
                    "description": f"缺少必需字段: {field}",
                }
            )

        for col, null_info in null_values.items():
            if null_info["percentage"] > 10:  # 空值超过10%警告
                issues.append(
                    {
                        "type": "missing_value",
                        "severity": (
                            "medium" if null_info["percentage"] < 50 else "high"
                        ),
                        "field": col,
                        "description": f"字段 {col} 空值过多: {null_info['percentage']:.1f}%",
                        "details": null_info,
                    }
                )

        return {
            "completeness_score": completeness_score,
            "missing_fields": missing_fields,
            "null_values": null_values,
            "issues": issues,
        }

    def check_accuracy(self, data: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """
        检查数据准确性

        Args:
            data: 数据DataFrame
            data_type: 数据类型

        Returns:
            Dict: 准确性检查结果
        """
        accuracy_issues = []

        # 获取数据类型的验证规则
        validation_rules = self._get_validation_rules(data_type)

        # 检查数值范围
        for field, rules in validation_rules.get("range_rules", {}).items():
            if field in data.columns:
                invalid_count = 0

                # 检查最小值
                if "min" in rules and rules["min"] is not None:
                    invalid_mask = data[field] < rules["min"]
                    invalid_count += invalid_mask.sum()
                    if invalid_mask.any():
                        accuracy_issues.append(
                            {
                                "type": "invalid_range",
                                "severity": "medium",
                                "field": field,
                                "description": f"字段 {field} 存在小于最小值({rules['min']})的数据",
                                "invalid_count": int(invalid_mask.sum()),
                            }
                        )

                # 检查最大值
                if "max" in rules and rules["max"] is not None:
                    invalid_mask = data[field] > rules["max"]
                    invalid_count += invalid_mask.sum()
                    if invalid_mask.any():
                        accuracy_issues.append(
                            {
                                "type": "invalid_range",
                                "severity": "medium",
                                "field": field,
                                "description": f"字段 {field} 存在大于最大值({rules['max']})的数据",
                                "invalid_count": int(invalid_mask.sum()),
                            }
                        )

        # 检查无穷大和非数值
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            # 检查无穷大
            inf_count = np.isinf(data[col]).sum()
            if inf_count > 0:
                accuracy_issues.append(
                    {
                        "type": "invalid_value",
                        "severity": "high",
                        "field": col,
                        "description": f"字段 {col} 包含无穷大值",
                        "invalid_count": int(inf_count),
                    }
                )

        # 计算准确性评分
        total_cells = len(data) * len(data.columns)
        invalid_cells = sum(issue.get("invalid_count", 0) for issue in accuracy_issues)
        accuracy_score = (
            max(0, (total_cells - invalid_cells) / total_cells)
            if total_cells > 0
            else 0
        )

        return {
            "accuracy_score": accuracy_score,
            "accuracy_issues": accuracy_issues,
            "total_checks": len(validation_rules.get("range_rules", {})),
            "invalid_values_count": invalid_cells,
        }

    def check_consistency(self, data: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """
        检查数据一致性

        Args:
            data: 数据DataFrame
            data_type: 数据类型

        Returns:
            Dict: 一致性检查结果
        """
        consistency_issues = []

        if data_type in ["futures", "stock"]:
            # 检查价格逻辑一致性
            price_columns = ["open", "high", "low", "close"]
            if all(col in data.columns for col in price_columns):
                # 检查 high >= max(open, close) 和 low <= min(open, close)
                high_invalid = (
                    (data["high"] < data["open"]) | (data["high"] < data["close"])
                ).sum()

                low_invalid = (
                    (data["low"] > data["open"]) | (data["low"] > data["close"])
                ).sum()

                if high_invalid > 0:
                    consistency_issues.append(
                        {
                            "type": "price_logic_error",
                            "severity": "high",
                            "description": f"最高价低于开盘价或收盘价的记录数: {high_invalid}",
                            "invalid_count": int(high_invalid),
                        }
                    )

                if low_invalid > 0:
                    consistency_issues.append(
                        {
                            "type": "price_logic_error",
                            "severity": "high",
                            "description": f"最低价高于开盘价或收盘价的记录数: {low_invalid}",
                            "invalid_count": int(low_invalid),
                        }
                    )

            # 检查成交量和成交额的一致性
            if "volume" in data.columns and "amount" in data.columns:
                # 成交量为0但成交额不为0的情况
                volume_amount_mismatch = (
                    (data["volume"] == 0) & (data["amount"] != 0)
                ).sum()
                # 成交额为0但成交量不为0的情况
                amount_volume_mismatch = (
                    (data["amount"] == 0) & (data["volume"] != 0)
                ).sum()

                if volume_amount_mismatch > 0 or amount_volume_mismatch > 0:
                    consistency_issues.append(
                        {
                            "type": "volume_amount_mismatch",
                            "severity": "medium",
                            "description": f"成交量和成交额不一致的记录数: {volume_amount_mismatch + amount_volume_mismatch}",
                            "invalid_count": int(
                                volume_amount_mismatch + amount_volume_mismatch
                            ),
                        }
                    )

        # 计算一致性评分
        total_records = len(data)
        inconsistent_records = sum(
            issue.get("invalid_count", 0) for issue in consistency_issues
        )
        consistency_score = (
            max(0, (total_records - inconsistent_records) / total_records)
            if total_records > 0
            else 1.0
        )

        return {
            "consistency_score": consistency_score,
            "consistency_issues": consistency_issues,
            "total_records": total_records,
            "inconsistent_records": inconsistent_records,
        }

    def calculate_quality_score(self, data: pd.DataFrame, data_type: str) -> float:
        """
        计算综合质量评分

        Args:
            data: 数据DataFrame
            data_type: 数据类型

        Returns:
            float: 质量评分 (0-1)
        """
        # 计算各维度评分
        completeness = self.metrics.calculate_completeness(data)

        # 简化的准确性评估（基于非空值和有限值的比例）
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            finite_ratio = (
                data[numeric_cols].apply(lambda x: np.isfinite(x).sum() / len(x)).mean()
            )
        else:
            finite_ratio = 1.0

        # 简化的一致性评估（基于价格关系）
        consistency = 1.0
        if data_type in ["futures", "stock"]:
            price_cols = ["open", "high", "low", "close"]
            if all(col in data.columns for col in price_cols):
                # 检查价格关系的一致性
                valid_high = (
                    (data["high"] >= data["open"]) & (data["high"] >= data["close"])
                ).sum()
                valid_low = (
                    (data["low"] <= data["open"]) & (data["low"] <= data["close"])
                ).sum()
                consistency = (valid_high + valid_low) / (2 * len(data))

        # 加权平均计算综合评分
        weights = {"completeness": 0.4, "accuracy": 0.3, "consistency": 0.3}

        quality_score = (
            weights["completeness"] * completeness
            + weights["accuracy"] * finite_ratio
            + weights["consistency"] * consistency
        )

        return min(1.0, max(0.0, quality_score))

    def assess_batch_quality(
        self, batch_data: Dict[str, pd.DataFrame], data_type: str, frequency: str
    ) -> Dict[str, Dict[str, Any]]:
        """
        批量质量评估

        Args:
            batch_data: 批量数据
            data_type: 数据类型
            frequency: 数据频率

        Returns:
            Dict: 批量评估结果
        """
        batch_results = {}

        for batch_id, data in batch_data.items():
            try:
                result = self.validate(data, data_type, frequency)
                batch_results[batch_id] = result
            except Exception as e:
                self.logger.error(f"批次 {batch_id} 质量评估失败: {e}")
                batch_results[batch_id] = {
                    "is_valid": False,
                    "quality_score": 0.0,
                    "error": str(e),
                }

        return batch_results

    def get_batch_summary(
        self, batch_results: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        获取批量评估结果的汇总

        Args:
            batch_results: 批量评估结果

        Returns:
            Dict: 汇总统计
        """
        total_batches = len(batch_results)
        valid_batches = sum(
            1 for result in batch_results.values() if result.get("is_valid", False)
        )
        invalid_batches = total_batches - valid_batches

        quality_scores = [
            result.get("quality_score", 0)
            for result in batch_results.values()
            if "quality_score" in result
        ]

        overall_quality_score = (
            sum(quality_scores) / len(quality_scores) if quality_scores else 0
        )

        return {
            "total_batches": total_batches,
            "valid_batches": valid_batches,
            "invalid_batches": invalid_batches,
            "overall_quality_score": overall_quality_score,
            "avg_quality_score": overall_quality_score,
            "min_quality_score": min(quality_scores) if quality_scores else 0,
            "max_quality_score": max(quality_scores) if quality_scores else 0,
        }

    def monitor_real_time(
        self, data: pd.DataFrame, data_type: str, frequency: str
    ) -> Dict[str, Any]:
        """
        实时监控数据质量

        Args:
            data: 数据DataFrame
            data_type: 数据类型
            frequency: 数据频率

        Returns:
            Dict: 监控结果
        """
        # 执行常规验证
        validation_result = self.validate(data, data_type, frequency)

        # 生成实时报警
        if not validation_result["is_valid"]:
            alert = {
                "timestamp": get_beijing_time_now(),
                "data_type": data_type,
                "frequency": frequency,
                "severity": self._determine_alert_severity(validation_result),
                "quality_score": validation_result["quality_score"],
                "issues_count": len(validation_result["issues"]),
                "message": f"数据质量问题检测: {data_type} 数据质量评分 {validation_result['quality_score']:.3f}",
            }
            self._alerts.append(alert)

        return validation_result

    def get_real_time_alerts(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取实时报警

        Args:
            limit: 返回数量限制

        Returns:
            List: 报警列表
        """
        return list(self._alerts)[-limit:] if self._alerts else []

    def _get_required_fields(self, data_type: str) -> List[str]:
        """获取数据类型的必需字段"""
        if data_type == "futures":
            return [
                "contract_code",
                "trade_date",
                "open",
                "high",
                "low",
                "close",
                "volume",
                "amount",
            ]
        elif data_type == "stock":
            return [
                "symbol",
                "trade_date",
                "open",
                "high",
                "low",
                "close",
                "volume",
                "amount",
            ]
        else:
            return []

    def _get_validation_rules(self, data_type: str) -> Dict[str, Any]:
        """获取数据类型的验证规则"""
        if data_type in ["futures", "stock"]:
            return {
                "range_rules": {
                    "open": {"min": 0, "max": None},
                    "high": {"min": 0, "max": None},
                    "low": {"min": 0, "max": None},
                    "close": {"min": 0, "max": None},
                    "volume": {"min": 0, "max": None},
                    "amount": {"min": 0, "max": None},
                }
            }
        return {}

    def _generate_recommendations(
        self, issues: List[Dict[str, Any]], quality_score: float
    ) -> List[Dict[str, Any]]:
        """生成质量改进建议"""
        recommendations = []

        # 按问题类型分组
        issue_types = {}
        for issue in issues:
            issue_type = issue["type"]
            if issue_type not in issue_types:
                issue_types[issue_type] = []
            issue_types[issue_type].append(issue)

        # 针对不同问题类型生成建议
        for issue_type, type_issues in issue_types.items():
            if issue_type == "missing_value":
                recommendations.append(
                    {
                        "type": "missing_value",
                        "priority": "high",
                        "description": "存在较多空值，建议检查数据源质量",
                        "action": "优化数据采集逻辑，或实施数据补全策略",
                        "affected_count": len(type_issues),
                    }
                )

            elif issue_type == "invalid_range":
                recommendations.append(
                    {
                        "type": "invalid_range",
                        "priority": "medium",
                        "description": "存在超出合理范围的数值",
                        "action": "加强数据验证规则，过滤异常值",
                        "affected_count": len(type_issues),
                    }
                )

            elif issue_type == "price_logic_error":
                recommendations.append(
                    {
                        "type": "logical_inconsistency",
                        "priority": "high",
                        "description": "存在价格逻辑不一致的问题",
                        "action": "检查数据采集源，确保价格数据的准确性",
                        "affected_count": len(type_issues),
                    }
                )

        # 根据质量评分生成综合建议
        if quality_score < 0.5:
            recommendations.append(
                {
                    "type": "overall_quality",
                    "priority": "high",
                    "description": f"数据质量评分过低 ({quality_score:.3f})，需要立即关注",
                    "action": "全面检查数据源和处理流程，优化数据质量",
                }
            )
        elif quality_score < 0.8:
            recommendations.append(
                {
                    "type": "overall_quality",
                    "priority": "medium",
                    "description": f"数据质量评分中等 ({quality_score:.3f})，有改进空间",
                    "action": "优化数据验证规则，提高数据处理标准",
                }
            )

        return recommendations

    def _determine_alert_severity(self, validation_result: Dict[str, Any]) -> str:
        """确定报警严重级别"""
        quality_score = validation_result.get("quality_score", 0)
        issues_count = len(validation_result.get("issues", []))

        if quality_score < 0.3 or issues_count > 10:
            return "high"
        elif quality_score < 0.7 or issues_count > 5:
            return "medium"
        else:
            return "low"

    def get_quality_statistics(self) -> Dict[str, Any]:
        """
        获取质量控制统计信息

        Returns:
            Dict: 统计信息
        """
        return {
            "total_validations": self._validation_count,
            "total_issues": self._total_issues,
            "avg_issues_per_validation": (
                self._total_issues / self._validation_count
                if self._validation_count > 0
                else 0
            ),
            "alerts_count": len(self._alerts),
            "quality_history_count": len(self._quality_history),
            "recent_quality_trend": self._get_quality_trend(),
        }

    def _get_quality_trend(self) -> Dict[str, Any]:
        """获取质量趋势"""
        if len(self._quality_history) < 2:
            return {"trend": "insufficient_data"}

        recent_scores = [
            record["quality_score"] for record in list(self._quality_history)[-10:]
        ]
        if len(recent_scores) < 2:
            return {"trend": "insufficient_data"}

        # 简单的趋势分析
        first_half = recent_scores[: len(recent_scores) // 2]
        second_half = recent_scores[len(recent_scores) // 2 :]

        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)

        if second_avg > first_avg + 0.05:
            trend = "improving"
        elif second_avg < first_avg - 0.05:
            trend = "declining"
        else:
            trend = "stable"

        return {
            "trend": trend,
            "first_half_avg": first_avg,
            "second_half_avg": second_avg,
            "change": second_avg - first_avg,
        }
