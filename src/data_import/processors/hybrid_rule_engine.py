#!/usr/bin/env python3
"""
混合规则引擎
实现YAML+数据库混合规则管理 (A3选择)

功能特性：
1. YAML基础规则加载和解析
2. 数据库动态规则管理
3. 规则优先级和合并策略
4. 热更新和缓存机制
5. 规则验证和完整性检查
"""

import yaml
import sqlite3
import threading
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ...utils.logger import get_logger
from ...utils.time_utils import get_beijing_time_now
from ...database.connection_manager import DuckDBConnectionManager


class RuleSource(Enum):
    """规则源类型"""
    YAML = "yaml"
    DATABASE = "database"
    HYBRID = "hybrid"


class ErrorLevel(Enum):
    """错误级别"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorAction(Enum):
    """错误处理动作"""
    IGNORE = "ignore"
    REPAIR = "repair"
    ISOLATE = "isolate"
    DISCARD = "discard"


@dataclass
class ValidationRule:
    """验证规则数据结构"""
    name: str
    rule_type: str
    rule_expression: str
    error_level: ErrorLevel
    error_action: ErrorAction
    priority: int = 100
    enabled: bool = True
    source: RuleSource = RuleSource.YAML
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class FieldRule:
    """字段规则数据结构"""
    field_name: str
    data_type: str
    constraints: List[str]
    nullable: bool = True
    default_value: Any = None
    format_pattern: Optional[str] = None
    valid_values: Optional[List[str]] = None
    range_min: Optional[float] = None
    range_max: Optional[float] = None


class HybridRuleEngine:
    """
    混合规则引擎主类
    
    支持YAML基础规则 + 数据库动态规则的混合管理模式
    """

    def __init__(self, config_path: Optional[str] = None, db_manager: Optional[DuckDBConnectionManager] = None):
        """
        初始化混合规则引擎
        
        Args:
            config_path: YAML配置文件路径
            db_manager: 数据库连接管理器
        """
        self.logger = get_logger(self.__class__.__name__)
        
        # 配置路径
        self.config_path = Path(config_path) if config_path else Path(__file__).parent.parent.parent / "config" / "data_rules.yaml"
        self.db_manager = db_manager
        
        # 规则缓存
        self._yaml_rules: Dict[str, Any] = {}
        self._database_rules: Dict[str, Any] = {}
        self._merged_rules: Dict[str, Any] = {}
        self._rule_cache_timestamp: Optional[datetime] = None
        
        # 缓存配置
        self.cache_ttl = 3600  # 1小时缓存TTL
        self.refresh_interval = 300  # 5分钟刷新间隔
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            "yaml_rules_loaded": 0,
            "database_rules_loaded": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "rule_validations": 0,
            "validation_errors": 0
        }
        
        # 初始化
        self._initialize()
        
        self.logger.info("混合规则引擎初始化完成")

    def _initialize(self):
        """初始化规则引擎"""
        try:
            # 加载YAML规则
            self._load_yaml_rules()
            
            # 初始化数据库规则表（如果需要）
            if self.db_manager:
                self._initialize_database_rules_table()
                self._load_database_rules()
            
            # 合并规则
            self._merge_rules()
            
        except Exception as e:
            self.logger.error(f"规则引擎初始化失败: {e}")
            raise

    def _load_yaml_rules(self):
        """加载YAML规则文件"""
        try:
            if not self.config_path.exists():
                self.logger.warning(f"YAML规则文件不存在: {self.config_path}")
                return
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._yaml_rules = yaml.safe_load(f)
                
            self.stats["yaml_rules_loaded"] = len(self._yaml_rules)
            self.logger.info(f"成功加载YAML规则: {self.stats['yaml_rules_loaded']}个表规则")
            
        except Exception as e:
            self.logger.error(f"加载YAML规则失败: {e}")
            raise

    def _initialize_database_rules_table(self):
        """初始化数据库规则表"""
        if not self.db_manager:
            return
            
        try:
            # 创建动态规则表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS data_processor_dynamic_rules (
                id INTEGER PRIMARY KEY,
                table_name VARCHAR(100) NOT NULL,
                rule_name VARCHAR(100) NOT NULL,
                rule_type VARCHAR(50) NOT NULL,
                rule_expression TEXT NOT NULL,
                error_level VARCHAR(20) NOT NULL,
                error_action VARCHAR(20) NOT NULL,
                priority INTEGER DEFAULT 100,
                enabled BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(table_name, rule_name)
            )
            """
            
            with self.db_manager.get_connection() as conn:
                conn.execute(create_table_sql)
                
            self.logger.debug("数据库规则表初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化数据库规则表失败: {e}")
            raise

    def _load_database_rules(self):
        """从数据库加载动态规则"""
        if not self.db_manager:
            return
            
        try:
            query_sql = """
            SELECT table_name, rule_name, rule_type, rule_expression, 
                   error_level, error_action, priority, enabled,
                   created_at, updated_at
            FROM data_processor_dynamic_rules 
            WHERE enabled = TRUE
            ORDER BY table_name, priority
            """
            
            with self.db_manager.get_connection() as conn:
                results = conn.execute(query_sql).fetchall()
                
            # 组织数据库规则
            self._database_rules = {}
            for row in results:
                table_name = row[0]
                if table_name not in self._database_rules:
                    self._database_rules[table_name] = {
                        "dynamic_rules": []
                    }
                
                rule = ValidationRule(
                    name=row[1],
                    rule_type=row[2],
                    rule_expression=row[3],
                    error_level=ErrorLevel(row[4]),
                    error_action=ErrorAction(row[5]),
                    priority=row[6],
                    enabled=bool(row[7]),
                    source=RuleSource.DATABASE,
                    created_at=row[8],
                    updated_at=row[9]
                )
                
                self._database_rules[table_name]["dynamic_rules"].append(rule)
                
            self.stats["database_rules_loaded"] = len(results)
            self.logger.info(f"成功加载数据库规则: {self.stats['database_rules_loaded']}条动态规则")
            
        except Exception as e:
            self.logger.error(f"加载数据库规则失败: {e}")
            # 数据库规则加载失败不应该影响整体功能
            self._database_rules = {}

    def _merge_rules(self):
        """合并YAML和数据库规则"""
        with self._lock:
            self._merged_rules = {}
            
            # 首先复制YAML规则
            for table_name, yaml_rule in self._yaml_rules.items():
                if isinstance(yaml_rule, dict):
                    self._merged_rules[table_name] = yaml_rule.copy()
            
            # 合并数据库规则（数据库规则优先级更高）
            for table_name, db_rule in self._database_rules.items():
                if table_name not in self._merged_rules:
                    self._merged_rules[table_name] = {}
                
                # 添加动态规则
                if "dynamic_rules" not in self._merged_rules[table_name]:
                    self._merged_rules[table_name]["dynamic_rules"] = []
                
                self._merged_rules[table_name]["dynamic_rules"].extend(
                    db_rule.get("dynamic_rules", [])
                )
            
            # 更新缓存时间戳
            self._rule_cache_timestamp = datetime.now()
            
            self.logger.debug(f"规则合并完成: {len(self._merged_rules)}个表")

    def get_table_rules(self, table_name: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        获取指定表的规则
        
        Args:
            table_name: 表名
            force_refresh: 是否强制刷新缓存
            
        Returns:
            Dict: 表规则配置
        """
        # 检查是否需要刷新缓存
        if (force_refresh or 
            not self._rule_cache_timestamp or 
            (datetime.now() - self._rule_cache_timestamp).total_seconds() > self.refresh_interval):
            
            self.logger.debug("刷新规则缓存")
            if self.db_manager:
                self._load_database_rules()
            self._merge_rules()
            self.stats["cache_misses"] += 1
        else:
            self.stats["cache_hits"] += 1
        
        with self._lock:
            return self._merged_rules.get(table_name, {})

    def get_field_rules(self, table_name: str, field_name: str) -> Optional[FieldRule]:
        """
        获取字段规则
        
        Args:
            table_name: 表名
            field_name: 字段名
            
        Returns:
            FieldRule: 字段规则对象
        """
        table_rules = self.get_table_rules(table_name)
        field_rules_dict = table_rules.get("field_rules", {})
        
        if field_name not in field_rules_dict:
            return None
            
        field_config = field_rules_dict[field_name]
        
        return FieldRule(
            field_name=field_name,
            data_type=field_config.get("type", "VARCHAR"),
            constraints=field_config.get("constraints", []),
            nullable=field_config.get("nullable", True),
            default_value=field_config.get("default"),
            format_pattern=field_config.get("format_pattern"),
            valid_values=field_config.get("valid_values"),
            range_min=field_config.get("range", [None, None])[0] if field_config.get("range") else None,
            range_max=field_config.get("range", [None, None])[1] if field_config.get("range") else None
        )

    def get_validation_rules(self, table_name: str) -> List[ValidationRule]:
        """
        获取验证规则列表
        
        Args:
            table_name: 表名
            
        Returns:
            List[ValidationRule]: 验证规则列表
        """
        table_rules = self.get_table_rules(table_name)
        validation_rules = []
        
        # 添加跨字段验证规则
        cross_field_rules = table_rules.get("cross_field_rules", [])
        for rule_config in cross_field_rules:
            rule = ValidationRule(
                name=rule_config["name"],
                rule_type="cross_field",
                rule_expression=rule_config["rule"],
                error_level=ErrorLevel(rule_config.get("error_level", "ERROR")),
                error_action=ErrorAction(rule_config.get("error_action", "isolate")),
                source=RuleSource.YAML
            )
            validation_rules.append(rule)
        
        # 添加动态规则
        dynamic_rules = table_rules.get("dynamic_rules", [])
        validation_rules.extend(dynamic_rules)
        
        # 按优先级排序
        validation_rules.sort(key=lambda x: x.priority)
        
        return validation_rules

    def get_data_source_priority(self, table_name: str) -> Dict[str, Dict[str, Any]]:
        """
        获取数据源优先级配置
        
        Args:
            table_name: 表名
            
        Returns:
            Dict: 数据源优先级配置
        """
        table_rules = self.get_table_rules(table_name)
        return table_rules.get("source_priority", {})

    def get_error_handling_config(self, table_name: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取错误处理配置
        
        Args:
            table_name: 表名
            
        Returns:
            Dict: 错误处理配置
        """
        table_rules = self.get_table_rules(table_name)
        return table_rules.get("error_handling", {})

    def get_primary_key(self, table_name: str) -> List[str]:
        """
        获取主键字段列表
        
        Args:
            table_name: 表名
            
        Returns:
            List[str]: 主键字段列表
        """
        table_rules = self.get_table_rules(table_name)
        return table_rules.get("primary_key", [])

    def get_required_fields(self, table_name: str) -> List[str]:
        """
        获取必需字段列表
        
        Args:
            table_name: 表名
            
        Returns:
            List[str]: 必需字段列表
        """
        table_rules = self.get_table_rules(table_name)
        return table_rules.get("required_fields", [])

    def add_dynamic_rule(self, table_name: str, rule: ValidationRule) -> bool:
        """
        添加动态规则到数据库
        
        Args:
            table_name: 表名
            rule: 验证规则
            
        Returns:
            bool: 是否添加成功
        """
        if not self.db_manager:
            self.logger.error("数据库管理器未初始化，无法添加动态规则")
            return False
            
        try:
            insert_sql = """
            INSERT OR REPLACE INTO data_processor_dynamic_rules 
            (table_name, rule_name, rule_type, rule_expression, 
             error_level, error_action, priority, enabled, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            with self.db_manager.get_connection() as conn:
                conn.execute(insert_sql, (
                    table_name,
                    rule.name,
                    rule.rule_type,
                    rule.rule_expression,
                    rule.error_level.value,
                    rule.error_action.value,
                    rule.priority,
                    rule.enabled,
                    get_beijing_time_now()
                ))
                
            # 强制刷新缓存
            self.get_table_rules(table_name, force_refresh=True)
            
            self.logger.info(f"成功添加动态规则: {table_name}.{rule.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加动态规则失败: {e}")
            return False

    def remove_dynamic_rule(self, table_name: str, rule_name: str) -> bool:
        """
        删除动态规则
        
        Args:
            table_name: 表名
            rule_name: 规则名
            
        Returns:
            bool: 是否删除成功
        """
        if not self.db_manager:
            self.logger.error("数据库管理器未初始化，无法删除动态规则")
            return False
            
        try:
            delete_sql = """
            DELETE FROM data_processor_dynamic_rules 
            WHERE table_name = ? AND rule_name = ?
            """
            
            with self.db_manager.get_connection() as conn:
                result = conn.execute(delete_sql, (table_name, rule_name))
                
            if result.rowcount > 0:
                # 强制刷新缓存
                self.get_table_rules(table_name, force_refresh=True)
                self.logger.info(f"成功删除动态规则: {table_name}.{rule_name}")
                return True
            else:
                self.logger.warning(f"动态规则不存在: {table_name}.{rule_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除动态规则失败: {e}")
            return False

    def validate_rule_expression(self, expression: str) -> bool:
        """
        验证规则表达式的有效性
        
        Args:
            expression: 规则表达式
            
        Returns:
            bool: 是否有效
        """
        try:
            # 这里可以实现更复杂的表达式验证逻辑
            # 目前只是简单检查基本语法
            if not expression or not expression.strip():
                return False
                
            # 检查是否包含危险关键字
            dangerous_keywords = ["DROP", "DELETE", "UPDATE", "INSERT", "EXEC", "EXECUTE"]
            upper_expr = expression.upper()
            for keyword in dangerous_keywords:
                if keyword in upper_expr:
                    return False
                    
            return True
            
        except Exception as e:
            self.logger.error(f"规则表达式验证失败: {e}")
            return False

    def get_performance_config(self) -> Dict[str, Any]:
        """
        获取性能配置
        
        Returns:
            Dict: 性能配置
        """
        global_config = self._yaml_rules.get("global", {})
        return global_config.get("performance_thresholds", {
            "small_data_limit": 100000,
            "large_data_limit": 1000000
        })

    def get_global_config(self) -> Dict[str, Any]:
        """
        获取全局配置
        
        Returns:
            Dict: 全局配置
        """
        return self._yaml_rules.get("global", {})

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息
        """
        return self.stats.copy()

    def reload_rules(self) -> bool:
        """
        重新加载所有规则
        
        Returns:
            bool: 是否重载成功
        """
        try:
            self._load_yaml_rules()
            if self.db_manager:
                self._load_database_rules()
            self._merge_rules()
            
            self.logger.info("规则重载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"规则重载失败: {e}")
            return False

    def close(self):
        """关闭规则引擎"""
        with self._lock:
            self._yaml_rules.clear()
            self._database_rules.clear()
            self._merged_rules.clear()
            
        self.logger.info("混合规则引擎已关闭")