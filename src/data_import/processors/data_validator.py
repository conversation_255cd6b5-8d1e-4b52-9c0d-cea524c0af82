#!/usr/bin/env python3
"""
数据验证器
实现数据的基础验证功能

验证功能：
1. 必需字段验证
2. 数据类型验证
3. 数值范围验证
4. 格式验证
"""

import pandas as pd
from typing import Dict, List, Any
import re

from ...utils.logger import get_logger


class DataValidator:
    """
    数据验证器主类

    提供基础的数据验证功能。
    """

    def __init__(self):
        """初始化数据验证器"""
        self.logger = get_logger(self.__class__.__name__)
        self.logger.info("数据验证器初始化完成")

    def validate_required_fields(
        self, data: pd.DataFrame, required_fields: List[str]
    ) -> Dict[str, Any]:
        """
        验证必需字段

        Args:
            data: 数据DataFrame
            required_fields: 必需字段列表

        Returns:
            Dict: 验证结果
        """
        missing_fields = []
        existing_fields = []

        for field in required_fields:
            if field in data.columns:
                existing_fields.append(field)
            else:
                missing_fields.append(field)

        is_valid = len(missing_fields) == 0

        return {
            "is_valid": is_valid,
            "missing_fields": missing_fields,
            "existing_fields": existing_fields,
            "coverage_rate": (
                len(existing_fields) / len(required_fields) if required_fields else 1.0
            ),
        }

    def validate_data_types(
        self, data: pd.DataFrame, type_requirements: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        验证数据类型

        Args:
            data: 数据DataFrame
            type_requirements: 类型要求字典

        Returns:
            Dict: 验证结果
        """
        type_errors = []
        valid_types = []

        for column, expected_type in type_requirements.items():
            if column not in data.columns:
                continue

            series = data[column]
            actual_type = self._infer_series_type(series)

            if self._is_compatible_type(actual_type, expected_type):
                valid_types.append(
                    {
                        "column": column,
                        "expected": expected_type,
                        "actual": actual_type,
                        "compatible": True,
                    }
                )
            else:
                type_errors.append(
                    {
                        "column": column,
                        "expected": expected_type,
                        "actual": actual_type,
                        "sample_values": list(series.dropna().head(3).astype(str)),
                        "error_description": f"字段 {column} 期望类型 {expected_type}，实际类型 {actual_type}",
                    }
                )

        return {
            "is_valid": len(type_errors) == 0,
            "type_errors": type_errors,
            "valid_types": valid_types,
            "total_checked": len(type_requirements),
        }

    def validate_value_ranges(
        self, data: pd.DataFrame, range_rules: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        验证数值范围

        Args:
            data: 数据DataFrame
            range_rules: 范围规则字典

        Returns:
            Dict: 验证结果
        """
        range_errors = []
        valid_ranges = []

        for column, rules in range_rules.items():
            if column not in data.columns:
                continue

            series = data[column].dropna()
            if len(series) == 0:
                continue

            # 检查最小值
            if "min" in rules and rules["min"] is not None:
                min_violations = (series < rules["min"]).sum()
                if min_violations > 0:
                    range_errors.append(
                        {
                            "column": column,
                            "rule_type": "min_value",
                            "rule_value": rules["min"],
                            "violations": int(min_violations),
                            "violation_rate": float(min_violations / len(series)),
                            "sample_violations": list(
                                series[series < rules["min"]].head(3)
                            ),
                        }
                    )

            # 检查最大值
            if "max" in rules and rules["max"] is not None:
                max_violations = (series > rules["max"]).sum()
                if max_violations > 0:
                    range_errors.append(
                        {
                            "column": column,
                            "rule_type": "max_value",
                            "rule_value": rules["max"],
                            "violations": int(max_violations),
                            "violation_rate": float(max_violations / len(series)),
                            "sample_violations": list(
                                series[series > rules["max"]].head(3)
                            ),
                        }
                    )

            # 如果没有违规，记录为有效
            column_violations = [e for e in range_errors if e["column"] == column]
            if not column_violations:
                valid_ranges.append(
                    {
                        "column": column,
                        "rules_applied": len(
                            [
                                k
                                for k in ["min", "max"]
                                if k in rules and rules[k] is not None
                            ]
                        ),
                        "total_values": len(series),
                    }
                )

        return {
            "is_valid": len(range_errors) == 0,
            "range_errors": range_errors,
            "valid_ranges": valid_ranges,
            "total_violations": sum(error["violations"] for error in range_errors),
        }

    def validate_format_patterns(
        self, data: pd.DataFrame, pattern_rules: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        验证格式模式

        Args:
            data: 数据DataFrame
            pattern_rules: 模式规则字典 (column -> regex pattern)

        Returns:
            Dict: 验证结果
        """
        format_errors = []
        valid_formats = []

        for column, pattern in pattern_rules.items():
            if column not in data.columns:
                continue

            series = data[column].dropna().astype(str)
            if len(series) == 0:
                continue

            try:
                # 编译正则表达式
                regex = re.compile(pattern)

                # 检查匹配
                matches = series.apply(lambda x: bool(regex.match(str(x))))
                mismatches = (~matches).sum()

                if mismatches > 0:
                    format_errors.append(
                        {
                            "column": column,
                            "pattern": pattern,
                            "mismatches": int(mismatches),
                            "mismatch_rate": float(mismatches / len(series)),
                            "sample_mismatches": list(series[~matches].head(3)),
                        }
                    )
                else:
                    valid_formats.append(
                        {
                            "column": column,
                            "pattern": pattern,
                            "total_values": len(series),
                        }
                    )

            except re.error as e:
                format_errors.append(
                    {
                        "column": column,
                        "pattern": pattern,
                        "error": f"正则表达式错误: {e}",
                        "mismatches": len(series),
                        "mismatch_rate": 1.0,
                    }
                )

        return {
            "is_valid": len(format_errors) == 0,
            "format_errors": format_errors,
            "valid_formats": valid_formats,
            "total_mismatches": sum(error["mismatches"] for error in format_errors),
        }

    def validate_uniqueness(
        self, data: pd.DataFrame, unique_fields: List[str]
    ) -> Dict[str, Any]:
        """
        验证唯一性

        Args:
            data: 数据DataFrame
            unique_fields: 需要唯一性的字段列表

        Returns:
            Dict: 验证结果
        """
        uniqueness_errors = []
        valid_unique_fields = []

        for field in unique_fields:
            if field not in data.columns:
                continue

            series = data[field].dropna()
            if len(series) == 0:
                continue

            duplicates = series.duplicated().sum()
            unique_count = series.nunique()
            total_count = len(series)

            if duplicates > 0:
                uniqueness_errors.append(
                    {
                        "field": field,
                        "total_values": total_count,
                        "unique_values": unique_count,
                        "duplicate_count": int(duplicates),
                        "uniqueness_rate": float(unique_count / total_count),
                        "sample_duplicates": list(series[series.duplicated()].head(3)),
                    }
                )
            else:
                valid_unique_fields.append(
                    {
                        "field": field,
                        "total_values": total_count,
                        "unique_values": unique_count,
                    }
                )

        return {
            "is_valid": len(uniqueness_errors) == 0,
            "uniqueness_errors": uniqueness_errors,
            "valid_unique_fields": valid_unique_fields,
            "total_duplicates": sum(
                error["duplicate_count"] for error in uniqueness_errors
            ),
        }

    def validate_cross_field_consistency(
        self, data: pd.DataFrame, consistency_rules: List[str]
    ) -> Dict[str, Any]:
        """
        验证跨字段一致性

        Args:
            data: 数据DataFrame
            consistency_rules: 一致性规则列表 (e.g., ['high >= open', 'low <= close'])

        Returns:
            Dict: 验证结果
        """
        consistency_errors = []
        valid_rules = []

        for rule in consistency_rules:
            try:
                # 解析规则表达式
                violations = self._evaluate_consistency_rule(data, rule)

                if violations > 0:
                    consistency_errors.append(
                        {
                            "rule": rule,
                            "violations": int(violations),
                            "violation_rate": float(violations / len(data)),
                            "description": f"规则 '{rule}' 不满足的记录数: {violations}",
                        }
                    )
                else:
                    valid_rules.append({"rule": rule, "total_records": len(data)})

            except Exception as e:
                consistency_errors.append(
                    {
                        "rule": rule,
                        "error": f"规则解析错误: {e}",
                        "violations": len(data),
                        "violation_rate": 1.0,
                    }
                )

        return {
            "is_valid": len(consistency_errors) == 0,
            "consistency_errors": consistency_errors,
            "valid_rules": valid_rules,
            "total_violations": sum(
                error["violations"]
                for error in consistency_errors
                if isinstance(error["violations"], int)
            ),
        }

    def _infer_series_type(self, series: pd.Series) -> str:
        """推断 Series 的数据类型"""
        # 去除空值
        non_null_series = series.dropna()
        if len(non_null_series) == 0:
            return "unknown"

        # 检查 pandas 的 dtype
        dtype_str = str(series.dtype)

        if "int" in dtype_str:
            return "integer"
        elif "float" in dtype_str:
            return "float"
        elif "bool" in dtype_str:
            return "boolean"
        elif "datetime" in dtype_str:
            return "datetime"
        elif "object" in dtype_str:
            # 对于 object 类型，进一步判断
            sample = non_null_series.head(100)

            # 检查是否为日期类型
            if self._is_date_series(sample):
                return "date"

            # 检查是否为数字类型
            if self._is_numeric_series(sample):
                if self._is_integer_series(sample):
                    return "integer"
                else:
                    return "float"

            return "string"
        else:
            return "unknown"

    def _is_compatible_type(self, actual_type: str, expected_type: str) -> bool:
        """检查类型是否兼容"""
        # 完全匹配
        if actual_type == expected_type:
            return True

        # 类型兼容性检查
        compatible_types = {
            "integer": ["integer", "float", "numeric"],
            "float": ["float", "numeric"],
            "string": ["string", "text"],
            "date": ["date", "datetime"],
            "datetime": ["datetime"],
            "boolean": ["boolean", "bool"],
        }

        return expected_type in compatible_types.get(actual_type, [])

    def _is_date_series(self, series: pd.Series) -> bool:
        """检查是否为日期类型的 Series"""
        try:
            sample_size = min(10, len(series))
            success_count = 0

            for value in series.head(sample_size):
                try:
                    pd.to_datetime(str(value))
                    success_count += 1
                except:
                    pass

            return success_count / sample_size >= 0.8
        except:
            return False

    def _is_numeric_series(self, series: pd.Series) -> bool:
        """检查是否为数字类型的 Series"""
        try:
            numeric_series = pd.to_numeric(series.astype(str), errors="coerce")
            valid_count = numeric_series.notna().sum()
            return valid_count / len(series) >= 0.8
        except:
            return False

    def _is_integer_series(self, series: pd.Series) -> bool:
        """检查是否为整数类型的 Series"""
        try:
            numeric_series = pd.to_numeric(series.astype(str), errors="coerce")
            valid_numeric = numeric_series.dropna()
            if len(valid_numeric) == 0:
                return False

            # 检查是否都是整数
            return (valid_numeric == valid_numeric.astype(int)).all()
        except:
            return False

    def _evaluate_consistency_rule(self, data: pd.DataFrame, rule: str) -> int:
        """评估一致性规则，返回违规数量"""
        # 简化的规则解析，支持基本的比较操作
        rule = rule.strip()

        # 支持的操作符
        operators = [">=", "<=", ">", "<", "==", "!="]

        for op in operators:
            if op in rule:
                parts = rule.split(op)
                if len(parts) == 2:
                    left_field = parts[0].strip()
                    right_field = parts[1].strip()

                    # 检查字段是否存在
                    if left_field in data.columns and right_field in data.columns:
                        left_series = data[left_field]
                        right_series = data[right_field]

                        # 执行比较
                        if op == ">=":
                            violations = (left_series < right_series).sum()
                        elif op == "<=":
                            violations = (left_series > right_series).sum()
                        elif op == ">":
                            violations = (left_series <= right_series).sum()
                        elif op == "<":
                            violations = (left_series >= right_series).sum()
                        elif op == "==":
                            violations = (left_series != right_series).sum()
                        elif op == "!=":
                            violations = (left_series == right_series).sum()
                        else:
                            violations = 0

                        return int(violations)
                break

        # 如果无法解析，返回 0
        return 0

    def get_validation_summary(
        self, validation_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        获取多个验证结果的汇总

        Args:
            validation_results: 验证结果列表

        Returns:
            Dict: 汇总统计
        """
        total_validations = len(validation_results)
        if total_validations == 0:
            return {"total_validations": 0, "overall_valid": True}

        valid_count = sum(
            1 for result in validation_results if result.get("is_valid", False)
        )
        overall_valid = valid_count == total_validations

        # 汇总错误信息
        total_errors = 0
        error_types = {}

        for result in validation_results:
            # 统计各类错误
            for error_key in [
                "type_errors",
                "range_errors",
                "format_errors",
                "uniqueness_errors",
                "consistency_errors",
            ]:
                if error_key in result:
                    errors = result[error_key]
                    total_errors += len(errors)

                    error_type = error_key.replace("_errors", "")
                    if error_type not in error_types:
                        error_types[error_type] = 0
                    error_types[error_type] += len(errors)

        return {
            "total_validations": total_validations,
            "valid_count": valid_count,
            "invalid_count": total_validations - valid_count,
            "overall_valid": overall_valid,
            "success_rate": valid_count / total_validations,
            "total_errors": total_errors,
            "error_types": error_types,
            "avg_errors_per_validation": total_errors / total_validations,
        }
