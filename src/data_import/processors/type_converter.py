#!/usr/bin/env python3
"""
类型转换器
实现数据类型的标准化转换

支持的类型转换：
1. 字符串转换
2. 整数转换
3. 浮点数转换
4. 日期转换
5. 布尔值转换
"""

import pandas as pd
import numpy as np
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Any, Optional

from ...utils.logger import get_logger


class TypeConverter:
    """
    类型转换器主类

    负责将数据从源类型转换为V4.0业务表所需的目标类型。
    """

    def __init__(self):
        """初始化类型转换器"""
        self.logger = get_logger(self.__class__.__name__)

        # 类型转换规则
        self._conversion_rules = self._initialize_conversion_rules()

        # 统计信息
        self._conversion_count = 0
        self._conversion_errors = 0

        self.logger.info("类型转换器初始化完成")

    def _initialize_conversion_rules(self) -> Dict[str, Dict[str, str]]:
        """
        初始化类型转换规则

        Returns:
            Dict: 类型转换规则字典
        """
        return {
            "futures": {
                "ts_code": "string",
                "contract_code": "string",
                "trade_date": "date",
                "trade_datetime": "datetime",
                "open": "float",
                "high": "float",
                "low": "float",
                "close": "float",
                "vol": "integer",
                "volume": "integer",
                "amount": "decimal",
                "oi": "integer",
                "open_interest": "integer",
            },
            "stock": {
                "ts_code": "string",
                "symbol": "string",
                "trade_date": "date",
                "trade_datetime": "datetime",
                "open": "float",
                "high": "float",
                "low": "float",
                "close": "float",
                "vol": "integer",
                "volume": "integer",
                "amount": "decimal",
                "turnover_rate": "float",
            },
        }

    def get_conversion_rules(self, data_category: str) -> Dict[str, str]:
        """
        获取指定数据分类的类型转换规则

        Args:
            data_category: 数据分类

        Returns:
            Dict[str, str]: 转换规则字典
        """
        return self._conversion_rules.get(data_category, {})

    def convert_to_string(self, series: pd.Series) -> pd.Series:
        """
        转换为字符串类型

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        try:
            # 处理空值
            result = series.astype(str)
            result = result.replace("nan", None)
            result = result.replace("None", None)

            self._conversion_count += 1
            self.logger.debug(f"字符串转换完成，处理记录数: {len(series)}")
            return result

        except Exception as e:
            self._conversion_errors += 1
            self.logger.error(f"字符串转换失败: {e}")
            return series

    def convert_to_integer(self, series: pd.Series) -> pd.Series:
        """
        转换为整数类型

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        try:
            # 先转换为数值类型，再转换为整数
            result = pd.to_numeric(series, errors="coerce")

            # 处理无穷大和超大值
            result = result.replace([np.inf, -np.inf], np.nan)

            # 转换为整数（允许空值）
            result = result.astype("Int64")

            self._conversion_count += 1
            self.logger.debug(f"整数转换完成，处理记录数: {len(series)}")
            return result

        except Exception as e:
            self._conversion_errors += 1
            self.logger.error(f"整数转换失败: {e}")
            return series

    def convert_to_float(self, series: pd.Series) -> pd.Series:
        """
        转换为浮点数类型

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        try:
            # 转换为数值类型
            result = pd.to_numeric(series, errors="coerce")

            # 处理无穷大和超大值
            result = result.replace([np.inf, -np.inf], np.nan)

            # 限制精度以避免过大的浮点数
            result = result.round(10)

            self._conversion_count += 1
            self.logger.debug(f"浮点数转换完成，处理记录数: {len(series)}")
            return result

        except Exception as e:
            self._conversion_errors += 1
            self.logger.error(f"浮点数转换失败: {e}")
            return series

    def convert_to_decimal(self, series: pd.Series) -> pd.Series:
        """
        转换为高精度十进制类型

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        try:

            def to_decimal(value):
                if pd.isna(value) or value in [np.inf, -np.inf]:
                    return None
                try:
                    return Decimal(str(value))
                except (ValueError, TypeError):
                    return None

            result = series.apply(to_decimal)

            self._conversion_count += 1
            self.logger.debug(f"十进制转换完成，处理记录数: {len(series)}")
            return result

        except Exception as e:
            self._conversion_errors += 1
            self.logger.error(f"十进制转换失败: {e}")
            return series

    def convert_to_date(self, series: pd.Series) -> pd.Series:
        """
        转换为日期类型

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        try:

            def parse_date(value):
                if pd.isna(value) or value is None:
                    return None

                # 如果已经是日期类型
                if isinstance(value, (date, datetime)):
                    return value.date() if isinstance(value, datetime) else value

                # 尝试多种日期格式
                date_str = str(value).strip()
                date_formats = [
                    "%Y%m%d",  # 20250129
                    "%Y-%m-%d",  # 2025-01-29
                    "%Y/%m/%d",  # 2025/01/29
                    "%d/%m/%Y",  # 29/01/2025
                    "%m/%d/%Y",  # 01/29/2025
                    "%Y-%m-%d %H:%M:%S",  # 2025-01-29 10:30:00
                    "%Y/%m/%d %H:%M:%S",  # 2025/01/29 10:30:00
                ]

                for fmt in date_formats:
                    try:
                        dt = datetime.strptime(date_str, fmt)
                        return dt.date()
                    except ValueError:
                        continue

                # 尝试pandas自动识别
                try:
                    dt = pd.to_datetime(value)
                    return dt.date() if not pd.isna(dt) else None
                except:
                    pass

                self.logger.warning(f"无法解析日期: {value}")
                return None

            result = series.apply(parse_date)

            self._conversion_count += 1
            self.logger.debug(f"日期转换完成，处理记录数: {len(series)}")
            return result

        except Exception as e:
            self._conversion_errors += 1
            self.logger.error(f"日期转换失败: {e}")
            return series

    def convert_to_datetime(self, series: pd.Series) -> pd.Series:
        """
        转换为日期时间类型

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        try:

            def parse_datetime(value):
                if pd.isna(value) or value is None:
                    return None

                # 如果已经是日期时间类型
                if isinstance(value, datetime):
                    return value
                elif isinstance(value, date):
                    return datetime.combine(value, datetime.min.time())

                # 尝试多种日期时间格式
                datetime_str = str(value).strip()
                datetime_formats = [
                    "%Y-%m-%d %H:%M:%S",  # 2025-01-29 10:30:00
                    "%Y/%m/%d %H:%M:%S",  # 2025/01/29 10:30:00
                    "%Y%m%d %H:%M:%S",  # 20250129 10:30:00
                    "%Y-%m-%d %H:%M",  # 2025-01-29 10:30
                    "%Y/%m/%d %H:%M",  # 2025/01/29 10:30
                    "%Y-%m-%d",  # 2025-01-29 (补充为00:00:00)
                    "%Y/%m/%d",  # 2025/01/29
                    "%Y%m%d",  # 20250129
                ]

                for fmt in datetime_formats:
                    try:
                        return datetime.strptime(datetime_str, fmt)
                    except ValueError:
                        continue

                # 尝试pandas自动识别
                try:
                    dt = pd.to_datetime(value)
                    return dt.to_pydatetime() if not pd.isna(dt) else None
                except:
                    pass

                self.logger.warning(f"无法解析日期时间: {value}")
                return None

            result = series.apply(parse_datetime)

            self._conversion_count += 1
            self.logger.debug(f"日期时间转换完成，处理记录数: {len(series)}")
            return result

        except Exception as e:
            self._conversion_errors += 1
            self.logger.error(f"日期时间转换失败: {e}")
            return series

    def convert_to_boolean(self, series: pd.Series) -> pd.Series:
        """
        转换为布尔类型

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        try:

            def parse_boolean(value):
                if pd.isna(value) or value is None:
                    return None

                # 已经是布尔类型
                if isinstance(value, bool):
                    return value

                # 字符串判断
                if isinstance(value, str):
                    value_lower = value.lower().strip()
                    if value_lower in ["true", "1", "yes", "y", "是", "真"]:
                        return True
                    elif value_lower in ["false", "0", "no", "n", "否", "假"]:
                        return False

                # 数值判断
                try:
                    num_value = float(value)
                    return bool(num_value)
                except:
                    pass

                self.logger.warning(f"无法解析布尔值: {value}")
                return None

            result = series.apply(parse_boolean)

            self._conversion_count += 1
            self.logger.debug(f"布尔转换完成，处理记录数: {len(series)}")
            return result

        except Exception as e:
            self._conversion_errors += 1
            self.logger.error(f"布尔转换失败: {e}")
            return series

    def auto_detect_and_convert(
        self, series: pd.Series, target_type: Optional[str] = None
    ) -> pd.Series:
        """
        自动检测并转换数据类型

        Args:
            series: 原始数据Series
            target_type: 目标类型（可选）

        Returns:
            pd.Series: 转换后的Series
        """
        if target_type:
            # 按照指定类型转换
            conversion_methods = {
                "string": self.convert_to_string,
                "integer": self.convert_to_integer,
                "float": self.convert_to_float,
                "decimal": self.convert_to_decimal,
                "date": self.convert_to_date,
                "datetime": self.convert_to_datetime,
                "boolean": self.convert_to_boolean,
            }

            if target_type in conversion_methods:
                return conversion_methods[target_type](series)

        # 自动检测类型
        return self._auto_detect_type(series)

    def _auto_detect_type(self, series: pd.Series) -> pd.Series:
        """
        自动检测数据类型并转换

        Args:
            series: 原始数据Series

        Returns:
            pd.Series: 转换后的Series
        """
        # 去除空值，获取样本数据
        non_null_series = series.dropna()
        if len(non_null_series) == 0:
            return series

        sample_values = non_null_series.head(min(100, len(non_null_series)))

        # 检测是否为日期类型
        if self._is_date_like(sample_values):
            return self.convert_to_date(series)

        # 检测是否为整数类型
        if self._is_integer_like(sample_values):
            return self.convert_to_integer(series)

        # 检测是否为浮点数类型
        if self._is_float_like(sample_values):
            return self.convert_to_float(series)

        # 检测是否为布尔类型
        if self._is_boolean_like(sample_values):
            return self.convert_to_boolean(series)

        # 默认作为字符串处理
        return self.convert_to_string(series)

    def _is_date_like(self, series: pd.Series) -> bool:
        """检测是否为日期类型"""
        try:
            sample_size = min(10, len(series))
            success_count = 0

            for value in series.head(sample_size):
                try:
                    pd.to_datetime(str(value))
                    success_count += 1
                except:
                    pass

            return success_count / sample_size >= 0.8
        except:
            return False

    def _is_integer_like(self, series: pd.Series) -> bool:
        """检测是否为整数类型"""
        try:
            # 尝试转换为数值
            numeric_series = pd.to_numeric(series, errors="coerce")
            valid_count = numeric_series.notna().sum()
            total_count = len(series)

            if valid_count / total_count < 0.8:
                return False

            # 检查是否都是整数
            valid_numeric = numeric_series.dropna()
            return (valid_numeric == valid_numeric.astype(int)).all()
        except:
            return False

    def _is_float_like(self, series: pd.Series) -> bool:
        """检测是否为浮点数类型"""
        try:
            numeric_series = pd.to_numeric(series, errors="coerce")
            valid_count = numeric_series.notna().sum()
            total_count = len(series)

            return valid_count / total_count >= 0.8
        except:
            return False

    def _is_boolean_like(self, series: pd.Series) -> bool:
        """检测是否为布尔类型"""
        try:
            unique_values = set(str(v).lower().strip() for v in series.unique())
            boolean_values = {
                "true",
                "false",
                "1",
                "0",
                "yes",
                "no",
                "y",
                "n",
                "是",
                "否",
                "真",
                "假",
            }

            return len(unique_values - boolean_values) == 0 and len(unique_values) > 0
        except:
            return False

    def batch_convert(
        self, data: pd.DataFrame, conversion_rules: Dict[str, str]
    ) -> pd.DataFrame:
        """
        批量转换数据类型

        Args:
            data: 原始数据DataFrame
            conversion_rules: 转换规则字典

        Returns:
            pd.DataFrame: 转换后的DataFrame
        """
        result_data = data.copy()
        conversion_summary = []

        for column, target_type in conversion_rules.items():
            if column in result_data.columns:
                try:
                    original_type = str(result_data[column].dtype)
                    result_data[column] = self.auto_detect_and_convert(
                        result_data[column], target_type
                    )
                    new_type = str(result_data[column].dtype)

                    conversion_summary.append(
                        {
                            "column": column,
                            "original_type": original_type,
                            "target_type": target_type,
                            "new_type": new_type,
                            "success": True,
                        }
                    )

                except Exception as e:
                    self.logger.error(f"字段 {column} 类型转换失败: {e}")
                    conversion_summary.append(
                        {
                            "column": column,
                            "original_type": str(result_data[column].dtype),
                            "target_type": target_type,
                            "new_type": str(result_data[column].dtype),
                            "success": False,
                            "error": str(e),
                        }
                    )

        self.logger.info(f"批量类型转换完成，处理字段: {len(conversion_summary)}个")
        return result_data

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取转换器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_conversions": self._conversion_count,
            "conversion_errors": self._conversion_errors,
            "success_rate": (
                (self._conversion_count - self._conversion_errors)
                / self._conversion_count
                if self._conversion_count > 0
                else 0
            ),
            "supported_types": [
                "string",
                "integer",
                "float",
                "decimal",
                "date",
                "datetime",
                "boolean",
            ],
            "conversion_rules_count": {
                category: len(rules)
                for category, rules in self._conversion_rules.items()
            },
        }
