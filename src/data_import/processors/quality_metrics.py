#!/usr/bin/env python3
"""
质量指标计算器
实现数据质量的各种指标计算

质量指标：
1. 完整性 (Completeness)
2. 准确性 (Accuracy)
3. 一致性 (Consistency)
4. 唯一性 (Uniqueness)
5. 时效性 (Timeliness)
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional

from ...utils.logger import get_logger


class QualityMetrics:
    """
    质量指标计算器主类

    提供各种数据质量指标的计算功能。
    """

    def __init__(self):
        """初始化质量指标计算器"""
        self.logger = get_logger(self.__class__.__name__)
        self.logger.info("质量指标计算器初始化完成")

    def calculate_completeness(self, data: pd.DataFrame) -> float:
        """
        计算数据完整性

        Args:
            data: 数据DataFrame

        Returns:
            float: 完整性评分 (0-1)
        """
        if data.empty:
            return 0.0

        total_cells = data.size
        non_null_cells = data.count().sum()

        completeness = non_null_cells / total_cells if total_cells > 0 else 0.0

        self.logger.debug(
            f"完整性计算: {completeness:.3f}, 非空值: {non_null_cells}/{total_cells}"
        )
        return completeness

    def calculate_uniqueness(self, data: pd.DataFrame, column: str) -> float:
        """
        计算指定列的唯一性

        Args:
            data: 数据DataFrame
            column: 列名

        Returns:
            float: 唯一性评分 (0-1)
        """
        if column not in data.columns or data.empty:
            return 0.0

        series = data[column].dropna()
        if len(series) == 0:
            return 0.0

        unique_count = series.nunique()
        total_count = len(series)

        uniqueness = unique_count / total_count

        self.logger.debug(
            f"列 {column} 唯一性: {uniqueness:.3f}, 唯一值: {unique_count}/{total_count}"
        )
        return uniqueness

    def calculate_consistency(
        self, data: pd.DataFrame, consistency_rules: List[str]
    ) -> float:
        """
        计算数据一致性

        Args:
            data: 数据DataFrame
            consistency_rules: 一致性规则列表

        Returns:
            float: 一致性评分 (0-1)
        """
        if data.empty or not consistency_rules:
            return 1.0

        total_violations = 0
        total_checks = len(data) * len(consistency_rules)

        for rule in consistency_rules:
            violations = self._evaluate_consistency_rule(data, rule)
            total_violations += violations

        consistency = (
            max(0.0, (total_checks - total_violations) / total_checks)
            if total_checks > 0
            else 1.0
        )

        self.logger.debug(
            f"一致性计算: {consistency:.3f}, 违规: {total_violations}/{total_checks}"
        )
        return consistency

    def calculate_accuracy(
        self, data: pd.DataFrame, accuracy_rules: Dict[str, Dict[str, Any]]
    ) -> float:
        """
        计算数据准确性

        Args:
            data: 数据DataFrame
            accuracy_rules: 准确性规则字典

        Returns:
            float: 准确性评分 (0-1)
        """
        if data.empty or not accuracy_rules:
            return 1.0

        total_violations = 0
        total_values = 0

        for column, rules in accuracy_rules.items():
            if column not in data.columns:
                continue

            series = data[column].dropna()
            if len(series) == 0:
                continue

            column_violations = 0

            # 检查数值范围
            if "min" in rules and rules["min"] is not None:
                column_violations += (series < rules["min"]).sum()

            if "max" in rules and rules["max"] is not None:
                column_violations += (series > rules["max"]).sum()

            # 检查格式模式
            if "pattern" in rules:
                import re

                try:
                    pattern = re.compile(rules["pattern"])
                    matches = series.astype(str).apply(lambda x: bool(pattern.match(x)))
                    column_violations += (~matches).sum()
                except re.error:
                    # 正则表达式错误，全部认为不准确
                    column_violations += len(series)

            total_violations += column_violations
            total_values += len(series)

        accuracy = (
            max(0.0, (total_values - total_violations) / total_values)
            if total_values > 0
            else 1.0
        )

        self.logger.debug(
            f"准确性计算: {accuracy:.3f}, 不准确值: {total_violations}/{total_values}"
        )
        return accuracy

    def calculate_timeliness(
        self,
        data: pd.DataFrame,
        timestamp_column: str,
        expected_frequency: str = "daily",
    ) -> float:
        """
        计算数据时效性

        Args:
            data: 数据DataFrame
            timestamp_column: 时间戳列名
            expected_frequency: 期望频率

        Returns:
            float: 时效性评分 (0-1)
        """
        if timestamp_column not in data.columns or data.empty:
            return 0.0

        timestamps = pd.to_datetime(data[timestamp_column], errors="coerce").dropna()
        if len(timestamps) < 2:
            return 1.0  # 数据不足，默认为及时

        # 计算时间间隔
        timestamps_sorted = timestamps.sort_values()
        intervals = timestamps_sorted.diff().dropna()

        # 根据频率设定期望间隔
        expected_intervals = {
            "daily": pd.Timedelta(days=1),
            "5min": pd.Timedelta(minutes=5),
            "15min": pd.Timedelta(minutes=15),
            "30min": pd.Timedelta(minutes=30),
            "hourly": pd.Timedelta(hours=1),
        }

        expected_interval = expected_intervals.get(
            expected_frequency, pd.Timedelta(days=1)
        )

        # 计算与期望间隔的偏差
        tolerance = expected_interval * 0.1  # 10%容差
        valid_intervals = intervals[
            (intervals >= expected_interval - tolerance)
            & (intervals <= expected_interval + tolerance)
        ]

        timeliness = (
            len(valid_intervals) / len(intervals) if len(intervals) > 0 else 1.0
        )

        self.logger.debug(
            f"时效性计算: {timeliness:.3f}, 正常间隔: {len(valid_intervals)}/{len(intervals)}"
        )
        return timeliness

    def calculate_validity(
        self, data: pd.DataFrame, data_types: Dict[str, str]
    ) -> float:
        """
        计算数据有效性

        Args:
            data: 数据DataFrame
            data_types: 数据类型字典

        Returns:
            float: 有效性评分 (0-1)
        """
        if data.empty or not data_types:
            return 1.0

        total_values = 0
        valid_values = 0

        for column, expected_type in data_types.items():
            if column not in data.columns:
                continue

            series = data[column].dropna()
            if len(series) == 0:
                continue

            column_valid = self._count_valid_values(series, expected_type)

            total_values += len(series)
            valid_values += column_valid

        validity = valid_values / total_values if total_values > 0 else 1.0

        self.logger.debug(
            f"有效性计算: {validity:.3f}, 有效值: {valid_values}/{total_values}"
        )
        return validity

    def calculate_density(self, data: pd.DataFrame, key_columns: List[str]) -> float:
        """
        计算数据密度（关键字段的填充率）

        Args:
            data: 数据DataFrame
            key_columns: 关键字段列表

        Returns:
            float: 密度评分 (0-1)
        """
        if data.empty or not key_columns:
            return 0.0

        # 过滤存在的列
        existing_key_columns = [col for col in key_columns if col in data.columns]
        if not existing_key_columns:
            return 0.0

        # 计算关键字段的完整性
        key_data = data[existing_key_columns]
        total_key_cells = key_data.size
        non_null_key_cells = key_data.count().sum()

        density = non_null_key_cells / total_key_cells if total_key_cells > 0 else 0.0

        self.logger.debug(
            f"数据密度: {density:.3f}, 关键字段非空: {non_null_key_cells}/{total_key_cells}"
        )
        return density

    def calculate_comprehensive_score(
        self, data: pd.DataFrame, weights: Optional[Dict[str, float]] = None, **kwargs
    ) -> Dict[str, Any]:
        """
        计算综合质量评分

        Args:
            data: 数据DataFrame
            weights: 各指标权重
            **kwargs: 其他参数

        Returns:
            Dict: 综合评分结果
        """
        # 默认权重
        default_weights = {
            "completeness": 0.25,
            "accuracy": 0.25,
            "consistency": 0.20,
            "uniqueness": 0.15,
            "validity": 0.15,
        }

        if weights:
            default_weights.update(weights)

        # 计算各个指标
        scores = {}

        # 完整性
        scores["completeness"] = self.calculate_completeness(data)

        # 有效性
        data_types = kwargs.get("data_types", {})
        scores["validity"] = self.calculate_validity(data, data_types)

        # 准确性
        accuracy_rules = kwargs.get("accuracy_rules", {})
        scores["accuracy"] = self.calculate_accuracy(data, accuracy_rules)

        # 一致性
        consistency_rules = kwargs.get("consistency_rules", [])
        scores["consistency"] = self.calculate_consistency(data, consistency_rules)

        # 唯一性（使用第一个列作为示例）
        if not data.empty and len(data.columns) > 0:
            first_column = data.columns[0]
            scores["uniqueness"] = self.calculate_uniqueness(data, first_column)
        else:
            scores["uniqueness"] = 1.0

        # 计算加权综合评分
        comprehensive_score = sum(
            scores[metric] * default_weights[metric]
            for metric in scores.keys()
            if metric in default_weights
        )

        result = {
            "comprehensive_score": comprehensive_score,
            "individual_scores": scores,
            "weights": default_weights,
            "total_records": len(data),
            "total_columns": len(data.columns) if not data.empty else 0,
        }

        self.logger.info(f"综合质量评分: {comprehensive_score:.3f}")
        return result

    def _evaluate_consistency_rule(self, data: pd.DataFrame, rule: str) -> int:
        """评估一致性规则，返回违规数量"""
        try:
            # 简化的规则解析，支持基本的比较操作
            rule = rule.strip()

            # 支持的操作符
            operators = [">=", "<=", ">", "<", "==", "!="]

            for op in operators:
                if op in rule:
                    parts = rule.split(op)
                    if len(parts) == 2:
                        left_field = parts[0].strip()
                        right_field = parts[1].strip()

                        # 检查字段是否存在
                        if left_field in data.columns and right_field in data.columns:
                            left_series = pd.to_numeric(
                                data[left_field], errors="coerce"
                            )
                            right_series = pd.to_numeric(
                                data[right_field], errors="coerce"
                            )

                            # 只对非空值进行比较
                            valid_mask = left_series.notna() & right_series.notna()
                            if valid_mask.sum() == 0:
                                return 0

                            left_valid = left_series[valid_mask]
                            right_valid = right_series[valid_mask]

                            # 执行比较
                            if op == ">=":
                                violations = (left_valid < right_valid).sum()
                            elif op == "<=":
                                violations = (left_valid > right_valid).sum()
                            elif op == ">":
                                violations = (left_valid <= right_valid).sum()
                            elif op == "<":
                                violations = (left_valid >= right_valid).sum()
                            elif op == "==":
                                violations = (left_valid != right_valid).sum()
                            elif op == "!=":
                                violations = (left_valid == right_valid).sum()
                            else:
                                violations = 0

                            return int(violations)
                    break

            return 0

        except Exception as e:
            self.logger.warning(f"规则解析失败: {rule}, 错误: {e}")
            return 0

    def _count_valid_values(self, series: pd.Series, expected_type: str) -> int:
        """计算符合类型要求的有效值数量"""
        try:
            if expected_type == "integer":
                numeric_series = pd.to_numeric(series, errors="coerce")
                valid_mask = numeric_series.notna() & (
                    numeric_series == numeric_series.astype(int)
                )
                return valid_mask.sum()

            elif expected_type == "float":
                numeric_series = pd.to_numeric(series, errors="coerce")
                return numeric_series.notna().sum()

            elif expected_type == "string":
                return series.notna().sum()

            elif expected_type in ["date", "datetime"]:
                try:
                    date_series = pd.to_datetime(series, errors="coerce")
                    return date_series.notna().sum()
                except:
                    return 0

            elif expected_type == "boolean":
                # 检查是否为布尔类型或可转换为布尔类型
                bool_values = {"true", "false", "1", "0", "yes", "no", "y", "n"}
                string_series = series.astype(str).str.lower().str.strip()
                valid_mask = string_series.isin(bool_values) | series.isin(
                    [True, False, 1, 0]
                )
                return valid_mask.sum()

            else:
                # 未知类型，默认为有效
                return series.notna().sum()

        except Exception as e:
            self.logger.warning(f"类型验证失败: {expected_type}, 错误: {e}")
            return 0

    def get_metrics_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取多个质量评估结果的汇总

        Args:
            results: 质量评估结果列表

        Returns:
            Dict: 汇总统计
        """
        if not results:
            return {"total_assessments": 0}

        # 提取综合评分
        comprehensive_scores = [r.get("comprehensive_score", 0) for r in results]

        # 提取各个指标的评分
        metrics = ["completeness", "accuracy", "consistency", "uniqueness", "validity"]
        metric_scores = {metric: [] for metric in metrics}

        for result in results:
            individual_scores = result.get("individual_scores", {})
            for metric in metrics:
                if metric in individual_scores:
                    metric_scores[metric].append(individual_scores[metric])

        # 计算统计信息
        summary = {
            "total_assessments": len(results),
            "comprehensive_score": {
                "mean": np.mean(comprehensive_scores),
                "median": np.median(comprehensive_scores),
                "std": np.std(comprehensive_scores),
                "min": np.min(comprehensive_scores),
                "max": np.max(comprehensive_scores),
            },
            "individual_metrics": {},
        }

        for metric, scores in metric_scores.items():
            if scores:
                summary["individual_metrics"][metric] = {
                    "mean": np.mean(scores),
                    "median": np.median(scores),
                    "std": np.std(scores),
                    "min": np.min(scores),
                    "max": np.max(scores),
                }

        return summary
