#!/usr/bin/env python3
"""
数据映射引擎
实现TUSHARE数据到V4.0统一业务表的完整映射

功能特性：
1. 多种数据源格式转换支持
2. 字段映射和类型转换
3. 批量数据处理
4. 自定义映射规则
5. 跨平台兼容性
"""

import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from pathlib import Path

from .field_mapper import FieldMapper
from .type_converter import TypeConverter
from ..mappers.business_table_mapper import BusinessTableMapper
from ..mappers.data_dictionary_mapper import DataDictionaryMapper
from ...utils.logger import get_logger


class DataMappingEngine:
    """
    数据映射引擎主类

    负责协调字段映射器、类型转换器等组件，
    实现数据从源格式到目标格式的完整转换。
    """

    def __init__(self):
        """初始化数据映射引擎"""
        self.logger = get_logger(self.__class__.__name__)

        # 初始化组件
        self.field_mapper = FieldMapper()
        self.type_converter = TypeConverter()
        self.business_mapper = BusinessTableMapper()
        self.dictionary_mapper = DataDictionaryMapper()

        # 自定义映射规则存储
        self._custom_rules = {}

        self.logger.info("数据映射引擎初始化完成")

    def map_fields(
        self,
        data: pd.DataFrame,
        source_type: str,
        target_type: str,
        data_category: str,
        frequency: str,
    ) -> pd.DataFrame:
        """
        执行字段映射转换

        Args:
            data: 原始数据DataFrame
            source_type: 数据源类型 (tushare, csv, etc.)
            target_type: 目标类型 (v4_business_table, etc.)
            data_category: 数据分类 (futures, stock, etc.)
            frequency: 数据频率 (daily, 5min, etc.)

        Returns:
            pd.DataFrame: 映射后的数据

        Raises:
            ValueError: 输入参数无效时
        """
        # 输入验证
        if data is None or data.empty:
            raise ValueError("数据不能为空")

        if source_type not in ["tushare", "csv", "custom_source"]:
            raise ValueError(f"不支持的数据源类型: {source_type}")

        self.logger.debug(
            f"开始字段映射: {source_type} -> {target_type}, 类别: {data_category}, 频率: {frequency}"
        )

        # 获取映射规则
        if source_type in self._custom_rules:
            mapping_rules = self._custom_rules[source_type]["field_mapping"]
        else:
            mapping_rules = self.field_mapper.get_mapping_rules(
                source_type, data_category, frequency
            )

        # 验证必需字段
        self._validate_required_fields(data, mapping_rules, data_category)

        # 执行字段映射
        mapped_data = self.field_mapper.apply_mapping(
            data=data,
            source_type=source_type,
            data_category=data_category,
            frequency=frequency,
        )

        self.logger.info(
            f"字段映射完成，数据行数: {len(mapped_data)}，字段数: {len(mapped_data.columns)}"
        )
        return mapped_data

    def convert_types(
        self, data: pd.DataFrame, source_type: str, target_type: str, data_category: str
    ) -> pd.DataFrame:
        """
        执行数据类型转换

        Args:
            data: 原始数据DataFrame
            source_type: 数据源类型
            target_type: 目标类型
            data_category: 数据分类

        Returns:
            pd.DataFrame: 类型转换后的数据
        """
        self.logger.debug(f"开始类型转换: {data_category}")

        # 获取类型转换规则
        if source_type in self._custom_rules:
            type_rules = self._custom_rules[source_type]["type_conversion"]
        else:
            type_rules = self.type_converter.get_conversion_rules(data_category)

        # 执行类型转换
        converted_data = data.copy()

        for column, target_type_name in type_rules.items():
            if column in converted_data.columns:
                try:
                    if target_type_name == "string":
                        converted_data[column] = self.type_converter.convert_to_string(
                            converted_data[column]
                        )
                    elif target_type_name == "integer":
                        converted_data[column] = self.type_converter.convert_to_integer(
                            converted_data[column]
                        )
                    elif target_type_name == "float":
                        converted_data[column] = self.type_converter.convert_to_float(
                            converted_data[column]
                        )
                    elif target_type_name == "date":
                        converted_data[column] = self.type_converter.convert_to_date(
                            converted_data[column]
                        )

                    self.logger.debug(f"字段 {column} 转换为 {target_type_name}")

                except Exception as e:
                    self.logger.warning(f"字段 {column} 类型转换失败: {e}")

        self.logger.info(f"类型转换完成，处理字段数: {len(type_rules)}")
        return converted_data

    def process_batch(
        self,
        batch_data: Dict[str, pd.DataFrame],
        mapping_config: Dict[str, Dict],
        target_type: str,
    ) -> Dict[str, pd.DataFrame]:
        """
        批量处理数据映射

        Args:
            batch_data: 批量数据字典
            mapping_config: 映射配置字典
            target_type: 目标类型

        Returns:
            Dict[str, pd.DataFrame]: 批量处理结果
        """
        self.logger.info(f"开始批量处理，批次数: {len(batch_data)}")

        results = {}
        total_records = 0

        for batch_id, data in batch_data.items():
            try:
                config = mapping_config[batch_id]

                # 执行字段映射
                mapped_data = self.map_fields(
                    data=data,
                    source_type=config["source_type"],
                    target_type=target_type,
                    data_category=config["data_category"],
                    frequency=config["frequency"],
                )

                # 执行类型转换
                converted_data = self.convert_types(
                    data=mapped_data,
                    source_type=config["source_type"],
                    target_type=target_type,
                    data_category=config["data_category"],
                )

                results[batch_id] = converted_data
                total_records += len(converted_data)

                self.logger.debug(
                    f"批次 {batch_id} 处理完成，数据行数: {len(converted_data)}"
                )

            except Exception as e:
                self.logger.error(f"批次 {batch_id} 处理失败: {e}")
                raise

        self.logger.info(f"批量处理完成，总处理记录数: {total_records}")
        return results

    def register_custom_rules(self, source_type: str, rules: Dict[str, Any]):
        """
        注册自定义映射规则

        Args:
            source_type: 数据源类型
            rules: 自定义规则字典
        """
        self._custom_rules[source_type] = rules
        self.logger.info(f"注册自定义映射规则: {source_type}")

    def _validate_required_fields(
        self, data: pd.DataFrame, mapping_rules: Dict[str, str], data_category: str
    ):
        """
        验证必需字段是否存在

        Args:
            data: 数据DataFrame
            mapping_rules: 映射规则
            data_category: 数据分类

        Raises:
            ValueError: 缺少必需字段时
        """
        # 根据数据分类获取必需字段
        if data_category == "futures":
            required_source_fields = [
                "ts_code",
                "trade_date",
                "open",
                "high",
                "low",
                "close",
            ]
        elif data_category == "stock":
            required_source_fields = [
                "ts_code",
                "trade_date",
                "open",
                "high",
                "low",
                "close",
            ]
        else:
            # 自定义类型不做强制验证
            return

        missing_fields = []
        for field in required_source_fields:
            if field not in data.columns:
                missing_fields.append(field)

        if missing_fields:
            raise ValueError(f"缺少必需字段: {missing_fields}")

    def _normalize_path(self, path: Path) -> Path:
        """
        标准化文件路径（跨平台兼容）

        Args:
            path: 原始路径

        Returns:
            Path: 标准化后的路径
        """
        return path.resolve()

    def _normalize_date(self, date_str: str) -> Optional[date]:
        """
        标准化日期格式（跨平台兼容）

        Args:
            date_str: 日期字符串

        Returns:
            Optional[date]: 标准化后的日期
        """
        try:
            # 尝试多种日期格式
            date_formats = [
                "%Y%m%d",  # 20250129
                "%Y-%m-%d",  # 2025-01-29
                "%Y/%m/%d",  # 2025/01/29
                "%d/%m/%Y",  # 29/01/2025
                "%m/%d/%Y",  # 01/29/2025
            ]

            for fmt in date_formats:
                try:
                    return datetime.strptime(date_str, fmt).date()
                except ValueError:
                    continue

            self.logger.warning(f"无法解析日期格式: {date_str}")
            return None

        except Exception as e:
            self.logger.error(f"日期标准化失败: {e}")
            return None

    def get_supported_sources(self) -> List[str]:
        """
        获取支持的数据源类型

        Returns:
            List[str]: 支持的数据源类型列表
        """
        built_in_sources = ["tushare", "csv"]
        custom_sources = list(self._custom_rules.keys())
        return built_in_sources + custom_sources

    def get_mapping_statistics(self) -> Dict[str, Any]:
        """
        获取映射引擎统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "engine_version": "1.0.0",
            "supported_sources": self.get_supported_sources(),
            "custom_rules_count": len(self._custom_rules),
            "field_mapper_stats": self.field_mapper.get_statistics(),
            "type_converter_stats": self.type_converter.get_statistics(),
            "business_mapper_stats": self.business_mapper.get_mapping_statistics(),
        }
