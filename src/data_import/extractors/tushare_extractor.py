#!/usr/bin/env python3
"""
TUSHARE数据提取器 - Feature 2核心实现

基于SimpleExtractor接口，实现TUSHARE Pro API的深度集成。
采用TDD驱动开发，确保所有功能经过完整测试验证。

核心特性：
- Token认证和连接验证
- 积分管理和频率控制
- 期货和股票数据采集
- 批量获取优化
- 异常处理和日志记录

设计原则：
- 遵循AQUA宪法TDD要求
- 优先复用现有SimpleExtractor基础设施
- 个人开发者友好的积分管理
- 完整的错误处理和监控
"""

import os
import re
from typing import Dict, Any

import polars as pl

try:
    import tushare as ts
except ImportError:
    ts = None

from .simple_extractor import SimpleExtractor, ExtractorException
from ...utils.time_utils import get_beijing_time_now
from ...tushare.points_calculator import PointsCalculator


class TushareApiError(ExtractorException):
    """TUSHARE API特定错误"""

    pass


class TushareExtractor(SimpleExtractor):
    """
    TUSHARE Pro数据提取器 - Feature 2核心实现

    基于SimpleExtractor的TUSHARE数据源适配器，提供：
    1. Token认证和API连接管理
    2. 期货/股票数据统一提取接口
    3. 积分预算感知的请求管理
    4. 批量获取和性能优化

    配置要求：
    - 环境变量TUSHARE_TOKEN必须设置
    - config.data_source必须为'tushare'
    - 支持各种TUSHARE API的target_table配置
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化TUSHARE提取器

        Args:
            config: 提取器配置字典
                必需字段:
                - name: 提取器名称
                - data_source: 必须为'tushare'
                - target_table: 目标表名
                环境要求:
                - TUSHARE_TOKEN: 有效的TUSHARE Pro Token

        Raises:
            ExtractorException: Token缺失或无效时抛出
            TushareApiError: TUSHARE API初始化失败时抛出
        """
        # 调用父类初始化
        super().__init__(config)

        # 验证数据源类型
        if self.data_source != "tushare":
            raise ExtractorException(
                f"数据源类型错误：期望'tushare'，实际'{self.data_source}'",
                self.name,
                "INVALID_DATA_SOURCE",
            )

        # 获取和验证TUSHARE Token（在检查tushare库之前）
        self.token = self._get_token_from_env()
        self._validate_token_format(self.token)

        # 检查tushare库是否可用
        if ts is None:
            raise ExtractorException(
                "tushare库未安装，请运行: pip install tushare",
                self.name,
                "MISSING_DEPENDENCY",
            )

        # 初始化TUSHARE Pro API客户端
        try:
            self.pro_api = ts.pro_api(self.token)
            self.logger.info(f"TUSHARE Pro API客户端初始化成功: {self.name}")
        except Exception as e:
            raise TushareApiError(
                f"TUSHARE API客户端初始化失败: {str(e)}", self.name, "API_INIT_FAILED"
            )

        # TUSHARE特定统计信息
        self.tushare_stats = {
            "api_calls": 0,
            "points_consumed": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "last_api_call": None,
            "connection_validated": False,
        }

        # 批量获取优化配置 - F2.T1.S5
        self.batch_size = config.get("batch_size", 1000)
        self.date_range = config.get("date_range", {})
        self.symbols = config.get("symbols", [])

        # 期货特定配置 - F2.T3.S1 & F2.T3.S2
        self.exchange = config.get("exchange", None)  # 交易所过滤
        self.include_settlement = config.get("include_settlement", True)  # 包含结算价
        self.include_position = config.get("include_position", True)  # 包含持仓量
        self.main_contract_only = config.get(
            "main_contract_only", False
        )  # 只获取主力合约

        # 集成积分计算器 - F2.T2.S4
        try:
            # 从配置获取积分预算，默认使用Feature 1配置的2100积分
            total_budget = config.get("total_points", 2100)
            self.points_calculator = PointsCalculator(total_budget)
            self.logger.info(f"积分计算器集成成功，总预算: {total_budget}积分")
        except Exception as e:
            self.logger.warning(f"积分计算器初始化失败: {e}，将使用基础统计")
            self.points_calculator = None

        self.logger.info(f"TushareExtractor初始化完成: {self.name}")

    def _get_token_from_env(self) -> str:
        """
        从环境变量获取TUSHARE Token

        Returns:
            str: TUSHARE Token

        Raises:
            ExtractorException: Token未设置时抛出
        """
        token = os.getenv("TUSHARE_TOKEN")
        if not token:
            raise ExtractorException(
                "TUSHARE_TOKEN环境变量未设置，请设置有效的TUSHARE Pro Token",
                self.name,
                "MISSING_TOKEN",
            )
        return token

    def _validate_token_format(self, token: str) -> None:
        """
        验证Token格式

        Args:
            token: TUSHARE Token字符串

        Raises:
            ExtractorException: Token格式无效时抛出
        """
        # TUSHARE Token通常是32位的十六进制字符串
        if len(token) < 20:  # 最小长度检查
            raise ExtractorException(
                f"Token格式无效：长度过短（{len(token)}字符），期望至少20字符",
                self.name,
                "INVALID_TOKEN_FORMAT",
            )

        # 检查是否包含明显的无效字符
        if not re.match(r"^[a-fA-F0-9]+$", token):
            self.logger.warning("Token可能包含非十六进制字符，请确认格式正确")

    def _validate_token(self) -> bool:
        """
        验证Token连接有效性

        通过调用stock_basic接口测试Token是否有效和API连接是否正常。

        Returns:
            bool: Token和连接是否有效
        """
        try:
            # 使用stock_basic接口测试连接（消耗积分最少）
            result = self.pro_api.stock_basic(list_status="L", limit=1)

            # 检查返回结果
            if result is not None and len(result) > 0:
                self.tushare_stats["connection_validated"] = True
                self.tushare_stats["last_api_call"] = get_beijing_time_now()
                self.logger.info("TUSHARE Token验证成功，API连接正常")
                return True
            else:
                self.logger.error("TUSHARE Token验证失败：API返回空结果")
                return False

        except Exception as e:
            self.logger.error(f"TUSHARE Token验证失败：{str(e)}")
            self.tushare_stats["connection_validated"] = False
            return False

    def extract(self, mode: str = "auto") -> pl.DataFrame:
        """
        统一数据提取方法

        这是TushareExtractor的核心方法，根据target_table配置
        自动调用相应的TUSHARE API进行数据提取。

        Args:
            mode: 提取模式
                - 'auto': 自动判断增量/全量（推荐）
                - 'full': 强制全量提取
                - 'incremental': 强制增量提取
                - 'validate': 仅验证连接，不提取数据

        Returns:
            pl.DataFrame: 标准化后的数据，包含业务字段和元数据字段

        Raises:
            TushareApiError: API调用失败或不支持的目标表类型
        """
        try:
            self.logger.info(
                f"开始数据提取: {self.name}, mode={mode}, target_table={self.target_table}"
            )

            # 验证模式：仅验证连接，不提取数据
            if mode == "validate":
                self.logger.info("验证模式：仅检查连接，不提取数据")
                # 返回空DataFrame但包含正确的元数据字段结构
                return self._create_empty_result()

            # 批量参数验证 - F2.T1.S5
            self._validate_batch_parameters()

            # 积分预算检查 - F2.T2.S4
            if self.points_calculator:
                api_name = self._get_api_name_for_target_table(self.target_table)
                if not self.points_calculator.can_afford_api_call(api_name):
                    required_points = self.points_calculator.get_required_points(
                        api_name
                    )
                    available_points = (
                        self.points_calculator.usage_stats.available_points
                    )

                    raise TushareApiError(
                        f"积分预算不足 - 需要: {required_points}积分, 可用: {available_points}积分",
                        self.name,
                        "INSUFFICIENT_POINTS",
                    )

            # 根据target_table判断数据类型并调用相应API
            if self.target_table in ["fut_daily", "futures_daily"]:
                # 期货日线数据
                raw_data = self._extract_futures_daily()

            elif self.target_table in ["fut_basic", "futures_basic"]:
                # 期货基础信息 - F2.T3.S1
                raw_data = self._extract_futures_basic()

            elif self.target_table in ["daily", "stock_daily"]:
                # 股票日线数据
                raw_data = self._extract_stocks_daily()

            else:
                # 不支持的目标表类型
                raise TushareApiError(
                    f"不支持的目标表类型: '{self.target_table}'。"
                    f"支持的类型: fut_daily, futures_daily, fut_basic, futures_basic, daily, stock_daily",
                    self.name,
                    "UNSUPPORTED_TARGET_TABLE",
                )

            # 数据格式标准化和元数据添加
            result = self._standardize_data(raw_data)

            # 更新统计信息
            self._update_stats(True, len(result))
            self.tushare_stats["successful_calls"] += 1

            self.logger.info(f"数据提取完成: {self.name}, 提取了{len(result)}条记录")
            return result

        except Exception as e:
            # 更新统计信息
            self._update_stats(False, 0, str(e))
            self.tushare_stats["failed_calls"] += 1

            if isinstance(e, TushareApiError):
                raise
            else:
                raise TushareApiError(
                    f"数据提取失败: {str(e)}", self.name, "EXTRACTION_FAILED"
                ) from e

    def _extract_futures_daily(self) -> pl.DataFrame:
        """
        提取期货日线数据 - F2.T3.S2增强版

        调用TUSHARE fut_daily接口获取期货日线数据。
        支持结算价、持仓量等期货特有字段的增强采集。

        增强功能：
        - 结算价数据 (include_settlement)
        - 持仓量数据 (include_position)
        - 主力合约过滤 (main_contract_only)

        Returns:
            pl.DataFrame: 增强的期货日线数据
        """
        try:
            self.logger.debug("调用TUSHARE fut_daily API")

            # 构建API调用参数 - F2.T1.S5
            api_params = self._build_api_parameters("fut_daily")

            # 调用TUSHARE API
            raw_data = self.pro_api.fut_daily(**api_params)

            # 转换为Polars DataFrame
            if raw_data is not None and len(raw_data) > 0:
                result = pl.DataFrame(raw_data)
                self.logger.debug(f"期货日线数据获取成功: {len(result)}条记录")

                # 记录积分消耗 - F2.T2.S4
                if self.points_calculator:
                    consumed_points = self.points_calculator.record_api_call(
                        "fut_daily"
                    )
                    self.tushare_stats["points_consumed"] += consumed_points

                return result
            else:
                self.logger.warning("期货日线数据为空")
                return pl.DataFrame()

        except Exception as e:
            raise TushareApiError(
                f"期货日线数据获取失败: {str(e)}", self.name, "FUTURES_DAILY_FAILED"
            ) from e

    def _extract_futures_basic(self) -> pl.DataFrame:
        """
        提取期货基础信息 - F2.T3.S1

        调用TUSHARE fut_basic接口获取期货合约基础信息。
        包含合约代码、名称、交易所、交易单位等基础信息。

        Returns:
            pl.DataFrame: 原始期货基础信息数据
        """
        try:
            self.logger.debug("调用TUSHARE fut_basic API")

            # 构建API调用参数 - F2.T3.S1
            api_params = self._build_api_parameters("fut_basic")

            # 调用TUSHARE API
            raw_data = self.pro_api.fut_basic(**api_params)

            # 转换为Polars DataFrame
            if raw_data is not None and len(raw_data) > 0:
                result = pl.DataFrame(raw_data)
                self.logger.debug(f"期货基础信息获取成功: {len(result)}条记录")

                # 记录积分消耗 - F2.T2.S4
                if self.points_calculator:
                    consumed_points = self.points_calculator.record_api_call(
                        "fut_basic"
                    )
                    self.tushare_stats["points_consumed"] += consumed_points

                return result
            else:
                self.logger.warning("期货基础信息为空")
                return pl.DataFrame()

        except Exception as e:
            raise TushareApiError(
                f"期货基础信息获取失败: {str(e)}", self.name, "FUTURES_BASIC_FAILED"
            ) from e

    def _extract_stocks_daily(self) -> pl.DataFrame:
        """
        提取股票日线数据

        调用TUSHARE daily接口获取股票日线数据。
        后续将在F2.T4.S2中添加更多股票特定功能。

        Returns:
            pl.DataFrame: 原始股票日线数据
        """
        try:
            self.logger.debug("调用TUSHARE daily API")

            # 构建API调用参数 - F2.T1.S5
            api_params = self._build_api_parameters("daily")

            # 调用TUSHARE API
            raw_data = self.pro_api.daily(**api_params)

            # 转换为Polars DataFrame
            if raw_data is not None and len(raw_data) > 0:
                result = pl.DataFrame(raw_data)
                self.logger.debug(f"股票日线数据获取成功: {len(result)}条记录")

                # 记录积分消耗 - F2.T2.S4
                if self.points_calculator:
                    consumed_points = self.points_calculator.record_api_call("daily")
                    self.tushare_stats["points_consumed"] += consumed_points

                return result
            else:
                self.logger.warning("股票日线数据为空")
                return pl.DataFrame()

        except Exception as e:
            raise TushareApiError(
                f"股票日线数据获取失败: {str(e)}", self.name, "STOCKS_DAILY_FAILED"
            ) from e

    def _standardize_data(self, raw_data: pl.DataFrame) -> pl.DataFrame:
        """
        数据格式标准化和元数据添加

        将TUSHARE原始数据转换为AQUA标准格式，添加必需的元数据字段。

        Args:
            raw_data: TUSHARE API返回的原始数据

        Returns:
            pl.DataFrame: 标准化后的数据
        """
        if len(raw_data) == 0:
            return self._create_empty_result()

        # 添加元数据字段
        current_time = get_beijing_time_now()

        result = raw_data.with_columns(
            [
                pl.lit("tushare").alias("data_source"),
                pl.lit(current_time).alias("created_at"),
                pl.lit(current_time).alias("updated_at"),
            ]
        )

        self.logger.debug(f"数据标准化完成: {len(result)}条记录")
        return result

    def _create_empty_result(self) -> pl.DataFrame:
        """
        创建空的结果DataFrame

        用于验证模式或无数据情况，确保返回格式一致。

        Returns:
            pl.DataFrame: 包含元数据字段的空DataFrame
        """
        return pl.DataFrame({"data_source": [], "created_at": [], "updated_at": []})

    def get_status(self) -> Dict[str, Any]:
        """
        获取TUSHARE提取器状态信息

        扩展父类状态信息，添加TUSHARE特定的统计数据。

        Returns:
            Dict[str, Any]: 包含TUSHARE统计的状态信息
        """
        base_status = super().get_status()

        # 添加TUSHARE特定状态
        base_status.update(
            {
                "tushare_stats": self.tushare_stats.copy(),
                "token_configured": bool(self.token),
                "api_client_ready": self.pro_api is not None,
                "connection_status": (
                    "verified"
                    if self.tushare_stats["connection_validated"]
                    else "unverified"
                ),
            }
        )

        # 添加积分计算器状态 - F2.T2.S4
        if self.points_calculator:
            base_status.update(
                {
                    "points_budget_status": self.points_calculator.get_usage_summary(),
                    "budget_health": self.points_calculator.check_budget_health(),
                }
            )

        return base_status

    def validate_config(self) -> Dict[str, Any]:
        """
        验证TUSHARE提取器配置

        扩展父类配置验证，添加TUSHARE特定的检查。

        Returns:
            Dict[str, Any]: 验证结果
        """
        result = super().validate_config()

        # TUSHARE特定验证
        if self.data_source != "tushare":
            result["errors"].append("data_source必须为'tushare'")
            result["valid"] = False

        if not self.token:
            result["errors"].append("TUSHARE_TOKEN环境变量未设置")
            result["valid"] = False

        if self.pro_api is None:
            result["errors"].append("TUSHARE Pro API客户端未初始化")
            result["valid"] = False

        return result

    def is_healthy(self) -> bool:
        """
        检查TUSHARE提取器健康状态

        扩展父类健康检查，添加TUSHARE特定的健康指标。

        Returns:
            bool: 是否健康
        """
        # 基础健康检查
        if not super().is_healthy():
            return False

        # TUSHARE特定健康检查
        if not self.token or self.pro_api is None:
            return False

        # 检查API调用成功率
        total_calls = self.tushare_stats["api_calls"]
        if total_calls > 0:
            success_rate = self.tushare_stats["successful_calls"] / total_calls
            if success_rate < 0.8:  # 成功率低于80%
                return False

        return True

    def __str__(self) -> str:
        """TUSHARE提取器字符串表示"""
        return (
            f"TushareExtractor({self.name}, {self.data_source} -> {self.target_table})"
        )

    def _validate_batch_parameters(self) -> None:
        """
        验证批量获取参数 - F2.T1.S5

        Raises:
            TushareApiError: 参数验证失败时抛出
        """
        # 验证日期范围
        if self.date_range:
            start_date = self.date_range.get("start_date")
            end_date = self.date_range.get("end_date")

            if start_date and end_date:
                # 简单的日期格式和大小比较
                if len(start_date) == 8 and len(end_date) == 8:
                    if start_date > end_date:
                        raise TushareApiError(
                            f"日期范围无效：开始日期({start_date})晚于结束日期({end_date})",
                            self.name,
                            "INVALID_DATE_RANGE",
                        )
                else:
                    raise TushareApiError(
                        "日期格式无效：期望YYYYMMDD格式",
                        self.name,
                        "INVALID_DATE_FORMAT",
                    )

        # 验证批量大小
        if self.batch_size <= 0:
            raise TushareApiError(
                f"批量大小无效：{self.batch_size}，必须大于0",
                self.name,
                "INVALID_BATCH_SIZE",
            )

        self.logger.debug(
            f"批量参数验证通过 - batch_size: {self.batch_size}, date_range: {self.date_range}"
        )

    def _build_api_parameters(self, api_name: str) -> Dict[str, Any]:
        """
        构建API调用参数 - F2.T1.S5

        Args:
            api_name: API接口名称

        Returns:
            Dict[str, Any]: API调用参数
        """
        params = {"limit": self.batch_size}

        # 添加日期范围参数
        if self.date_range:
            if "start_date" in self.date_range:
                params["start_date"] = self.date_range["start_date"]
            if "end_date" in self.date_range:
                params["end_date"] = self.date_range["end_date"]

        # 添加合约/股票代码参数
        if self.symbols:
            if api_name == "fut_daily":
                # 期货日线API使用ts_code参数
                if len(self.symbols) == 1:
                    params["ts_code"] = self.symbols[0]
                else:
                    params["ts_code"] = ",".join(self.symbols)
            elif api_name == "fut_basic":
                # 期货基础信息API使用fut_type参数（品种代码）
                if len(self.symbols) == 1:
                    params["fut_type"] = self.symbols[0]
                else:
                    # fut_basic API不支持多个品种，需要单独调用
                    params["fut_type"] = self.symbols[0]  # 暂时只取第一个
            elif api_name in ["daily", "stock_basic"]:
                # 股票API使用ts_code参数
                if len(self.symbols) == 1:
                    params["ts_code"] = self.symbols[0]
                else:
                    params["ts_code"] = ",".join(self.symbols)

        # 添加交易所参数 - F2.T3.S1
        if self.exchange and api_name == "fut_basic":
            params["exchange"] = self.exchange

        self.logger.debug(f"API参数构建完成 - {api_name}: {params}")
        return params

    def _get_api_name_for_target_table(self, target_table: str) -> str:
        """
        根据目标表名获取对应的API名称

        Args:
            target_table: 目标表名

        Returns:
            str: TUSHARE API名称
        """
        # 映射目标表到TUSHARE API名称
        table_to_api_map = {
            "fut_daily": "fut_daily",
            "futures_daily": "fut_daily",
            "fut_basic": "fut_basic",
            "futures_basic": "fut_basic",
            "daily": "daily",
            "stock_daily": "daily",
        }

        return table_to_api_map.get(target_table, target_table)

    def __repr__(self) -> str:
        """TUSHARE提取器详细字符串表示"""
        return (
            f"TushareExtractor(name='{self.name}', "
            f"data_source='{self.data_source}', "
            f"target_table='{self.target_table}', "
            f"enabled={self.enabled}, "
            f"token_configured={bool(self.token)})"
        )
