#!/usr/bin/env python3
"""
简化的数据提取器基类 - 个人开发者友好版

基于现有FromC2C导入器的成功实践，抽取出简化的提取器接口。
遵循"最小可行重构(MVR)"策略，最大化复用现有验证代码。

设计原则:
- 单一extract方法，自动判断增量/全量
- 配置驱动，支持跨平台
- 状态透明，便于调试和监控
- 接口简单，个人开发者友好
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any

import polars as pl

from ...utils.config_loader import ConfigLoader
from ...utils.time_utils import get_beijing_time_now


class SimpleExtractor(ABC):
    """
    简化的数据提取器基类 - EPIC3个人开发者优化版

    基于现有FromC2C导入器的核心功能抽象，提供统一的数据提取接口。
    相比原BaseExtractor，大幅简化接口复杂度，适合个人开发者快速上手。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化简化提取器

        Args:
            config: 提取器配置字典
                必需字段:
                - name: 提取器名称
                - data_source: 数据源类型 (csv/tushare/mysql)
                - target_table: 目标分层表名
                可选字段:
                - enabled: 是否启用 (默认True)
                - batch_size: 批处理大小 (默认1000)
                - max_retries: 最大重试次数 (默认3)
        """
        self.config = config
        self.name = config.get("name", "unknown_extractor")
        self.data_source = config.get("data_source", "unknown")
        self.target_table = config.get("target_table")
        self.enabled = config.get("enabled", True)

        # 日志配置
        self.logger = logging.getLogger(f"{__name__}.{self.name}")

        # 统计信息
        self.extraction_stats = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "total_records": 0,
            "last_extraction_time": None,
            "errors": [],
        }

        # 配置加载器（用于跨平台路径处理）
        self.config_loader = ConfigLoader()

        self.logger.info(
            f"SimpleExtractor初始化: {self.name} (数据源: {self.data_source})"
        )

    @abstractmethod
    def extract(self, mode: str = "auto") -> pl.DataFrame:
        """
        统一提取方法，自动判断增量/全量

        这是SimpleExtractor的核心方法，子类必须实现。
        设计理念：一个方法解决所有提取需求，降低使用复杂度。

        Args:
            mode: 提取模式
                - 'auto': 自动判断增量/全量（推荐）
                - 'full': 强制全量提取
                - 'incremental': 强制增量提取
                - 'validate': 仅验证数据源，不提取

        Returns:
            pl.DataFrame: 标准化后的数据
                必须包含以下字段:
                - 业务字段: 符合target_table的DATA_DICTIONARY.md定义
                - 元数据字段: data_source, created_at, updated_at

        Raises:
            NotImplementedError: 子类必须实现此方法
            ExtractorException: 提取过程中的各种异常

        Examples:
            >>> extractor = CsvLayerExtractor(config)
            >>> data = extractor.extract('auto')
            >>> print(f"提取了 {len(data)} 条记录")
        """
        pass

    def get_status(self) -> Dict[str, Any]:
        """
        获取提取器状态信息

        提供提取器运行状态的完整视图，便于监控和调试。

        Returns:
            Dict[str, Any]: 状态信息字典
                - name: 提取器名称
                - data_source: 数据源类型
                - target_table: 目标表名
                - enabled: 是否启用
                - stats: 统计信息
                - last_extraction: 最后提取时间
                - config_summary: 配置摘要
        """
        return {
            "name": self.name,
            "data_source": self.data_source,
            "target_table": self.target_table,
            "enabled": self.enabled,
            "stats": self.extraction_stats.copy(),
            "last_extraction": self.extraction_stats.get("last_extraction_time"),
            "config_summary": {
                "batch_size": self.config.get("batch_size", 1000),
                "max_retries": self.config.get("max_retries", 3),
                "has_target_table": bool(self.target_table),
            },
            "timestamp": get_beijing_time_now().isoformat(),
        }

    def validate_config(self) -> Dict[str, Any]:
        """
        验证提取器配置完整性

        Returns:
            Dict[str, Any]: 验证结果
                - valid: 配置是否有效
                - errors: 错误列表
                - warnings: 警告列表
        """
        result = {"valid": True, "errors": [], "warnings": []}

        # 必需字段检查
        required_fields = ["name", "data_source"]
        for field in required_fields:
            if not self.config.get(field):
                result["errors"].append(f"缺少必需配置字段: {field}")
                result["valid"] = False

        # 目标表检查
        if not self.target_table:
            result["warnings"].append("未指定target_table，可能影响数据路由")

        # 数据源类型检查
        valid_sources = ["csv", "tushare", "mysql", "unknown"]
        if self.data_source not in valid_sources:
            result["warnings"].append(f"未知数据源类型: {self.data_source}")

        return result

    def _update_stats(self, success: bool, records_count: int = 0, error: str = None):
        """
        更新提取统计信息

        Args:
            success: 是否成功
            records_count: 记录数量
            error: 错误信息
        """
        self.extraction_stats["total_extractions"] += 1
        self.extraction_stats["last_extraction_time"] = get_beijing_time_now()

        if success:
            self.extraction_stats["successful_extractions"] += 1
            self.extraction_stats["total_records"] += records_count
        else:
            self.extraction_stats["failed_extractions"] += 1
            if error:
                self.extraction_stats["errors"].append(
                    {"timestamp": get_beijing_time_now().isoformat(), "error": error}
                )

    def reset_stats(self):
        """重置统计信息"""
        self.extraction_stats = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "total_records": 0,
            "last_extraction_time": None,
            "errors": [],
        }
        self.logger.info(f"重置提取器统计信息: {self.name}")

    def is_healthy(self) -> bool:
        """
        检查提取器健康状态

        Returns:
            bool: 是否健康
        """
        # 简单的健康检查逻辑
        if not self.enabled:
            return False

        config_validation = self.validate_config()
        if not config_validation["valid"]:
            return False

        # 检查错误率
        total = self.extraction_stats["total_extractions"]
        if total > 0:
            error_rate = self.extraction_stats["failed_extractions"] / total
            if error_rate > 0.5:  # 错误率超过50%
                return False

        return True

    def __str__(self) -> str:
        """字符串表示"""
        return (
            f"SimpleExtractor({self.name}, {self.data_source} -> {self.target_table})"
        )

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"SimpleExtractor(name='{self.name}', "
            f"data_source='{self.data_source}', "
            f"target_table='{self.target_table}', "
            f"enabled={self.enabled})"
        )


class ExtractorException(Exception):
    """提取器异常基类"""

    def __init__(
        self, message: str, extractor_name: str = None, error_code: str = None
    ):
        self.message = message
        self.extractor_name = extractor_name
        self.error_code = error_code
        super().__init__(self.message)


class ConfigurationError(ExtractorException):
    """配置错误"""

    pass


class DataSourceError(ExtractorException):
    """数据源错误"""

    pass


class DataValidationError(ExtractorException):
    """数据验证错误"""

    pass
