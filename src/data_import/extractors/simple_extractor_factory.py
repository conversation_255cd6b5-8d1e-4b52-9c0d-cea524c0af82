#!/usr/bin/env python3
"""
简化提取器工厂 - Feature 2集成支持

提供统一的提取器创建接口，支持各种数据源类型的提取器实例化。
遵循工厂模式，实现提取器类型与客户端代码的解耦。

支持的数据源类型：
- 'csv': CSV文件数据源 -> CsvLayerExtractor
- 'tushare': TUSHARE Pro API数据源 -> TushareExtractor
- 'mysql': MySQL数据库数据源 -> MySQLExtractor (待实现)

设计原则：
- 单一入口：通过create_extractor统一创建
- 类型安全：基于config.data_source自动选择
- 可扩展：新增数据源类型只需添加分支
- 错误友好：明确的错误信息和异常处理
"""

import logging
from typing import Dict, Any

from .simple_extractor import SimpleExtractor
from .csv_layer_extractor import CsvLayerExtractor
from .tushare_extractor import TushareExtractor


class SimpleExtractorFactory:
    """
    简化提取器工厂类

    负责根据配置创建相应的数据提取器实例。
    采用静态工厂方法模式，无需实例化即可使用。
    """

    @staticmethod
    def create_extractor(config: Dict[str, Any]) -> SimpleExtractor:
        """
        根据配置创建相应的数据提取器

        这是工厂类的核心方法，基于config.data_source字段
        自动选择并创建相应的提取器实例。

        Args:
            config: 提取器配置字典
                必需字段:
                - name: 提取器名称
                - data_source: 数据源类型 ('csv', 'tushare', 'mysql')
                - target_table: 目标表名
                其他字段根据具体提取器类型而定

        Returns:
            SimpleExtractor: 相应的提取器实例

        Raises:
            NotImplementedError: 不支持的数据源类型
            ExtractorException: 提取器创建失败

        Examples:
            >>> # 创建TUSHARE提取器
            >>> config = {
            ...     "name": "tushare_fut_extractor",
            ...     "data_source": "tushare",
            ...     "target_table": "fut_daily"
            ... }
            >>> extractor = SimpleExtractorFactory.create_extractor(config)
            >>> isinstance(extractor, TushareExtractor)
            True

            >>> # 创建CSV提取器
            >>> config = {
            ...     "name": "csv_extractor",
            ...     "data_source": "csv",
            ...     "data_dir": "/path/to/csv/files",
            ...     "target_table": "csv_fut_main_contract_kline_15min"
            ... }
            >>> extractor = SimpleExtractorFactory.create_extractor(config)
            >>> isinstance(extractor, CsvLayerExtractor)
            True
        """
        logger = logging.getLogger(__name__)

        # 获取数据源类型
        data_source = config.get("data_source")
        if not data_source:
            raise ValueError("配置中缺少必需字段: data_source")

        extractor_name = config.get("name", "unknown")
        logger.info(f"创建提取器: {extractor_name} (数据源: {data_source})")

        # 根据数据源类型创建相应的提取器
        if data_source == "csv":
            # CSV文件数据源
            return CsvLayerExtractor(config)

        elif data_source == "tushare":
            # TUSHARE Pro API数据源
            return TushareExtractor(config)

        elif data_source == "mysql":
            # MySQL数据库源（待实现）
            # TODO: 在后续Feature中实现MySQLExtractor
            raise NotImplementedError(
                "MySQL数据源提取器尚未实现，计划在后续Feature中添加支持"
            )

        else:
            # 不支持的数据源类型
            supported_sources = ["csv", "tushare", "mysql"]
            raise NotImplementedError(
                f"不支持的数据源类型: '{data_source}'。"
                f"支持的类型: {', '.join(supported_sources)}"
            )

    @staticmethod
    def get_supported_data_sources() -> list:
        """
        获取支持的数据源类型列表

        Returns:
            list: 支持的数据源类型列表
        """
        return ["csv", "tushare"]  # mysql暂时不包含，因为尚未实现

    @staticmethod
    def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证提取器配置的基础有效性

        在创建提取器之前进行基础配置验证，
        可以提前发现配置问题，避免创建失败。

        Args:
            config: 提取器配置字典

        Returns:
            Dict[str, Any]: 验证结果
                - valid: 配置是否有效
                - errors: 错误列表
                - warnings: 警告列表
        """
        result = {"valid": True, "errors": [], "warnings": []}

        # 检查必需字段
        required_fields = ["name", "data_source"]
        for field in required_fields:
            if not config.get(field):
                result["errors"].append(f"缺少必需配置字段: {field}")
                result["valid"] = False

        # 检查数据源类型
        data_source = config.get("data_source")
        if data_source:
            supported_sources = SimpleExtractorFactory.get_supported_data_sources()
            if data_source not in supported_sources:
                result["errors"].append(
                    f"不支持的数据源类型: '{data_source}'，"
                    f"支持的类型: {', '.join(supported_sources)}"
                )
                result["valid"] = False

        # 数据源特定验证
        if data_source == "csv":
            if not config.get("data_dir"):
                result["warnings"].append("CSV数据源建议配置data_dir字段")

        elif data_source == "tushare":
            import os

            if not os.getenv("TUSHARE_TOKEN"):
                result["warnings"].append("TUSHARE数据源需要设置TUSHARE_TOKEN环境变量")

        return result
