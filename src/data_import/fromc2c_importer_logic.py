#!/usr/bin/env python3
"""
FromC2C导入器逻辑接口模块

为API路由提供FromC2C数据导入的逻辑函数接口，
封装FromC2C_csv_main_contract_importer类的功能。
"""

import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

from .fromC2C_csv_main_contract_importer import FromC2C_csv_main_contract_importer

logger = logging.getLogger(__name__)


def check_environment_logic(environment: str = "dev") -> Dict[str, Any]:
    """
    检查FromC2C导入环境
    
    Args:
        environment: 环境名称
        
    Returns:
        Dict: 环境检查结果
    """
    try:
        with FromC2C_csv_main_contract_importer(environment=environment) as importer:
            # 检查数据库连接
            connection_status = importer.connection_manager.test_connection()
            
            # 检查配置
            config_status = {
                "data_source_path": str(importer.config.get("data_source_path", "未配置")),
                "database_path": str(importer.config.get("database_path", "未配置")),
                "environment": environment
            }
            
            return {
                "success": True,
                "database_connection": connection_status,
                "configuration": config_status,
                "message": "环境检查完成"
            }
            
    except Exception as e:
        logger.error(f"环境检查失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "环境检查失败"
        }


def check_data_source_logic(environment: str = "dev") -> Dict[str, Any]:
    """
    检查FromC2C数据源
    
    Args:
        environment: 环境名称
        
    Returns:
        Dict: 数据源检查结果
    """
    try:
        with FromC2C_csv_main_contract_importer(environment=environment) as importer:
            # 扫描CSV文件
            files_by_table = importer.get_fromC2C_csv_files()
            
            total_files = sum(len(files) for files in files_by_table.values())
            
            return {
                "success": True,
                "files_by_table": {
                    table: len(files) for table, files in files_by_table.items()
                },
                "total_files": total_files,
                "data_source_path": str(importer.config.get("data_source_path", "未配置")),
                "message": f"发现 {total_files} 个CSV文件"
            }
            
    except Exception as e:
        logger.error(f"数据源检查失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "数据源检查失败"
        }


def execute_import_logic(
    environment: str = "dev",
    max_files_per_table: Optional[int] = None,
    import_type: str = "manual"
) -> Dict[str, Any]:
    """
    执行FromC2C数据导入
    
    Args:
        environment: 环境名称
        max_files_per_table: 每个表最大文件数限制
        import_type: 导入类型
        
    Returns:
        Dict: 导入结果
    """
    try:
        with FromC2C_csv_main_contract_importer(environment=environment) as importer:
            # 开始导入会话
            import_id = importer.start_import_session(import_type=import_type)
            
            if not import_id:
                return {
                    "success": False,
                    "error": "无法创建导入会话",
                    "message": "导入失败"
                }
            
            # 执行导入
            import_result = importer.import_all_fromC2C_data(
                max_files_per_table=max_files_per_table
            )
            
            # 结束导入会话
            summary = import_result.get("summary", {})
            importer.end_import_session(
                records_imported=summary.get("total_imported_records", 0),
                records_failed=summary.get("total_failed_records", 0),
                error_message=None if import_result.get("success", False) else "导入过程中出现错误"
            )
            
            return {
                "success": import_result.get("success", False),
                "import_id": import_id,
                "summary": summary,
                "details": import_result.get("details", {}),
                "message": "导入完成" if import_result.get("success", False) else "导入失败"
            }
            
    except Exception as e:
        logger.error(f"导入执行失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "导入执行失败"
        }


def get_import_status_logic(environment: str = "dev") -> Dict[str, Any]:
    """
    获取导入状态
    
    Args:
        environment: 环境名称
        
    Returns:
        Dict: 导入状态信息
    """
    try:
        with FromC2C_csv_main_contract_importer(environment=environment) as importer:
            summary = importer.get_fromC2C_import_summary()
            
            return {
                "success": True,
                "summary": summary,
                "message": "状态获取成功"
            }
            
    except Exception as e:
        logger.error(f"状态获取失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "状态获取失败"
        }


def preview_data_logic(
    environment: str = "dev",
    table_name: str = "fut_main_contract_kline_5min",
    limit: int = 100
) -> Dict[str, Any]:
    """
    预览导入的数据
    
    Args:
        environment: 环境名称
        table_name: 表名
        limit: 限制行数
        
    Returns:
        Dict: 预览数据结果
    """
    try:
        with FromC2C_csv_main_contract_importer(environment=environment) as importer:
            connection = importer.connection_manager.get_connection()
            
            # 检查表是否存在
            table_exists_query = f"""
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_name = '{table_name}'
            """
            
            result = connection.execute(table_exists_query).fetchone()
            if not result or result[0] == 0:
                return {
                    "success": False,
                    "error": f"表 {table_name} 不存在",
                    "message": "预览失败"
                }
            
            # 获取数据预览
            preview_query = f"SELECT * FROM {table_name} LIMIT {limit}"
            rows = connection.execute(preview_query).fetchall()
            
            # 获取列信息
            columns_query = f"DESCRIBE {table_name}"
            columns = connection.execute(columns_query).fetchall()
            column_names = [col[0] for col in columns]
            
            # 转换为字典格式
            data = []
            for row in rows:
                data.append(dict(zip(column_names, row)))
            
            return {
                "success": True,
                "table_name": table_name,
                "columns": column_names,
                "data": data,
                "row_count": len(data),
                "message": f"预览 {table_name} 表数据"
            }
            
    except Exception as e:
        logger.error(f"数据预览失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "数据预览失败"
        }
