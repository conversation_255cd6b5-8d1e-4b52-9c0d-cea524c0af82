#!/usr/bin/env python3
"""
统一数据加载器
提供标准化的数据加载和导入功能

特性：
- CSV数据加载
- 数据库导入
- 临时文件管理
- 错误处理和重试
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Optional
import pandas as pd

from ...database.connection_manager import DuckDBConnectionManager


class DataLoader:
    """统一数据加载器"""

    def __init__(self, environment: str = "test", encoding: str = "utf-8"):
        """
        初始化数据加载器

        Args:
            environment: 环境名称
            encoding: 文件编码
        """
        self.environment = environment
        self.encoding = encoding
        self.logger = logging.getLogger(__name__)

        # 数据库连接管理器
        self.connection_manager = DuckDBConnectionManager(environment)

    def load_csv(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """
        加载CSV文件

        Args:
            file_path: CSV文件路径
            **kwargs: pandas.read_csv的额外参数

        Returns:
            pd.DataFrame: 加载的数据
        """
        try:
            # 设置默认参数
            default_params = {
                "encoding": self.encoding,
                "na_values": ["", "NA", "N/A", "null", "NULL"],
                "keep_default_na": True,
                "skipinitialspace": True,
            }

            # 合并用户参数
            params = {**default_params, **kwargs}

            # 加载数据
            df = pd.read_csv(file_path, **params)

            self.logger.info(f"成功加载CSV文件: {file_path}, 数据形状: {df.shape}")
            return df

        except Exception as e:
            self.logger.error(f"加载CSV文件失败: {file_path} - {str(e)}")
            raise e

    def save_temp_csv(self, df: pd.DataFrame, prefix: str = "temp_") -> Path:
        """
        保存DataFrame为临时CSV文件

        Args:
            df: 数据
            prefix: 文件名前缀

        Returns:
            Path: 临时文件路径
        """
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                mode="w",
                suffix=".csv",
                prefix=prefix,
                delete=False,
                encoding=self.encoding,
            )

            temp_path = Path(temp_file.name)
            temp_file.close()

            # 保存数据
            df.to_csv(temp_path, index=False, encoding=self.encoding)

            self.logger.debug(f"保存临时CSV文件: {temp_path}")
            return temp_path

        except Exception as e:
            self.logger.error(f"保存临时CSV文件失败: {str(e)}")
            raise e

    def cleanup_temp_file(self, file_path: Path):
        """
        清理临时文件

        Args:
            file_path: 临时文件路径
        """
        try:
            if file_path.exists():
                file_path.unlink()
                self.logger.debug(f"清理临时文件: {file_path}")
        except Exception as e:
            self.logger.warning(f"清理临时文件失败: {file_path} - {str(e)}")

    def import_to_database(
        self, df: pd.DataFrame, table_name: str, mode: str = "append"
    ) -> Dict:
        """
        导入数据到数据库

        Args:
            df: 要导入的数据
            table_name: 目标表名
            mode: 导入模式 ("append", "replace", "create")

        Returns:
            Dict: 导入结果
        """
        import_result = {
            "success": False,
            "table_name": table_name,
            "records_imported": 0,
            "error": None,
            "mode": mode,
        }

        temp_csv_path = None

        try:
            # 保存为临时CSV文件
            temp_csv_path = self.save_temp_csv(df, f"import_{table_name}_")

            # 根据模式执行导入
            if mode == "create":
                success = self._create_table_and_import(table_name, temp_csv_path)
            elif mode == "replace":
                success = self._replace_table_data(table_name, temp_csv_path)
            elif mode == "append":
                success = self._append_table_data(table_name, temp_csv_path)
            else:
                raise ValueError(f"不支持的导入模式: {mode}")

            if success:
                import_result["success"] = True
                import_result["records_imported"] = len(df)
                self.logger.info(
                    f"数据导入成功: {table_name}, 模式: {mode}, 记录数: {len(df)}"
                )
            else:
                import_result["error"] = "数据库操作失败"
                self.logger.error(f"数据导入失败: {table_name}")

        except Exception as e:
            import_result["error"] = str(e)
            self.logger.error(f"数据导入异常: {table_name} - {str(e)}")

        finally:
            # 清理临时文件
            if temp_csv_path:
                self.cleanup_temp_file(temp_csv_path)

        return import_result

    def _create_table_and_import(self, table_name: str, csv_path: Path) -> bool:
        """创建表并导入数据"""
        try:
            # 如果表已存在，先删除
            if self.connection_manager.table_exists(table_name):
                self.connection_manager.drop_table(table_name)
                self.logger.info(f"已删除现有表: {table_name}")

            # 创建表并导入数据
            success = self.connection_manager.create_table_from_csv(
                table_name, csv_path
            )
            return success

        except Exception as e:
            self.logger.error(f"创建表并导入数据失败: {table_name} - {str(e)}")
            return False

    def _replace_table_data(self, table_name: str, csv_path: Path) -> bool:
        """替换表数据"""
        try:
            # 使用TRUNCATE + INSERT的方式替换数据
            success = self.connection_manager.insert_from_csv(
                table_name, csv_path, replace=True
            )
            return success

        except Exception as e:
            self.logger.error(f"替换表数据失败: {table_name} - {str(e)}")
            return False

    def _append_table_data(self, table_name: str, csv_path: Path) -> bool:
        """追加表数据"""
        try:
            # 检查表是否存在
            if not self.connection_manager.table_exists(table_name):
                # 表不存在，创建表
                success = self.connection_manager.create_table_from_csv(
                    table_name, csv_path
                )
            else:
                # 表存在，追加数据
                success = self.connection_manager.insert_from_csv(
                    table_name, csv_path, replace=False
                )

            return success

        except Exception as e:
            self.logger.error(f"追加表数据失败: {table_name} - {str(e)}")
            return False

    def get_table_info(self, table_name: str) -> Optional[Dict]:
        """
        获取表信息

        Args:
            table_name: 表名

        Returns:
            Optional[Dict]: 表信息
        """
        try:
            if not self.connection_manager.table_exists(table_name):
                return None

            # 获取表结构信息
            columns_info = self.connection_manager.get_table_columns(table_name)
            record_count = self.connection_manager.get_table_count(table_name)

            return {
                "table_name": table_name,
                "columns": columns_info,
                "record_count": record_count,
                "exists": True,
            }

        except Exception as e:
            self.logger.error(f"获取表信息失败: {table_name} - {str(e)}")
            return None

    def validate_import_compatibility(self, df: pd.DataFrame, table_name: str) -> Dict:
        """
        验证导入兼容性

        Args:
            df: 要导入的数据
            table_name: 目标表名

        Returns:
            Dict: 兼容性验证结果
        """
        validation_result = {"compatible": True, "issues": [], "warnings": []}

        try:
            # 获取现有表信息
            table_info = self.get_table_info(table_name)

            if table_info is None:
                # 表不存在，可以创建新表
                validation_result["warnings"].append("目标表不存在，将创建新表")
                return validation_result

            # 检查字段兼容性
            existing_columns = {
                col["name"]: col["type"] for col in table_info["columns"]
            }
            df_columns = set(df.columns)

            # 检查缺少的字段
            missing_columns = set(existing_columns.keys()) - df_columns
            if missing_columns:
                validation_result["issues"].append(
                    f"数据缺少字段: {', '.join(missing_columns)}"
                )

            # 检查额外的字段
            extra_columns = df_columns - set(existing_columns.keys())
            if extra_columns:
                validation_result["warnings"].append(
                    f"数据包含额外字段: {', '.join(extra_columns)}"
                )

            # 检查数据类型兼容性（简化检查）
            for col in df.columns:
                if col in existing_columns:
                    # 这里可以添加更详细的数据类型检查
                    pass

            validation_result["compatible"] = len(validation_result["issues"]) == 0

        except Exception as e:
            validation_result["compatible"] = False
            validation_result["issues"].append(f"兼容性验证异常: {str(e)}")

        return validation_result

    def get_import_statistics(self) -> Dict:
        """获取导入统计信息"""
        try:
            all_tables = self.connection_manager.get_all_tables()

            statistics = {"total_tables": len(all_tables), "table_details": []}

            for table in all_tables:
                table_info = self.get_table_info(table)
                if table_info:
                    statistics["table_details"].append(
                        {
                            "table_name": table,
                            "record_count": table_info["record_count"],
                            "column_count": len(table_info["columns"]),
                        }
                    )

            return statistics

        except Exception as e:
            self.logger.error(f"获取导入统计信息失败: {str(e)}")
            return {"error": str(e)}

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.connection_manager.close_connection()
