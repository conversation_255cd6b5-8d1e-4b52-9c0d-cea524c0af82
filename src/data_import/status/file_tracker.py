#!/usr/bin/env python3
"""
基于文件时间戳的简化状态追踪器

为个人开发者优化的轻量级状态管理，使用JSON文件替代复杂的数据库表。
支持跨平台文件时间戳处理和增量导入逻辑。

设计原则:
- JSON文件存储，无需数据库表
- 跨平台时间戳处理
- 增量导入支持
- 简单直观的API
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime, timezone
import hashlib

from ...utils.time_utils import get_beijing_time_now
from ...utils.config_loader import ConfigLoader


class FileStatusTracker:
    """
    基于文件时间戳的简化状态追踪器

    为EPIC3个人开发者优化设计，避免复杂的数据库状态表。
    使用JSON文件存储文件处理状态，支持增量导入和跨平台兼容。
    """

    def __init__(self, status_file: Path, tracker_name: str = "default"):
        """
        初始化文件状态追踪器

        Args:
            status_file: JSON状态文件路径
            tracker_name: 追踪器名称，用于区分不同的追踪实例
        """
        self.status_file = Path(status_file)
        self.tracker_name = tracker_name
        self.logger = logging.getLogger(f"{__name__}.{tracker_name}")

        # 确保状态文件目录存在
        self.status_file.parent.mkdir(parents=True, exist_ok=True)

        # 加载现有状态
        self.status_data = self._load_status()

        # 配置加载器（用于跨平台路径处理）
        self.config_loader = ConfigLoader()

        self.logger.info(f"FileStatusTracker初始化: {tracker_name} -> {status_file}")

    def _load_status(self) -> Dict[str, Any]:
        """
        加载JSON状态文件

        Returns:
            Dict: 状态数据字典
        """
        if not self.status_file.exists():
            # 创建初始状态结构
            initial_status = {
                "tracker_info": {
                    "name": self.tracker_name,
                    "created_at": get_beijing_time_now().isoformat(),
                    "last_updated": None,
                    "version": "1.0",
                },
                "file_status": {},  # 文件状态记录
                "statistics": {
                    "total_files_tracked": 0,
                    "files_processed": 0,
                    "last_scan_time": None,
                    "total_records_processed": 0,
                },
            }
            self._save_status(initial_status)
            return initial_status

        try:
            with open(self.status_file, "r", encoding="utf-8") as f:
                status = json.load(f)
            self.logger.debug(
                f"加载状态文件成功: {len(status.get('file_status', {}))} 个文件记录"
            )
            return status

        except (json.JSONDecodeError, IOError) as e:
            self.logger.error(f"加载状态文件失败: {e}")
            # 备份损坏的文件并创建新的
            if self.status_file.exists():
                backup_file = self.status_file.with_suffix(".backup")
                self.status_file.rename(backup_file)
                self.logger.warning(f"损坏的状态文件已备份到: {backup_file}")

            return self._load_status()  # 递归创建新文件

    def _save_status(self, status_data: Dict[str, Any] = None):
        """
        保存状态数据到JSON文件

        Args:
            status_data: 要保存的状态数据，如果为None则保存当前状态
        """
        if status_data is None:
            status_data = self.status_data

        # 更新最后修改时间
        status_data["tracker_info"]["last_updated"] = get_beijing_time_now().isoformat()

        try:
            # 原子性写入：先写临时文件，再重命名
            temp_file = self.status_file.with_suffix(".tmp")
            with open(temp_file, "w", encoding="utf-8") as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False, default=str)

            # 原子性重命名
            temp_file.replace(self.status_file)

        except Exception as e:
            self.logger.error(f"保存状态文件失败: {e}")
            raise

    def _get_file_signature(self, file_path: Path) -> Dict[str, Any]:
        """
        获取文件的唯一签名信息

        Args:
            file_path: 文件路径

        Returns:
            Dict: 文件签名信息
        """
        try:
            stat = file_path.stat()

            # 跨平台时间戳处理
            modified_time = datetime.fromtimestamp(stat.st_mtime, tz=timezone.utc)

            signature = {
                "file_path": str(file_path.resolve()),  # 绝对路径
                "file_size": stat.st_size,
                "modified_time": modified_time.isoformat(),
                "modified_timestamp": stat.st_mtime,  # 数值时间戳，便于比较
            }

            # 可选：添加文件哈希（对于小文件）
            if stat.st_size < 1024 * 1024:  # 小于1MB的文件计算MD5
                signature["md5_hash"] = self._calculate_file_hash(file_path)

            return signature

        except (OSError, IOError) as e:
            self.logger.error(f"获取文件签名失败: {file_path} - {e}")
            raise

    def _calculate_file_hash(self, file_path: Path) -> str:
        """
        计算文件MD5哈希值

        Args:
            file_path: 文件路径

        Returns:
            str: MD5哈希值
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.warning(f"计算文件哈希失败: {file_path} - {e}")
            return ""

    def is_file_updated(self, file_path: Path) -> bool:
        """
        检查文件是否有更新

        根据文件的修改时间和大小判断文件是否发生变化。

        Args:
            file_path: 要检查的文件路径

        Returns:
            bool: True表示文件有更新或为新文件，False表示无变化
        """
        if not file_path.exists():
            self.logger.warning(f"文件不存在: {file_path}")
            return False

        file_key = str(file_path.resolve())

        # 新文件
        if file_key not in self.status_data["file_status"]:
            self.logger.debug(f"发现新文件: {file_path.name}")
            return True

        # 获取当前文件签名
        try:
            current_signature = self._get_file_signature(file_path)
            stored_signature = self.status_data["file_status"][file_key]

            # 比较修改时间和文件大小
            is_updated = current_signature["modified_timestamp"] > stored_signature.get(
                "modified_timestamp", 0
            ) or current_signature["file_size"] != stored_signature.get("file_size", -1)

            # 如果有哈希值，也进行比较
            if (
                current_signature.get("md5_hash")
                and stored_signature.get("md5_hash")
                and current_signature["md5_hash"] != stored_signature["md5_hash"]
            ):
                is_updated = True

            if is_updated:
                self.logger.debug(f"文件已更新: {file_path.name}")

            return is_updated

        except Exception as e:
            self.logger.error(f"检查文件更新状态失败: {file_path} - {e}")
            # 出错时保守返回True，确保文件会被处理
            return True

    def update_status(
        self,
        file_path: Path,
        records_count: int,
        processing_result: Dict[str, Any] = None,
    ):
        """
        更新文件处理状态

        Args:
            file_path: 已处理的文件路径
            records_count: 处理的记录数量
            processing_result: 处理结果详情（可选）
        """
        if not file_path.exists():
            self.logger.warning(f"尝试更新不存在文件的状态: {file_path}")
            return

        file_key = str(file_path.resolve())

        try:
            # 获取文件当前签名
            file_signature = self._get_file_signature(file_path)

            # 构建状态记录
            status_record = {
                **file_signature,
                "processing_info": {
                    "records_count": records_count,
                    "processed_at": get_beijing_time_now().isoformat(),
                    "tracker_name": self.tracker_name,
                    "success": True,
                },
            }

            # 添加处理结果详情
            if processing_result:
                status_record["processing_info"]["result_details"] = processing_result

            # 更新状态数据
            self.status_data["file_status"][file_key] = status_record

            # 更新统计信息
            stats = self.status_data["statistics"]
            stats["total_files_tracked"] = len(self.status_data["file_status"])
            stats["files_processed"] += 1
            stats["total_records_processed"] += records_count
            stats["last_scan_time"] = get_beijing_time_now().isoformat()

            # 保存到文件
            self._save_status()

            self.logger.info(f"更新文件状态: {file_path.name} ({records_count} 条记录)")

        except Exception as e:
            self.logger.error(f"更新文件状态失败: {file_path} - {e}")
            raise

    def mark_file_error(self, file_path: Path, error_message: str):
        """
        标记文件处理错误

        Args:
            file_path: 出错的文件路径
            error_message: 错误信息
        """
        file_key = str(file_path.resolve())

        try:
            # 获取基础文件信息
            if file_path.exists():
                file_signature = self._get_file_signature(file_path)
            else:
                file_signature = {"file_path": file_key}

            # 记录错误信息
            error_record = {
                **file_signature,
                "processing_info": {
                    "records_count": 0,
                    "processed_at": get_beijing_time_now().isoformat(),
                    "tracker_name": self.tracker_name,
                    "success": False,
                    "error_message": error_message,
                },
            }

            self.status_data["file_status"][file_key] = error_record
            self._save_status()

            self.logger.warning(f"标记文件错误: {file_path.name} - {error_message}")

        except Exception as e:
            self.logger.error(f"标记文件错误失败: {file_path} - {e}")

    def get_files_to_process(self, file_list: List[Path]) -> List[Path]:
        """
        从文件列表中筛选出需要处理的文件

        Args:
            file_list: 待检查的文件列表

        Returns:
            List[Path]: 需要处理的文件列表
        """
        files_to_process = []

        for file_path in file_list:
            if self.is_file_updated(file_path):
                files_to_process.append(file_path)

        self.logger.info(
            f"筛选结果: {len(files_to_process)}/{len(file_list)} 个文件需要处理"
        )

        return files_to_process

    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理摘要信息

        Returns:
            Dict: 处理摘要
        """
        stats = self.status_data["statistics"]
        file_status = self.status_data["file_status"]

        # 统计成功和失败的文件
        successful_files = 0
        failed_files = 0

        for file_record in file_status.values():
            if file_record.get("processing_info", {}).get("success", False):
                successful_files += 1
            else:
                failed_files += 1

        return {
            "tracker_name": self.tracker_name,
            "status_file": str(self.status_file),
            "statistics": {
                "total_files_tracked": stats.get("total_files_tracked", 0),
                "successful_files": successful_files,
                "failed_files": failed_files,
                "total_records_processed": stats.get("total_records_processed", 0),
                "last_scan_time": stats.get("last_scan_time"),
            },
            "tracker_info": self.status_data["tracker_info"],
            "generated_at": get_beijing_time_now().isoformat(),
        }

    def cleanup_old_records(self, days_to_keep: int = 30):
        """
        清理旧的状态记录

        Args:
            days_to_keep: 保留最近几天的记录
        """
        from datetime import timedelta

        cutoff_time = get_beijing_time_now() - timedelta(days=days_to_keep)
        cutoff_timestamp = cutoff_time.timestamp()

        files_to_remove = []

        for file_key, file_record in self.status_data["file_status"].items():
            # 检查文件是否还存在
            file_path = Path(file_key)
            if not file_path.exists():
                # 检查记录时间
                processed_at = file_record.get("processing_info", {}).get(
                    "processed_at"
                )
                if processed_at:
                    try:
                        record_time = datetime.fromisoformat(
                            processed_at.replace("Z", "+00:00")
                        )
                        if record_time.timestamp() < cutoff_timestamp:
                            files_to_remove.append(file_key)
                    except ValueError:
                        # 时间格式错误，也删除
                        files_to_remove.append(file_key)

        # 删除旧记录
        for file_key in files_to_remove:
            del self.status_data["file_status"][file_key]

        if files_to_remove:
            self._save_status()
            self.logger.info(f"清理了 {len(files_to_remove)} 条旧状态记录")

    def reset_tracker(self):
        """重置追踪器状态"""
        self.status_data = {
            "tracker_info": {
                "name": self.tracker_name,
                "created_at": get_beijing_time_now().isoformat(),
                "last_updated": None,
                "version": "1.0",
            },
            "file_status": {},
            "statistics": {
                "total_files_tracked": 0,
                "files_processed": 0,
                "last_scan_time": None,
                "total_records_processed": 0,
            },
        }
        self._save_status()
        self.logger.info(f"重置追踪器状态: {self.tracker_name}")
