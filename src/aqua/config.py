import toml
import sys
from pathlib import Path
import re
from functools import lru_cache
import copy

class ConfigLoader:
    """
    一个健壮的配置加载器，用于处理 settings.toml。
    它能根据运行平台和指定环境，动态地加载、合并和解析配置。
    """
    
    def __init__(self, config_path: Path | str | None = None, env: str | None = None):
        self.project_root = Path(__file__).resolve().parent.parent.parent
        self.config_path = Path(config_path) if config_path else self.project_root / "config" / "settings.toml"
        
        self.platform = 'windows' if sys.platform == 'win32' else 'unix'
        
        self._raw_config = self._load_toml()
        
        self.env = env or self._raw_config.get('app', {}).get('default_environment', 'dev')
        
        if self.env not in self._raw_config.get('app', {}).get('environments', []):
            raise ValueError(f"Environment '{self.env}' not found in supported environments list.")

        self._config = self._merge_config()
        self._path_cache = {}

    def _load_toml(self) -> dict:
        """加载并解析TOML配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return toml.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        except toml.TomlDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")

    def _merge_config(self) -> dict:
        """将基础配置与特定环境的配置进行深度合并"""
        base_config = copy.deepcopy(self._raw_config)
        env_config = base_config.get(self.env, {})

        # 移除所有环境的配置，只保留基础和当前环境
        for e in base_config.get('app', {}).get('environments', []):
            base_config.pop(e, None)
            
        return self._deep_merge(base_config, env_config)

    def _deep_merge(self, base: dict, new: dict) -> dict:
        """递归地合并两个字典"""
        for k, v in new.items():
            if k in base and isinstance(base[k], dict) and isinstance(v, dict):
                base[k] = self._deep_merge(base[k], v)
            else:
                base[k] = v
        return base

    def get_config(self) -> dict:
        """返回合并后的完整配置字典"""
        return self._config

    def get_value(self, key: str, default=None):
        """通过点分路径获取配置值，例如 'database.pool.size'"""
        keys = key.split('.')
        value = self._config
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def get_path(self, key: str) -> Path:
        """
        获取一个配置中的路径，并解析平台和占位符。
        路径会被缓存以提高性能。
        """
        if key in self._path_cache:
            return self._path_cache[key]

        # 1. 获取平台特定的路径配置
        platform_config = self.get_value(f'platform.{self.platform}', {})
        
        # 2. 获取路径字符串
        # 优先从平台配置中寻找，再从根配置中寻找
        path_str = self._get_path_str_from_dict(platform_config, key)
        if path_str is None:
            path_str = self.get_value(key)

        if not isinstance(path_str, str):
            raise TypeError(f"Path key '{key}' did not resolve to a string. It resolved to {type(path_str)}")

        # 3. 解析占位符 {placeholder}
        path_str = self._resolve_placeholders(path_str)

        # 4. 转换为Path对象并展开用户目录 (~)
        final_path = Path(path_str).expanduser()
        
        # 5. 缓存结果
        self._path_cache[key] = final_path
        return final_path

    def _get_path_str_from_dict(self, config_dict: dict, key: str):
        """辅助函数：从字典中通过点分路径获取值"""
        keys = key.split('.')
        value = config_dict
        try:
            for k in keys:
                value = value[k]
            return value if isinstance(value, str) else None
        except (KeyError, TypeError):
            return None

    def _resolve_placeholders(self, path_str: str) -> str:
        """解析路径字符串中的 {key} 占位符"""
        placeholder_pattern = re.compile(r'\{([^}]+)\}')
        
        # 使用一个循环来处理嵌套占位符
        for _ in range(5): # 限制递归深度防止无限循环
            placeholders = placeholder_pattern.findall(path_str)
            if not placeholders:
                break
            
            for placeholder_key in placeholders:
                # 占位符本身可能是一个需要平台解析的路径
                resolved_path_segment = str(self.get_path(f'paths.{placeholder_key}'))
                path_str = path_str.replace(f'{{{placeholder_key}}}', resolved_path_segment)
        
        return path_str