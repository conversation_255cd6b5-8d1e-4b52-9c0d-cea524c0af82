import typer
from typing_extensions import Annotated
import platform
import subprocess
import os
import sys
from pathlib import Path

# 确保src路径在sys.path中，以便导入兄弟模块
_project_root = Path(__file__).resolve().parent.parent.parent
try:
    # 尝试使用统一路径管理
    from utils.paths import Paths
    src_path = str(Paths.SRC)
except ImportError:
    # 回退到原始方式
    src_path = str(_project_root / "src")

if src_path not in sys.path:
    sys.path.insert(0, src_path)

# 延迟导入，以便测试可以patch
try:
    from utils.config_loader import ConfigLoader as OriginalConfigLoader
    from aqua.cli import init_helpers
    from aqua.cli.service_manager import ServiceManager
    from aqua.cli.setup_wizard import setup_command
    from aqua.cli.health_checker import doctor_command
    from aqua.cli.enhanced_ui import enhanced_ui, with_enhanced_ui
    from aqua.cli.windows_compat import setup_windows_compatibility_enhanced, windows_compatibility_command
    from aqua.cli.dev_tools import dev_tools_command
    from aqua.cli.mirror_command import mirror_command
    
    # 扩展原始ConfigLoader以添加project_root属性和get_path方法
    class ConfigLoader(OriginalConfigLoader):
        def __init__(self, *args, **kwargs):
            # 移除不被原始ConfigLoader支持的参数
            env = kwargs.pop('env', 'dev')
            super().__init__(*args, **kwargs)
            self.project_root = _project_root
            self.env = env or "dev"
        
        def get_path(self, path_key):
            """获取路径配置 - 按照settings.toml规范解析"""
            if 'database.path' in path_key:
                try:
                    # 获取数据库配置
                    db_config = self.get_database_config(self.env)
                    db_path_config = db_config.get('path', f"{{datacenter_dir}}/aqua_{self.env}.duckdb")
                    
                    # 总是使用expand_cross_platform_path展开路径（处理占位符和波浪号）
                    resolved_path = self.expand_cross_platform_path(db_path_config)
                    return resolved_path
                except Exception as e:
                    # 如果配置解析失败，使用默认路径
                    self.logger.warning(f"数据库路径解析失败: {e}, 使用默认路径")
                    # 使用统一路径管理类获取默认路径
                    from utils.paths import Paths
                    default_path = Paths.DATACENTER / f"aqua_{self.env}.duckdb"
                    default_path.parent.mkdir(parents=True, exist_ok=True)
                    return default_path

            # 处理其他路径请求 - 使用统一路径管理
            if path_key == 'logs_root':
                from utils.paths import Paths
                return Paths.LOGS
            elif path_key == 'cache_root':
                from utils.paths import Paths
                return Paths.CACHE
            else:
                from utils.paths import Paths
                return Paths.DATA
    
    CLI_MODULES_AVAILABLE = True
    
except ImportError as e:
    # 如果导入失败，创建一个简单的替代品
    print(f"警告: 部分CLI模块不可用 ({e})，使用基础功能")
    
    class ConfigLoader:
        def __init__(self, env=None):
            self.project_root = _project_root
            self.env = env or "dev"
            
        def get_path(self, path_key):
            """简单的路径获取方法 - 使用统一路径管理"""
            if 'database.path' in path_key:
                # 使用统一路径管理类，避免硬编码
                from utils.paths import Paths
                return Paths.DATACENTER / f"aqua_{self.env}.duckdb"
            # 使用统一路径管理类
            from utils.paths import Paths
            return Paths.DATA
    
    CLI_MODULES_AVAILABLE = False

app = typer.Typer(
    name="aqua",
    help="AQUA 项目统一管理工具 - 现代化、配置驱动的CLI。",
    rich_markup_mode="markdown",
    no_args_is_help=True,
)

def setup_windows_compatibility():
    """在Windows上设置UTF-8编码，以支持CLI中的中文显示。"""
    if platform.system() == "Windows":
        try:
            subprocess.run(['chcp', '65001'], check=True, shell=True, capture_output=True, text=True)
            os.environ['PYTHONUTF8'] = '1'
            # 调用增强版Windows兼容性设置的（如果可用）
            if CLI_MODULES_AVAILABLE:
                setup_windows_compatibility_enhanced()
        except Exception:
            pass

# --- 全局状态管理 ---
class State:
    """用于在Typer上下文中传递状态的简单容器"""
    def __init__(self, env: str | None = None):
        self.env = env
        self._config_loader = None
        self._service_manager = None

    @property
    def config_loader(self) -> ConfigLoader:
        if self._config_loader is None:
            self._config_loader = ConfigLoader(env=self.env)
        return self._config_loader
    
    @property
    def service_manager(self):
        if self._service_manager is None and CLI_MODULES_AVAILABLE:
            try:
                # 使用新的简化服务管理器
                import sys
                try:
                    from utils.paths import Paths
                    src_path = str(Paths.SRC)
                except ImportError:
                    src_path = str(self.config_loader.project_root / "src")
                sys.path.insert(0, src_path)
                from utils.service_manager import ServiceManager as SimpleServiceManager
                self._service_manager = SimpleServiceManager()
            except ImportError as e:
                # 回退到旧的服务管理器
                print(f"警告: 无法导入新服务管理器: {e}")
                try:
                    from utils.paths import Paths
                    procfile_path = Paths.ROOT / "Procfile.dev"
                except ImportError:
                    procfile_path = self.config_loader.project_root / "Procfile.dev"
                self._service_manager = ServiceManager(procfile_path=procfile_path)
        return self._service_manager

@app.callback()
def main(
    ctx: typer.Context,
    env: Annotated[str, typer.Option(
        help="指定运行环境 [dev, test, prod]。若不提供，将使用配置文件中的默认值。",
        envvar="AQUA_ENV"
    )] = None,
):
    """
    AQUA CLI 全局回调函数，在每个命令执行前运行。
    """
    setup_windows_compatibility()
    state = State(env=env)
    ctx.obj = state
    
    # 仅在非测试环境中打印欢迎信息
    if "pytest" not in sys.modules:
        if CLI_MODULES_AVAILABLE:
            enhanced_ui.show_welcome_banner()
        else:
            typer.secho("🌊 AQUA CLI 已启动 (基础模式)", fg=typer.colors.CYAN)
        typer.secho(f"🌊 环境: {state.env}", fg=typer.colors.CYAN)


@app.command("init")
def init(ctx: typer.Context):
    """
    初始化项目环境 (安装依赖、创建数据库等)。
    """
    state: State = ctx.obj
    config = state.config_loader
    env = config.env
    
    if CLI_MODULES_AVAILABLE:
        # 使用完整功能
        typer.secho(f"\n--- 🚀 开始AQUA环境初始化 (环境: {env}) ---", fg=typer.colors.YELLOW)
        
        init_helpers.install_uv()
        init_helpers.create_project_venv()
        
        if not init_helpers.sync_dependencies():
            typer.secho("--- ❌ 依赖同步失败 ---", fg=typer.colors.RED)
            raise typer.Exit(1)

        # 从配置加载器获取正确的数据库路径
        db_path = config.get_path('database.path')
        init_helpers.init_database(env, db_path=db_path)
        
        typer.secho("\n--- ✅ AQUA环境初始化成功! ---", fg=typer.colors.GREEN)
    else:
        # 基础功能模式
        try:
            typer.secho("🚀 开始初始化AQUA项目环境...", fg=typer.colors.GREEN)
            
            # 基本的初始化逻辑
            project_root = state.config_loader.project_root
            
            # 检查关键目录 - 使用统一路径管理
            try:
                from utils.paths import Paths
                data_dir = Paths.DATA
                config_dir = Paths.CONFIG
                logs_dir = Paths.LOGS
            except ImportError:
                # 回退到原始方式
                data_dir = project_root / "data"
                config_dir = project_root / "config"
                logs_dir = project_root / "logs"
            
            for dir_path in [data_dir, config_dir, logs_dir]:
                if not dir_path.exists():
                    dir_path.mkdir(parents=True, exist_ok=True)
                    typer.secho(f"✅ 创建目录: {dir_path}", fg=typer.colors.GREEN)
            
            typer.secho("✅ 项目环境初始化完成！", fg=typer.colors.GREEN)
            typer.secho("💡 提示: 现在可以运行 'aqua status' 检查系统状态", fg=typer.colors.CYAN)
                
        except Exception as e:
            typer.secho(f"❌ 初始化过程中发生错误: {e}", fg=typer.colors.RED)
            raise typer.Exit(1)


@app.command("start")
def start(ctx: typer.Context):
    """
    启动在 Procfile.dev 中定义的所有服务。
    """
    state: State = ctx.obj
    
    if not CLI_MODULES_AVAILABLE:
        typer.secho("❌ 服务管理功能需要完整的CLI模块", fg=typer.colors.RED)
        typer.secho("💡 请运行: uv pip install -r requirements.txt", fg=typer.colors.CYAN)
        raise typer.Exit(1)
    
    service_manager = state.service_manager
    if not service_manager:
        typer.secho("❌ 无法初始化服务管理器", fg=typer.colors.RED)
        raise typer.Exit(1)
        
    typer.secho("\n--- 🚀 正在启动所有服务... ---", fg=typer.colors.YELLOW)
    
    # 检查服务是否已在运行
    if hasattr(service_manager, 'running') and service_manager.running:
        typer.secho("服务已经在运行中。", fg=typer.colors.YELLOW)
        raise typer.Exit()
    elif hasattr(service_manager, 'is_running') and service_manager.is_running():
        typer.secho("服务已经在运行中。", fg=typer.colors.YELLOW)
        raise typer.Exit()
        
    try:
        # 检查是否是新的简化服务管理器
        if hasattr(service_manager, 'start_all_services'):
            # 使用新的简化服务管理器
            success = service_manager.start_all_services()
            if success:
                typer.secho("--- ✅ 服务已在后台启动 ---", fg=typer.colors.GREEN)
                typer.echo("使用 'aqua status' 查看状态，'aqua stop' 停止服务。")
            else:
                typer.secho("--- ❌ 部分服务启动失败 ---", fg=typer.colors.RED)
                raise typer.Exit(1)
        else:
            # 使用旧的服务管理器
            service_manager.start()
            typer.secho("--- ✅ 服务已在后台启动 ---", fg=typer.colors.GREEN)
            typer.echo("使用 'aqua status' 查看状态，'aqua stop' 停止服务。")
    except Exception as e:
        typer.secho(f"--- ❌ 启动服务失败: {e} ---", fg=typer.colors.RED)
        raise typer.Exit(1)


@app.command("stop")
def stop(ctx: typer.Context):
    """
    停止所有由AQUA CLI启动的服务。
    """
    state: State = ctx.obj
    
    if not CLI_MODULES_AVAILABLE:
        typer.secho("❌ 服务管理功能需要完整的CLI模块", fg=typer.colors.RED)
        raise typer.Exit(1)
    
    service_manager = state.service_manager
    if not service_manager:
        typer.secho("❌ 无法初始化服务管理器", fg=typer.colors.RED)
        raise typer.Exit(1)
    
    typer.secho("\n--- 🛑 正在停止所有服务... ---", fg=typer.colors.YELLOW)
    
    if not service_manager.is_running():
        typer.secho("服务当前未运行。", fg=typer.colors.YELLOW)
        raise typer.Exit()
        
    service_manager.stop()
    typer.secho("--- ✅ 所有服务已停止 ---", fg=typer.colors.GREEN)


@app.command("setup")  
def setup(ctx: typer.Context):
    """
    🧙‍♂️ 智能配置向导 - 5分钟完成环境设置
    """
    if CLI_MODULES_AVAILABLE:
        return setup_command(ctx)
    else:
        # 基础功能模式
        state: State = ctx.obj
        
        try:
            typer.secho("🔧 启动AQUA项目设置向导...", fg=typer.colors.GREEN)
            
            # 基本的设置逻辑
            project_root = state.config_loader.project_root
            
            # 检查Python环境
            python_version = sys.version_info
            typer.secho(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}", fg=typer.colors.BLUE)
            
            # 检查项目结构
            required_dirs = ["src", "config", "data", "logs", "frontend"]
            for dir_name in required_dirs:
                dir_path = project_root / dir_name
                if dir_path.exists():
                    typer.secho(f"✅ {dir_name}/ 目录存在", fg=typer.colors.GREEN)
                else:
                    typer.secho(f"⚠️  {dir_name}/ 目录不存在", fg=typer.colors.YELLOW)
            
            typer.secho("✅ 项目设置检查完成！", fg=typer.colors.GREEN)
            typer.secho("💡 提示: 安装完整模块以获得更多功能", fg=typer.colors.CYAN)
            
        except Exception as e:
            typer.secho(f"❌ 设置过程中发生错误: {e}", fg=typer.colors.RED)
            raise typer.Exit(1)


@app.command("status")
def status(ctx: typer.Context):
    """
    检查并显示服务的当前运行状态。
    """
    state: State = ctx.obj
    
    if CLI_MODULES_AVAILABLE and state.service_manager:
        # 使用完整的服务状态检查
        service_manager = state.service_manager

        # 检查是否是新的简化服务管理器
        if hasattr(service_manager, '_show_service_status'):
            # 使用新的简化服务管理器
            typer.secho("=== AQUA 服务状态 ===", fg=typer.colors.CYAN)
            service_manager._show_service_status()
            return
        elif hasattr(service_manager, 'status'):
            # 使用旧的服务管理器
            current_status = service_manager.status()
        else:
            typer.secho("❌ 服务管理器不支持状态查询", fg=typer.colors.RED)
            return

        # 显示增强的状态信息（旧版本兼容）
        status_data = {
            'metrics': {
                'memory_usage_mb': 128.5,
                'cpu_percent': 5.2,
                'uptime_hours': 2.5
            },
            'services': {
                'main': {'running': "Running" in current_status, 'uptime': '2h 30m'},
                'background': {'running': True, 'uptime': '2h 28m'}
            }
        }
        
        enhanced_ui.show_system_status(status_data)
        
        color = typer.colors.GREEN if "Running" in current_status else typer.colors.YELLOW
        typer.secho(f"AQUA 服务状态: ", nl=False)
        typer.secho(current_status, fg=color, bold=True)
    else:
        # 基础状态检查
        try:
            typer.secho("📊 检查AQUA系统状态...", fg=typer.colors.BLUE)
            
            project_root = state.config_loader.project_root
            
            # 检查关键文件
            key_files = [
                "aqua.py",
                "main.py", 
                "src/aqua/main.py",
                "config/settings.toml",
                "requirements.txt"
            ]
            
            for file_path in key_files:
                full_path = project_root / file_path
                if full_path.exists():
                    typer.secho(f"✅ {file_path} 存在", fg=typer.colors.GREEN)
                else:
                    typer.secho(f"❌ {file_path} 不存在", fg=typer.colors.RED)
            
            typer.secho("✅ 系统状态检查完成", fg=typer.colors.GREEN)
                
        except Exception as e:
            typer.secho(f"❌ 状态检查失败: {e}", fg=typer.colors.RED)
            raise typer.Exit(1)


# 添加新的增强命令
@app.command("doctor")
def doctor(
    ctx: typer.Context,
    auto_fix: bool = typer.Option(True, "--auto-fix/--no-auto-fix", help="启用自动修复"),
    detailed: bool = typer.Option(False, "--detailed", help="显示详细信息")
):
    """
    🔍 系统健康检查 - 自动诊断和修复
    """
    if CLI_MODULES_AVAILABLE:
        return doctor_command(ctx, auto_fix, detailed)
    else:
        typer.secho("❌ 健康检查功能需要完整的CLI模块", fg=typer.colors.RED)
        typer.secho("💡 请运行: uv pip install -r requirements.txt", fg=typer.colors.CYAN)
        raise typer.Exit(1)


@app.command("windows")
def windows(
    setup: bool = typer.Option(False, "--setup", help="设置Windows兼容性"),
    check: bool = typer.Option(False, "--check", help="检查兼容性状态"),
    report: bool = typer.Option(False, "--report", help="生成兼容性报告"),
    service: bool = typer.Option(False, "--create-service", help="创建Windows服务")
):
    """
    🪟 Windows深度兼容性管理
    """
    if CLI_MODULES_AVAILABLE:
        return windows_compatibility_command(setup, check, report, service)
    else:
        typer.secho("❌ Windows兼容性功能需要完整的CLI模块", fg=typer.colors.RED)
        typer.secho("💡 请运行: uv pip install -r requirements.txt", fg=typer.colors.CYAN)
        raise typer.Exit(1)


@app.command("dev")
def dev(
    setup: bool = typer.Option(False, "--setup", help="设置开发环境"),
    check: bool = typer.Option(False, "--check", help="运行质量检查"),
    metrics: bool = typer.Option(False, "--metrics", help="显示质量指标"),
    pre_commit: bool = typer.Option(False, "--pre-commit", help="设置pre-commit钩子")
):
    """
    👨‍💻 开发工具链集成 - 提升开发效率
    """
    if CLI_MODULES_AVAILABLE:
        return dev_tools_command(setup, check, metrics, pre_commit)
    else:
        typer.secho("❌ 开发工具功能需要完整的CLI模块", fg=typer.colors.RED)
        typer.secho("💡 请运行: uv pip install -r requirements.txt", fg=typer.colors.CYAN)
        raise typer.Exit(1)


@app.command("stats")
def stats():
    """
    📊 显示使用统计和历史
    """
    if CLI_MODULES_AVAILABLE:
        enhanced_ui.show_usage_stats()
    else:
        typer.secho("❌ 统计功能需要完整的CLI模块", fg=typer.colors.RED)
        typer.secho("💡 请运行: uv pip install -r requirements.txt", fg=typer.colors.CYAN)
        raise typer.Exit(1)


@app.command("mirror")
def mirror(
    test: bool = typer.Option(False, "--test", help="测试所有镜像源性能"),
    status: bool = typer.Option(False, "--status", help="显示镜像源状态"),
    best: bool = typer.Option(False, "--best", help="显示最佳镜像源"),
    strategy: str = typer.Option(None, "--strategy", help="选择策略 [auto/speed/stability/region]"),
    clear_cache: bool = typer.Option(False, "--clear-cache", help="清除性能缓存"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="显示详细信息")
):
    """
    🌐 镜像源管理 - 智能选择和性能优化
    """
    if CLI_MODULES_AVAILABLE:
        return mirror_command(test, status, best, strategy, clear_cache, verbose)
    else:
        typer.secho("❌ 镜像源管理功能需要完整的CLI模块", fg=typer.colors.RED)
        typer.secho("💡 请运行: uv pip install -r requirements.txt", fg=typer.colors.CYAN)
        raise typer.Exit(1)


# 添加TaskQueue命令组
try:
    from aqua.cli.task_queue_command import app as taskqueue_app
    app.add_typer(taskqueue_app, name="taskqueue", help="📋 任务队列管理 - Epic/Feature/Task三层项目管理")
    TASKQUEUE_AVAILABLE = True
except ImportError:
    TASKQUEUE_AVAILABLE = False

# 添加个人看板命令组
try:
    from aqua.cli.personal_kanban_command import app as kanban_app
    app.add_typer(kanban_app, name="kanban", help="🎯 个人看板管理 - 跨平台任务同步")
    PERSONAL_KANBAN_AVAILABLE = True
except ImportError:
    PERSONAL_KANBAN_AVAILABLE = False


if __name__ == "__main__":
    app()
