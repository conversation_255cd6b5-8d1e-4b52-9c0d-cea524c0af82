#!/usr/bin/env python3
"""
AQUA TaskQueue CLI命令

提供任务队列管理的命令行接口
"""

import typer
import json
from typing import Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# 导入AQUA核心模块
try:
    from ...core.task_queue_engine import TaskQueueEngine, TaskPriority, TaskStatus
    from ...utils.config_loader import ConfigLoader
except ImportError:
    # 兼容不同的导入路径
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent.parent))
    from core.task_queue_engine import TaskQueueEngine, TaskPriority, TaskStatus
    from utils.config_loader import ConfigLoader

console = Console()
app = typer.Typer(name="taskqueue", help="AQUA任务队列管理")


def get_task_engine() -> TaskQueueEngine:
    """获取配置好的TaskQueueEngine实例"""
    try:
        # 简化配置，避免复杂依赖
        task_queue_config = {
            "max_queue_size": 1000,
            "worker_count": 4,
            "max_retry_count": 3,
            "task_timeout": 30,
            "enable_monitoring": True
        }

        return TaskQueueEngine(task_queue_config)
    except Exception as e:
        console.print(f"[red]初始化TaskQueueEngine失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("status")
def status_command():
    """显示任务队列状态"""
    try:
        engine = get_task_engine()
        stats = engine.get_project_statistics()
        
        # 创建状态表格
        table = Table(title="AQUA任务队列状态")
        table.add_column("指标", style="cyan")
        table.add_column("数值", style="green")
        
        table.add_row("总任务数", str(stats["total_tasks"]))
        table.add_row("Epic数量", str(stats["epic_count"]))
        table.add_row("Feature数量", str(stats["feature_count"]))
        table.add_row("Task数量", str(stats["task_count"]))
        table.add_row("待处理", str(stats["pending_count"]))
        table.add_row("运行中", str(stats["running_count"]))
        table.add_row("已完成", str(stats["completed_count"]))
        table.add_row("失败", str(stats["failed_count"]))
        table.add_row("已取消", str(stats["cancelled_count"]))
        table.add_row("完成率", f"{stats['completion_rate']:.2%}")
        table.add_row("失败率", f"{stats['failure_rate']:.2%}")
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]获取状态失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("submit-epic")
def submit_epic_command(
    title: str = typer.Argument(..., help="Epic标题"),
    description: Optional[str] = typer.Option(None, "--desc", "-d", help="Epic描述"),
    priority: str = typer.Option("high", "--priority", "-p", help="优先级 (critical/high/medium/low)"),
    estimated_hours: Optional[int] = typer.Option(None, "--hours", "-h", help="预估工时")
):
    """提交Epic任务"""
    try:
        engine = get_task_engine()
        
        # 构建Epic数据
        epic_data = {"title": title}
        if description:
            epic_data["description"] = description
        if estimated_hours:
            epic_data["estimated_hours"] = estimated_hours
        
        # 转换优先级
        priority_map = {
            "critical": TaskPriority.CRITICAL,
            "high": TaskPriority.HIGH,
            "medium": TaskPriority.MEDIUM,
            "low": TaskPriority.LOW
        }
        task_priority = priority_map.get(priority.lower(), TaskPriority.HIGH)
        
        # 提交任务
        task_id = engine.submit_epic_task(epic_data, task_priority)
        
        console.print(Panel(
            f"[green]Epic任务提交成功![/green]\n"
            f"任务ID: {task_id}\n"
            f"标题: {title}\n"
            f"优先级: {priority}",
            title="任务提交结果"
        ))
        
    except Exception as e:
        console.print(f"[red]提交Epic任务失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("submit-feature")
def submit_feature_command(
    title: str = typer.Argument(..., help="Feature标题"),
    epic_id: Optional[str] = typer.Option(None, "--epic", "-e", help="所属Epic ID"),
    description: Optional[str] = typer.Option(None, "--desc", "-d", help="Feature描述"),
    estimated_hours: Optional[int] = typer.Option(None, "--hours", "-h", help="预估工时")
):
    """提交Feature任务"""
    try:
        engine = get_task_engine()
        
        # 构建Feature数据
        feature_data = {"title": title}
        if epic_id:
            feature_data["epic_id"] = epic_id
        if description:
            feature_data["description"] = description
        if estimated_hours:
            feature_data["estimated_hours"] = estimated_hours
        
        # 提交任务
        task_id = engine.submit_feature_task(feature_data)
        
        console.print(Panel(
            f"[green]Feature任务提交成功![/green]\n"
            f"任务ID: {task_id}\n"
            f"标题: {title}\n"
            f"Epic ID: {epic_id or 'N/A'}",
            title="任务提交结果"
        ))
        
    except Exception as e:
        console.print(f"[red]提交Feature任务失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("submit-task")
def submit_task_command(
    title: str = typer.Argument(..., help="Task标题"),
    feature_id: Optional[str] = typer.Option(None, "--feature", "-f", help="所属Feature ID"),
    description: Optional[str] = typer.Option(None, "--desc", "-d", help="Task描述"),
    assignee: Optional[str] = typer.Option(None, "--assignee", "-a", help="指派给"),
    estimated_hours: Optional[int] = typer.Option(None, "--hours", "-h", help="预估工时")
):
    """提交Task任务"""
    try:
        engine = get_task_engine()
        
        # 构建Task数据
        task_data = {"title": title}
        if feature_id:
            task_data["feature_id"] = feature_id
        if description:
            task_data["description"] = description
        if assignee:
            task_data["assignee"] = assignee
        if estimated_hours:
            task_data["estimated_hours"] = estimated_hours
        
        # 提交任务
        task_id = engine.submit_task_task(task_data)
        
        console.print(Panel(
            f"[green]Task任务提交成功![/green]\n"
            f"任务ID: {task_id}\n"
            f"标题: {title}\n"
            f"Feature ID: {feature_id or 'N/A'}\n"
            f"指派给: {assignee or 'N/A'}",
            title="任务提交结果"
        ))
        
    except Exception as e:
        console.print(f"[red]提交Task任务失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("cancel")
def cancel_command(
    task_id: str = typer.Argument(..., help="要取消的任务ID")
):
    """取消任务"""
    try:
        engine = get_task_engine()
        
        if engine.cancel_task(task_id):
            console.print(f"[green]任务 {task_id} 已成功取消[/green]")
        else:
            console.print(f"[yellow]无法取消任务 {task_id}（可能已完成或不存在）[/yellow]")
        
    except Exception as e:
        console.print(f"[red]取消任务失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("start")
def start_command():
    """启动任务队列工作线程"""
    try:
        engine = get_task_engine()
        engine.start_workers()
        
        console.print("[green]任务队列工作线程已启动[/green]")
        console.print("使用 Ctrl+C 停止...")
        
        # 保持运行直到用户中断
        try:
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            console.print("\n[yellow]正在停止任务队列...[/yellow]")
            engine.shutdown()
            console.print("[green]任务队列已停止[/green]")
        
    except Exception as e:
        console.print(f"[red]启动任务队列失败: {e}[/red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
