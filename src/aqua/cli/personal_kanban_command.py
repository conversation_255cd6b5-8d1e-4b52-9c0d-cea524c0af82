#!/usr/bin/env python3
"""
AQUA个人看板CLI命令

集成个人跨平台任务管理到AQUA项目中
"""

import typer
import json
from typing import Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint
from datetime import datetime
import sys
from pathlib import Path

# 导入个人看板核心模块
def import_kanban_modules():
    """动态导入个人看板模块"""
    try:
        # 方法1: 尝试从AQUA项目结构导入
        from ...personal_kanban.core.personal_kanban_manager import (
            PersonalKanbanManager, TaskType, TaskPriority, TaskStatus
        )
        from ...personal_kanban.sync.gitee_sync_engine import GiteeSyncEngine, GiteeConfig
        return PersonalKanbanManager, TaskType, TaskPriority, TaskStatus, GiteeSyncEngine, GiteeConfig
    except ImportError:
        pass

    try:
        # 方法2: 从外部kanban项目导入
        # 计算kanban项目路径: D:\AQUA\AQUA -> D:\AQUA\kanban-local-with-gitee\src
        current_file = Path(__file__)
        aqua_root = current_file.parent.parent.parent.parent  # D:\AQUA\AQUA
        kanban_src = aqua_root.parent / "kanban-local-with-gitee" / "src"  # D:\AQUA\kanban-local-with-gitee\src

        if kanban_src.exists():
            sys.path.insert(0, str(kanban_src))
            from core.personal_kanban_manager import (
                PersonalKanbanManager, TaskType, TaskPriority, TaskStatus
            )
            from sync.gitee_sync_engine import GiteeSyncEngine, GiteeConfig
            return PersonalKanbanManager, TaskType, TaskPriority, TaskStatus, GiteeSyncEngine, GiteeConfig
    except ImportError:
        pass

    # 如果都失败了，抛出错误
    raise ImportError("无法导入个人看板模块，请确保kanban-local-with-gitee项目在正确位置")

# 执行导入
try:
    PersonalKanbanManager, TaskType, TaskPriority, TaskStatus, GiteeSyncEngine, GiteeConfig = import_kanban_modules()
except ImportError as e:
    print(f"❌ {e}")
    print("💡 请确保kanban-local-with-gitee项目位于D:\\AQUA\\kanban-local-with-gitee")
    sys.exit(1)

console = Console()
app = typer.Typer(name="kanban", help="🎯 个人看板管理 - 跨平台任务同步")


def get_aqua_kanban_manager(project_name: str = "aqua") -> PersonalKanbanManager:
    """获取AQUA项目专用的看板管理器"""
    # AQUA项目根目录: D:\AQUA\AQUA
    # 当前文件路径: D:\AQUA\AQUA\src\aqua\cli\personal_kanban_command.py
    # 需要向上4级到达根目录
    aqua_root = Path(__file__).parent.parent.parent.parent

    # 验证路径正确性
    if not (aqua_root / "aqua.py").exists():
        # 如果aqua.py不存在，说明路径计算错误，使用当前工作目录
        aqua_root = Path.cwd()
        if not (aqua_root / "aqua.py").exists():
            raise RuntimeError(f"无法找到AQUA项目根目录，当前路径: {aqua_root}")

    # 数据存储在项目的kanban目录下: D:\AQUA\AQUA\kanban\
    return PersonalKanbanManager(
        project_name=project_name,
        project_root=str(aqua_root)
    )


@app.command("init")
def init_kanban(
    project_name: str = typer.Option("aqua", "--project", "-p", help="项目名称")
):
    """初始化个人看板项目"""
    try:
        with get_aqua_kanban_manager(project_name) as manager:
            console.print(Panel(
                f"[green]✅ 个人看板项目初始化成功![/green]\n\n"
                f"📁 项目名称: {project_name}\n"
                f"🗄️  数据库: {manager.db_path}\n"
                f"🖥️  设备ID: {manager.device_id}\n"
                f"📊 任务数量: {manager.get_task_count()}",
                title="看板初始化"
            ))
    except Exception as e:
        console.print(f"[red]❌ 初始化失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("create")
def create_task(
    title: str = typer.Argument(..., help="任务标题"),
    description: str = typer.Option("", "--desc", "-d", help="任务描述"),
    task_type: str = typer.Option("task", "--type", "-t", help="任务类型 (epic/feature/task)"),
    priority: str = typer.Option("medium", "--priority", "-p", help="优先级 (low/medium/high/urgent)"),
    parent_id: Optional[str] = typer.Option(None, "--parent", help="父任务ID"),
    project_name: str = typer.Option("aqua", "--project", help="项目名称")
):
    """创建新任务"""
    try:
        with get_aqua_kanban_manager(project_name) as manager:
            # 转换枚举类型
            task_type_enum = TaskType(task_type.lower())
            priority_enum = TaskPriority[priority.upper()]
            
            # 创建任务
            task = manager.create_task(
                title=title,
                description=description,
                task_type=task_type_enum,
                priority=priority_enum,
                parent_id=parent_id
            )
            
            console.print(Panel(
                f"[green]✅ 任务创建成功![/green]\n\n"
                f"🆔 ID: {task.id[:8]}...\n"
                f"📝 标题: {task.title}\n"
                f"🏷️  类型: {task.task_type.value}\n"
                f"⭐ 优先级: {task.priority.name}\n"
                f"📊 状态: {task.status.value}",
                title="任务创建结果"
            ))
            
    except Exception as e:
        console.print(f"[red]❌ 创建任务失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("list")
def list_tasks(
    status: Optional[str] = typer.Option(None, "--status", "-s", help="按状态筛选"),
    task_type: Optional[str] = typer.Option(None, "--type", "-t", help="按类型筛选"),
    project_name: str = typer.Option("aqua", "--project", help="项目名称")
):
    """列出任务"""
    try:
        with get_aqua_kanban_manager(project_name) as manager:
            # 转换筛选条件
            status_enum = TaskStatus(status) if status else None
            type_enum = TaskType(task_type) if task_type else None
            
            tasks = manager.get_tasks(status=status_enum, task_type=type_enum)
            
            if not tasks:
                console.print("[yellow]📭 没有找到匹配的任务[/yellow]")
                return
            
            # 创建任务表格
            table = Table(title=f"📋 {project_name.upper()} 项目任务列表 ({len(tasks)} 个任务)")
            table.add_column("ID", style="cyan", width=10)
            table.add_column("标题", style="white", min_width=25)
            table.add_column("类型", style="blue", width=8)
            table.add_column("状态", style="green", width=12)
            table.add_column("优先级", style="red", width=8)
            
            for task in tasks:
                status_colors = {
                    TaskStatus.TODO: "white",
                    TaskStatus.IN_PROGRESS: "blue",
                    TaskStatus.DONE: "green",
                    TaskStatus.BLOCKED: "red",
                    TaskStatus.CANCELLED: "dim"
                }
                
                status_color = status_colors.get(task.status, "white")
                
                table.add_row(
                    task.id[:8] + "...",
                    task.title[:35] + "..." if len(task.title) > 35 else task.title,
                    task.task_type.value,
                    f"[{status_color}]{task.status.value}[/{status_color}]",
                    task.priority.name
                )
            
            console.print(table)
            
    except Exception as e:
        console.print(f"[red]❌ 列出任务失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("board")
def show_board(
    project_name: str = typer.Option("aqua", "--project", help="项目名称")
):
    """显示看板视图"""
    try:
        with get_aqua_kanban_manager(project_name) as manager:
            board = manager.get_kanban_board()
            
            console.print(f"\n🎯 [bold blue]{project_name.upper()} 项目看板[/bold blue]\n")
            
            status_display = {
                'todo': '📝 待办',
                'in_progress': '🔄 进行中',
                'done': '✅ 已完成',
                'blocked': '🚫 阻塞',
                'cancelled': '❌ 已取消'
            }
            
            for status_key, tasks in board.items():
                if not tasks:
                    continue
                    
                panel_title = f"{status_display.get(status_key, status_key)} ({len(tasks)})"
                
                task_list = []
                for task in tasks[:5]:  # 只显示前5个任务
                    priority_icons = {
                        TaskPriority.LOW: "🟢",
                        TaskPriority.MEDIUM: "🟡", 
                        TaskPriority.HIGH: "🟠",
                        TaskPriority.URGENT: "🔴"
                    }
                    
                    icon = priority_icons.get(task.priority, "⚪")
                    task_list.append(f"{icon} {task.title[:40]}")
                
                if len(tasks) > 5:
                    task_list.append(f"... 还有 {len(tasks) - 5} 个任务")
                
                console.print(Panel(
                    "\n".join(task_list) if task_list else "暂无任务",
                    title=panel_title,
                    border_style="blue"
                ))
        
    except Exception as e:
        console.print(f"[red]❌ 显示看板失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("stats")
def show_statistics(
    project_name: str = typer.Option("aqua", "--project", help="项目名称")
):
    """显示项目统计"""
    try:
        with get_aqua_kanban_manager(project_name) as manager:
            stats = manager.get_statistics()
            
            # 创建统计表格
            table = Table(title=f"📊 {project_name.upper()} 项目统计")
            table.add_column("指标", style="cyan")
            table.add_column("数值", style="green")
            
            table.add_row("总任务数", str(stats['total_tasks']))
            table.add_row("完成率", f"{stats['completion_rate']:.1%}")
            table.add_row("预估总工时", f"{stats['total_estimated_hours']:.1f}h")
            table.add_row("实际总工时", f"{stats['total_actual_hours']:.1f}h")
            
            console.print(table)
            
            # 按状态统计
            if any(count > 0 for count in stats['by_status'].values()):
                status_table = Table(title="📈 状态分布")
                status_table.add_column("状态", style="blue")
                status_table.add_column("数量", style="green")
                
                for status, count in stats['by_status'].items():
                    if count > 0:
                        status_table.add_row(status, str(count))
                
                console.print(status_table)
        
    except Exception as e:
        console.print(f"[red]❌ 显示统计失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("info")
def show_info(
    project_name: str = typer.Option("aqua", "--project", help="项目名称")
):
    """显示项目信息"""
    try:
        with get_aqua_kanban_manager(project_name) as manager:
            # 项目信息
            info_table = Table(title=f"🖥️  {project_name.upper()} 项目信息")
            info_table.add_column("项目", style="cyan")
            info_table.add_column("值", style="green")
            
            info_table.add_row("项目名称", project_name)
            info_table.add_row("设备ID", manager.device_id)
            info_table.add_row("数据库路径", str(manager.db_path))
            info_table.add_row("应用目录", str(manager.app_dir))
            info_table.add_row("任务数量", str(manager.get_task_count()))
            
            console.print(info_table)
        
    except Exception as e:
        console.print(f"[red]❌ 显示信息失败: {e}[/red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
