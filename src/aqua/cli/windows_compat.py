"""
AQUA CLI: Windows深度兼容性优化
解决Windows特有问题，实现跨平台一致体验
"""
import os
import sys
import subprocess
import platform
try:
    if platform.system() == "Windows":
        import winreg
    else:
        winreg = None
except ImportError:
    winreg = None
import ctypes
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class WindowsCompatibilityManager:
    """Windows兼容性管理器"""
    
    def __init__(self):
        self.console = console
        self.is_windows = platform.system() == "Windows"
        self.is_admin = self._check_admin_privileges() if self.is_windows else False
        
    def _check_admin_privileges(self) -> bool:
        """检查是否具有管理员权限"""
        if not self.is_windows:
            return False
        
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def setup_enhanced_compatibility(self) -> Dict[str, bool]:
        """设置增强Windows兼容性"""
        if not self.is_windows:
            return {"status": "not_windows"}
        
        results = {}
        
        # 1. UTF-8 编码设置
        results["utf8_encoding"] = self._setup_utf8_encoding()
        
        # 2. 控制台增强
        results["console_enhancement"] = self._setup_console_enhancement()
        
        # 3. 长路径支持
        results["long_path_support"] = self._enable_long_path_support()
        
        # 4. 权限优化
        results["permission_optimization"] = self._optimize_permissions()
        
        # 5. 环境变量设置
        results["environment_variables"] = self._setup_environment_variables()
        
        # 6. PowerShell优化
        results["powershell_optimization"] = self._optimize_powershell()
        
        # 7. 字体和显示优化
        results["display_optimization"] = self._optimize_display()
        
        return results
    
    def _setup_utf8_encoding(self) -> bool:
        """设置UTF-8编码支持"""
        try:
            # 1. 设置控制台代码页为UTF-8
            subprocess.run(['chcp', '65001'], 
                         check=True, shell=True, capture_output=True, text=True)
            
            # 2. 设置Python UTF-8环境变量
            os.environ['PYTHONUTF8'] = '1'
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            
            # 3. 设置控制台输出编码
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
                sys.stderr.reconfigure(encoding='utf-8')
            
            # 4. 设置区域设置
            try:
                import locale
                locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            except:
                try:
                    locale.setlocale(locale.LC_ALL, 'C.UTF-8')
                except:
                    pass
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]UTF-8编码设置失败: {e}[/yellow]")
            return False
    
    def _setup_console_enhancement(self) -> bool:
        """设置控制台增强"""
        try:
            # 1. 启用虚拟终端处理
            kernel32 = ctypes.windll.kernel32
            handle = kernel32.GetStdHandle(-11)  # STD_OUTPUT_HANDLE
            
            # 获取当前控制台模式
            mode = ctypes.c_ulong()
            kernel32.GetConsoleMode(handle, ctypes.byref(mode))
            
            # 启用虚拟终端处理 (ENABLE_VIRTUAL_TERMINAL_PROCESSING)
            mode.value |= 0x0004
            kernel32.SetConsoleMode(handle, mode)
            
            # 2. 设置控制台缓冲区大小
            try:
                subprocess.run(['mode', 'con', 'cols=120', 'lines=30'], 
                             check=True, shell=True, capture_output=True)
            except:
                pass
            
            # 3. 启用快速编辑模式
            try:
                # 这需要修改注册表，需要管理员权限
                if self.is_admin:
                    self._enable_quick_edit_mode()
            except:
                pass
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]控制台增强设置失败: {e}[/yellow]")
            return False
    
    def _enable_long_path_support(self) -> bool:
        """启用长路径支持"""
        try:
            if not self.is_admin:
                return False
            
            # 修改注册表启用长路径支持
            key_path = r"SYSTEM\CurrentControlSet\Control\FileSystem"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, 
                              winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "LongPathsEnabled", 0, winreg.REG_DWORD, 1)
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]长路径支持启用失败: {e}[/yellow]")
            return False
    
    def _optimize_permissions(self) -> bool:
        """优化权限设置"""
        try:
            # 1. 检查当前用户权限
            user_name = os.getenv('USERNAME')
            
            # 2. 确保AQUA目录有正确权限
            aqua_dirs = [
                Path.home() / ".aqua",
                Path("D:/Data/duckdb/AQUA") if Path("D:/").exists() else None,
                Path.cwd()
            ]
            
            for dir_path in aqua_dirs:
                if dir_path and dir_path.exists():
                    self._set_directory_permissions(dir_path, user_name)
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]权限优化失败: {e}[/yellow]")
            return False
    
    def _set_directory_permissions(self, dir_path: Path, user_name: str):
        """设置目录权限"""
        try:
            # 使用icacls设置权限
            subprocess.run([
                'icacls', str(dir_path), 
                '/grant', f'{user_name}:(OI)(CI)F',
                '/T'
            ], check=True, capture_output=True, shell=True)
        except:
            pass
    
    def _setup_environment_variables(self) -> bool:
        """设置环境变量"""
        try:
            # 需要设置的环境变量
            env_vars = {
                'PYTHONUTF8': '1',
                'PYTHONIOENCODING': 'utf-8',
                'PYTHONLEGACYWINDOWSSTDIO': '0',
                'AQUA_WINDOWS_COMPAT': '1'
            }
            
            # 设置当前会话环境变量
            for key, value in env_vars.items():
                os.environ[key] = value
            
            # 尝试设置系统环境变量（需要管理员权限）
            if self.is_admin:
                self._set_system_environment_variables(env_vars)
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]环境变量设置失败: {e}[/yellow]")
            return False
    
    def _set_system_environment_variables(self, env_vars: Dict[str, str]):
        """设置系统环境变量"""
        try:
            key_path = r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, 
                              winreg.KEY_SET_VALUE) as key:
                for name, value in env_vars.items():
                    winreg.SetValueEx(key, name, 0, winreg.REG_SZ, value)
            
            # 通知系统环境变量已更改
            import win32gui
            import win32con
            win32gui.SendMessage(win32con.HWND_BROADCAST, win32con.WM_SETTINGCHANGE, 
                               0, 'Environment')
            
        except Exception:
            pass
    
    def _optimize_powershell(self) -> bool:
        """优化PowerShell设置"""
        try:
            # 1. 检查PowerShell版本
            result = subprocess.run(['powershell', '-Command', '$PSVersionTable.PSVersion'], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            # 2. 设置PowerShell执行策略（如果是管理员）
            if self.is_admin:
                try:
                    subprocess.run([
                        'powershell', '-Command', 
                        'Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine -Force'
                    ], check=True, capture_output=True)
                except:
                    pass
            
            # 3. 创建PowerShell配置文件（支持UTF-8）
            self._create_powershell_profile()
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]PowerShell优化失败: {e}[/yellow]")
            return False
    
    def _create_powershell_profile(self):
        """创建PowerShell配置文件"""
        try:
            # 获取PowerShell配置文件路径
            result = subprocess.run([
                'powershell', '-Command', '$PROFILE'
            ], capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                profile_path = Path(result.stdout.strip())
                profile_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 创建配置内容
                profile_content = '''
# AQUA Windows Compatibility Profile
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 设置别名
Set-Alias -Name aqua -Value python -Option AllScope
Set-Alias -Name ll -Value Get-ChildItem -Option AllScope

# 函数
function aqua-dev { python aqua.py --env dev @args }
function aqua-test { python aqua.py --env test @args }
function aqua-prod { python aqua.py --env prod @args }

# 欢迎信息
Write-Host "🌊 AQUA PowerShell 环境已加载" -ForegroundColor Cyan
                '''.strip()
                
                profile_path.write_text(profile_content, encoding='utf-8')
        
        except Exception:
            pass
    
    def _optimize_display(self) -> bool:
        """优化显示设置"""
        try:
            # 1. 设置控制台字体（如果可能）
            try:
                # 尝试设置等宽字体
                subprocess.run([
                    'reg', 'add', 
                    'HKCU\\Console', 
                    '/v', 'FaceName', 
                    '/t', 'REG_SZ', 
                    '/d', 'Consolas', 
                    '/f'
                ], check=True, capture_output=True)
            except:
                pass
            
            # 2. 启用颜色支持
            try:
                subprocess.run([
                    'reg', 'add',
                    'HKCU\\Console',
                    '/v', 'VirtualTerminalLevel',
                    '/t', 'REG_DWORD',
                    '/d', '1',
                    '/f'
                ], check=True, capture_output=True)
            except:
                pass
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]显示优化失败: {e}[/yellow]")
            return False
    
    def _enable_quick_edit_mode(self):
        """启用快速编辑模式"""
        try:
            subprocess.run([
                'reg', 'add',
                'HKCU\\Console',
                '/v', 'QuickEdit',
                '/t', 'REG_DWORD',
                '/d', '1',
                '/f'
            ], check=True, capture_output=True)
        except:
            pass
    
    def check_compatibility_status(self) -> Dict[str, any]:
        """检查兼容性状态"""
        if not self.is_windows:
            return {"platform": "not_windows"}
        
        status = {
            "platform": "windows",
            "version": platform.release(),
            "is_admin": self.is_admin,
            "encoding": {},
            "console": {},
            "paths": {},
            "permissions": {}
        }
        
        # 检查编码设置
        try:
            result = subprocess.run(['chcp'], capture_output=True, text=True, shell=True)
            status["encoding"]["codepage"] = "65001" in result.stdout
            status["encoding"]["python_utf8"] = os.getenv('PYTHONUTF8') == '1'
            status["encoding"]["io_encoding"] = os.getenv('PYTHONIOENCODING') == 'utf-8'
        except:
            status["encoding"] = {"error": True}
        
        # 检查控制台设置
        try:
            status["console"]["virtual_terminal"] = self._check_virtual_terminal_support()
        except:
            status["console"] = {"error": True}
        
        # 检查路径支持
        try:
            status["paths"]["long_path_support"] = self._check_long_path_support()
        except:
            status["paths"] = {"error": True}
        
        # 检查权限
        try:
            test_dir = Path.home() / ".aqua"
            test_dir.mkdir(exist_ok=True)
            test_file = test_dir / "permission_test.tmp"
            test_file.write_text("test")
            test_file.unlink()
            status["permissions"]["write_access"] = True
        except:
            status["permissions"]["write_access"] = False
        
        return status
    
    def _check_virtual_terminal_support(self) -> bool:
        """检查虚拟终端支持"""
        try:
            kernel32 = ctypes.windll.kernel32
            handle = kernel32.GetStdHandle(-11)
            mode = ctypes.c_ulong()
            kernel32.GetConsoleMode(handle, ctypes.byref(mode))
            return bool(mode.value & 0x0004)
        except:
            return False
    
    def _check_long_path_support(self) -> bool:
        """检查长路径支持"""
        try:
            key_path = r"SYSTEM\CurrentControlSet\Control\FileSystem"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, 
                              winreg.KEY_READ) as key:
                value, _ = winreg.QueryValueEx(key, "LongPathsEnabled")
                return bool(value)
        except:
            return False
    
    def create_windows_service(self, service_name: str = "AQUAService") -> bool:
        """创建Windows服务（可选功能）"""
        if not self.is_windows or not self.is_admin:
            return False
        
        try:
            # 创建服务脚本
            service_script = self._create_service_script()
            
            # 注册服务
            subprocess.run([
                'sc', 'create', service_name,
                'binPath=', f'python {service_script}',
                'start=', 'auto',
                'DisplayName=', 'AQUA Quantitative Analysis Service'
            ], check=True, capture_output=True)
            
            return True
            
        except Exception as e:
            self.console.print(f"[yellow]Windows服务创建失败: {e}[/yellow]")
            return False
    
    def _create_service_script(self) -> Path:
        """创建服务脚本"""
        script_content = '''
import servicemanager
import socket
import sys
import win32event
import win32service
import win32serviceutil

class AQUAService(win32serviceutil.ServiceFramework):
    _svc_name_ = "AQUAService"
    _svc_display_name_ = "AQUA Quantitative Analysis Service"
    _svc_description_ = "AQUA platform background service"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                            servicemanager.PYS_SERVICE_STARTED,
                            (self._svc_name_, ''))
        self.main()

    def main(self):
        # 这里可以放置AQUA服务的主要逻辑
        import time
        while True:
            # 检查停止事件
            if win32event.WaitForSingleObject(self.hWaitStop, 1000) == win32event.WAIT_OBJECT_0:
                break
            # 执行服务逻辑
            time.sleep(1)

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(AQUAService)
        '''.strip()
        
        script_path = Path.cwd() / "scripts" / "aqua_service.py"
        script_path.parent.mkdir(exist_ok=True)
        script_path.write_text(script_content, encoding='utf-8')
        
        return script_path
    
    def generate_compatibility_report(self) -> str:
        """生成兼容性报告"""
        if not self.is_windows:
            return "当前平台不是Windows，无需兼容性报告。"
        
        status = self.check_compatibility_status()
        
        report = f"""
# Windows 兼容性报告

## 系统信息
- 平台: Windows {status['version']}
- 管理员权限: {'是' if status['is_admin'] else '否'}

## 编码支持
- 控制台代码页 (UTF-8): {'✅' if status['encoding'].get('codepage') else '❌'}
- Python UTF-8: {'✅' if status['encoding'].get('python_utf8') else '❌'}
- IO 编码: {'✅' if status['encoding'].get('io_encoding') else '❌'}

## 控制台功能
- 虚拟终端支持: {'✅' if status['console'].get('virtual_terminal') else '❌'}

## 路径支持
- 长路径支持: {'✅' if status['paths'].get('long_path_support') else '❌'}

## 权限检查
- 写入权限: {'✅' if status['permissions'].get('write_access') else '❌'}

## 建议操作
"""
        
        # 生成建议
        suggestions = []
        
        if not status['encoding'].get('codepage'):
            suggestions.append("- 运行 'chcp 65001' 设置UTF-8代码页")
        
        if not status['encoding'].get('python_utf8'):
            suggestions.append("- 设置环境变量 PYTHONUTF8=1")
        
        if not status['console'].get('virtual_terminal'):
            suggestions.append("- 启用虚拟终端处理（需要Windows 10+）")
        
        if not status['paths'].get('long_path_support'):
            suggestions.append("- 启用长路径支持（需要管理员权限）")
        
        if not status['permissions'].get('write_access'):
            suggestions.append("- 检查并修复目录权限")
        
        if not suggestions:
            suggestions.append("- 系统兼容性良好，无需额外操作")
        
        report += '\n'.join(suggestions)
        
        return report

# 全局兼容性管理器实例
windows_compat = WindowsCompatibilityManager()

def setup_windows_compatibility_enhanced():
    """增强版Windows兼容性设置"""
    if not windows_compat.is_windows:
        return True
    
    console.print("🔧 [yellow]正在设置Windows深度兼容性...[/yellow]")
    
    results = windows_compat.setup_enhanced_compatibility()
    
    # 显示结果
    table = Table(title="Windows兼容性设置结果")
    table.add_column("功能", style="cyan")
    table.add_column("状态", justify="center")
    table.add_column("说明", style="dim")
    
    feature_descriptions = {
        "utf8_encoding": "UTF-8编码支持",
        "console_enhancement": "控制台增强",
        "long_path_support": "长路径支持",
        "permission_optimization": "权限优化",
        "environment_variables": "环境变量设置",
        "powershell_optimization": "PowerShell优化",
        "display_optimization": "显示优化"
    }
    
    for feature, success in results.items():
        if feature == "status":
            continue
        
        status_icon = "✅" if success else "❌"
        description = feature_descriptions.get(feature, feature)
        note = "成功" if success else "失败"
        
        table.add_row(description, status_icon, note)
    
    console.print(table)
    
    # 显示建议
    if not all(results.values()):
        console.print("\n💡 [yellow]部分功能设置失败，建议：[/yellow]")
        console.print("  • 以管理员权限运行以获得完整功能")
        console.print("  • 检查Windows版本是否支持相关功能")
        console.print("  • 运行 'aqua doctor' 进行详细诊断")
    
    return True

def windows_compatibility_command(
    setup: bool = typer.Option(False, "--setup", help="设置Windows兼容性"),
    check: bool = typer.Option(False, "--check", help="检查兼容性状态"),
    report: bool = typer.Option(False, "--report", help="生成兼容性报告"),
    service: bool = typer.Option(False, "--create-service", help="创建Windows服务")
):
    """Windows兼容性管理命令"""
    
    if not windows_compat.is_windows:
        console.print("❌ [red]当前平台不是Windows[/red]")
        return
    
    if setup:
        setup_windows_compatibility_enhanced()
    
    elif check:
        status = windows_compat.check_compatibility_status()
        console.print("🔍 [cyan]Windows兼容性状态:[/cyan]")
        console.print(status)
    
    elif report:
        report_content = windows_compat.generate_compatibility_report()
        console.print(Panel.fit(
            report_content,
            title="Windows兼容性报告",
            border_style="blue"
        ))
    
    elif service:
        if windows_compat.create_windows_service():
            console.print("✅ [green]Windows服务创建成功[/green]")
        else:
            console.print("❌ [red]Windows服务创建失败[/red]")
    
    else:
        console.print("请指定操作参数，使用 --help 查看帮助")