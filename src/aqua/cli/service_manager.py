"""
AQUA CLI: 服务管理模块 (基于 Honcho)
"""
import os
import sys
import subprocess
import platform
from pathlib import Path
import time

PID_FILE = Path(__file__).resolve().parent.parent.parent.parent / "temp" / "aqua_services.pid"

class ServiceManager:
    """使用 Honcho 在单个子进程中启动和管理服务。"""

    def __init__(self, procfile_path: Path | str):
        self.procfile_path = Path(procfile_path)
        self.project_root = self.procfile_path.parent
        PID_FILE.parent.mkdir(exist_ok=True)

    def _write_pid(self, pid: int):
        with open(PID_FILE, 'w') as f:
            f.write(str(pid))

    def _read_pid(self) -> int | None:
        if not PID_FILE.exists():
            return None
        try:
            with open(PID_FILE, 'r') as f:
                return int(f.read().strip())
        except (ValueError, FileNotFoundError):
            return None

    def _remove_pid(self):
        if PID_FILE.exists():
            PID_FILE.unlink()

    def _is_process_running(self, pid: int | None) -> bool:
        if pid is None:
            return False
        if platform.system() == "Windows":
            try:
                result = subprocess.run(
                    ['tasklist', '/FI', f'PID eq {pid}'],
                    check=True, capture_output=True, text=True, encoding='utf-8'
                )
                return str(pid) in result.stdout
            except subprocess.CalledProcessError:
                return False
        else:
            try:
                os.kill(pid, 0)
            except OSError:
                return False
            else:
                return True

    def start(self):
        """启动一个 'honcho start' 子进程。"""
        if self.is_running():
            print("服务已经在运行中。", file=sys.stderr)
            return

        if not self.procfile_path.exists():
            raise FileNotFoundError(f"Procfile not found at: {self.procfile_path}")

        # 准备环境变量
        proc_env = os.environ.copy()
        src_path = str(self.project_root / "src")
        python_path = proc_env.get("PYTHONPATH", "")
        if src_path not in python_path.split(os.pathsep):
            proc_env["PYTHONPATH"] = f"{src_path}{os.pathsep}{python_path}"
        
        # honcho 应该在 venv 中被调用
        # 首先尝试直接使用honcho，如果失败再尝试uv run
        venv_scripts = self.project_root / ".venv" / ("Scripts" if platform.system() == "Windows" else "bin")
        honcho_exec = venv_scripts / ("honcho.exe" if platform.system() == "Windows" else "honcho")

        if honcho_exec.exists():
            honcho_cmd = [str(honcho_exec), 'start', '-f', str(self.procfile_path)]
        else:
            # 回退到uv run方式
            uv_exec = venv_scripts / ("uv.exe" if platform.system() == "Windows" else "uv")
            if uv_exec.exists():
                honcho_cmd = [str(uv_exec), 'run', 'honcho', 'start', '-f', str(self.procfile_path)]
            else:
                # 最后尝试系统级别的命令
                honcho_cmd = ['honcho', 'start', '-f', str(self.procfile_path)]

        # 确保logs目录存在
        logs_dir = self.project_root / 'logs'
        logs_dir.mkdir(exist_ok=True)

        try:
            print(f"🚀 启动服务命令: {' '.join(honcho_cmd)}")

            # 在后台启动 honcho 进程
            process = subprocess.Popen(
                honcho_cmd,
                env=proc_env,
                cwd=self.project_root,
                stdout=open(logs_dir / 'services.log', 'w'),
                stderr=subprocess.STDOUT
            )

            self._write_pid(process.pid)
            time.sleep(3) # 等待服务初始化

            # 检查进程是否还在运行
            if process.poll() is None:
                print(f"✅ 服务已在后台启动 (PID: {process.pid})。日志请查看 logs/services.log")
            else:
                print(f"❌ 服务启动失败，进程已退出。请查看 logs/services.log 获取详细信息")

        except FileNotFoundError as e:
            print(f"❌ 找不到命令: {e}")
            print(f"💡 提示: 请确保honcho已安装在虚拟环境中")
            print(f"💡 安装命令: .venv\\Scripts\\python.exe -m pip install honcho")
        except Exception as e:
            print(f"❌ 启动服务时发生错误: {e}")
            print(f"💡 请检查Procfile.dev文件格式是否正确")

    def stop(self):
        """停止 'honcho start' 子进程。"""
        pid = self._read_pid()
        if not self._is_process_running(pid):
            print("服务未在运行中。", file=sys.stderr)
            self._remove_pid()
            return
        
        print(f"正在停止服务 (PID: {pid})...")
        try:
            if platform.system() == "Windows":
                subprocess.run(['taskkill', '/F', '/T', '/PID', str(pid)], check=True)
            else:
                os.kill(pid, 15) # SIGTERM
            self._remove_pid()
            print("服务已停止。")
        except Exception as e:
            print(f"停止服务时发生错误: {e}", file=sys.stderr)

    def is_running(self) -> bool:
        """检查 honcho 进程是否正在运行。"""
        pid = self._read_pid()
        return self._is_process_running(pid)

    def status(self) -> str:
        """返回服务的当前状态。"""
        return "Running" if self.is_running() else "Not Running"