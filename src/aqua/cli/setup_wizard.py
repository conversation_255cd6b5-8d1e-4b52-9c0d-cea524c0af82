"""
AQUA CLI: 智能配置向导系统
支持个人开发者5分钟快速配置全环境
"""
import os
import sys
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import typer
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich.text import Text

console = Console()

@dataclass
class SetupConfig:
    """设置配置数据类"""
    environment: str
    platform: str
    data_source_types: List[str]
    tushare_token: Optional[str] = None
    mysql_config: Optional[Dict] = None
    csv_data_path: Optional[str] = None
    memory_limit: int = 1536  # MB
    enable_monitoring: bool = True
    backup_enabled: bool = True

class EnvironmentDetector:
    """环境自动检测器"""
    
    @staticmethod
    def detect_platform() -> str:
        """检测运行平台"""
        system = platform.system().lower()
        if system == "windows":
            return "windows"
        elif system == "darwin":
            return "unix"  # macOS使用unix配置
        else:
            return "unix"  # Linux使用unix配置
    
    @staticmethod
    def detect_memory() -> int:
        """检测系统内存(MB)"""
        try:
            if platform.system() == "Windows":
                try:
                    import psutil
                    return int(psutil.virtual_memory().total / (1024 * 1024))
                except ImportError:
                    # 如果psutil不可用，返回默认值
                    return 8192  # 8GB 默认值
            else:
                # macOS/Linux
                result = subprocess.run(['sysctl', '-n', 'hw.memsize'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    return int(result.stdout.strip()) // (1024 * 1024)
                else:
                    # 默认假设8GB
                    return 8192
        except:
            return 4096  # 默认4GB
    
    @staticmethod
    def check_network_speed() -> str:
        """检测网络环境（中国/海外）"""
        try:
            import urllib.request
            # 测试访问清华镜像源
            with urllib.request.urlopen('https://pypi.tuna.tsinghua.edu.cn/simple', timeout=3) as response:
                if response.status == 200:
                    return "china"
        except:
            pass
        return "international"
    
    @staticmethod
    def detect_data_sources() -> List[str]:
        """检测可用的数据源"""
        available_sources = []
        
        # 检测TUSHARE TOKEN
        if os.getenv('TUSHARE_TOKEN'):
            available_sources.append('tushare')
        
        # 检测CSV数据目录
        csv_paths = [
            os.getenv('CSV_DATA_PATH'),
            'D:/Data/RAW/FromC2C',  # Windows
            '~/Documents/Data/FromC2C'  # macOS/Linux
        ]
        for path in csv_paths:
            if path and Path(path).expanduser().exists():
                available_sources.append('csv')
                break
        
        # 检测MySQL连接（基础检测）
        try:
            import pymysql
            available_sources.append('mysql')
        except ImportError:
            pass
        
        return available_sources

class SetupWizard:
    """智能配置向导"""
    
    def __init__(self):
        self.console = console
        self.detector = EnvironmentDetector()
        self.config = None
    
    def run(self) -> SetupConfig:
        """运行完整的配置向导"""
        self.console.print(Panel.fit(
            "🧙‍♂️ [bold cyan]AQUA 智能配置向导[/bold cyan]\n\n"
            "将在 5 分钟内完成全环境配置\n"
            "支持自动检测和智能推荐",
            title="欢迎使用 AQUA",
            border_style="cyan"
        ))
        
        # 步骤1：环境检测
        detected_config = self._detect_environment()
        
        # 步骤2：用户配置确认和调整
        user_config = self._collect_user_preferences(detected_config)
        
        # 步骤3：配置验证和应用
        final_config = self._validate_and_apply_config(user_config)
        
        # 步骤4：完成设置
        self._complete_setup(final_config)
        
        return final_config
    
    def _detect_environment(self) -> SetupConfig:
        """自动检测环境配置"""
        self.console.print("🔍 [yellow]正在检测系统环境...[/yellow]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            # 检测任务
            tasks = [
                ("检测运行平台", self.detector.detect_platform),
                ("检测系统内存", self.detector.detect_memory),
                ("检测网络环境", self.detector.check_network_speed),
                ("扫描数据源", self.detector.detect_data_sources),
            ]
            
            results = {}
            for desc, func in tasks:
                task = progress.add_task(desc, total=None)
                results[desc] = func()
                progress.remove_task(task)
        
        # 构建检测结果
        platform_type = results["检测运行平台"]
        memory_mb = results["检测系统内存"]
        network_type = results["检测网络环境"]
        available_sources = results["扫描数据源"]
        
        # 显示检测结果
        table = Table(title="环境检测结果")
        table.add_column("项目", style="cyan")
        table.add_column("检测结果", style="green")
        table.add_column("建议", style="yellow")
        
        table.add_row("运行平台", platform_type.upper(), "✅ 已优化支持")
        table.add_row("系统内存", f"{memory_mb} MB", 
                     "✅ 充足" if memory_mb >= 4096 else "⚠️ 建议4GB+")
        table.add_row("网络环境", 
                     "中国大陆" if network_type == "china" else "海外",
                     "已配置镜像源" if network_type == "china" else "使用默认源")
        table.add_row("可用数据源", ", ".join(available_sources) or "无",
                     f"发现 {len(available_sources)} 个数据源")
        
        self.console.print(table)
        
        # 推荐环境配置
        if memory_mb < 2048:
            recommended_env = "dev"
            memory_limit = min(1024, memory_mb // 2)
        elif memory_mb < 4096:
            recommended_env = "dev"
            memory_limit = 1536
        else:
            recommended_env = "test"
            memory_limit = min(4096, memory_mb // 2)
        
        return SetupConfig(
            environment=recommended_env,
            platform=platform_type,
            data_source_types=available_sources,
            memory_limit=memory_limit,
            enable_monitoring=True,
            backup_enabled=True
        )
    
    def _collect_user_preferences(self, detected: SetupConfig) -> SetupConfig:
        """收集用户配置偏好"""
        self.console.print("\n📝 [yellow]配置用户偏好...[/yellow]")
        
        # 选择运行环境
        env_choices = {
            "dev": "开发环境 (轻量级，适合日常开发)",
            "test": "测试环境 (标准配置，适合测试验证)",
            "prod": "生产环境 (高性能，适合正式使用)"
        }
        
        self.console.print("\n🏷️ 选择运行环境：")
        for key, desc in env_choices.items():
            marker = "✅" if key == detected.environment else "  "
            self.console.print(f"{marker} {key}: {desc}")
        
        env = Prompt.ask(
            "请选择环境",
            choices=list(env_choices.keys()),
            default=detected.environment
        )
        detected.environment = env
        
        # 配置数据源
        if detected.data_source_types:
            self.console.print(f"\n📊 检测到数据源: {', '.join(detected.data_source_types)}")
            use_detected = Confirm.ask("使用检测到的数据源配置？", default=True)
            
            if not use_detected:
                detected.data_source_types = self._configure_data_sources_manually()
        else:
            self.console.print("\n📊 未检测到数据源，请手动配置：")
            detected.data_source_types = self._configure_data_sources_manually()
        
        # TUSHARE配置
        if 'tushare' in detected.data_source_types:
            if not os.getenv('TUSHARE_TOKEN'):
                token = Prompt.ask(
                    "请输入TUSHARE TOKEN (可留空稍后配置)",
                    default=""
                )
                if token:
                    detected.tushare_token = token
        
        # MySQL配置
        if 'mysql' in detected.data_source_types:
            configure_mysql = Confirm.ask("配置MySQL连接？", default=True)
            if configure_mysql:
                detected.mysql_config = self._configure_mysql()
        
        # CSV路径配置
        if 'csv' in detected.data_source_types:
            csv_path = Prompt.ask(
                "CSV数据路径",
                default=os.getenv('CSV_DATA_PATH', 'auto-detect')
            )
            if csv_path != 'auto-detect':
                detected.csv_data_path = csv_path
        
        # 性能配置
        memory_limit = Prompt.ask(
            f"内存限制 (MB)",
            default=str(detected.memory_limit)
        )
        detected.memory_limit = int(memory_limit)
        
        # 监控和备份
        detected.enable_monitoring = Confirm.ask("启用性能监控？", default=True)
        detected.backup_enabled = Confirm.ask("启用自动备份？", default=True)
        
        return detected
    
    def _configure_data_sources_manually(self) -> List[str]:
        """手动配置数据源"""
        sources = []
        source_options = {
            'tushare': 'TUSHARE (在线股票期货数据)',
            'csv': 'CSV文件 (本地历史数据)',
            'mysql': 'MySQL数据库 (关系型数据库)'
        }
        
        for key, desc in source_options.items():
            if Confirm.ask(f"启用 {desc}？"):
                sources.append(key)
        
        return sources
    
    def _configure_mysql(self) -> Dict:
        """配置MySQL连接"""
        return {
            'host': Prompt.ask("MySQL主机", default="localhost"),
            'port': int(Prompt.ask("MySQL端口", default="3306")),
            'username': Prompt.ask("用户名", default="root"),
            'password': Prompt.ask("密码", password=True, default=""),
            'database': Prompt.ask("数据库名", default="aqua_db")
        }
    
    def _validate_and_apply_config(self, config: SetupConfig) -> SetupConfig:
        """验证并应用配置"""
        self.console.print("\n🔧 [yellow]验证并应用配置...[/yellow]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            # 验证任务
            tasks = [
                "创建数据目录",
                "验证数据源连接",
                "生成配置文件",
                "初始化数据库",
                "安装依赖"
            ]
            
            for task_name in tasks:
                task = progress.add_task(task_name, total=None)
                
                if task_name == "创建数据目录":
                    self._create_directories(config)
                elif task_name == "验证数据源连接":
                    self._validate_data_sources(config)
                elif task_name == "生成配置文件":
                    self._generate_config_overrides(config)
                elif task_name == "初始化数据库":
                    self._initialize_database(config)
                elif task_name == "安装依赖":
                    self._install_dependencies(config)
                
                progress.remove_task(task)
        
        return config
    
    def _create_directories(self, config: SetupConfig):
        """创建必要的目录结构"""
        # 基于平台配置创建目录
        if config.platform == "windows":
            base_path = Path("D:/Data/duckdb/AQUA/DataCenter")
        else:
            base_path = Path("~/Documents/Data/duckdb/AQUA/DataCenter").expanduser()
        
        dirs_to_create = [
            base_path,
            base_path / "backup",
            base_path / "cache" / config.environment,
            base_path / "logs" / config.environment,
            base_path / "datasources" / "csv" / config.environment,
            base_path / "realtime",
            base_path / "exports"
        ]
        
        for dir_path in dirs_to_create:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def _validate_data_sources(self, config: SetupConfig):
        """验证数据源连接"""
        for source in config.data_source_types:
            if source == 'tushare' and config.tushare_token:
                # 简单的TUSHARE连接测试
                pass
            elif source == 'mysql' and config.mysql_config:
                # 简单的MySQL连接测试
                pass
            elif source == 'csv' and config.csv_data_path:
                # CSV路径验证
                csv_path = Path(config.csv_data_path).expanduser()
                if not csv_path.exists():
                    self.console.print(f"[yellow]警告: CSV路径不存在 {csv_path}[/yellow]")
    
    def _generate_config_overrides(self, config: SetupConfig):
        """生成配置覆盖文件"""
        # 生成环境变量文件
        env_file_path = Path.cwd() / ".env"
        env_content = []
        
        env_content.append(f"AQUA_ENV={config.environment}")
        
        if config.tushare_token:
            env_content.append(f"TUSHARE_TOKEN={config.tushare_token}")
        
        if config.csv_data_path:
            env_content.append(f"CSV_DATA_PATH={config.csv_data_path}")
        
        if env_content:
            with open(env_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(env_content))
    
    def _initialize_database(self, config: SetupConfig):
        """初始化数据库"""
        # 使用现有的数据库初始化逻辑
        pass
    
    def _install_dependencies(self, config: SetupConfig):
        """安装必要的依赖"""
        # 根据数据源类型安装特定依赖
        extra_deps = []
        
        if 'mysql' in config.data_source_types:
            extra_deps.append('pymysql')
        
        if 'tushare' in config.data_source_types:
            extra_deps.append('tushare')
        
        # 这里可以调用uv安装额外依赖
        if extra_deps:
            self.console.print(f"安装额外依赖: {', '.join(extra_deps)}")
    
    def _complete_setup(self, config: SetupConfig):
        """完成设置"""
        self.console.print(Panel.fit(
            "🎉 [bold green]配置完成！[/bold green]\n\n"
            f"环境: {config.environment}\n"
            f"平台: {config.platform}\n"
            f"数据源: {', '.join(config.data_source_types)}\n"
            f"内存限制: {config.memory_limit} MB\n\n"
            "[cyan]下一步操作：[/cyan]\n"
            "• 运行 'aqua init' 初始化项目\n"
            "• 运行 'aqua start' 启动服务\n"  
            "• 运行 'aqua doctor' 检查系统健康",
            title="设置完成",
            border_style="green"
        ))


def setup_command(ctx: typer.Context):
    """智能配置向导命令"""
    wizard = SetupWizard()
    config = wizard.run()
    
    # 保存配置到上下文（如果需要）
    if ctx.obj:
        ctx.obj.setup_config = config
    
    return config