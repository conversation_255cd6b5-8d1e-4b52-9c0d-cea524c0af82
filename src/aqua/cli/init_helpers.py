
"""
AQUA CLI: 环境初始化帮助函数
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

# --- Rich 美化输出 ---
try:
    from rich.console import Console
    console = Console()
except ImportError:
    console = None

def _get_project_root() -> Path:
    return Path(__file__).resolve().parent.parent.parent.parent

def _get_venv_dir() -> Path:
    return _get_project_root() / ".venv"

def _get_python_exec() -> Path:
    is_windows = platform.system() == "Windows"
    return _get_venv_dir() / "Scripts" / "python.exe" if is_windows else _get_venv_dir() / "bin" / "python"

def _get_uv_exec() -> Path:
    """获取UV可执行文件路径，优先检查系统级安装"""
    # 首先检查系统是否有全局UV安装
    try:
        result = subprocess.run(['uv', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            # 系统有UV，返回'uv'让系统PATH处理
            return Path('uv')
    except (subprocess.SubprocessError, FileNotFoundError):
        pass
    
    # 如果系统没有UV，检查虚拟环境中的UV
    is_windows = platform.system() == "Windows"
    venv_uv = _get_venv_dir() / "Scripts" / "uv.exe" if is_windows else _get_venv_dir() / "bin" / "uv"
    
    if venv_uv.exists():
        return venv_uv
    
    # 都没有找到，返回系统级路径让后续处理
    return Path('uv')

def _run_command(command, description, cwd=None, quiet=False):
    """通用命令执行函数"""
    if console and not quiet:
        console.print(f"▶️ [bold cyan]执行: {description}[/bold cyan] ([italic]{' '.join(map(str, command))}[/italic])")
    
    try:
        if platform.system() == "Windows":
            command = [str(c) for c in command]

        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            cwd=cwd or _get_project_root()
        )
        if console and not quiet and result.stdout:
            console.print(f"[dim]{result.stdout.strip()}[/dim]")
        return True
    except FileNotFoundError:
        if console:
            console.print(f"❌ [bold red]错误: 命令 '{command[0]}' 未找到。[/bold red]")
        return False
    except subprocess.CalledProcessError as e:
        if console:
            from rich.panel import Panel
            console.print(Panel(
                f"[bold red]命令执行失败:[/bold red]\n\n"
                f"[yellow]命令:[/yellow] {' '.join(map(str, command))}\n"
                f"[yellow]返回码:[/yellow] {e.returncode}\n\n"
                f"[bold]--- STDOUT ---[/bold]\n{e.stdout}\n"
                f"[bold]--- STDERR ---[/bold]\n{e.stderr}",
                title="[bold red]执行错误[/bold red]",
                border_style="red"
            ))
        else:
            print(f"错误: {description} 失败。\n{e.stderr}")
        return False
    except Exception as e:
        if console:
            console.print(f"❌ [bold red]未知错误: {e}[/bold red]")
        else:
            print(f"未知错误: {e}")
        return False

def install_uv():
    """检查或安装uv到系统路径"""
    uv_exec = _get_uv_exec()
    if uv_exec.exists():
        if console:
            console.print("✅ [green]uv[/green] 已在虚拟环境中找到。")
        return True

    if console:
        console.print("🔎 [yellow]正在检查系统是否已安装 uv...[/yellow]")
    
    try:
        subprocess.run(['uv', '--version'], check=True, capture_output=True)
        if console:
            console.print("✅ [green]uv[/green] 已在系统中找到。")
        return True
    except (FileNotFoundError, subprocess.CalledProcessError):
        if console:
            console.print("🔧 [yellow]系统中未找到 uv，正在尝试安装...[/yellow]")
        else:
            print("安装 uv...")
        
        install_command = [sys.executable, '-m', 'pip', 'install', 'uv']
        if _run_command(install_command, "使用 pip 安装 uv"):
            if console:
                console.print("✅ [bold green]uv 安装成功！[/bold green]")
            return True
        
        if console:
            console.print("❌ [bold red]uv 安装失败。请手动安装 uv: 'pip install uv'[/bold red]")
        return False

def create_project_venv():
    """使用uv创建项目虚拟环境"""
    venv_dir = _get_venv_dir()
    python_exec = _get_python_exec()
    
    if venv_dir.exists() and python_exec.exists():
        if console:
            console.print(f"✅ [green]虚拟环境[/green] '{venv_dir.name}' 已存在。")
        return True

    if console:
        console.print(f"🔧 [yellow]正在创建虚拟环境 '{venv_dir.name}'...[/yellow]")
    else:
        print(f"创建虚拟环境 '{venv_dir.name}'...")

    if not _run_command(['uv', 'venv', str(venv_dir), '--python', sys.executable], "创建虚拟环境"):
        return False
    
    if console:
        console.print(f"✅ [bold green]虚拟环境创建成功！[/bold green] at [cyan]{venv_dir}[/cyan]")
    return True

def _get_requirements_file():
    """根据平台选择合适的requirements文件"""
    project_root = _get_project_root()
    
    if platform.system() == "Windows":
        windows_req = project_root / "requirements-windows.txt"
        if windows_req.exists():
            if console:
                console.print("🪟 [cyan]检测到Windows环境，使用Windows优化的依赖文件[/cyan]")
            return windows_req
        else:
            if console:
                console.print("⚠️ [yellow]Windows专用依赖文件不存在，使用通用依赖文件[/yellow]")
    
    # 默认使用通用requirements文件
    return project_root / "requirements.txt"

def sync_dependencies():
    """使用智能镜像源选择同步依赖"""
    requirements_file = _get_requirements_file()
    uv_exec = _get_uv_exec()

    if not requirements_file.exists():
        if console:
            console.print(f"⚠️ [yellow]警告: 未找到 '{requirements_file}'，跳过依赖安装。[/yellow]")
        return True

    if console:
        console.print("同步依赖...")
    
    # 使用智能镜像源管理器
    try:
        from utils.smart_mirror_manager import SmartMirrorManager
        mirror_manager = SmartMirrorManager()
        
        # 获取带故障转移的镜像源列表
        mirror_urls = mirror_manager.get_mirror_with_failover()
        
        if console:
            console.print(f"🔍 [cyan]发现 {len(mirror_urls)} 个可用镜像源，开始智能选择...[/cyan]")
        
        # 按优先级尝试每个镜像源
        for i, mirror_url in enumerate(mirror_urls):
            mirror_name = None
            for mirror in mirror_manager.mirrors:
                if mirror.url == mirror_url:
                    mirror_name = mirror.name
                    break
            
            if console:
                console.print(f"📡 [yellow]尝试镜像源 {i+1}/{len(mirror_urls)}: {mirror_name or 'Unknown'}[/yellow]")
            
            command = [str(uv_exec), 'pip', 'sync', str(requirements_file), '--index-url', mirror_url]
            
            if _run_command(command, f"使用镜像源同步依赖 ({command[-1]})"):
                if console:
                    console.print(f"✅ [bold green]项目依赖已是最新状态！[/bold green] (使用: {mirror_name})")
                return True
            else:
                # 报告镜像源故障
                mirror_manager.report_mirror_failure(mirror_url)
                if console:
                    console.print(f"⚠️ [yellow]镜像源 {mirror_name} 同步失败，尝试下一个...[/yellow]")
    
    except ImportError:
        # 如果智能镜像源管理器不可用，使用简单的故障转移
        if console:
            console.print("⚠️ [yellow]智能镜像源管理器不可用，使用简单故障转移[/yellow]")
        
        # 简单的镜像源列表（优先级顺序）
        mirror_sources = [
            ("清华大学", "https://pypi.tuna.tsinghua.edu.cn/simple"),
            ("阿里云", "http://mirrors.aliyun.com/pypi/simple"),
            ("华为云", "https://repo.huaweicloud.com/repository/pypi/simple"),
            ("腾讯云", "http://mirrors.cloud.tencent.com/pypi/simple"),
            ("PyPI官方", "https://pypi.org/simple/")
        ]
        
        for mirror_name, mirror_url in mirror_sources:
            if console:
                console.print(f"📡 [yellow]尝试镜像源: {mirror_name}[/yellow]")
            
            command = [str(uv_exec), 'pip', 'sync', str(requirements_file), '--index-url', mirror_url]
            
            if _run_command(command, f"使用镜像源同步依赖 ({mirror_name})"):
                if console:
                    console.print(f"✅ [bold green]项目依赖已是最新状态！[/bold green] (使用: {mirror_name})")
                return True
            else:
                if console:
                    console.print(f"⚠️ [yellow]镜像源 {mirror_name} 同步失败，尝试下一个...[/yellow]")

    # 所有镜像源都失败，尝试无镜像源同步
    if console:
        console.print("🔄 [yellow]所有镜像源均失败，尝试直接同步...[/yellow]")
    
    command = [str(uv_exec), 'pip', 'sync', str(requirements_file)]
    if _run_command(command, "使用默认源同步依赖"):
        if console:
            console.print("✅ [bold green]项目依赖已是最新状态！[/bold green]")
        return True

    if console:
        console.print("❌ [bold red]依赖同步失败。请检查 'requirements.txt' 和网络连接。[/bold red]")
    return False

def init_database(env: str, db_path: Path):
    """
    根据给定的路径初始化数据库。
    """
    if console:
        console.print(f"🔧 [yellow]正在为 '{env}' 环境初始化数据库 at {db_path}...[/yellow]")
    else:
        print(f"为 '{env}' 环境初始化数据库 at {db_path}...")

    db_path.parent.mkdir(parents=True, exist_ok=True)

    if db_path.exists():
        if console:
            console.print(f"✅ [green]数据库[/green] '{db_path.name}' 已存在。")
        return True

    try:
        db_path.touch()
        if console:
            console.print(f"✅ [bold green]数据库 '{db_path.name}' 创建成功！[/bold green]")
        return True
    except Exception as e:
        if console:
            console.print(f"❌ [bold red]数据库创建失败: {e}[/bold red]")
        return False
