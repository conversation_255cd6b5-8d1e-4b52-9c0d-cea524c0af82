#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA CLI镜像源管理命令
提供镜像源测试、选择和管理功能
"""

import typer
from typing import Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from pathlib import Path
import json

# 避免循环导入
console = Console()

def mirror_command(
    test: bool = typer.Option(False, "--test", help="测试所有镜像源性能"),
    status: bool = typer.Option(False, "--status", help="显示镜像源状态"),
    best: bool = typer.Option(False, "--best", help="显示最佳镜像源"),
    strategy: Optional[str] = typer.Option(None, "--strategy", help="选择策略 [auto/speed/stability/region]"),
    clear_cache: bool = typer.Option(False, "--clear-cache", help="清除性能缓存"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="显示详细信息")
):
    """
    🌐 镜像源管理 - 智能选择和性能优化
    """
    try:
        from utils.smart_mirror_manager import SmartMirrorManager
        
        if clear_cache:
            cache_file = Path("cache/mirror_performance.json")
            if cache_file.exists():
                cache_file.unlink()
                console.print("✅ [green]镜像源性能缓存已清除[/green]")
            else:
                console.print("ℹ️  [yellow]缓存文件不存在[/yellow]")
            return
        
        manager = SmartMirrorManager()
        
        if test:
            console.print("🔍 [cyan]开始测试所有镜像源性能...[/cyan]")
            console.print()
            
            # 同步测试所有镜像源
            results = manager.test_all_mirrors_sync()
            
            # 显示测试结果
            table = Table(title="镜像源性能测试结果")
            table.add_column("镜像源", style="cyan", no_wrap=True)
            table.add_column("地区", style="blue")
            table.add_column("响应时间", style="green")
            table.add_column("状态", style="bold")
            table.add_column("特性", style="dim")
            
            for mirror in sorted(manager.mirrors, key=lambda m: m.response_time):
                status_icon = "✅" if mirror.is_healthy else "❌"
                response_time = f"{mirror.response_time:.2f}s" if mirror.response_time > 0 else "超时"
                features = ", ".join(mirror.features[:2])  # 只显示前两个特性
                
                table.add_row(
                    mirror.name,
                    mirror.region,
                    response_time,
                    status_icon,
                    features
                )
            
            console.print(table)
            console.print()
            
            # 保存结果到缓存
            manager._save_performance_cache()
            console.print("💾 [green]性能测试结果已保存到缓存[/green]")
        
        elif status:
            status_info = manager.get_mirror_status()
            
            # 显示总体状态
            status_panel = Panel(
                f"总镜像源: {status_info['total_mirrors']} | "
                f"健康镜像源: {status_info['healthy_mirrors']} | "
                f"当前选择: {status_info['selected_mirror'] or '未选择'}",
                title="📊 镜像源总体状态",
                style="blue"
            )
            console.print(status_panel)
            console.print()
            
            # 显示详细状态表格
            table = Table(title="镜像源详细状态")
            table.add_column("镜像源", style="cyan", no_wrap=True)
            table.add_column("地区", style="blue")
            table.add_column("优先级", style="magenta")
            table.add_column("响应时间", style="green")
            table.add_column("成功率", style="yellow")
            table.add_column("状态", style="bold")
            table.add_column("失败次数", style="red")
            
            if verbose:
                table.add_column("最后检查", style="dim")
                table.add_column("特性", style="dim")
            
            for mirror_status in status_info['mirrors']:
                row = [
                    mirror_status['name'],
                    mirror_status['region'],
                    str(mirror_status['priority']),
                    mirror_status['response_time'],
                    mirror_status['success_rate'],
                    mirror_status['is_healthy'],
                    str(mirror_status['failure_count'])
                ]
                
                if verbose:
                    row.extend([
                        mirror_status['last_check'],
                        ", ".join(mirror_status['features'][:2])
                    ])
                
                table.add_row(*row)
            
            console.print(table)
        
        elif best:
            if strategy:
                best_mirror = manager.select_best_mirror(strategy)
            else:
                # 自动测试性能并选择最佳镜像源
                console.print("🔍 [cyan]正在测试镜像源性能...[/cyan]")
                manager.test_all_mirrors_sync()
                best_mirror = manager.select_best_mirror()
            
            # 显示最佳镜像源信息
            best_info = Panel(
                f"**名称**: {best_mirror.name}\n"
                f"**URL**: {best_mirror.url}\n" 
                f"**地区**: {best_mirror.region}\n"
                f"**优先级**: {best_mirror.priority}\n"
                f"**响应时间**: {best_mirror.response_time:.2f}s\n"
                f"**成功率**: {best_mirror.success_rate:.1%}\n"
                f"**特性**: {', '.join(best_mirror.features)}",
                title=f"🏆 最佳镜像源 (策略: {strategy or 'auto'})",
                style="green"
            )
            console.print(best_info)
            
            # 显示使用建议
            console.print("\n💡 [cyan]使用建议:[/cyan]")
            console.print(f"   pip install -i {best_mirror.url} <package>")
            console.print(f"   uv add --index-url {best_mirror.url} <package>")
        
        else:
            # 默认显示简要状态和使用说明
            status_info = manager.get_mirror_status()
            
            console.print(Panel(
                f"总镜像源: {status_info['total_mirrors']} | "
                f"健康镜像源: {status_info['healthy_mirrors']}",
                title="🌐 AQUA 镜像源管理",
                style="blue"
            ))
            
            console.print("\n📋 [bold cyan]可用命令:[/bold cyan]")
            console.print("   [green]--test[/green]     测试所有镜像源性能")
            console.print("   [green]--status[/green]   显示镜像源详细状态") 
            console.print("   [green]--best[/green]     显示和选择最佳镜像源")
            console.print("   [green]--clear-cache[/green] 清除性能缓存")
            
            console.print("\n🔧 [bold cyan]选择策略:[/bold cyan]")
            console.print("   [yellow]auto[/yellow]       自动选择 (综合考虑)")
            console.print("   [yellow]speed[/yellow]      速度优先")
            console.print("   [yellow]stability[/yellow]  稳定性优先")
            console.print("   [yellow]region[/yellow]     地区优先")
            
            console.print("\n💡 [bold cyan]使用示例:[/bold cyan]")
            console.print("   aqua mirror --test")
            console.print("   aqua mirror --best --strategy speed")
            console.print("   aqua mirror --status --verbose")
    
    except ImportError as e:
        console.print(f"❌ [red]智能镜像源管理器不可用: {e}[/red]")
        console.print("💡 [yellow]请确保已安装 aiohttp 和 requests 依赖[/yellow]")
        raise typer.Exit(1)
    
    except Exception as e:
        console.print(f"❌ [red]镜像源管理失败: {e}[/red]")
        if verbose:
            import traceback
            console.print(traceback.format_exc())
        raise typer.Exit(1)