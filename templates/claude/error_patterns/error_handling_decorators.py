"""
Error Handling Patterns and Decorators for AQUA Project

Standard error handling decorators and patterns following AQUA best practices.
Provides comprehensive error management, retry logic, circuit breakers, and monitoring.
"""

from typing import Any, Callable, Dict, Optional, Type, Union, List
from functools import wraps
import logging
import traceback
import time
import threading
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

# Setup logger
logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels for categorization"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorContext:
    """Error context information for comprehensive error tracking"""
    module: str
    function: str
    operation: str
    input_data: Optional[Dict[str, Any]] = None
    user_message: str = "An error occurred"
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
    timestamp: datetime = None
    correlation_id: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


# Base Exception Classes
class AQUABaseException(Exception):
    """Base exception for AQUA project with enhanced context"""
    
    def __init__(self, message: str, context: Optional[ErrorContext] = None):
        super().__init__(message)
        self.context = context or ErrorContext(
            module="unknown", 
            function="unknown", 
            operation="unknown"
        )
        self.timestamp = datetime.now()


class ValidationError(AQUABaseException):
    """Data validation error"""
    def __init__(self, message: str, context: Optional[ErrorContext] = None):
        super().__init__(message, context)
        if context:
            context.severity = ErrorSeverity.HIGH


class ProcessingError(AQUABaseException):
    """Data processing error"""
    pass


class ConfigurationError(AQUABaseException):
    """Configuration error"""
    def __init__(self, message: str, context: Optional[ErrorContext] = None):
        super().__init__(message, context)
        if context:
            context.severity = ErrorSeverity.CRITICAL


class ExternalServiceError(AQUABaseException):
    """External service communication error"""
    pass


class PerformanceError(AQUABaseException):
    """Performance threshold exceeded error"""
    pass


class TimeoutError(AQUABaseException):
    """Operation timeout error"""
    pass


# Error Handling Decorators
def handle_errors(
    fallback_return: Any = None,
    reraise: bool = True,
    log_errors: bool = True,
    custom_handler: Optional[Callable] = None,
    expected_exceptions: Optional[List[Type[Exception]]] = None
):
    """
    Comprehensive error handling decorator
    
    Args:
        fallback_return: Value to return if error occurs and reraise=False
        reraise: Whether to reraise the exception after handling
        log_errors: Whether to log errors
        custom_handler: Custom error handling function
        expected_exceptions: List of expected exception types to handle
        
    Example:
        @handle_errors(reraise=True, log_errors=True)
        def risky_operation(data):
            return process_data(data)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Check if this is an expected exception
                if expected_exceptions and not isinstance(e, tuple(expected_exceptions)):
                    # Unexpected exception, always reraise
                    raise
                
                # Create error context
                context = ErrorContext(
                    module=func.__module__,
                    function=func.__name__,
                    operation=f"Executing {func.__name__}",
                    input_data={"args": str(args), "kwargs": str(kwargs)},
                    correlation_id=getattr(threading.current_thread(), 'correlation_id', None)
                )
                
                # Log error if requested
                if log_errors:
                    logger.error(
                        f"Error in {context.module}.{context.function}: {str(e)}",
                        extra={
                            "context": context,
                            "traceback": traceback.format_exc(),
                            "correlation_id": context.correlation_id
                        }
                    )
                
                # Apply custom handler if provided
                if custom_handler:
                    try:
                        return custom_handler(e, context)
                    except Exception as handler_error:
                        logger.error(f"Error in custom handler: {handler_error}")
                
                # Reraise or return fallback
                if reraise:
                    if isinstance(e, AQUABaseException):
                        raise e
                    else:
                        raise ProcessingError(str(e), context)
                else:
                    return fallback_return
        
        return wrapper
    return decorator


def retry_with_backoff(
    max_retries: int = 3,
    backoff_factor: float = 1.5,
    max_delay: float = 60.0,
    exceptions: tuple = (Exception,),
    on_retry: Optional[Callable] = None
):
    """
    Retry decorator with exponential backoff
    
    Args:
        max_retries: Maximum number of retry attempts
        backoff_factor: Multiplier for delay between retries
        max_delay: Maximum delay between retries in seconds
        exceptions: Tuple of exception types to retry on
        on_retry: Callback function called on each retry
        
    Example:
        @retry_with_backoff(max_retries=3, exceptions=(ConnectionError,))
        def fetch_data():
            return api_call()
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        # Final attempt failed
                        context = ErrorContext(
                            module=func.__module__,
                            function=func.__name__,
                            operation="Retry operation",
                            severity=ErrorSeverity.HIGH
                        )
                        raise ExternalServiceError(
                            f"Operation failed after {max_retries} attempts: {str(e)}",
                            context
                        )
                    
                    # Calculate delay with exponential backoff
                    delay = min(backoff_factor ** attempt, max_delay)
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed for {func.__name__}, "
                        f"retrying in {delay:.2f}s: {str(e)}"
                    )
                    
                    # Call retry callback if provided
                    if on_retry:
                        try:
                            on_retry(attempt + 1, e, delay)
                        except Exception as callback_error:
                            logger.error(f"Error in retry callback: {callback_error}")
                    
                    time.sleep(delay)
                except Exception as e:
                    # Non-retryable exception
                    raise e
            
            # This should never be reached, but just in case
            raise last_exception
        
        return wrapper
    return decorator


def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    expected_exception: Type[Exception] = Exception
):
    """
    Circuit breaker pattern for external dependencies
    
    Args:
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Time in seconds before attempting to close circuit
        expected_exception: Exception type that triggers circuit breaker
        
    Example:
        @circuit_breaker(failure_threshold=3, recovery_timeout=60)
        def call_external_api():
            return api.get_data()
    """
    def decorator(func: Callable) -> Callable:
        # Circuit state tracking
        failure_count = 0
        last_failure_time = 0
        circuit_state = "closed"  # closed, open, half-open
        state_lock = threading.Lock()
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal failure_count, last_failure_time, circuit_state
            
            with state_lock:
                current_time = time.time()
                
                # Check if circuit should transition from open to half-open
                if circuit_state == "open":
                    if current_time - last_failure_time >= recovery_timeout:
                        circuit_state = "half-open"
                        logger.info(f"Circuit breaker for {func.__name__} transitioning to half-open")
                    else:
                        # Circuit is still open
                        context = ErrorContext(
                            module=func.__module__,
                            function=func.__name__,
                            operation="Circuit breaker check",
                            severity=ErrorSeverity.HIGH
                        )
                        raise ExternalServiceError(
                            f"Circuit breaker is open - service temporarily unavailable. "
                            f"Will retry in {recovery_timeout - (current_time - last_failure_time):.1f}s",
                            context
                        )
            
            try:
                result = func(*args, **kwargs)
                
                # Success - reset failure count and close circuit
                with state_lock:
                    if circuit_state in ["half-open", "closed"]:
                        failure_count = 0
                        circuit_state = "closed"
                        if circuit_state == "half-open":
                            logger.info(f"Circuit breaker for {func.__name__} closed after successful call")
                
                return result
                
            except expected_exception as e:
                with state_lock:
                    failure_count += 1
                    last_failure_time = current_time
                    
                    if failure_count >= failure_threshold:
                        circuit_state = "open"
                        logger.error(
                            f"Circuit breaker for {func.__name__} opened after "
                            f"{failure_count} failures"
                        )
                    elif circuit_state == "half-open":
                        circuit_state = "open"
                        logger.warning(f"Circuit breaker for {func.__name__} reopened")
                
                raise e
        
        return wrapper
    return decorator


def timeout(seconds: float):
    """
    Timeout decorator for function execution
    
    Args:
        seconds: Maximum execution time in seconds
        
    Example:
        @timeout(30.0)
        def long_running_operation():
            return heavy_computation()
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_handler(signum, frame):
                context = ErrorContext(
                    module=func.__module__,
                    function=func.__name__,
                    operation="Timeout check",
                    severity=ErrorSeverity.HIGH
                )
                raise TimeoutError(
                    f"Function {func.__name__} timed out after {seconds} seconds",
                    context
                )
            
            # Set up timeout
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(seconds))
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # Clean up timeout
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator


def performance_monitor(max_execution_time: float = 10.0, log_slow_calls: bool = True):
    """
    Performance monitoring decorator
    
    Args:
        max_execution_time: Maximum expected execution time in seconds
        log_slow_calls: Whether to log calls that exceed expected time
        
    Example:
        @performance_monitor(max_execution_time=5.0)
        def expensive_operation():
            return complex_calculation()
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # Log performance metrics
                if log_slow_calls and execution_time > max_execution_time:
                    logger.warning(
                        f"Slow execution: {func.__name__} took {execution_time:.2f}s "
                        f"(expected < {max_execution_time}s)"
                    )
                elif execution_time > max_execution_time * 2:
                    # Performance error for extremely slow calls
                    context = ErrorContext(
                        module=func.__module__,
                        function=func.__name__,
                        operation="Performance monitoring",
                        severity=ErrorSeverity.MEDIUM
                    )
                    raise PerformanceError(
                        f"Function {func.__name__} exceeded maximum execution time: "
                        f"{execution_time:.2f}s > {max_execution_time}s",
                        context
                    )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.warning(f"Function {func.__name__} failed after {execution_time:.2f}s")
                raise e
        
        return wrapper
    return decorator


def validate_input(validator: Callable[[Any], bool], error_message: str):
    """
    Input validation decorator
    
    Args:
        validator: Function that returns True if input is valid
        error_message: Error message to raise if validation fails
        
    Example:
        @validate_input(lambda x: isinstance(x, dict), "Input must be a dictionary")
        def process_dict(data):
            return data
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Validate first argument (common pattern)
            if args and not validator(args[0]):
                context = ErrorContext(
                    module=func.__module__,
                    function=func.__name__,
                    operation="Input validation",
                    input_data={"args": str(args), "kwargs": str(kwargs)},
                    severity=ErrorSeverity.HIGH
                )
                raise ValidationError(error_message, context)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def log_execution(level: int = logging.INFO, include_args: bool = False):
    """
    Execution logging decorator
    
    Args:
        level: Logging level (e.g., logging.INFO, logging.DEBUG)
        include_args: Whether to include function arguments in logs
        
    Example:
        @log_execution(level=logging.DEBUG, include_args=True)
        def important_function(data):
            return process(data)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # Log function entry
            if include_args:
                logger.log(level, f"Entering {func.__name__} with args={args}, kwargs={kwargs}")
            else:
                logger.log(level, f"Entering {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # Log successful completion
                logger.log(level, f"Completed {func.__name__} in {execution_time:.3f}s")
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.log(level, f"Failed {func.__name__} after {execution_time:.3f}s: {str(e)}")
                raise
        
        return wrapper
    return decorator


# Utility Classes for Error Management
class ErrorHandlingPatterns:
    """Collection of common error handling patterns"""
    
    @staticmethod
    def validate_data(data: Any, validator: Callable[[Any], bool], error_message: str):
        """
        Data validation pattern
        
        Args:
            data: Data to validate
            validator: Validation function
            error_message: Error message if validation fails
        """
        if not validator(data):
            raise ValidationError(
                error_message,
                ErrorContext(
                    module="validation",
                    function="validate_data",
                    operation="Data validation",
                    input_data={"data": str(data)},
                    severity=ErrorSeverity.HIGH
                )
            )
    
    @staticmethod
    def safe_execute(operation: Callable, fallback_value=None, log_errors: bool = True):
        """
        Safe execution pattern that never raises exceptions
        
        Args:
            operation: Function to execute safely
            fallback_value: Value to return if operation fails
            log_errors: Whether to log errors
            
        Returns:
            Operation result or fallback value
        """
        try:
            return operation()
        except Exception as e:
            if log_errors:
                logger.error(f"Safe execution failed: {str(e)}")
            return fallback_value
    
    @staticmethod
    def chain_operations(*operations, stop_on_error: bool = True):
        """
        Chain multiple operations with error handling
        
        Args:
            operations: List of operations to execute in sequence
            stop_on_error: Whether to stop on first error
            
        Returns:
            List of results from successful operations
        """
        results = []
        
        for i, operation in enumerate(operations):
            try:
                result = operation()
                results.append(result)
            except Exception as e:
                logger.error(f"Operation {i} failed: {str(e)}")
                
                if stop_on_error:
                    raise ProcessingError(f"Chain stopped at operation {i}: {str(e)}")
                else:
                    results.append(None)
        
        return results


# Example Usage and Testing
if __name__ == "__main__":
    # Example 1: Basic error handling
    @handle_errors(reraise=True)
    def example_processing(data):
        if not data:
            raise ValueError("Data cannot be empty")
        return {"processed": data}
    
    # Example 2: Retry with backoff
    @retry_with_backoff(max_retries=3, exceptions=(ConnectionError,))
    def example_api_call():
        import random
        if random.random() < 0.7:  # 70% chance of failure
            raise ConnectionError("Network error")
        return {"status": "success"}
    
    # Example 3: Circuit breaker
    @circuit_breaker(failure_threshold=2, recovery_timeout=5)
    def example_external_service():
        import random
        if random.random() < 0.8:  # 80% chance of failure
            raise ExternalServiceError("Service unavailable")
        return {"data": "response"}
    
    # Example 4: Performance monitoring
    @performance_monitor(max_execution_time=1.0)
    def example_computation():
        time.sleep(0.5)  # Simulate work
        return "result"
    
    # Run examples
    try:
        result1 = example_processing({"test": "data"})
        print(f"Processing result: {result1}")
        
        result2 = example_computation()
        print(f"Computation result: {result2}")
        
    except Exception as e:
        print(f"Example failed: {e}")