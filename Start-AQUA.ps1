#Requires -Version 5.1
<#
.SYNOPSIS
    AQUA Quantitative Analysis Platform - PowerShell Launcher for Windows 11
.DESCRIPTION
    Enhanced launcher with automatic dependency resolution and service management
.EXAMPLE
    .\Start-AQUA.ps1
#>

[CmdletBinding()]
param()

# Set error handling
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Console setup
$Host.UI.RawUI.WindowTitle = "AQUA Service Launcher - PowerShell"
Clear-Host

Write-Host @"
========================================================
 🌊 AQUA Quantitative Analysis Platform - Windows 11
    PowerShell Enhanced Launcher v2.0
========================================================
"@ -ForegroundColor Cyan

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

function Test-Port {
    param([int]$Port)
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $Port -InformationLevel Quiet -WarningAction SilentlyContinue
        return $connection
    } catch {
        return $false
    }
}

function Write-Status {
    param(
        [string]$Message,
        [string]$Status = "Info"
    )
    
    $color = switch ($Status) {
        "Success" { "Green" }
        "Warning" { "Yellow" }
        "Error" { "Red" }
        default { "White" }
    }
    
    $prefix = switch ($Status) {
        "Success" { "[✓]" }
        "Warning" { "[!]" }
        "Error" { "[✗]" }
        default { "[i]" }
    }
    
    Write-Host "$prefix $Message" -ForegroundColor $color
}

# Check prerequisites
Write-Host "`n🔍 Checking Prerequisites..." -ForegroundColor Yellow

# Check Python
if (-not (Test-Command "python")) {
    Write-Status "Python not found in PATH" "Error"
    Write-Host "Please install Python from https://python.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Status "Python found" "Success"

# Check Node.js
if (-not (Test-Command "node")) {
    Write-Status "Node.js not found in PATH" "Error"
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Status "Node.js found" "Success"

# Setup virtual environment
Write-Host "`n🔧 Setting up Python Environment..." -ForegroundColor Yellow

if (-not (Test-Path ".venv")) {
    Write-Status "Creating virtual environment..." "Info"
    python -m venv .venv
    if ($LASTEXITCODE -ne 0) {
        Write-Status "Failed to create virtual environment" "Error"
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Install Python dependencies
Write-Status "Checking Python dependencies..." "Info"
$pythonExe = ".\.venv\Scripts\python.exe"
$pipExe = ".\.venv\Scripts\pip.exe"

& $pythonExe -c "import uvicorn, fastapi, h11, pydantic_core" 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Status "Installing Python dependencies..." "Info"
    & $pipExe install --upgrade pip | Out-Null
    & $pipExe install uvicorn[standard] fastapi h11 pydantic-core pydantic | Out-Null
    
    if (Test-Path "requirements.txt") {
        & $pipExe install -r requirements.txt | Out-Null
    }
    Write-Status "Python dependencies installed" "Success"
} else {
    Write-Status "Python dependencies OK" "Success"
}

# Setup frontend environment
Write-Host "`n📦 Setting up Frontend Environment..." -ForegroundColor Yellow

# Check package manager
$packageManager = "npm"
if (Test-Command "pnpm") {
    $packageManager = "pnpm"
    Write-Status "Using pnpm package manager" "Success"
} elseif (Test-Command "yarn") {
    $packageManager = "yarn"
    Write-Status "Using yarn package manager" "Success"
} else {
    Write-Status "Using npm package manager" "Info"
}

# Install frontend dependencies
if (-not (Test-Path "frontend\node_modules")) {
    Write-Status "Installing frontend dependencies..." "Info"
    Push-Location "frontend"
    try {
        & $packageManager install
        if ($LASTEXITCODE -ne 0) {
            throw "Package installation failed"
        }
        Write-Status "Frontend dependencies installed" "Success"
    } catch {
        Write-Status "Failed to install frontend dependencies: $_" "Error"
        Pop-Location
        Read-Host "Press Enter to exit"
        exit 1
    }
    Pop-Location
} else {
    Write-Status "Frontend dependencies OK" "Success"
}

# Test backend import
Write-Host "`n🧪 Testing Backend..." -ForegroundColor Yellow
& $pythonExe -c @"
import sys
sys.path.insert(0, 'src')
try:
    from api.main import app
    print('Backend import: SUCCESS')
except Exception as e:
    print(f'Backend import: FAILED - {e}')
    sys.exit(1)
"@

if ($LASTEXITCODE -ne 0) {
    Write-Status "Backend test failed" "Error"
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Status "Backend test passed" "Success"

# Start services
Write-Host "`n🚀 Starting Services..." -ForegroundColor Yellow

# Start backend
Write-Status "Starting backend API service..." "Info"
$backendJob = Start-Process -FilePath "cmd.exe" -ArgumentList "/k", "title AQUA Backend API - Port 8000 && `"$pythonExe`" -m uvicorn src.api.main:app --host 127.0.0.1 --port 8000 --reload" -PassThru

Start-Sleep -Seconds 3

# Start frontend
Write-Status "Starting frontend development service..." "Info"
$frontendArgs = "/k", "title AQUA Frontend Dev - Port 5173 && cd frontend && $packageManager dev"
$frontendJob = Start-Process -FilePath "cmd.exe" -ArgumentList $frontendArgs -PassThru

# Wait for services to initialize
Write-Status "Waiting for services to initialize..." "Info"
Start-Sleep -Seconds 8

# Verify services
Write-Host "`n🔍 Verifying Services..." -ForegroundColor Yellow

$backendRunning = Test-Port -Port 8000
$frontendRunning = Test-Port -Port 5173

if ($backendRunning) {
    Write-Status "Backend service is running on port 8000" "Success"
} else {
    Write-Status "Backend service not yet available on port 8000" "Warning"
}

if ($frontendRunning) {
    Write-Status "Frontend service is running on port 5173" "Success"
} else {
    Write-Status "Frontend service not yet available on port 5173" "Warning"
}

# Display results
Write-Host @"

========================================================
 🎉 AQUA Service Status Summary
========================================================
"@ -ForegroundColor Cyan

Write-Host "Backend Process:  " -NoNewline
if ($backendJob.HasExited -eq $false) { 
    Write-Host "RUNNING" -ForegroundColor Green 
} else { 
    Write-Host "STOPPED" -ForegroundColor Red 
}

Write-Host "Backend Port:     " -NoNewline
if ($backendRunning) { 
    Write-Host "LISTENING" -ForegroundColor Green 
} else { 
    Write-Host "NOT AVAILABLE" -ForegroundColor Yellow 
}

Write-Host "Frontend Process: " -NoNewline
if ($frontendJob.HasExited -eq $false) { 
    Write-Host "RUNNING" -ForegroundColor Green 
} else { 
    Write-Host "STOPPED" -ForegroundColor Red 
}

Write-Host "Frontend Port:    " -NoNewline
if ($frontendRunning) { 
    Write-Host "LISTENING" -ForegroundColor Green 
} else { 
    Write-Host "NOT AVAILABLE" -ForegroundColor Yellow 
}

Write-Host @"

📍 Access URLs:
  • Backend API:        http://127.0.0.1:8000
  • API Documentation:  http://127.0.0.1:8000/docs
  • Frontend Interface: http://localhost:5173

💡 Service Management:
  • Services run in separate command windows
  • Close service window or press Ctrl+C to stop
  • Run this script again to restart services
"@ -ForegroundColor White

if ($backendRunning -and $frontendRunning) {
    Write-Host "`n[✓] ALL SERVICES SUCCESSFULLY STARTED!" -ForegroundColor Green
    Write-Host "`nOpening frontend in default browser..." -ForegroundColor Cyan
    Start-Sleep -Seconds 2
    Start-Process "http://localhost:5173"
} else {
    Write-Host "`n[!] Some services may still be starting..." -ForegroundColor Yellow
    Write-Host "    Please wait 30-60 seconds and check the service windows" -ForegroundColor Yellow
    Write-Host "    If issues persist, check the service windows for error messages" -ForegroundColor Yellow
}

Write-Host "`nPress any key to exit this launcher..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
