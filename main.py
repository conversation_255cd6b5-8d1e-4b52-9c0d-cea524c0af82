#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA后端主入口
作者: AI
创建时间: 2024-07-04
版本: 0.1.0
变更记录:
    - 2024-07-27: 改造为FastAPI应用入口
    - 2024-07-04: 创建最小化后端入口
"""
import sys
import os
from datetime import datetime, timezone, timedelta
from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import json

# 导入API路由
from src.api.routers.unified_data_router import router as unified_data_router
# 保留旧路由作为过渡期备用
from src.api.routers import data_router
# 临时注释掉已删除的api_service导入
# from src.api_service.routers.import_router import router as import_router

# 导入数据库初始化器
from src.database.duckdb_init_check import DuckDBInitChecker

# 导入异常处理框架
from src.utils.exceptions import (
    get_exception_handler, 
    handle_exception,
    AquaException,
    ErrorCode
)

# 导入配置加载器
from src.utils.config_loader_v2 import ConfigLoaderV2

# 导入WebSocket管理器
from src.data_import.websocket_manager import get_websocket_manager
from src.data_import.task_control_manager import TaskControlManager

# --- FastAPI 应用实例 ---
app = FastAPI(
    title="AQUA API",
    description="AQUA项目后端API服务",
    version="0.1.0",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
# 使用新的统一数据路由器（推荐）
app.include_router(unified_data_router)

# 过渡期保留旧路由（逐步废弃）
app.include_router(data_router, prefix="/api/legacy/data", tags=["Legacy Data API"])
# 临时注释掉已删除的import_router
# app.include_router(import_router, prefix="/api/legacy/import", tags=["Legacy Import API"])

# --- 应用启动事件 ---
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    beijing_time = get_beijing_time()
    print(f"[{beijing_time}][INFO][startup] AQUA API服务启动中...")
    
    try:
        # 加载配置
        config_loader = ConfigLoaderV2()
        
        # 从配置文件读取默认环境或从环境变量获取
        app_config = config_loader._load_config().get("app", {})
        default_env = app_config.get("default_environment", "dev")
        current_env = os.getenv("AQUA_ENV", default_env)
        
        print(f"[{beijing_time}][INFO][startup] 使用环境: {current_env}")
        
        # 从配置获取环境特定参数
        env_config = config_loader.get_config(current_env)
        
        # 初始化数据库
        print(f"[{beijing_time}][INFO][startup] 开始数据库自动初始化...")
        db_checker = DuckDBInitChecker(environment=current_env)
        
        # 执行数据库初始化
        init_result = db_checker.initialize_database(force=False)
        
        if init_result.success:
            print(f"[{beijing_time}][INFO][startup] 数据库初始化成功: {init_result.message}")
        else:
            print(f"[{beijing_time}][WARNING][startup] 数据库初始化警告: {init_result.message}")
            
        # 检查数据库健康状态
        health_report = db_checker.get_database_health_report()
        print(f"[{beijing_time}][INFO][startup] 数据库健康状态: {health_report.get('connection_status', 'unknown')}")
        
        # 记录配置信息
        logging_config = env_config.get("logging", {})
        print(f"[{beijing_time}][INFO][startup] 日志级别: {logging_config.get('level', 'INFO')}")
        
        performance_config = env_config.get("performance", {})
        print(f"[{beijing_time}][INFO][startup] 最大工作线程: {performance_config.get('max_workers', 4)}")
        
        print(f"[{beijing_time}][INFO][startup] AQUA API服务启动完成")
        
        # 将配置存储到应用状态中，供API使用
        app.state.config_loader = config_loader
        app.state.current_environment = current_env
        app.state.environment_config = env_config
        
        # 初始化WebSocket管理器
        websocket_manager = get_websocket_manager(current_env)
        app.state.websocket_manager = websocket_manager
        
        # 设置任务控制管理器引用（如果需要）
        # 这里可以集成任务控制管理器，使其能够通过WebSocket发送状态变更
        print(f"[{beijing_time}][INFO][startup] WebSocket管理器初始化完成")
        
    except Exception as e:
        print(f"[{beijing_time}][ERROR][startup] 服务初始化失败: {e}")
        # 不阻止应用启动，但记录错误
        import logging
        logging.error(f"服务初始化失败: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理操作"""
    beijing_time = get_beijing_time()
    print(f"[{beijing_time}][INFO][shutdown] AQUA API服务正在关闭...")
    
    # 关闭WebSocket管理器
    if hasattr(app.state, 'websocket_manager'):
        await app.state.websocket_manager.shutdown()
        print(f"[{beijing_time}][INFO][shutdown] WebSocket管理器已关闭")
    
    # 这里可以添加其他清理操作，如关闭数据库连接等
    print(f"[{beijing_time}][INFO][shutdown] AQUA API服务已关闭")

# --- 全局异常处理器 ---
@app.exception_handler(AquaException)
async def aqua_exception_handler(request: Request, exc: AquaException):
    """处理AQUA系统异常"""
    error_info = exc.to_dict()
    status_code = 500
    
    # 根据错误代码设置HTTP状态码
    if exc.error_code.value < 2000:  # 通用错误
        status_code = 400
    elif exc.error_code.value < 3000:  # 数据库错误
        status_code = 500
    elif exc.error_code.value < 4000:  # 缓存错误
        status_code = 503
    elif exc.error_code.value < 5000:  # 数据处理错误
        status_code = 422
    
    return JSONResponse(
        status_code=status_code,
        content={
            "success": False,
            "error": error_info,
            "timestamp": get_beijing_time(),
            "path": str(request.url.path)
        }
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证异常"""
    error_handler = get_exception_handler()
    error_info = error_handler.handle_exception(exc, {"request_path": str(request.url.path)})
    
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "error": error_info,
            "timestamp": get_beijing_time(),
            "path": str(request.url.path)
        }
    )

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """处理HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "error_code": 1000 + exc.status_code,
                "error_name": "HTTP_ERROR",
                "message": exc.detail,
                "details": {"status_code": exc.status_code},
                "timestamp": get_beijing_time()
            },
            "timestamp": get_beijing_time(),
            "path": str(request.url.path)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理所有未捕获的异常"""
    error_handler = get_exception_handler()
    error_info = error_handler.handle_exception(exc, {
        "request_path": str(request.url.path),
        "request_method": request.method
    })
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": error_info,
            "timestamp": get_beijing_time(),
            "path": str(request.url.path)
        }
    )

def get_beijing_time() -> str:
    """获取北京时间的ISO 8601格式字符串"""
    tz = timezone(timedelta(hours=8))
    return datetime.now(tz).isoformat()

def get_app_config():
    """获取应用配置的便利函数"""
    return {
        "config_loader": getattr(app.state, "config_loader", None),
        "environment": getattr(app.state, "current_environment", "dev"),
        "config": getattr(app.state, "environment_config", {})
    }

# --- WebSocket Endpoints ---

@app.websocket("/ws/progress/{client_id}")
async def websocket_progress_endpoint(websocket: WebSocket, client_id: str):
    """
    WebSocket进度监控端点
    
    Args:
        websocket: WebSocket连接
        client_id: 客户端标识符
    """
    websocket_manager = app.state.websocket_manager
    
    try:
        # 接受连接
        await websocket_manager.connect(websocket, {
            "client_id": client_id,
            "endpoint": "progress"
        })
        
        # 处理客户端消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理客户端消息
                await websocket_manager.handle_client_message(websocket, message)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket_manager._send_error(websocket, "Invalid JSON format")
            except Exception as e:
                await websocket_manager._send_error(websocket, f"Message processing error: {str(e)}")
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"WebSocket连接错误: {e}")
    finally:
        # 断开连接
        await websocket_manager.disconnect(websocket)

@app.get("/api/websocket/stats", tags=["WebSocket"])
async def get_websocket_stats():
    """
    获取WebSocket统计信息
    """
    try:
        websocket_manager = app.state.websocket_manager
        stats = websocket_manager.get_statistics()
        
        return JSONResponse(content={
            "success": True,
            "data": stats,
            "timestamp": get_beijing_time()
        })
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "message": f"获取WebSocket统计失败: {str(e)}",
                    "timestamp": get_beijing_time()
                }
            }
        )

# --- API Endpoints ---

@app.get("/", tags=["General"])
async def read_root():
    """
    根路径，返回服务状态。
    """
    app_config = get_app_config()
    config = app_config["config"]
    
    return JSONResponse(content={
        "service": "AQUA API",
        "status": "ok",
        "environment": app_config["environment"],
        "version": config.get("app", {}).get("version", "1.0"),
        "database_path": config.get("database", {}).get("path", "unknown"),
        "timestamp": get_beijing_time()
    })

@app.get("/health", tags=["General"])
async def health_check():
    """
    健康检查端点，用于监控。
    """
    app_config = get_app_config()
    config = app_config["config"]
    
    # 获取性能配置信息
    performance_config = config.get("performance", {})
    cache_config = config.get("cache", {})
    
    return JSONResponse(content={
        "status": "healthy",
        "environment": app_config["environment"],
        "max_workers": performance_config.get("max_workers", "unknown"),
        "cache_enabled": cache_config.get("enable_l1", False),
        "timestamp": get_beijing_time()
    })


if __name__ == "__main__":
    # 这个部分仅用于直接运行此脚本进行调试，不推荐在生产中使用
    # 推荐使用 uvicorn main:app --reload
    import uvicorn
    print(f"[{get_beijing_time()}][INFO][main] 以调试模式启动Uvicorn服务...")
    uvicorn.run(app, host="127.0.0.1", port=8000) 