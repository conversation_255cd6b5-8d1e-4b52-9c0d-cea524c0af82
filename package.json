{"devDependencies": {"@cypress/vite-dev-server": "^6.0.3", "@eslint/config-helpers": "^0.3.0", "@eslint/js": "^9.30.1", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-vue": "^6.0.0", "@vue/test-utils": "^2.4.6", "cypress": "^14.5.1", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "vite": "^7.0.0", "vitest": "^3.2.4", "vue": "^3.5.17", "vue-tsc": "^3.0.0"}, "scripts": {"lint:fix": "eslint --fix \"src/**/*.{ts,js,vue}\" \"tests/**/*.{ts,js,vue}\" \"mock/**/*.{ts,js,vue}\" --no-error-on-unmatched-pattern"}}